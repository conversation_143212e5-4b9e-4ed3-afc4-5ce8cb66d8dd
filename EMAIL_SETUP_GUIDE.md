# Email Setup Guide - Google SMTP Configuration

This guide will help you configure Google SMTP for sending emails from your Asset Visibility System.

## Prerequisites

1. A Gmail account
2. 2-Factor Authentication enabled on your Gmail account
3. <PERSON><PERSON><PERSON>ail<PERSON> installed (already done via <PERSON>)

## Step 1: Enable 2-Factor Authentication

1. Go to your [Google Account](https://myaccount.google.com/)
2. Click on **Security** in the left sidebar
3. Under "Signing in to Google", click **2-Step Verification**
4. Follow the setup process to enable 2FA

## Step 2: Generate App Password

1. In your Google Account Security settings
2. Under "Signing in to Google", click **App passwords**
3. Select **Mail** from the dropdown
4. Click **Generate**
5. Copy the 16-character password (save it securely)

## Step 3: Update Configuration

Edit `app/config/config.php` and update these values:

```php
// Replace with your actual Gmail address
define('SMTP_USERNAME', '<EMAIL>');

// Replace with the app password you generated
define('SMTP_PASSWORD', 'your-16-character-app-password');

// Replace with your actual Gmail address
define('MAIL_FROM_EMAIL', '<EMAIL>');

// Optional: Enable debug mode for troubleshooting
define('MAIL_DEBUG', true); // Set to false in production
```

## Step 4: Test Email Configuration

1. Run the test script: `http://localhost/asset_visibility/test_email.php`
2. Check the configuration status
3. If configured correctly, a test email will be sent
4. Check your inbox for the test email

## Troubleshooting

### Common Issues:

1. **"Username and Password not accepted"**
   - Make sure you're using an App Password, not your regular Gmail password
   - Verify 2FA is enabled on your account

2. **"Could not connect to SMTP host"**
   - Check your internet connection
   - Verify firewall settings allow outbound connections on port 587

3. **"Authentication failed"**
   - Double-check your Gmail address and App Password
   - Make sure there are no extra spaces in the configuration

### Debug Mode:

Enable debug mode in `config.php`:
```php
define('MAIL_DEBUG', true);
```

This will show detailed SMTP communication for troubleshooting.

## Security Notes

1. **Never commit your App Password to version control**
2. **Use environment variables in production**
3. **Disable debug mode in production**
4. **Consider using OAuth2 for enhanced security**

## Alternative SMTP Providers

If you prefer not to use Gmail, you can configure other SMTP providers:

### Outlook/Hotmail:
```php
define('SMTP_HOST_GMAIL', 'smtp-mail.outlook.com');
define('SMTP_PORT_GMAIL', 587);
define('SMTP_SECURE', 'tls');
```

### Yahoo Mail:
```php
define('SMTP_HOST', 'smtp.mail.yahoo.com');
define('SMTP_PORT', 587);
define('SMTP_SECURE', 'tls');
```

## Production Considerations

1. Use environment variables for sensitive data
2. Set up proper error logging
3. Implement rate limiting for email sending
4. Consider using a dedicated email service (SendGrid, Mailgun, etc.)
