# Asset Visibility System

A PHP OOP MVC application for managing ICT assets inventory.

## Features

- User authentication (login/register)
- Asset management (CRUD operations)
- CSV import functionality for bulk asset uploads
- Search functionality
- Responsive design with Bootstrap and <PERSON><PERSON>C<PERSON> UI
- jQuery for enhanced interactivity

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache web server with mod_rewrite enabled

## Installation

1. Clone the repository to your web server's document root:
   ```
   git clone https://github.com/jmmaguigad/asset_visibility.git
   ```

2. Create a MySQL database:
   ```
   mysql -u root -p
   CREATE DATABASE asset_visibility;
   exit;
   ```

3. Import the database schema:
   ```
   mysql -u root -p asset_visibility < database.sql
   ```

4. Configure the database connection in `app/config/config.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   define('DB_NAME', 'asset_visibility');
   ```

5. Configure the URL root in `app/config/config.php`:
   ```php
   define('URLROOT', 'http://localhost/asset_visibility');
   ```

6. Import data from the CSV file:
   ```
   php import_csv.php
   ```

7. Set up Apache virtual host (optional):
   ```
   <VirtualHost *:80>
       ServerName asset_visibility.local
       DocumentRoot /path/to/asset_visibility
       <Directory /path/to/asset_visibility>
           Options Indexes FollowSymLinks MultiViews
           AllowOverride All
           Require all granted
       </Directory>
   </VirtualHost>
   ```

8. Add the domain to your hosts file (optional):
   ```
   127.0.0.1 asset_visibility.local
   ```

## Usage

1. Navigate to the application URL (e.g., http://localhost/asset_visibility)
2. Login with the default admin account:
   - Email: <EMAIL>
   - Password: password123
3. Start managing your assets!

### Importing Assets from CSV

1. Login as an administrator
2. Navigate to the Assets page
3. Click on the "Import CSV" button
4. Upload your CSV file following the required format
5. Adjust the number of header rows to skip if needed
6. Click "Import" to process the file

The CSV file should have the following columns in order:
- Date
- Site Name
- Employee Name
- Active Directory Name
- Position
- Program/Section
- Computer/Host Name (required)
- Type of Equipment (required)
- Acquisition Type
- Operating System
- Administration Type
- XDR Installed (Yes/No)
- Device Custodian
- Remarks
- PAR Number
- Serial Number (required)
- Acquisition Date
- Estimated Useful Life

## Directory Structure

```
asset_visibility/
├── app/
│   ├── bootstrap.php
│   ├── config/
│   ├── controllers/
│   ├── core/
│   ├── helpers/
│   ├── models/
│   └── views/
├── public/
│   ├── css/
│   ├── js/
│   ├── img/
│   ├── .htaccess
│   └── index.php
├── .htaccess
├── database.sql
└── import_csv.php
```

## License

This project is licensed under the MIT License.
