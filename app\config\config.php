<?php
/**
 * Database Configuration
 */
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'asset_visibility');

/**
 * Application Configuration
 */
define('APPROOT', dirname(dirname(__FILE__)));
define('URLROOT', 'http://localhost/asset_visibility');
define('SITENAME', 'EVIS');

/**
 * Security Configuration
 */
// Google reCAPTCHA v2 keys (replace with your actual keys when deploying)
define('RECAPTCHA_SITE_KEY', '6LfiqkorAAAAAAZYWXIl9SMwCbH2lKYNbEFZHgnZ'); // Test key
define('RECAPTCHA_SECRET_KEY', '6LfiqkorAAAAAC8D-klmT58vMbs03Z1RpCwQtX7E'); // Test key

/**
 * Email Configuration
 */
// SMTP Configuration - Multiple options for different email providers
// Option 1: Gmail SMTP (if using Gmail)
define('SMTP_HOST_GMAIL', 'smtp.gmail.com');
define('SMTP_PORT_GMAIL', 587);
define('SMTP_SECURE', 'tls'); // 'tls' or 'ssl'
define('SMTP_AUTH', true);

// Email credentials
define('SMTP_USERNAME', '<EMAIL>'); // Your email address
define('SMTP_PASSWORD', 'hnwtuzwzspsjmoio'); // Your email password or app password

// Email settings
define('MAIL_FROM_EMAIL', '<EMAIL>'); // From email address (should match SMTP_USERNAME)
define('MAIL_FROM_NAME', SITENAME); // From name
define('MAIL_DEBUG', false); // Set to true for debugging, false for production

// Email timeout settings
define('SMTP_TIMEOUT', 30); // Timeout in seconds

// Cookie settings for Remember Me functionality
define('COOKIE_SECURE', false); // Set to true in production with HTTPS
define('COOKIE_HTTP_ONLY', true);
define('COOKIE_SAME_SITE', 'Lax'); // Options: Strict, Lax, None

define('ASSET_LIFETIME', 1825); // Default 5 years (5*365) expected lifetime of an asset