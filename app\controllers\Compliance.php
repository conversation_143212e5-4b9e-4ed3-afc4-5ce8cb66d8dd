<?php
class Compliance extends Controller {
    private $complianceModel;
    private $assetModel;

    public function __construct() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('users/login');
        }

        $this->complianceModel = $this->model('ComplianceModel');
        $this->assetModel = $this->model('Asset');
    }

    /**
     * Compliance dashboard
     */
    public function index() {
        // Get all frameworks
        $frameworks = $this->complianceModel->getFrameworks();

        // Get default framework (first one)
        $defaultFramework = !empty($frameworks) ? $frameworks[0] : null;
        $frameworkId = $defaultFramework ? $defaultFramework->id : null;

        // If no frameworks, show message
        if (!$frameworkId) {
            $data = [
                'frameworks' => [],
                'selected_framework' => null,
                'compliance_summary' => [],
                'control_summary' => [],
                'non_compliant_assets' => [],
                'recent_reports' => []
            ];

            $this->view('compliance/index', $data);
            return;
        }

        // Get compliance summary
        $complianceSummary = $this->complianceModel->getComplianceSummary($frameworkId);

        // Get compliance summary by control
        $controlSummary = $this->complianceModel->getComplianceSummaryByControl($frameworkId);

        // Get non-compliant assets
        $nonCompliantAssets = $this->complianceModel->getNonCompliantAssets($frameworkId);

        // Get recent reports
        $recentReports = $this->complianceModel->getRecentReports();

        $data = [
            'frameworks' => $frameworks,
            'selected_framework' => $defaultFramework,
            'compliance_summary' => $complianceSummary,
            'control_summary' => $controlSummary,
            'non_compliant_assets' => $nonCompliantAssets,
            'recent_reports' => $recentReports
        ];

        $this->view('compliance/index', $data);
    }

    /**
     * View compliance dashboard for a specific framework
     *
     * @param int $frameworkId
     */
    public function framework($frameworkId) {
        // Get framework details
        $framework = $this->complianceModel->getFrameworkById($frameworkId);

        if (!$framework) {
            flash('compliance_message', 'Framework not found', 'alert alert-danger');
            redirect('compliance');
        }

        // Get all frameworks for dropdown
        $frameworks = $this->complianceModel->getFrameworks();

        // Get compliance summary
        $complianceSummary = $this->complianceModel->getComplianceSummary($frameworkId);

        // Get compliance summary by control
        $controlSummary = $this->complianceModel->getComplianceSummaryByControl($frameworkId);

        // Get non-compliant assets
        $nonCompliantAssets = $this->complianceModel->getNonCompliantAssets($frameworkId);

        // Get recent reports
        $recentReports = $this->complianceModel->getRecentReports();

        $data = [
            'frameworks' => $frameworks,
            'selected_framework' => $framework,
            'compliance_summary' => $complianceSummary,
            'control_summary' => $controlSummary,
            'non_compliant_assets' => $nonCompliantAssets,
            'recent_reports' => $recentReports
        ];

        $this->view('compliance/index', $data);
    }

    /**
     * View asset compliance details
     *
     * @param int $assetId
     * @param int $frameworkId
     */
    public function asset($assetId, $frameworkId) {
        // Get asset details
        $asset = $this->assetModel->getAssetById($assetId);

        if (!$asset) {
            flash('compliance_message', 'Asset not found', 'alert alert-danger');
            redirect('compliance');
        }

        // Get framework details
        $framework = $this->complianceModel->getFrameworkById($frameworkId);

        if (!$framework) {
            flash('compliance_message', 'Framework not found', 'alert alert-danger');
            redirect('compliance');
        }

        // Get controls for this framework
        $controls = $this->complianceModel->getControlsByFramework($frameworkId);

        // Get compliance status for this asset
        $complianceStatus = $this->complianceModel->getAssetComplianceStatus($assetId, $frameworkId);

        // Map compliance status to controls
        $controlsWithStatus = [];
        foreach ($controls as $control) {
            $status = null;
            foreach ($complianceStatus as $cs) {
                if ($cs->control_id == $control->id) {
                    $status = $cs;
                    break;
                }
            }

            $controlsWithStatus[] = [
                'control' => $control,
                'status' => $status
            ];
        }

        $data = [
            'asset' => $asset,
            'framework' => $framework,
            'controls_with_status' => $controlsWithStatus
        ];

        $this->view('compliance/asset', $data);
    }

    /**
     * Update asset compliance status
     *
     * @param int $assetId
     * @param int $controlId
     */
    public function update($assetId, $controlId) {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);

            // Process form
            $data = [
                'asset_id' => $assetId,
                'control_id' => $controlId,
                'status' => trim($_POST['status']),
                'evidence' => trim($_POST['evidence']),
                'notes' => trim($_POST['notes']),
                'last_assessed_by' => $_SESSION['user_id'],
                'status_err' => ''
            ];

            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select a status';
            }

            // Make sure no errors
            if (empty($data['status_err'])) {
                // Update compliance status
                if ($this->complianceModel->updateAssetCompliance($data)) {
                    flash('compliance_message', 'Compliance status updated');

                    // Get control details to get framework ID
                    $control = $this->complianceModel->getControlById($controlId);
                    $frameworkId = $control ? $control->framework_id : 1;

                    redirect('compliance/asset/' . $assetId . '/' . $frameworkId);
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $this->view('compliance/update', $data);
            }
        } else {
            // Get asset details
            $asset = $this->assetModel->getAssetById($assetId);

            if (!$asset) {
                flash('compliance_message', 'Asset not found', 'alert alert-danger');
                redirect('compliance');
            }

            // Get control details
            $control = $this->complianceModel->getControlById($controlId);

            if (!$control) {
                flash('compliance_message', 'Control not found', 'alert alert-danger');
                redirect('compliance');
            }

            // Get current compliance status
            $status = $this->complianceModel->getAssetControlStatus($assetId, $controlId);

            $data = [
                'asset' => $asset,
                'control' => $control,
                'status' => $status ? $status->status : 'in_progress',
                'evidence' => $status ? $status->evidence : '',
                'notes' => $status ? $status->notes : '',
                'status_err' => ''
            ];

            $this->view('compliance/update', $data);
        }
    }

    /**
     * Generate compliance report
     *
     * @param int $frameworkId
     */
    public function generate($frameworkId) {
        // Get framework details
        $framework = $this->complianceModel->getFrameworkById($frameworkId);

        if (!$framework) {
            flash('compliance_message', 'Framework not found', 'alert alert-danger');
            redirect('compliance');
        }

        // Generate report
        $reportId = $this->complianceModel->generateReport($frameworkId, $_SESSION['user_id']);

        if ($reportId) {
            flash('compliance_message', 'Compliance report generated successfully');
            redirect('compliance/report/' . $reportId);
        } else {
            flash('compliance_message', 'Failed to generate report', 'alert alert-danger');
            redirect('compliance/framework/' . $frameworkId);
        }
    }

    /**
     * View compliance report
     *
     * @param int $reportId
     */
    public function report($reportId) {
        // Get report details
        $report = $this->complianceModel->getReportById($reportId);

        if (!$report) {
            flash('compliance_message', 'Report not found', 'alert alert-danger');
            redirect('compliance');
        }

        $data = [
            'report' => $report
        ];

        $this->view('compliance/report', $data);
    }
}
