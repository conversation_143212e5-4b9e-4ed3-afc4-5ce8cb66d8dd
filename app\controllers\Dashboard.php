<?php
class Dashboard extends Controller {
    public function __construct() {
        // Check if user is logged in
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        // Load models
        $this->assetModel = $this->model('Asset');
    }

    public function index() {
        // Get total assets count
        $totalAssets = $this->assetModel->getTotalAssetsCount();

        // Get assets by type
        $assetsByType = $this->assetModel->getAssetCountByType();

        // Get assets by acquisition date (last 12 months)
        $assetsByDate = $this->assetModel->getAssetCountByAcquisitionDate();

        // Get assets by employee (top 10)
        $assetsByEmployee = $this->assetModel->getAssetCountByEmployee();

        // Get assets by operating system
        $assetsByOS = $this->assetModel->getAssetCountByOS();

        // Get assets by site
        $assetsBySite = $this->assetModel->getAssetCountBySite();

        // Prepare chart data for assets by type
        $typeLabels = [];
        $typeCounts = [];
        $typeColors = [];

        foreach($assetsByType as $asset) {
            $typeLabels[] = $asset->equipment_type;
            $typeCounts[] = $asset->count;
            // Generate random colors for the chart
            $typeColors[] = 'rgba(' . rand(0, 200) . ',' . rand(0, 200) . ',' . rand(0, 200) . ', 0.7)';
        }

        // Prepare chart data for assets by date
        $dateLabels = [];
        $dateCounts = [];

        foreach($assetsByDate as $asset) {
            $dateLabels[] = date('M Y', strtotime($asset->month . '-01'));
            $dateCounts[] = $asset->count;
        }

        // Prepare data for the view
        $data = [
            'totalAssets' => $totalAssets,
            'assetsByType' => $assetsByType,
            'assetsByDate' => $assetsByDate,
            'assetsByEmployee' => $assetsByEmployee,
            'assetsByOS' => $assetsByOS,
            'assetsBySite' => $assetsBySite,
            'typeLabels' => json_encode($typeLabels),
            'typeCounts' => json_encode($typeCounts),
            'typeColors' => json_encode($typeColors),
            'dateLabels' => json_encode($dateLabels),
            'dateCounts' => json_encode($dateCounts)
        ];

        $this->view('dashboard/index', $data);
    }
}
