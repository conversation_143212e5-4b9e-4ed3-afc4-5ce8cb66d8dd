<?php
class ErrorLogs extends Controller {
    private $errorLogModel;

    public function __construct() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('users/login');
        }

        // Check if user is admin
        if (!isAdmin()) {
            flash('error_log_message', 'You do not have permission to access error logs', 'alert alert-danger');
            redirect('pages/index');
        }

        $this->errorLogModel = $this->model('ErrorLog');
    }

    /**
     * Display error logs with pagination and filtering
     *
     * @param int $page Current page number
     */
    public function index($page = 1) {
        // Set records per page
        $recordsPerPage = 20;

        // Calculate offset
        $offset = ($page - 1) * $recordsPerPage;

        // Get filter parameters
        $level = isset($_GET['level']) ? $_GET['level'] : null;
        $search = isset($_GET['search']) ? $_GET['search'] : null;

        // Get error logs
        $errorLogs = $this->errorLogModel->getErrorLogs($recordsPerPage, $offset, $level, $search);

        // Count total records
        $totalRecords = $this->errorLogModel->countErrorLogs($level, $search);

        // Calculate total pages
        $totalPages = ceil($totalRecords / $recordsPerPage);

        $data = [
            'error_logs' => $errorLogs,
            'total_records' => $totalRecords,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'records_per_page' => $recordsPerPage,
            'level' => $level,
            'search' => $search
        ];

        $this->view('error_logs/index', $data);
    }

    /**
     * View error log details
     *
     * @param int $id Error log ID
     */
    public function view($id) {
        // Get error log
        $errorLog = $this->errorLogModel->getErrorLogById($id);

        if (!$errorLog) {
            flash('error_log_message', 'Error log not found', 'alert alert-danger');
            redirect('error_logs');
        }

        $data = [
            'error_log' => $errorLog
        ];

        $this->view('error_logs/view', $data);
    }

    /**
     * Delete error log
     *
     * @param int $id Error log ID
     */
    public function delete($id) {
        // Check if POST request
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete error log
            if ($this->errorLogModel->deleteErrorLog($id)) {
                flash('error_log_message', 'Error log deleted successfully', 'alert alert-success');
            } else {
                flash('error_log_message', 'Failed to delete error log', 'alert alert-danger');
            }
        }

        redirect('error_logs');
    }

    /**
     * Clear all error logs
     */
    public function clear() {
        // Check if POST request
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Clear error logs
            if ($this->errorLogModel->clearErrorLogs()) {
                flash('error_log_message', 'All error logs cleared successfully', 'alert alert-success');
            } else {
                flash('error_log_message', 'Failed to clear error logs', 'alert alert-danger');
            }
        }

        redirect('error_logs');
    }

    /**
     * Test error logging
     */
    public function test() {
        // Log test error
        $errorId = $this->errorLogModel->logError(
            'info',
            'Test error message',
            ['test' => 'data'],
            __FILE__,
            __LINE__,
            debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
        );

        if ($errorId) {
            flash('error_log_message', 'Test error logged successfully with ID: ' . $errorId, 'alert alert-success');
        } else {
            flash('error_log_message', 'Failed to log test error', 'alert alert-danger');
        }

        redirect('error_logs');
    }
}
