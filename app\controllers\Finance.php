<?php
class Finance extends Controller {
    private $financeModel;
    private $assetModel;

    public function __construct() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('users/login');
        }

        // Check permissions for certain pages
        $currentUrl = $_GET['url'] ?? '';
        if (strpos($currentUrl, 'finance/budget') === 0 && !hasPermission('manage_budget')) {
            flash('finance_message', 'You do not have permission to access the budget page', 'alert alert-danger');
            redirect('dashboard');
        }

        if (strpos($currentUrl, 'finance/settings') === 0 && !hasPermission('manage_finance')) {
            flash('finance_message', 'You do not have permission to access finance settings', 'alert alert-danger');
            redirect('dashboard');
        }

        $this->financeModel = $this->model('FinanceModel');
        $this->assetModel = $this->model('Asset');
    }

    /**
     * Finance dashboard
     */
    public function index() {
        // Get current fiscal year
        $currentMonth = date('n');
        $currentYear = date('Y');
        // Assuming fiscal year starts in July
        $fiscalYear = ($currentMonth >= 7) ? $currentYear : $currentYear - 1;

        // Get costs by fiscal year
        $costsByType = $this->financeModel->getCostsByFiscalYear($fiscalYear);

        // Get monthly costs
        $monthlyCosts = $this->financeModel->getMonthlyCosts($fiscalYear);

        // Get costs by equipment type
        $costsByEquipmentType = $this->financeModel->getCostsByEquipmentType($fiscalYear);

        // Prepare chart data for costs by type
        $typeLabels = [];
        $typeAmounts = [];
        $typeColors = [
            'acquisition' => 'rgba(59, 130, 246, 0.7)', // Blue
            'maintenance' => 'rgba(16, 185, 129, 0.7)', // Green
            'upgrade' => 'rgba(245, 158, 11, 0.7)',     // Yellow
            'repair' => 'rgba(239, 68, 68, 0.7)',       // Red
            'license' => 'rgba(139, 92, 246, 0.7)',     // Purple
            'other' => 'rgba(156, 163, 175, 0.7)'       // Gray
        ];
        $chartColors = [];

        foreach($costsByType as $cost) {
            $typeLabels[] = ucfirst($cost->cost_type);
            $typeAmounts[] = $cost->total_amount;
            $chartColors[] = $typeColors[$cost->cost_type] ?? 'rgba(156, 163, 175, 0.7)';
        }

        // Prepare chart data for monthly costs
        $monthLabels = [];
        $monthAmounts = [];

        foreach($monthlyCosts as $cost) {
            $date = DateTime::createFromFormat('Y-m', $cost->month);
            $monthLabels[] = $date->format('M Y');
            $monthAmounts[] = $cost->total_amount;
        }

        // Generate budget forecast for next fiscal year
        $nextFiscalYear = $fiscalYear + 1;
        $budgetForecast = $this->financeModel->generateBudgetForecast($nextFiscalYear);

        $data = [
            'fiscal_year' => $fiscalYear,
            'costs_by_type' => $costsByType,
            'monthly_costs' => $monthlyCosts,
            'costs_by_equipment_type' => $costsByEquipmentType,
            'budget_forecast' => $budgetForecast,
            'type_labels' => json_encode($typeLabels),
            'type_amounts' => json_encode($typeAmounts),
            'type_colors' => json_encode($chartColors),
            'month_labels' => json_encode($monthLabels),
            'month_amounts' => json_encode($monthAmounts)
        ];

        $this->view('finance/index', $data);
    }

    /**
     * Asset cost details
     *
     * @param int $assetId
     */
    public function asset($assetId) {
        // Get asset details
        $asset = $this->assetModel->getAssetById($assetId);

        if (!$asset) {
            flash('asset_message', 'Asset not found', 'alert alert-danger');
            redirect('assets');
        }

        // Get asset costs
        $assetCosts = $this->financeModel->getAssetCosts($assetId);

        // Get total cost of ownership
        $tco = $this->financeModel->getTotalCostOfOwnership($assetId);

        // Calculate depreciation
        $depreciation = $this->financeModel->calculateDepreciation($assetId);

        $data = [
            'asset' => $asset,
            'costs' => $assetCosts,
            'tco' => $tco,
            'depreciation' => $depreciation
        ];

        $this->view('finance/asset', $data);
    }

    /**
     * Add asset cost
     *
     * @param int $assetId
     */
    public function addCost($assetId = null) {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            // Process form
            $data = [
                'asset_id' => $assetId ?? trim($_POST['asset_id']),
                'cost_type' => trim($_POST['cost_type']),
                'amount' => trim($_POST['amount']),
                'description' => trim($_POST['description']),
                'date_incurred' => trim($_POST['date_incurred']),
                'fiscal_year' => trim($_POST['fiscal_year']),
                'budget_category' => trim($_POST['budget_category']),
                'created_by' => $_SESSION['user_id'],
                'asset_id_err' => '',
                'cost_type_err' => '',
                'amount_err' => '',
                'date_incurred_err' => '',
                'fiscal_year_err' => ''
            ];

            // Validate asset_id
            if (empty($data['asset_id'])) {
                $data['asset_id_err'] = 'Please select an asset';
            } else {
                // Check if asset exists
                $asset = $this->assetModel->getAssetById($data['asset_id']);
                if (!$asset) {
                    $data['asset_id_err'] = 'Asset not found';
                }
            }

            // Validate cost_type
            if (empty($data['cost_type'])) {
                $data['cost_type_err'] = 'Please select cost type';
            }

            // Validate amount
            if (empty($data['amount'])) {
                $data['amount_err'] = 'Please enter amount';
            } elseif (!is_numeric($data['amount']) || $data['amount'] <= 0) {
                $data['amount_err'] = 'Amount must be a positive number';
            }

            // Validate date_incurred
            if (empty($data['date_incurred'])) {
                $data['date_incurred_err'] = 'Please enter date incurred';
            }

            // Validate fiscal_year
            if (empty($data['fiscal_year'])) {
                $data['fiscal_year_err'] = 'Please enter fiscal year';
            } elseif (!is_numeric($data['fiscal_year']) || $data['fiscal_year'] < 2000 || $data['fiscal_year'] > 2100) {
                $data['fiscal_year_err'] = 'Please enter a valid fiscal year';
            }

            // Make sure no errors
            if (empty($data['asset_id_err']) && empty($data['cost_type_err']) &&
                empty($data['amount_err']) && empty($data['date_incurred_err']) &&
                empty($data['fiscal_year_err'])) {

                // Add cost record
                if ($this->financeModel->addAssetCost($data)) {
                    flash('finance_message', 'Cost record added');
                    redirect('finance/asset/' . $data['asset_id']);
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $this->view('finance/add_cost', $data);
            }
        } else {
            // Get asset if assetId is provided
            $asset = null;
            if ($assetId) {
                $asset = $this->assetModel->getAssetById($assetId);
                if (!$asset) {
                    flash('asset_message', 'Asset not found', 'alert alert-danger');
                    redirect('assets');
                }
            }

            // Get all assets for dropdown if no assetId provided
            $assets = [];
            if (!$assetId) {
                $assets = $this->assetModel->getAllAssets();
            }

            // Calculate fiscal year based on current date
            $currentMonth = date('n');
            $currentYear = date('Y');
            // Assuming fiscal year starts in July
            $fiscalYear = ($currentMonth >= 7) ? $currentYear : $currentYear - 1;

            $data = [
                'asset_id' => $assetId,
                'asset' => $asset,
                'assets' => $assets,
                'cost_type' => '',
                'amount' => '',
                'description' => '',
                'date_incurred' => date('Y-m-d'),
                'fiscal_year' => $fiscalYear,
                'budget_category' => '',
                'asset_id_err' => '',
                'cost_type_err' => '',
                'amount_err' => '',
                'date_incurred_err' => '',
                'fiscal_year_err' => ''
            ];

            $this->view('finance/add_cost', $data);
        }
    }

    /**
     * Budget forecasting
     */
    public function budget() {
        // Check if user has permission to manage budget
        if (!hasPermission('manage_budget')) {
            flash('finance_message', 'You do not have permission to access the budget page', 'alert alert-danger');
            redirect('dashboard');
        }

        // Get current and next fiscal years
        $currentMonth = date('n');
        $currentYear = date('Y');
        // Assuming fiscal year starts in July
        $currentFiscalYear = ($currentMonth >= 7) ? $currentYear : $currentYear - 1;
        $nextFiscalYear = $currentFiscalYear + 1;

        // Generate budget forecast
        $budgetForecast = $this->financeModel->generateBudgetForecast($nextFiscalYear);

        $data = [
            'current_fiscal_year' => $currentFiscalYear,
            'next_fiscal_year' => $nextFiscalYear,
            'budget_forecast' => $budgetForecast
        ];

        $this->view('finance/budget', $data);
    }
}
