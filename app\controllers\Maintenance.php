<?php
class Maintenance extends Controller {
    private $maintenanceModel;
    private $assetModel;
    private $guidelineModel;
    private $complianceModel;
    private $db;

    public function __construct() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('users/login');
        }

        // Check permissions for certain pages
        $currentUrl = $_GET['url'] ?? '';
        if (strpos($currentUrl, 'maintenance/add') === 0 && !hasPermission('add_maintenance')) {
            flash('maintenance_message', 'You do not have permission to add maintenance records', 'alert alert-danger');
            redirect('dashboard');
        }

        if (strpos($currentUrl, 'maintenance/edit') === 0 && !hasPermission('edit_maintenance')) {
            flash('maintenance_message', 'You do not have permission to edit maintenance records', 'alert alert-danger');
            redirect('dashboard');
        }

        if (strpos($currentUrl, 'maintenance/delete') === 0 && !hasPermission('delete_maintenance')) {
            flash('maintenance_message', 'You do not have permission to delete maintenance records', 'alert alert-danger');
            redirect('dashboard');
        }

        $this->maintenanceModel = $this->model('MaintenanceModel');
        $this->assetModel = $this->model('Asset');
        $this->guidelineModel = $this->model('MaintenanceGuideline');
        $this->complianceModel = $this->model('MaintenanceCompliance');
        $this->db = new Database;
    }

    /**
     * Maintenance dashboard
     *
     * @param int $page Current page for asset health metrics pagination
     */
    public function index($page = 1) {
        // Clear any form error session variables that might be persisting
        $this->clearFormErrors();

        // Get assets due for maintenance
        $assetsDueForMaintenance = $this->maintenanceModel->getAssetsDueForMaintenance(30);

        // Get search parameters from GET request
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        $orderBy = isset($_GET['order_by']) ? $_GET['order_by'] : 'health_score';
        $orderDir = isset($_GET['order_dir']) ? $_GET['order_dir'] : 'ASC';

        // Set records per page for pagination
        $recordsPerPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10;
        // Validate records per page
        $validPerPage = [10, 25, 50, 100];
        if (!in_array($recordsPerPage, $validPerPage)) {
            $recordsPerPage = 10;
        }

        // Calculate offset for pagination
        $offset = ($page - 1) * $recordsPerPage;

        // Get total count for pagination
        $totalAssetCount = $this->maintenanceModel->countAssetsWithHealthMetrics($search);

        // Calculate total pages
        $totalPages = ceil($totalAssetCount / $recordsPerPage);

        // Get assets with health metrics with pagination and search
        $assetsWithHealthMetrics = $this->maintenanceModel->getAssetsWithHealthMetrics(
            $recordsPerPage,
            $offset,
            $search,
            $orderBy,
            $orderDir
        );

        // Update compliance status for all assets
        $this->complianceModel->updateAllComplianceStatus();

        // Get overdue maintenance tasks
        $overdueMaintenance = $this->complianceModel->getOverdueMaintenance();

        // Get maintenance tasks due soon
        $maintenanceDueSoon = $this->complianceModel->getMaintenanceDueSoon(30);

        // Get all implemented guidelines without limit
        $mostImplementedGuidelines = $this->maintenanceModel->getMostImplementedGuidelines();

        $data = [
            'assets_due' => $assetsDueForMaintenance,
            'assets_health' => $assetsWithHealthMetrics,
            'overdue_maintenance' => $overdueMaintenance,
            'maintenance_due_soon' => $maintenanceDueSoon,
            'most_implemented_guidelines' => $mostImplementedGuidelines,
            'search' => $search,
            'order_by' => $orderBy,
            'order_dir' => $orderDir,
            'current_page' => $page,
            'records_per_page' => $recordsPerPage,
            'total_pages' => $totalPages,
            'total_count' => $totalAssetCount
        ];

        $this->view('maintenance/index', $data);
    }

    /**
     * Clear any form error session variables
     */
    private function clearFormErrors() {
        // Check if there are any form error session variables
        if (isset($_SESSION)) {
            foreach ($_SESSION as $key => $value) {
                // Check if the session variable is a form error
                if (strpos($key, '_err') !== false) {
                    error_log("Clearing form error session variable: $key = $value");
                    unset($_SESSION[$key]);
                }
            }
        }

        // Also clear any maintenance_message flash message that might be persisting
        if (isset($_SESSION['maintenance_message'])) {
            // Check if the message contains "Please correct the errors in the form"
            if (strpos($_SESSION['maintenance_message'], 'Please correct the errors in the form') !== false) {
                error_log("Clearing persisting error message: " . $_SESSION['maintenance_message']);
                unset($_SESSION['maintenance_message']);
                unset($_SESSION['maintenance_message_class']);
            } else {
                error_log("Found maintenance_message but not clearing it: " . $_SESSION['maintenance_message']);
            }
        }

        // Check if there's a form validation error message set
        if (isset($_SESSION['form_validation_error'])) {
            error_log("Clearing form validation error: " . $_SESSION['form_validation_error']);
            unset($_SESSION['form_validation_error']);
        }
    }

    /**
     * View maintenance history for an asset
     *
     * @param int $assetId
     */
    public function history($assetId) {
        // Get asset details
        $asset = $this->assetModel->getAssetById($assetId);

        if (!$asset) {
            flash('asset_message', 'Asset not found', 'alert alert-danger');
            redirect('assets');
        }

        // Get maintenance history
        $maintenanceHistory = $this->maintenanceModel->getMaintenanceHistory($assetId);

        // Get implemented guidelines for each maintenance record
        $implementedGuidelines = [];
        foreach ($maintenanceHistory as $record) {
            $implementedGuidelines[$record->id] = $this->maintenanceModel->getImplementedGuidelines($record->id);
        }

        // Calculate health metrics
        $healthMetrics = $this->maintenanceModel->calculateAssetHealthScore($assetId);

        // Get applicable guidelines for this asset type
        $applicableGuidelines = $this->guidelineModel->getGuidelinesByEquipmentType($asset->equipment_type);

        // Get compliance status for this asset
        $complianceStatus = $this->complianceModel->getAssetComplianceStatus($assetId);

        $data = [
            'asset' => $asset,
            'maintenance_history' => $maintenanceHistory,
            'health_metrics' => $healthMetrics,
            'implemented_guidelines' => $implementedGuidelines,
            'applicable_guidelines' => $applicableGuidelines,
            'compliance_status' => $complianceStatus
        ];

        $this->view('maintenance/history', $data);
    }

    /**
     * Add maintenance record
     *
     * @param int $assetId
     */
    public function add($assetId = null) {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Debug log for form submission
            error_log("Maintenance::add - POST data: " . print_r($_POST, true));
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);

            // Debug log after sanitization
            error_log("Maintenance::add - Sanitized POST data: " . print_r($_POST, true));

            // Process form
            $data = [
                'asset_id' => $assetId ?? trim($_POST['asset_id']),
                'maintenance_type' => trim($_POST['maintenance_type']),
                'description' => trim($_POST['description']),
                'performed_by' => $_SESSION['user_id'],
                'performed_date' => trim($_POST['performed_date']),
                'cost' => !empty($_POST['cost']) ? trim($_POST['cost']) : 0,
                'next_scheduled_date' => !empty($_POST['next_scheduled_date']) ? trim($_POST['next_scheduled_date']) : null,
                'status' => trim($_POST['status']),
                'asset_id_err' => '',
                'maintenance_type_err' => '',
                'description_err' => '',
                'performed_date_err' => '',
                'status_err' => ''
            ];

            // Validate asset_id
            if (empty($data['asset_id'])) {
                $data['asset_id_err'] = 'Please select an asset';
            } else {
                // Check if asset exists
                $asset = $this->assetModel->getAssetById($data['asset_id']);
                if (!$asset) {
                    $data['asset_id_err'] = 'Asset not found';
                }
            }

            // Validate maintenance_type
            if (empty($data['maintenance_type'])) {
                $data['maintenance_type_err'] = 'Please select maintenance type';
            }

            // Validate description
            if (empty($data['description'])) {
                $data['description_err'] = 'Please enter description';
            }

            // Validate performed_date
            if (empty($data['performed_date'])) {
                $data['performed_date_err'] = 'Please enter performed date';
            }

            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select status';
            }

            // Make sure no errors
            if (empty($data['asset_id_err']) && empty($data['maintenance_type_err']) &&
                empty($data['description_err']) && empty($data['performed_date_err']) &&
                empty($data['status_err'])) {

                // Add maintenance record
                $maintenanceId = $this->maintenanceModel->addMaintenance($data);
                if ($maintenanceId) {
                    error_log("Maintenance::add - Maintenance record added with ID: $maintenanceId");

                    // Process implemented guidelines if any
                    if (isset($_POST['implemented_guidelines']) && is_array($_POST['implemented_guidelines'])) {
                        error_log("Maintenance::add - Found implemented_guidelines: " . print_r($_POST['implemented_guidelines'], true));
                        $performedDate = $data['performed_date'];

                        // Ensure the maintenance_guideline_implementation table exists
                        $this->maintenanceModel->ensureGuidelineImplementationTableExists();

                        // Create a direct database connection for fallback operations
                        $db = new Database;

                        // Track success/failure for each guideline
                        $guidelineResults = [];
                        $successCount = 0;
                        $failureCount = 0;

                        foreach ($_POST['implemented_guidelines'] as $guidelineId) {
                            error_log("Maintenance::add - Processing guideline ID: $guidelineId");

                            // Validate guideline ID
                            if (!is_numeric($guidelineId) || $guidelineId <= 0) {
                                error_log("Maintenance::add - Invalid guideline ID: $guidelineId");
                                $guidelineResults[$guidelineId] = [
                                    'status' => 'failed',
                                    'reason' => 'Invalid guideline ID'
                                ];
                                $failureCount++;
                                continue;
                            }

                            // Get guideline details
                            $guideline = $this->guidelineModel->getGuidelineById($guidelineId);

                            if ($guideline) {
                                error_log("Maintenance::add - Guideline found: " . $guideline->name);

                                // Calculate next due date based on frequency
                                $nextDueDate = date('Y-m-d', strtotime($performedDate . ' + ' . $guideline->frequency_days . ' days'));
                                error_log("Maintenance::add - Next due date: $nextDueDate");

                                // Update compliance status
                                $complianceResult = $this->complianceModel->updateComplianceStatus(
                                    $data['asset_id'],
                                    $guidelineId,
                                    'compliant',
                                    $performedDate,
                                    $nextDueDate
                                );
                                error_log("Maintenance::add - Compliance status update result: " . ($complianceResult ? 'Success' : 'Failed'));

                                // Log guideline implementation using the model method
                                $implementationResult = $this->maintenanceModel->logGuidelineImplementation(
                                    $maintenanceId,
                                    $guidelineId
                                );
                                error_log("Maintenance::add - Guideline implementation log result: " . ($implementationResult ? 'Success' : 'Failed'));

                                // Process checklist items for this guideline if any
                                if (isset($_POST['checklist_items'][$guidelineId]) && is_array($_POST['checklist_items'][$guidelineId])) {
                                    error_log("Maintenance::add - Processing checklist items for guideline ID: $guidelineId");

                                    foreach ($_POST['checklist_items'][$guidelineId] as $checklistItemId) {
                                        $checklistItemId = filter_var($checklistItemId, FILTER_SANITIZE_NUMBER_INT);

                                        // Record checklist completion
                                        $checklistResult = $this->maintenanceModel->recordChecklistCompletion(
                                            $maintenanceId,
                                            $checklistItemId,
                                            true,
                                            $_SESSION['user_id'] ?? null
                                        );

                                        error_log("Maintenance::add - Checklist item $checklistItemId completion result: " . ($checklistResult ? 'Success' : 'Failed'));
                                    }
                                }

                                // If the model method failed, try a direct database approach
                                if (!$implementationResult) {
                                    error_log("Maintenance::add - Model method failed, trying direct database approach");

                                    try {
                                        // First check if any records already exist and get the count
                                        $db->query('SELECT COUNT(*) as count FROM maintenance_guideline_implementation
                                                   WHERE maintenance_id = :maintenance_id AND guideline_id = :guideline_id');
                                        $db->bind(':maintenance_id', $maintenanceId);
                                        $db->bind(':guideline_id', $guidelineId);
                                        $result = $db->single();
                                        $existingCount = ($result && isset($result->count)) ? $result->count : 0;
                                        error_log("Maintenance::add - Existing record count: $existingCount");

                                        // Always insert a new record (no update) since we want multiple instances
                                        $db->query('INSERT INTO maintenance_guideline_implementation
                                                   (maintenance_id, guideline_id, implemented_date)
                                                   VALUES (:maintenance_id, :guideline_id, NOW())');
                                        $db->bind(':maintenance_id', $maintenanceId);
                                        $db->bind(':guideline_id', $guidelineId);
                                        $implementationResult = $db->execute();

                                        error_log("Maintenance::add - Direct database approach result: " . ($implementationResult ? 'Success' : 'Failed'));
                                    } catch (Exception $e) {
                                        error_log("Maintenance::add - Error in direct database approach: " . $e->getMessage());

                                        // Last resort: try a raw SQL query
                                        try {
                                            $sql = "INSERT INTO maintenance_guideline_implementation
                                                   (maintenance_id, guideline_id, implemented_date)
                                                   VALUES ($maintenanceId, $guidelineId, NOW())";
                                            $db->query($sql);
                                            $implementationResult = $db->execute();
                                            error_log("Maintenance::add - Raw SQL query result: " . ($implementationResult ? 'Success' : 'Failed'));
                                        } catch (Exception $e2) {
                                            error_log("Maintenance::add - Error in raw SQL query: " . $e2->getMessage());
                                        }
                                    }
                                }

                                // Verify the record was inserted by checking if the count increased
                                $recordFound = false;
                                $countBefore = $existingCount ?? 0;

                                try {
                                    $db->query('SELECT COUNT(*) as count FROM maintenance_guideline_implementation
                                               WHERE maintenance_id = :maintenance_id AND guideline_id = :guideline_id');
                                    $db->bind(':maintenance_id', $maintenanceId);
                                    $db->bind(':guideline_id', $guidelineId);
                                    $result = $db->single();
                                    $countAfter = ($result && $result->count) ? $result->count : 0;
                                    $recordFound = ($countAfter > $countBefore);
                                    error_log("Maintenance::add - Record count before: $countBefore, after: $countAfter");
                                } catch (Exception $e) {
                                    error_log("Maintenance::add - Error verifying record: " . $e->getMessage());
                                }

                                error_log("Maintenance::add - Final verification: " . ($recordFound ? 'Record found' : 'Record not found'));

                                // Track result
                                if ($recordFound) {
                                    $guidelineResults[$guidelineId] = [
                                        'status' => 'success',
                                        'name' => $guideline->name
                                    ];
                                    $successCount++;
                                } else {
                                    $guidelineResults[$guidelineId] = [
                                        'status' => 'failed',
                                        'name' => $guideline->name,
                                        'reason' => 'Record not found after insertion attempts'
                                    ];
                                    $failureCount++;
                                }
                            } else {
                                error_log("Maintenance::add - Guideline not found for ID: $guidelineId");
                                $guidelineResults[$guidelineId] = [
                                    'status' => 'failed',
                                    'reason' => 'Guideline not found'
                                ];
                                $failureCount++;
                            }
                        }

                        // Log summary of results
                        error_log("Maintenance::add - Guidelines processing summary: Success: $successCount, Failed: $failureCount");
                        if ($failureCount > 0) {
                            error_log("Maintenance::add - Failed guidelines: " . print_r(array_filter($guidelineResults, function($result) {
                                return $result['status'] === 'failed';
                            }), true));
                        }
                    } else {
                        error_log("Maintenance::add - No implemented_guidelines found in POST data");
                    }

                    // Recalculate health score
                    $this->maintenanceModel->calculateAssetHealthScore($data['asset_id']);

                    // Update all compliance statuses
                    $this->complianceModel->updateAllComplianceStatus();

                    flash('maintenance_message', 'Maintenance record added');
                    redirect('maintenance/history/' . $data['asset_id']);
                } else {
                    die('Something went wrong');
                }
            } else {
                // Set a flash message for form errors
                flash('maintenance_message', 'Please correct the errors in the form', 'bg-red-100 text-red-800');

                // Load view with errors
                $this->view('maintenance/add', $data);
            }
        } else {
            // Get asset if assetId is provided
            $asset = null;
            $applicableGuidelines = [];

            if ($assetId) {
                $asset = $this->assetModel->getAssetById($assetId);
                if (!$asset) {
                    flash('asset_message', 'Asset not found', 'alert alert-danger');
                    redirect('assets');
                }

                // Get applicable guidelines for this asset type
                $applicableGuidelines = $this->guidelineModel->getGuidelinesByEquipmentType($asset->equipment_type);

                // Get compliance status for this asset
                $complianceStatus = $this->complianceModel->getAssetComplianceStatus($assetId);
            }

            // Get all assets for dropdown if no assetId provided
            $assets = [];
            if (!$assetId) {
                $assets = $this->assetModel->getAllAssets();
            }

            $data = [
                'asset_id' => $assetId,
                'asset' => $asset,
                'assets' => $assets,
                'maintenance_type' => '',
                'description' => '',
                'performed_date' => date('Y-m-d'),
                'cost' => '',
                'next_scheduled_date' => '',
                'status' => 'completed',
                'asset_id_err' => '',
                'maintenance_type_err' => '',
                'description_err' => '',
                'performed_date_err' => '',
                'status_err' => '',
                'applicable_guidelines' => $applicableGuidelines,
                'compliance_status' => $complianceStatus ?? []
            ];

            $this->view('maintenance/add', $data);
        }
    }

    /**
     * Recalculate health metrics for all assets
     */
    public function recalculateHealthMetrics() {
        // Check if user is admin
        if (!isAdmin()) {
            flash('maintenance_message', 'You do not have permission to perform this action', 'bg-red-100 text-red-800');
            redirect('maintenance');
        }

        // Update all health metrics
        $updatedCount = $this->maintenanceModel->updateAllHealthMetrics();

        // Flash message with result
        flash('maintenance_message', "Health metrics recalculated for $updatedCount assets", 'bg-green-100 text-green-800');
        redirect('maintenance');
    }

    /**
     * Mark maintenance as completed
     *
     * @param int $assetId
     * @param string $maintenanceType
     */
    public function complete($assetId, $maintenanceType) {
        // URL decode the maintenance type
        $maintenanceType = urldecode($maintenanceType);
        // Check if asset exists
        $asset = $this->assetModel->getAssetById($assetId);

        if (!$asset) {
            flash('maintenance_message', 'Asset not found', 'alert alert-danger');
            redirect('maintenance');
        }

        // Create maintenance record data
        $data = [
            'asset_id' => $assetId,
            'maintenance_type' => $maintenanceType,
            'description' => 'Maintenance completed via quick action from dashboard.',
            'performed_by' => $_SESSION['user_id'],
            'performed_date' => date('Y-m-d'), // Today
            'cost' => 0, // Default cost
            'next_scheduled_date' => null, // No next scheduled date by default
            'status' => 'completed'
        ];

        // Get all scheduled maintenance for debugging
        $scheduledMaintenance = $this->maintenanceModel->getAllScheduledMaintenance($assetId);
        error_log("Maintenance::complete - Asset ID: $assetId, Type: $maintenanceType, Scheduled Maintenance Count: " . count($scheduledMaintenance));

        // Log each scheduled maintenance record
        foreach ($scheduledMaintenance as $record) {
            error_log("Scheduled Maintenance - ID: {$record->id}, Type: {$record->maintenance_type}, Next Date: {$record->next_scheduled_date}, Status: {$record->status}");
        }

        // First, update any scheduled maintenance record to completed
        $scheduledUpdated = $this->maintenanceModel->updateScheduledMaintenanceStatus($assetId, $maintenanceType);

        // Log the result for debugging
        error_log("Maintenance::complete - Asset ID: $assetId, Type: $maintenanceType, Scheduled Updated: " . ($scheduledUpdated ? 'Yes' : 'No'));

        // Then add a new maintenance record (as a backup in case there was no scheduled record)
        $newRecordAdded = false;
        $maintenanceId = null;

        if (!$scheduledUpdated) {
            $maintenanceId = $this->maintenanceModel->addMaintenance($data);
            $newRecordAdded = ($maintenanceId !== false);
            error_log("Maintenance::complete - New record added: " . ($newRecordAdded ? "Yes, ID: $maintenanceId" : 'No'));

            if (!$newRecordAdded) {
                flash('maintenance_message', 'Failed to create maintenance record', 'bg-red-100 text-red-800');
                redirect('maintenance');
                return;
            }
        } else {
            // Get the ID of the updated record
            $maintenanceId = $this->maintenanceModel->getScheduledMaintenanceId($assetId, $maintenanceType);
            error_log("Maintenance::complete - Updated record ID: " . ($maintenanceId ? $maintenanceId : 'Not found'));

            if (!$maintenanceId) {
                flash('maintenance_message', 'Failed to find updated maintenance record', 'bg-red-100 text-red-800');
                redirect('maintenance');
                return;
            }
        }

        if (($scheduledUpdated || $newRecordAdded) && $maintenanceId) {
            // Get applicable guidelines for this asset type
            $applicableGuidelines = $this->guidelineModel->getGuidelinesByEquipmentType($asset->equipment_type);
            error_log("Maintenance::complete - Found " . count($applicableGuidelines) . " applicable guidelines");

            // Process each applicable guideline
            if (!empty($applicableGuidelines)) {
                // Ensure the maintenance_guideline_implementation table exists
                $this->maintenanceModel->ensureGuidelineImplementationTableExists();

                foreach ($applicableGuidelines as $guideline) {
                    error_log("Maintenance::complete - Processing guideline: " . $guideline->name);

                    // Calculate next due date based on frequency
                    $nextDueDate = date('Y-m-d', strtotime($data['performed_date'] . ' + ' . $guideline->frequency_days . ' days'));

                    // Update compliance status
                    $complianceResult = $this->complianceModel->updateComplianceStatus(
                        $assetId,
                        $guideline->id,
                        'compliant',
                        $data['performed_date'],
                        $nextDueDate
                    );
                    error_log("Maintenance::complete - Compliance status update result: " . ($complianceResult ? 'Success' : 'Failed'));

                    // Log guideline implementation
                    $implementationResult = $this->maintenanceModel->logGuidelineImplementation(
                        $maintenanceId,
                        $guideline->id
                    );
                    error_log("Maintenance::complete - Guideline implementation log result: " . ($implementationResult ? 'Success' : 'Failed'));

                    // Try direct database approach if the model method failed
                    if (!$implementationResult) {
                        error_log("Maintenance::complete - Trying direct database approach");
                        $db = new Database;

                        try {
                            $db->query('INSERT INTO maintenance_guideline_implementation
                                       (maintenance_id, guideline_id, implemented_date)
                                       VALUES (:maintenance_id, :guideline_id, NOW())');
                            $db->bind(':maintenance_id', $maintenanceId);
                            $db->bind(':guideline_id', $guideline->id);
                            $directResult = $db->execute();
                            error_log("Maintenance::complete - Direct database approach result: " . ($directResult ? 'Success' : 'Failed'));
                        } catch (Exception $e) {
                            error_log("Maintenance::complete - Error in direct database approach: " . $e->getMessage());
                        }
                    }
                }
            }

            // Recalculate health score
            $this->maintenanceModel->calculateAssetHealthScore($assetId);

            $message = $scheduledUpdated
                ? "Scheduled maintenance record updated to completed status"
                : "New completed maintenance record created";

            flash('maintenance_message', $message, 'bg-green-100 text-green-800');
            redirect('maintenance');
        } else {
            die('Something went wrong');
        }
    }

    /**
     * Guidelines dashboard
     */
    public function guidelines() {
        // Get all guidelines
        $guidelines = $this->guidelineModel->getAllGuidelines();

        $data = [
            'guidelines' => $guidelines
        ];

        $this->view('maintenance/guidelines', $data);
    }

    /**
     * View guideline details
     *
     * @param int $id
     */
    public function guideline($id) {
        // Get guideline details
        $guideline = $this->guidelineModel->getGuidelineById($id);

        if (!$guideline) {
            // Check if the guidelines table exists and has data
            $this->db = new Database;
            $this->db->query("SHOW TABLES LIKE 'maintenance_guidelines'");
            $tableExists = $this->db->rowCount() > 0;

            if (!$tableExists) {
                flash('maintenance_message', 'Maintenance guidelines table does not exist. Please run the database setup.', 'bg-red-100 text-red-800');
            } else {
                // Check if there are any guidelines in the table
                $this->db->query("SELECT COUNT(*) as count FROM maintenance_guidelines");
                $result = $this->db->single();
                $guidelineCount = $result->count;

                if ($guidelineCount == 0) {
                    flash('maintenance_message', 'No guidelines found in the database. Please create a guideline first.', 'bg-yellow-100 text-yellow-800');
                } else {
                    flash('maintenance_message', 'Guideline not found. The requested guideline may have been deleted.', 'bg-red-100 text-red-800');
                }
            }

            redirect('maintenance/guidelines');
        }

        // Get checklist items
        $checklistItems = $this->guidelineModel->getChecklistItems($id);

        // Get compliance status for this guideline
        $complianceStatus = $this->complianceModel->getGuidelineComplianceStatus($id);

        $data = [
            'guideline' => $guideline,
            'checklist_items' => $checklistItems,
            'compliance_status' => $complianceStatus
        ];

        $this->view('maintenance/guideline', $data);
    }

    /**
     * Manage checklist items for a guideline
     *
     * @param int $guidelineId
     */
    public function manageChecklist($guidelineId) {
        // Get guideline details
        $guideline = $this->guidelineModel->getGuidelineById($guidelineId);

        if (!$guideline) {
            flash('maintenance_message', 'Guideline not found', 'alert alert-danger');
            redirect('maintenance/guidelines');
        }

        // Get checklist items - the getChecklistItems method now handles deduplication internally
        $checklistItems = $this->guidelineModel->getChecklistItems($guidelineId);

        // Debug: Log the number of checklist items
        error_log("Maintenance::manageChecklist - Found " . count($checklistItems) . " checklist items for guideline ID: $guidelineId");

        // Add diagnostic information to the flash message
        $itemCount = count($checklistItems);
        $uniqueIds = array_unique(array_column((array)$checklistItems, 'id'));
        $uniqueCount = count($uniqueIds);

        if ($itemCount > $uniqueCount) {
            flash('maintenance_message', "Note: Found $itemCount items with $uniqueCount unique IDs. Duplicates will be filtered in the view.", 'alert alert-info');
        }

        $data = [
            'guideline' => $guideline,
            'checklist_items' => $checklistItems
        ];

        $this->view('maintenance/manage_checklist', $data);
    }

    /**
     * Add or edit a checklist item
     *
     * @param int $guidelineId
     * @param int $itemId (optional)
     */
    public function editChecklistItem($guidelineId, $itemId = null) {
        // Get guideline details
        $guideline = $this->guidelineModel->getGuidelineById($guidelineId);

        if (!$guideline) {
            flash('maintenance_message', 'Guideline not found', 'alert alert-danger');
            redirect('maintenance/guidelines');
        }

        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);

            // Process form
            $data = [
                'id' => $itemId,
                'guideline_id' => $guidelineId,
                'step_number' => isset($_POST['step_number']) ? intval($_POST['step_number']) : $this->guidelineModel->getNextStepNumber($guidelineId),
                'description' => trim($_POST['description']),
                'is_required' => isset($_POST['is_required']) ? 1 : 0,
                'description_err' => ''
            ];

            // Validate description
            if (empty($data['description'])) {
                $data['description_err'] = 'Please enter checklist item description';
            }

            // Make sure no errors
            if (empty($data['description_err'])) {
                // Add or update checklist item
                if ($itemId) {
                    // Update existing checklist item
                    if ($this->guidelineModel->updateChecklistItem($data)) {
                        flash('maintenance_message', 'Checklist item updated successfully');
                        redirect('maintenance/manageChecklist/' . $guidelineId);
                    } else {
                        die('Something went wrong');
                    }
                } else {
                    // Add new checklist item
                    if ($this->guidelineModel->addChecklistItem($data)) {
                        flash('maintenance_message', 'Checklist item added successfully');
                        redirect('maintenance/manageChecklist/' . $guidelineId);
                    } else {
                        die('Something went wrong');
                    }
                }
            } else {
                // Load view with errors
                $data['guideline'] = $guideline;
                $this->view('maintenance/edit_checklist_item', $data);
            }
        } else {
            // Get checklist item if itemId is provided
            $checklistItem = null;
            if ($itemId) {
                // Get the checklist item directly using the guideline model
                $checklistItem = $this->guidelineModel->getChecklistItemById($itemId, $guidelineId);

                // Debug: Log if we found the checklist item
                error_log("Maintenance::editChecklistItem - " . ($checklistItem ? "Found" : "Did not find") . " checklist item with ID: $itemId for guideline ID: $guidelineId");

                if (!$checklistItem) {
                    flash('maintenance_message', 'Checklist item not found', 'alert alert-danger');
                    redirect('maintenance/manageChecklist/' . $guidelineId);
                }
            }

            $data = [
                'id' => $itemId,
                'guideline_id' => $guidelineId,
                'guideline' => $guideline,
                'step_number' => $checklistItem ? $checklistItem->step_number : $this->guidelineModel->getNextStepNumber($guidelineId),
                'description' => $checklistItem ? $checklistItem->description : '',
                'is_required' => $checklistItem ? $checklistItem->is_required : 1,
                'description_err' => ''
            ];

            $this->view('maintenance/edit_checklist_item', $data);
        }
    }

    /**
     * Delete a checklist item
     *
     * @param int $guidelineId
     * @param int $itemId
     */
    public function deleteChecklistItem($guidelineId, $itemId) {
        // Get guideline details
        $guideline = $this->guidelineModel->getGuidelineById($guidelineId);

        if (!$guideline) {
            flash('maintenance_message', 'Guideline not found', 'alert alert-danger');
            redirect('maintenance/guidelines');
        }

        // Delete checklist item
        if ($this->guidelineModel->deleteChecklistItem($itemId)) {
            flash('maintenance_message', 'Checklist item deleted successfully');
        } else {
            flash('maintenance_message', 'Failed to delete checklist item', 'alert alert-danger');
        }

        redirect('maintenance/manageChecklist/' . $guidelineId);
    }

    /**
     * Add or edit guideline
     *
     * @param int $id
     */
    public function editGuideline($id = null) {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);

            // Process form
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'equipment_type' => trim($_POST['equipment_type']),
                'frequency_days' => intval($_POST['frequency_days']),
                'importance' => trim($_POST['importance']),
                'name_err' => '',
                'description_err' => '',
                'equipment_type_err' => '',
                'frequency_days_err' => '',
                'importance_err' => ''
            ];

            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter guideline name';
            }

            // Validate description
            if (empty($data['description'])) {
                $data['description_err'] = 'Please enter description';
            }

            // Validate equipment_type
            if (empty($data['equipment_type'])) {
                $data['equipment_type_err'] = 'Please select equipment type';
            }

            // Validate frequency_days
            if (empty($data['frequency_days']) || $data['frequency_days'] <= 0) {
                $data['frequency_days_err'] = 'Please enter a valid frequency in days';
            }

            // Validate importance
            if (empty($data['importance'])) {
                $data['importance_err'] = 'Please select importance';
            }

            // Make sure no errors
            if (empty($data['name_err']) && empty($data['description_err']) &&
                empty($data['equipment_type_err']) && empty($data['frequency_days_err']) &&
                empty($data['importance_err'])) {

                // Add or update guideline
                if ($id) {
                    // Update existing guideline
                    if ($this->guidelineModel->updateGuideline($data)) {
                        flash('maintenance_message', 'Guideline updated successfully');
                        redirect('maintenance/guideline/' . $id);
                    } else {
                        die('Something went wrong');
                    }
                } else {
                    // Add new guideline
                    $newId = $this->guidelineModel->addGuideline($data);
                    if ($newId) {
                        flash('maintenance_message', 'Guideline added successfully');
                        redirect('maintenance/guideline/' . $newId);
                    } else {
                        die('Something went wrong');
                    }
                }
            } else {
                // Load view with errors
                $this->view('maintenance/edit_guideline', $data);
            }
        } else {
            // Get guideline if id is provided
            $guideline = null;
            if ($id) {
                $guideline = $this->guidelineModel->getGuidelineById($id);
                if (!$guideline) {
                    flash('maintenance_message', 'Guideline not found', 'alert alert-danger');
                    redirect('maintenance/guidelines');
                }
            }

            // Get equipment types
            $equipmentTypes = $this->assetModel->getUniqueFieldValues('equipment_type');

            $data = [
                'id' => $id,
                'name' => $guideline ? $guideline->name : '',
                'description' => $guideline ? $guideline->description : '',
                'equipment_type' => $guideline ? $guideline->equipment_type : '',
                'frequency_days' => $guideline ? $guideline->frequency_days : 30,
                'importance' => $guideline ? $guideline->importance : 'medium',
                'equipment_types' => $equipmentTypes,
                'name_err' => '',
                'description_err' => '',
                'equipment_type_err' => '',
                'frequency_days_err' => '',
                'importance_err' => ''
            ];

            $this->view('maintenance/edit_guideline', $data);
        }
    }

    /**
     * Delete guideline
     *
     * @param int $id
     */
    public function deleteGuideline($id) {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Get guideline
            $guideline = $this->guidelineModel->getGuidelineById($id);

            if (!$guideline) {
                flash('maintenance_message', 'Guideline not found', 'alert alert-danger');
                redirect('maintenance/guidelines');
            }

            // Delete guideline
            if ($this->guidelineModel->deleteGuideline($id)) {
                flash('maintenance_message', 'Guideline deleted successfully');
                redirect('maintenance/guidelines');
            } else {
                die('Something went wrong');
            }
        } else {
            redirect('maintenance/guidelines');
        }
    }

    /**
     * View all maintenance history
     *
     * @param int $page Current page number for pagination
     */
    public function allHistory($page = 1) {
        // Set records per page
        $recordsPerPage = 20;

        // Calculate offset
        $offset = ($page - 1) * $recordsPerPage;

        // Get total records count
        $totalRecords = $this->maintenanceModel->countAllMaintenanceHistory();

        // Calculate total pages
        $totalPages = ceil($totalRecords / $recordsPerPage);

        // Get maintenance history with pagination
        $maintenanceHistory = $this->maintenanceModel->getAllMaintenanceHistory($recordsPerPage, $offset);

        // Get implemented guidelines for each maintenance record
        $implementedGuidelines = [];
        foreach ($maintenanceHistory as $record) {
            $implementedGuidelines[$record->id] = $this->maintenanceModel->getImplementedGuidelines($record->id);
        }

        $data = [
            'maintenance_history' => $maintenanceHistory,
            'implemented_guidelines' => $implementedGuidelines,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_records' => $totalRecords,
            'records_per_page' => $recordsPerPage
        ];

        $this->view('maintenance/all_history', $data);
    }

    /**
     * Redirect to viewRecord method
     * This method exists to handle legacy URLs that use /maintenance/view/ instead of /maintenance/viewRecord/
     *
     * @param int $maintenanceId
     */
    public function viewRedirect($maintenanceId) {
        // Redirect to the viewRecord method
        redirect('maintenance/viewRecord/' . $maintenanceId);
    }

    /**
     * View detailed maintenance record with implemented guidelines
     *
     * @param int $maintenanceId
     */
    public function viewRecord($maintenanceId) {
        error_log("Maintenance::viewRecord - Starting with maintenanceId: $maintenanceId");

        // Get maintenance record
        $maintenanceRecord = $this->maintenanceModel->getMaintenanceById($maintenanceId);

        if (!$maintenanceRecord) {
            error_log("Maintenance::viewRecord - Maintenance record not found for ID: $maintenanceId");
            flash('maintenance_message', 'Maintenance record not found', 'alert alert-danger');
            redirect('maintenance');
        }

        error_log("Maintenance::viewRecord - Found maintenance record: " . print_r($maintenanceRecord, true));

        // Get asset details
        $asset = $this->assetModel->getAssetById($maintenanceRecord->asset_id);

        if (!$asset) {
            error_log("Maintenance::viewRecord - Asset not found for ID: " . $maintenanceRecord->asset_id);
            flash('maintenance_message', 'Asset not found', 'alert alert-danger');
            redirect('maintenance');
        }

        error_log("Maintenance::viewRecord - Found asset: " . $asset->computer_host_name);

        // Check if there are any guidelines in the implementation table for this record
        $db = new Database;
        $db->query('SELECT COUNT(*) as count FROM maintenance_guideline_implementation WHERE maintenance_id = :maintenance_id');
        $db->bind(':maintenance_id', $maintenanceId);
        $result = $db->single();
        $guidelineCount = $result->count;
        error_log("Maintenance::viewRecord - Direct DB Count of guidelines: $guidelineCount");

        // If we have guidelines, let's see what they are
        if ($guidelineCount > 0) {
            $db->query('SELECT * FROM maintenance_guideline_implementation WHERE maintenance_id = :maintenance_id');
            $db->bind(':maintenance_id', $maintenanceId);
            $rawGuidelines = $db->resultSet();
            error_log("Maintenance::viewRecord - Raw guidelines from DB: " . print_r($rawGuidelines, true));
        }

        // Direct database check for guidelines
        $db = new Database;
        $directGuidelines = [];

        // Check if the table exists
        $db->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
        $tableExists = $db->rowCount() > 0;
        error_log("Maintenance::viewRecord - maintenance_guideline_implementation table exists: " . ($tableExists ? 'Yes' : 'No'));

        if ($tableExists) {
            // Try a direct query to get guidelines
            try {
                $db->query('SELECT i.*, g.id as guideline_id, g.name, g.description, g.equipment_type, g.frequency_days, g.importance, i.implemented_date
                            FROM maintenance_guideline_implementation i
                            JOIN maintenance_guidelines g ON i.guideline_id = g.id
                            WHERE i.maintenance_id = :maintenance_id');
                $db->bind(':maintenance_id', $maintenanceId);
                $directGuidelines = $db->resultSet();
                error_log("Maintenance::viewRecord - Direct query found " . count($directGuidelines) . " guidelines");

                if (count($directGuidelines) > 0) {
                    error_log("Maintenance::viewRecord - First direct guideline: " . print_r(get_object_vars($directGuidelines[0]), true));

                    // Add empty checklist_items array to each guideline
                    foreach ($directGuidelines as &$guideline) {
                        $guideline->checklist_items = [];
                    }
                }
            } catch (Exception $e) {
                error_log("Maintenance::viewRecord - Error in direct query: " . $e->getMessage());
            }

            // If direct query didn't find any guidelines but we know they exist, try a simpler query
            if (empty($directGuidelines) && $guidelineCount > 0) {
                try {
                    // First get the raw guideline IDs
                    $db->query('SELECT guideline_id FROM maintenance_guideline_implementation WHERE maintenance_id = :maintenance_id');
                    $db->bind(':maintenance_id', $maintenanceId);
                    $rawGuidelineIds = $db->resultSet();
                    error_log("Maintenance::viewRecord - Raw guideline IDs: " . print_r($rawGuidelineIds, true));

                    // Then get the guideline details for each ID
                    if (!empty($rawGuidelineIds)) {
                        foreach ($rawGuidelineIds as $rawGuideline) {
                            $guidelineId = $rawGuideline->guideline_id;
                            $db->query('SELECT g.*, :maintenance_id as maintenance_id, NOW() as implemented_date, g.id as guideline_id
                                        FROM maintenance_guidelines g
                                        WHERE g.id = :guideline_id');
                            $db->bind(':maintenance_id', $maintenanceId);
                            $db->bind(':guideline_id', $guidelineId);
                            $guideline = $db->single();

                            if ($guideline) {
                                $guideline->checklist_items = [];
                                $directGuidelines[] = $guideline;
                            }
                        }
                        error_log("Maintenance::viewRecord - Fallback query found " . count($directGuidelines) . " guidelines");
                    }
                } catch (Exception $e) {
                    error_log("Maintenance::viewRecord - Error in fallback query: " . $e->getMessage());
                }
            }
        }

        // Get detailed implemented guidelines for this maintenance record
        $implementedGuidelines = $this->maintenanceModel->getDetailedImplementedGuidelines($maintenanceId);
        error_log("Maintenance::viewRecord - Detailed guidelines count: " . count($implementedGuidelines));

        // Debug the getImplementedGuidelines method
        $basicGuidelines = $this->maintenanceModel->getImplementedGuidelines($maintenanceId);
        error_log("Maintenance::viewRecord - Basic guidelines count: " . count($basicGuidelines));
        if (count($basicGuidelines) > 0) {
            error_log("Maintenance::viewRecord - Basic guidelines: " . print_r($basicGuidelines, true));
        }

        // Use direct query results as a fallback if the model method returned no results
        if (empty($implementedGuidelines) && !empty($directGuidelines)) {
            error_log("Maintenance::viewRecord - Using direct query results as fallback");
            $implementedGuidelines = $directGuidelines;
        }

        // Get all completed checklist items for this maintenance record
        $completedChecklistItems = $this->maintenanceModel->getAllCompletedChecklistItems($maintenanceId);
        error_log("Maintenance::viewRecord - Retrieved completed checklist items: " . print_r(array_keys($completedChecklistItems), true));

        // If there are no completed checklist items and we're in debug mode, create some test data
        if (empty($completedChecklistItems) && isset($_GET['create_test_data']) && $_GET['create_test_data'] == 1) {
            error_log("Maintenance::viewRecord - Creating test checklist completion data");

            // Get all guidelines for this maintenance record
            $guidelineIds = [];
            if (!empty($directGuidelines)) {
                foreach ($directGuidelines as $guideline) {
                    $guidelineIds[] = $guideline->guideline_id ?? $guideline->id;
                }
            }

            if (!empty($guidelineIds)) {
                // For each guideline, get checklist items and mark some as completed
                foreach ($guidelineIds as $guidelineId) {
                    $checklistItems = $this->maintenanceModel->getChecklistItems($guidelineId);

                    if (!empty($checklistItems)) {
                        error_log("Maintenance::viewRecord - Found " . count($checklistItems) . " checklist items for guideline ID: $guidelineId");

                        // Mark each checklist item as completed
                        foreach ($checklistItems as $item) {
                            $result = $this->maintenanceModel->recordChecklistCompletion(
                                $maintenanceId,
                                $item->id,
                                true,
                                $_SESSION['user_id'] ?? null,
                                'Test completion'
                            );

                            error_log("Maintenance::viewRecord - Marked checklist item " . $item->id . " as completed: " . ($result ? 'Success' : 'Failed'));
                        }
                    }
                }

                // Retrieve the completed checklist items again
                $completedChecklistItems = $this->maintenanceModel->getAllCompletedChecklistItems($maintenanceId);
                error_log("Maintenance::viewRecord - After creating test data, retrieved completed checklist items: " . print_r(array_keys($completedChecklistItems), true));
            }
        }

        // Get user who performed the maintenance
        $performedBy = null;
        if ($maintenanceRecord->performed_by) {
            $userModel = $this->model('User');
            $performedBy = $userModel->getUserById($maintenanceRecord->performed_by);
        }

        // Get compliance status for this asset
        $complianceStatus = $this->complianceModel->getAssetComplianceStatus($asset->id);
        error_log("Maintenance::viewRecord - Retrieved " . count($complianceStatus) . " compliance records");

        // Get compliance status for implemented guidelines
        $guidelineComplianceStatus = [];
        if (!empty($implementedGuidelines)) {
            foreach ($implementedGuidelines as $guideline) {
                $guidelineId = $guideline->guideline_id ?? $guideline->id;
                foreach ($complianceStatus as $compliance) {
                    if ($compliance->guideline_id == $guidelineId) {
                        $guidelineComplianceStatus[$guidelineId] = $compliance;
                        break;
                    }
                }
            }
            error_log("Maintenance::viewRecord - Mapped " . count($guidelineComplianceStatus) . " guideline compliance records");
        }

        $data = [
            'maintenance_record' => $maintenanceRecord,
            'asset' => $asset,
            'implemented_guidelines' => $implementedGuidelines,
            'performed_by' => $performedBy,
            'compliance_status' => $complianceStatus,
            'guideline_compliance_status' => $guidelineComplianceStatus,
            'completed_checklist_items' => $completedChecklistItems
        ];

        $this->view('maintenance/view_record', $data);
    }

    /**
     * Monitoring dashboard
     */
    public function monitoring() {
        try {
            // Update compliance status for all assets
            $this->complianceModel->updateAllComplianceStatus();

            // Get all compliance status
            $complianceStatus = $this->complianceModel->getAllComplianceStatus();
            if (!is_array($complianceStatus)) {
                $complianceStatus = [];
            }

            // Get overdue maintenance tasks
            $overdueMaintenance = $this->complianceModel->getOverdueMaintenance();
            if (!is_array($overdueMaintenance)) {
                $overdueMaintenance = [];
            }

            // Get maintenance tasks due soon
            $maintenanceDueSoon = $this->complianceModel->getMaintenanceDueSoon(30);
            if (!is_array($maintenanceDueSoon)) {
                $maintenanceDueSoon = [];
            }

            // Get compliant endpoints
            $compliantEndpoints = $this->complianceModel->getCompliantEndpoints();
            if (!is_array($compliantEndpoints)) {
                $compliantEndpoints = [];
            }

            // Calculate compliance statistics
            $totalAssets = $this->assetModel->getAssetCount();
            $totalGuidelines = count($this->guidelineModel->getAllGuidelines());

            // Get counts directly from the database for accuracy
            try {
                // Count compliant records
                $this->db->query('SELECT COUNT(*) as count FROM maintenance_compliance WHERE compliance_status = "compliant"');
                $result = $this->db->single();
                $compliantCount = $result ? $result->count : 0;

                // Count due soon records
                $this->db->query('SELECT COUNT(*) as count FROM maintenance_compliance WHERE compliance_status = "due_soon"');
                $result = $this->db->single();
                $dueSoonCount = $result ? $result->count : 0;

                // Count overdue records
                $this->db->query('SELECT COUNT(*) as count FROM maintenance_compliance WHERE compliance_status = "overdue"');
                $result = $this->db->single();
                $overdueCount = $result ? $result->count : 0;

                // Count not applicable records
                $this->db->query('SELECT COUNT(*) as count FROM maintenance_compliance WHERE compliance_status = "not_applicable"');
                $result = $this->db->single();
                $notApplicableCount = $result ? $result->count : 0;

                // Log the counts for debugging
                error_log("Maintenance::monitoring - Compliance counts from DB: Compliant: $compliantCount, Due Soon: $dueSoonCount, Overdue: $overdueCount, Not Applicable: $notApplicableCount");
            } catch (Exception $e) {
                error_log("Maintenance::monitoring - Error getting compliance counts: " . $e->getMessage());

                // Fallback to counting from the array if DB query fails
                $compliantCount = 0;
                $dueSoonCount = 0;
                $overdueCount = 0;
                $notApplicableCount = 0;

                foreach ($complianceStatus as $status) {
                    if (isset($status->compliance_status)) {
                        switch ($status->compliance_status) {
                            case 'compliant':
                                $compliantCount++;
                                break;
                            case 'due_soon':
                                $dueSoonCount++;
                                break;
                            case 'overdue':
                                $overdueCount++;
                                break;
                            case 'not_applicable':
                                $notApplicableCount++;
                                break;
                        }
                    }
                }

                error_log("Maintenance::monitoring - Fallback compliance counts from array: Compliant: $compliantCount, Due Soon: $dueSoonCount, Overdue: $overdueCount, Not Applicable: $notApplicableCount");
            }

            $data = [
                'compliance_status' => $complianceStatus,
                'overdue_maintenance' => $overdueMaintenance,
                'maintenance_due_soon' => $maintenanceDueSoon,
                'compliant_endpoints' => $compliantEndpoints,
                'total_assets' => $totalAssets,
                'total_guidelines' => $totalGuidelines,
                'compliant_count' => $compliantCount,
                'due_soon_count' => $dueSoonCount,
                'overdue_count' => $overdueCount,
                'not_applicable_count' => $notApplicableCount
            ];

            $this->view('maintenance/monitoring', $data);
        } catch (Exception $e) {
            error_log("Maintenance::monitoring - Fatal error: " . $e->getMessage());
            flash('maintenance_message', 'An error occurred while loading the monitoring dashboard. Please try again or contact support.', 'bg-red-100 text-red-800');
            redirect('maintenance');
        }
    }

    /**
     * View asset compliance
     *
     * @param int $assetId
     */
    public function assetCompliance($assetId) {
        // Get asset details
        $asset = $this->assetModel->getAssetById($assetId);

        if (!$asset) {
            flash('maintenance_message', 'Asset not found', 'alert alert-danger');
            redirect('maintenance/monitoring');
        }

        // Get compliance status for this asset
        $complianceStatus = $this->complianceModel->getAssetComplianceStatus($assetId);

        // Get maintenance history
        $maintenanceHistory = $this->maintenanceModel->getMaintenanceHistory($assetId);

        $data = [
            'asset' => $asset,
            'compliance_status' => $complianceStatus,
            'maintenance_history' => $maintenanceHistory
        ];

        $this->view('maintenance/asset_compliance', $data);
    }

    /**
     * View all implementations of a specific guideline across different maintenance records
     *
     * @param int $guidelineId
     * @param int $page Current page number for pagination
     */
    public function guidelineImplementations($guidelineId, $page = 1) {
        // Set records per page
        $recordsPerPage = 20;

        // Calculate offset
        $offset = ($page - 1) * $recordsPerPage;

        // Get guideline implementations
        $result = $this->maintenanceModel->getGuidelineImplementations($guidelineId, $recordsPerPage, $offset);

        if (!$result['guideline']) {
            flash('maintenance_message', 'Guideline not found', 'alert alert-danger');
            redirect('maintenance/guidelines');
        }

        // Calculate total pages
        $totalPages = ceil($result['total_count'] / $recordsPerPage);

        $data = [
            'guideline' => $result['guideline'],
            'implementations' => $result['implementations'],
            'total_count' => $result['total_count'],
            'current_page' => $page,
            'total_pages' => $totalPages,
            'records_per_page' => $recordsPerPage
        ];

        $this->view('maintenance/guideline_implementations', $data);
    }

    /**
     * Data Integrity Check Tool
     * Identifies and fixes orphaned records in the maintenance_guideline_implementation table
     */
    public function dataIntegrityCheck() {
        // Check if user is admin
        if (!isAdmin()) {
            flash('maintenance_message', 'You do not have permission to access this page', 'alert alert-danger');
            redirect('maintenance');
        }

        // Initialize data array
        $data = [
            'orphaned_records' => [],
            'fixed_records' => [],
            'total_records' => 0,
            'orphaned_count' => 0,
            'fixed_count' => 0,
            'action_taken' => false
        ];

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Check if fix action was requested
            if (isset($_POST['fix_orphaned']) && $_POST['fix_orphaned'] == 'true') {
                // Fix orphaned records
                $fixResult = $this->maintenanceModel->fixOrphanedRecords();
                $data['fixed_records'] = $fixResult['fixed_records'];
                $data['fixed_count'] = $fixResult['fixed_count'];
                $data['action_taken'] = true;

                // Log the action
                error_log("Maintenance::dataIntegrityCheck - Fixed {$data['fixed_count']} orphaned records");

                // Flash message
                flash('maintenance_message', "Fixed {$data['fixed_count']} orphaned records successfully", 'alert alert-success');
            }
        }

        // Get orphaned records
        $integrityResult = $this->maintenanceModel->checkDataIntegrity();
        $data['orphaned_records'] = $integrityResult['orphaned_records'];
        $data['total_records'] = $integrityResult['total_records'];
        $data['orphaned_count'] = $integrityResult['orphaned_count'];

        // Load view
        $this->view('maintenance/data_integrity', $data);
    }

    /**
     * Recreate Missing Maintenance Records
     * Recreates maintenance history records based on implementation records
     */
    public function recreateMissingRecords() {
        // Check if user is admin
        if (!isAdmin()) {
            flash('maintenance_message', 'You do not have permission to access this page', 'alert alert-danger');
            redirect('maintenance');
        }

        // Initialize data array
        $data = [
            'missing_records' => [],
            'recreated_records' => [],
            'total_implementations' => 0,
            'missing_count' => 0,
            'recreated_count' => 0,
            'action_taken' => false
        ];

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Check if recreate action was requested
            if (isset($_POST['recreate_records']) && $_POST['recreate_records'] == 'true') {
                // Recreate missing records
                $recreateResult = $this->maintenanceModel->recreateMissingRecords();
                $data['recreated_records'] = $recreateResult['recreated_records'];
                $data['recreated_count'] = $recreateResult['recreated_count'];
                $data['action_taken'] = true;

                // Log the action
                error_log("Maintenance::recreateMissingRecords - Recreated {$data['recreated_count']} missing maintenance records");

                // Flash message
                flash('maintenance_message', "Recreated {$data['recreated_count']} missing maintenance records successfully", 'alert alert-success');
            }
        }

        // Get missing records
        $missingResult = $this->maintenanceModel->findMissingMaintenanceRecords();
        $data['missing_records'] = $missingResult['missing_records'];
        $data['total_implementations'] = $missingResult['total_implementations'];
        $data['missing_count'] = $missingResult['missing_count'];

        // Load view
        $this->view('maintenance/recreate_records', $data);
    }

    /**
     * Run Database Migration for Guideline Implementation Table
     * Executes the migration script to ensure the table exists and has the correct structure
     *
     * @param int $guidelineId Optional guideline ID to redirect back to
     */
    public function runMigration($guidelineId = null) {
        // Check if user is admin
        if (!isAdmin()) {
            flash('maintenance_message', 'You do not have permission to access this page', 'alert alert-danger');
            redirect('maintenance');
        }

        // Path to the migration script
        $migrationScript = APPROOT . '/scripts/create_guideline_implementation_table.php';

        // Check if the script exists
        if (file_exists($migrationScript)) {
            // Execute the script and capture output
            ob_start();
            include $migrationScript;
            $output = ob_get_clean();

            // Log the output
            error_log("Maintenance::runMigration - Migration script executed. Output: " . str_replace("\n", " ", $output));

            // Flash success message
            flash('maintenance_message', 'Database migration completed successfully. The implementation table has been created or updated.', 'bg-green-100 text-green-800');
        } else {
            // Log error
            error_log("Maintenance::runMigration - Migration script not found at: $migrationScript");

            // Flash error message
            flash('maintenance_message', 'Migration script not found. Please contact the system administrator.', 'bg-red-100 text-red-800');
        }

        // Redirect back to the appropriate page
        if ($guidelineId) {
            redirect("maintenance/guidelineImplementations/$guidelineId");
        } else {
            redirect('maintenance/guidelines');
        }
    }
}
