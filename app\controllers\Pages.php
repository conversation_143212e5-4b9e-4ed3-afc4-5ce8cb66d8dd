<?php
/**
 * Pages Controller
 *
 * Handles static pages and general content pages for the application.
 * This includes the home page, about page, and other informational pages.
 *
 * @package AssetVisibility
 * <AUTHOR> Visibility Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
class Pages extends Controller {

    /**
     * Constructor
     *
     * Initializes the Pages controller. No special initialization required
     * for static pages.
     *
     * @since 1.0.0
     */
    public function __construct() {
        // No initialization required for static pages
    }

    /**
     * Display the home page
     *
     * Shows the main landing page with system statistics and overview.
     * Displays asset count if user is logged in.
     *
     * @return void
     * @since 1.0.0
     */
    public function index() {
        // Get some statistics for the home page
        $assetModel = $this->model('Asset');
        $assetCount = 0;

        if(isLoggedIn()) {
            // Get asset count if user is logged in
            $assetCount = $assetModel->getAssetCount();
        }

        $data = [
            'title' => 'Endpoint Visibility and Insight System',
            'description' => 'Comprehensive asset management system for tracking and managing ICT assets',
            'asset_count' => $assetCount
        ];

        $this->view('pages/index', $data);
    }

    /**
     * Display the about page
     *
     * Shows information about the application and organization.
     *
     * @return void
     * @since 1.0.0
     */
    public function about() {
        $data = [
            'title' => 'About Us',
            'description' => 'App to manage ICT assets inventory'
        ];

        $this->view('pages/about', $data);
    }
}
