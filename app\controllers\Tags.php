<?php
class Tags extends Controller {
    private $tagModel;

    public function __construct() {
        // Check if user is logged in
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        $this->tagModel = $this->model('Tag');
    }

    // Display all tags
    public function index() {
        $tags = $this->tagModel->getTags();

        $data = [
            'tags' => $tags
        ];

        $this->view('tags/index', $data);
    }

    // Add new tag
    public function add() {
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form

            // Sanitize POST data
            $_POST = Security::sanitizePostData($_POST);

            // Init data
            $data = [
                'name' => trim($_POST['name']),
                'color' => trim($_POST['color']),
                'created_by' => $_SESSION['user_id'],
                'name_err' => ''
            ];

            // Validate name
            if(empty($data['name'])) {
                $data['name_err'] = 'Please enter tag name';
            } else {
                // Check if tag already exists
                if($this->tagModel->getTagByName($data['name'])) {
                    $data['name_err'] = 'Tag name already exists';
                }
            }

            // Make sure there are no errors
            if(empty($data['name_err'])) {
                // Validated
                if($this->tagModel->addTag($data)) {
                    flash('tag_message', 'Tag Added');
                    redirect('tags');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $this->view('tags/add', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'color' => '#3b82f6',
                'name_err' => ''
            ];

            // Load view
            $this->view('tags/add', $data);
        }
    }

    // Edit tag
    public function edit($id) {
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form

            // Sanitize POST data
            $_POST = Security::sanitizePostData($_POST);

            // Init data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'color' => trim($_POST['color']),
                'name_err' => ''
            ];

            // Validate name
            if(empty($data['name'])) {
                $data['name_err'] = 'Please enter tag name';
            } else {
                // Check if tag already exists (excluding current tag)
                $existingTag = $this->tagModel->getTagByName($data['name']);
                if($existingTag && $existingTag->id != $id) {
                    $data['name_err'] = 'Tag name already exists';
                }
            }

            // Make sure there are no errors
            if(empty($data['name_err'])) {
                // Validated
                if($this->tagModel->updateTag($data)) {
                    flash('tag_message', 'Tag Updated');
                    redirect('tags');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $this->view('tags/edit', $data);
            }
        } else {
            // Get tag from model
            $tag = $this->tagModel->getTagById($id);

            // Check for owner
            if(!$tag) {
                redirect('tags');
            }

            $data = [
                'id' => $id,
                'name' => $tag->name,
                'color' => $tag->color,
                'name_err' => ''
            ];

            $this->view('tags/edit', $data);
        }
    }

    // Delete tag
    public function delete($id) {
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Get tag from model
            $tag = $this->tagModel->getTagById($id);

            // Check if tag exists
            if(!$tag) {
                redirect('tags');
            }

            // Check if tag is in use
            $assetCount = $this->tagModel->countAssetsWithTag($id);
            if($assetCount > 0) {
                flash('tag_message', 'Cannot delete tag that is in use by ' . $assetCount . ' assets', 'alert alert-danger');
                redirect('tags');
            }

            if($this->tagModel->deleteTag($id)) {
                flash('tag_message', 'Tag Removed');
                redirect('tags');
            } else {
                die('Something went wrong');
            }
        } else {
            redirect('tags');
        }
    }

    // Get assets by tag (AJAX)
    public function getAssets($id) {
        // Get assets with this tag
        $assets = $this->tagModel->getAssetsByTag($id);

        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode($assets);
    }
}
