<?php
/**
 * Users Controller
 *
 * Handles all user-related operations including authentication,
 * registration, profile management, and user administration.
 *
 * @package AssetVisibility
 * <AUTHOR> Visibility Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
class Users extends Controller {
    /**
     * User model instance
     * @var User
     */
    private $userModel;

    /**
     * Constructor
     *
     * Initializes the Users controller with user model loading.
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->userModel = $this->model('User');
    }

    /**
     * User registration
     *
     * Handles both GET (display form) and POST (process registration) requests.
     * Includes comprehensive validation and password strength checking.
     *
     * @return void
     * @since 1.0.0
     */
    public function register() {
        // Check for POST
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form

            // Sanitize POST data
            $_POST = Security::sanitizePostData($_POST);

            // Init data
            $data = [
                'name' => trim($_POST['name']),
                'email' => trim($_POST['email']),
                'password' => trim($_POST['password']),
                'confirm_password' => trim($_POST['confirm_password']),
                'name_err' => '',
                'email_err' => '',
                'password_err' => '',
                'confirm_password_err' => ''
            ];

            // Validate Email
            if(empty($data['email'])) {
                $data['email_err'] = 'Please enter email';
            } else {
                // Check email
                if($this->userModel->findUserByEmail($data['email'])) {
                    $data['email_err'] = 'Email is already taken';
                }
            }

            // Validate Name
            if(empty($data['name'])) {
                $data['name_err'] = 'Please enter name';
            }

            // Validate Password
            if(empty($data['password'])) {
                $data['password_err'] = 'Please enter password';
            } elseif(strlen($data['password']) < 8) {
                $data['password_err'] = 'Password must be at least 8 characters';
            } elseif(!preg_match('/[A-Z]/', $data['password'])) {
                $data['password_err'] = 'Password must contain at least one uppercase letter';
            } elseif(!preg_match('/[a-z]/', $data['password'])) {
                $data['password_err'] = 'Password must contain at least one lowercase letter';
            } elseif(!preg_match('/[0-9]/', $data['password'])) {
                $data['password_err'] = 'Password must contain at least one number';
            } elseif(!preg_match('/[^A-Za-z0-9]/', $data['password'])) {
                $data['password_err'] = 'Password must contain at least one special character';
            }

            // Validate Confirm Password
            if(empty($data['confirm_password'])) {
                $data['confirm_password_err'] = 'Please confirm password';
            } else {
                if($data['password'] != $data['confirm_password']) {
                    $data['confirm_password_err'] = 'Passwords do not match';
                }
            }

            // Make sure errors are empty
            if(empty($data['email_err']) && empty($data['name_err']) && empty($data['password_err']) && empty($data['confirm_password_err'])) {
                // Validated

                // Hash Password
                $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);

                // Register User
                if($this->userModel->register($data)) {
                    // Redirect to login
                    header('Location: ' . URLROOT . '/users/login');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $this->view('users/register', $data);
            }

        } else {
            // Init data
            $data = [
                'name' => '',
                'email' => '',
                'password' => '',
                'confirm_password' => '',
                'name_err' => '',
                'email_err' => '',
                'password_err' => '',
                'confirm_password_err' => ''
            ];

            // Load view
            $this->view('users/register', $data);
        }
    }

    public function login() {
        // Check for POST
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            // Sanitize POST data
            $_POST = Security::sanitizePostData($_POST);

            // Init data
            $data = [
                'email' => trim($_POST['email']),
                'password' => trim($_POST['password']),
                'email_err' => '',
                'password_err' => '',
            ];

            // Validate Email
            if(empty($data['email'])) {
                $data['email_err'] = 'Please enter email';
            }

            // Validate Password
            if(empty($data['password'])) {
                $data['password_err'] = 'Please enter password';
            }

            // Check for user/email
            if($this->userModel->findUserByEmail($data['email'])) {
                // User found
            } else {
                // User not found
                $data['email_err'] = 'No user found';
            }

            // Make sure errors are empty
            if(empty($data['email_err']) && empty($data['password_err'])) {
                // Validated
                // Check and set logged in user
                $loggedInUser = $this->userModel->login($data['email'], $data['password']);

                if($loggedInUser === 'inactive') {
                    $data['email_err'] = 'Your account is inactive. Please contact an administrator.';
                    $data['form_submitted'] = true;
                    $this->view('users/login', $data);
                } elseif($loggedInUser === 'locked') {
                    $data['email_err'] = 'Your account has been locked due to too many failed login attempts. Please try again later or contact an administrator.';
                    $data['form_submitted'] = true;
                    $this->view('users/login', $data);
                } elseif($loggedInUser) {
                    // Check if remember me is checked
                    $rememberMe = isset($_POST['remember_me']) ? true : false;

                    // Create Session
                    $this->createUserSession($loggedInUser, $rememberMe);
                } else {
                    $data['password_err'] = 'Password incorrect';
                    $data['form_submitted'] = true;
                    $this->view('users/login', $data);
                }
            } else {
                // Load view with errors
                $data['form_submitted'] = true;
                $this->view('users/login', $data);
            }

        } else {
            // Init data
            $data = [
                'email' => '',
                'password' => '',
                'email_err' => '',
                'password_err' => '',
            ];

            // Load view
            $this->view('users/login', $data);
        }
    }

    public function createUserSession($user, $rememberMe = false) {
        $_SESSION['user_id'] = $user->id;
        $_SESSION['user_email'] = $user->email;
        $_SESSION['user_name'] = $user->name;
        $_SESSION['user_role'] = $user->role;

        // Handle remember me functionality
        if($rememberMe) {
            // Create a remember me token
            $tokenData = $this->userModel->createRememberMeToken($user->id);

            if($tokenData) {
                // Create a cookie with the token
                $cookieValue = $tokenData['selector'] . ':' . $tokenData['validator'];
                $cookieExpiry = strtotime($tokenData['expiry']);

                // Set the cookie
                $this->setRememberMeCookie($cookieValue, $cookieExpiry);
            }
        }

        header('Location: ' . URLROOT . '/');
    }

    /**
     * Set the remember me cookie
     *
     * @param string $value Cookie value
     * @param int $expiry Cookie expiry timestamp
     */
    private function setRememberMeCookie($value, $expiry) {
        $params = [
            'expires' => $expiry,
            'path' => '/',
            'domain' => '',
            'secure' => COOKIE_SECURE,
            'httponly' => COOKIE_HTTP_ONLY,
            'samesite' => COOKIE_SAME_SITE
        ];

        setcookie('remember_me', $value, $params);
    }

    public function profile() {
        // Check if user is logged in
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        // Get user data
        $user = $this->userModel->getUserById($_SESSION['user_id']);

        if(!$user) {
            flash('user_message', 'User not found', 'alert alert-danger');
            redirect('');
        }

        // Check if form is submitted
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            // Sanitize POST data
            $_POST = Security::sanitizePostData($_POST);

            // Init data
            $data = [
                'id' => $_SESSION['user_id'],
                'name' => trim($_POST['name']),
                'email' => $user->email, // Email cannot be changed
                'current_password' => trim($_POST['current_password']),
                'new_password' => trim($_POST['new_password']),
                'confirm_password' => trim($_POST['confirm_password']),
                'name_err' => '',
                'current_password_err' => '',
                'new_password_err' => '',
                'confirm_password_err' => ''
            ];

            // Validate Name
            if(empty($data['name'])) {
                $data['name_err'] = 'Please enter name';
            }

            // Check if password change is requested
            $changePassword = !empty($data['current_password']) || !empty($data['new_password']) || !empty($data['confirm_password']);

            if($changePassword) {
                // Validate Current Password
                if(empty($data['current_password'])) {
                    $data['current_password_err'] = 'Please enter current password';
                } elseif(!password_verify($data['current_password'], $user->password)) {
                    $data['current_password_err'] = 'Current password is incorrect';
                }

                // Validate New Password
                if(empty($data['new_password'])) {
                    $data['new_password_err'] = 'Please enter new password';
                } elseif(strlen($data['new_password']) < 8) {
                    $data['new_password_err'] = 'Password must be at least 8 characters';
                } elseif(!preg_match('/[A-Z]/', $data['new_password'])) {
                    $data['new_password_err'] = 'Password must contain at least one uppercase letter';
                } elseif(!preg_match('/[a-z]/', $data['new_password'])) {
                    $data['new_password_err'] = 'Password must contain at least one lowercase letter';
                } elseif(!preg_match('/[0-9]/', $data['new_password'])) {
                    $data['new_password_err'] = 'Password must contain at least one number';
                } elseif(!preg_match('/[^A-Za-z0-9]/', $data['new_password'])) {
                    $data['new_password_err'] = 'Password must contain at least one special character';
                }

                // Validate Confirm Password
                if(empty($data['confirm_password'])) {
                    $data['confirm_password_err'] = 'Please confirm password';
                } elseif($data['new_password'] != $data['confirm_password']) {
                    $data['confirm_password_err'] = 'Passwords do not match';
                }
            }

            // Make sure errors are empty
            $nameValid = empty($data['name_err']);
            $passwordValid = !$changePassword || (empty($data['current_password_err']) && empty($data['new_password_err']) && empty($data['confirm_password_err']));

            if($nameValid && $passwordValid) {
                // Update user
                $updateData = [
                    'id' => $data['id'],
                    'name' => $data['name']
                ];

                if($changePassword) {
                    $updateData['password'] = password_hash($data['new_password'], PASSWORD_DEFAULT);
                }

                if($this->userModel->updateUser($updateData)) {
                    // Update session
                    $_SESSION['user_name'] = $data['name'];

                    flash('user_message', 'Profile updated successfully', 'alert alert-success');
                    redirect('users/profile');
                } else {
                    flash('user_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('users/profile', $data);
                }
            } else {
                // Load view with errors
                $this->view('users/profile', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'status' => $user->status,
                'created_at' => $user->created_at,
                'current_password' => '',
                'new_password' => '',
                'confirm_password' => '',
                'name_err' => '',
                'current_password_err' => '',
                'new_password_err' => '',
                'confirm_password_err' => ''
            ];

            // Load view
            $this->view('users/profile', $data);
        }
    }

    public function logout() {
        // Get user ID before destroying session
        $userId = $_SESSION['user_id'] ?? null;

        // Clear session
        unset($_SESSION['user_id']);
        unset($_SESSION['user_email']);
        unset($_SESSION['user_name']);
        unset($_SESSION['user_role']);
        session_destroy();

        // Clear remember me cookie if it exists
        if(isset($_COOKIE['remember_me'])) {
            // Get the selector from the cookie
            $cookieParts = explode(':', $_COOKIE['remember_me']);
            if(count($cookieParts) == 2) {
                $selector = $cookieParts[0];

                // Delete the token from the database
                if($userId) {
                    $this->userModel->deleteRememberMeToken($selector);
                }
            }

            // Delete the cookie
            $params = [
                'expires' => time() - 3600, // Set expiry in the past
                'path' => '/',
                'domain' => '',
                'secure' => COOKIE_SECURE,
                'httponly' => COOKIE_HTTP_ONLY,
                'samesite' => COOKIE_SAME_SITE
            ];

            setcookie('remember_me', '', $params);
        }

        // Log the logout event
        if($userId) {
            SecurityEnhancements::logSecurityEvent('logout', 'User logged out', $userId);
        }

        header('Location: ' . URLROOT . '/users/login');
    }

    // User management methods

    public function manage() {
        // Check if user is logged in and has permission to view users
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        if(!hasPermission('view_users')) {
            flash('user_message', 'You do not have permission to access that page', 'alert alert-danger');
            redirect('dashboard');
        }

        $users = $this->userModel->getAllUsers();

        // Load the Role model to get roles for each user
        $roleModel = $this->model('Role');

        // Add roles to each user
        foreach($users as $user) {
            $user->roles = $this->userModel->getUserRoles($user->id);
        }

        $data = [
            'users' => $users
        ];

        $this->view('users/manage', $data);
    }

    public function toggleStatus($id) {
        // Check if user is logged in and is admin
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        if(!isAdmin()) {
            flash('user_message', 'You are not authorized to perform that action', 'alert alert-danger');
            redirect('assets');
        }

        // Get the user
        $user = $this->userModel->getUserById($id);

        if(!$user) {
            flash('user_message', 'User not found', 'alert alert-danger');
            redirect('users/manage');
        }

        // Don't allow admins to deactivate themselves
        if($user->id == $_SESSION['user_id']) {
            flash('user_message', 'You cannot change your own status', 'alert alert-danger');
            redirect('users/manage');
        }

        // Toggle status
        $newStatus = ($user->status == 'active') ? 'inactive' : 'active';

        if($this->userModel->updateStatus($id, $newStatus)) {
            flash('user_message', 'User status updated', 'alert alert-success');
        } else {
            flash('user_message', 'Something went wrong', 'alert alert-danger');
        }

        redirect('users/manage');
    }

    public function toggleRole($id) {
        // Check if user is logged in and has permission to assign roles
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        if(!hasPermission('assign_roles')) {
            flash('user_message', 'You do not have permission to perform that action', 'alert alert-danger');
            redirect('dashboard');
        }

        // Get the user
        $user = $this->userModel->getUserById($id);

        if(!$user) {
            flash('user_message', 'User not found', 'alert alert-danger');
            redirect('users/manage');
        }

        // Don't allow users to change their own role
        if($user->id == $_SESSION['user_id']) {
            flash('user_message', 'You cannot change your own role', 'alert alert-danger');
            redirect('users/manage');
        }

        // Toggle role (legacy method, still supported for backward compatibility)
        $newRole = ($user->role == 'admin') ? 'user' : 'admin';

        if($this->userModel->updateRole($id, $newRole)) {
            // Clear permission cache for the current user
            clearPermissionCache();

            flash('user_message', 'User role updated', 'alert alert-success');
        } else {
            flash('user_message', 'Something went wrong', 'alert alert-danger');
        }

        redirect('users/manage');
    }

    /**
     * Manage roles for a user
     *
     * @param int $id User ID
     */
    public function roles($id) {
        // Check if user is logged in and has permission to assign roles
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        if(!hasPermission('assign_roles')) {
            flash('user_message', 'You do not have permission to manage user roles', 'alert alert-danger');
            redirect('dashboard');
        }

        // Get the user
        $user = $this->userModel->getUserById($id);

        if(!$user) {
            flash('user_message', 'User not found', 'alert alert-danger');
            redirect('users/manage');
        }

        // Don't allow users to change their own roles if they're not an admin
        if($user->id == $_SESSION['user_id'] && !isAdmin()) {
            flash('user_message', 'You cannot change your own roles', 'alert alert-danger');
            redirect('users/manage');
        }

        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if(!Security::validateCsrfToken($_POST['csrf_token'])) {
                flash('user_message', 'Security token validation failed', 'alert alert-danger');
                redirect('users/roles/' . $id);
            }

            // Get selected roles
            $roleIds = isset($_POST['roles']) ? $_POST['roles'] : [];

            // Make sure at least one role is selected
            if(empty($roleIds)) {
                flash('user_message', 'User must have at least one role', 'alert alert-danger');
                redirect('users/roles/' . $id);
            }

            // Assign roles to user
            if($this->userModel->assignRoles($id, $roleIds)) {
                // Update the legacy role field in the users table
                $roleModel = $this->model('Role');
                $primaryRole = $roleModel->getRoleById($roleIds[0]);

                // Map the role name to the legacy role value
                $legacyRole = ($primaryRole->name == 'Administrator') ? 'admin' : 'user';

                // Update the legacy role field
                $this->userModel->updateLegacyRole($id, $legacyRole);

                // Clear permission cache for the current user
                clearPermissionCache();

                flash('user_message', 'User roles updated successfully', 'alert alert-success');
            } else {
                flash('user_message', 'Something went wrong', 'alert alert-danger');
            }

            redirect('users/roles/' . $id);
        } else {
            // Get all roles
            $roleModel = $this->model('Role');
            $allRoles = $roleModel->getAllRoles();

            // Get user's current roles
            $userRoles = $this->userModel->getUserRoles($id);

            // Create an array of role IDs for easy checking
            $userRoleIds = [];
            foreach($userRoles as $role) {
                $userRoleIds[] = $role->id;
            }

            $data = [
                'user' => $user,
                'userRoles' => $userRoles,
                'userRoleIds' => $userRoleIds,
                'allRoles' => $allRoles
            ];

            $this->view('users/roles', $data);
        }
    }

    public function resetPassword($id = null) {
        // Check if ID parameter is provided
        if($id === null || empty($id)) {
            flash('user_message', 'Invalid user ID provided', 'alert alert-danger');
            redirect('users/manage');
        }

        // Check if user is logged in and is admin
        if(!isLoggedIn()) {
            redirect('users/login');
        }

        if(!isAdmin()) {
            flash('user_message', 'You are not authorized to perform that action', 'alert alert-danger');
            redirect('assets');
        }

        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            // Sanitize POST data
            $_POST = Security::sanitizePostData($_POST);

            // Init data
            $data = [
                'id' => $id,
                'password' => trim($_POST['password']),
                'confirm_password' => trim($_POST['confirm_password']),
                'password_err' => '',
                'confirm_password_err' => ''
            ];

            // Validate Password
            if(empty($data['password'])) {
                $data['password_err'] = 'Please enter password';
            } elseif(strlen($data['password']) < 6) {
                $data['password_err'] = 'Password must be at least 6 characters';
            }

            // Validate Confirm Password
            if(empty($data['confirm_password'])) {
                $data['confirm_password_err'] = 'Please confirm password';
            } else {
                if($data['password'] != $data['confirm_password']) {
                    $data['confirm_password_err'] = 'Passwords do not match';
                }
            }

            // Make sure errors are empty
            if(empty($data['password_err']) && empty($data['confirm_password_err'])) {
                // Hash Password
                $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);

                // Update Password
                if($this->userModel->updatePassword($id, $data['password'])) {
                    flash('user_message', 'Password updated', 'alert alert-success');
                    redirect('users/manage');
                } else {
                    flash('user_message', 'Something went wrong', 'alert alert-danger');
                    redirect('users/manage');
                }
            } else {
                // Load view with errors
                $this->view('users/reset_password', $data);
            }
        } else {
            // Get the user
            $user = $this->userModel->getUserById($id);

            if(!$user) {
                flash('user_message', 'User not found', 'alert alert-danger');
                redirect('users/manage');
            }

            $data = [
                'id' => $id,
                'user' => $user,
                'password' => '',
                'confirm_password' => '',
                'password_err' => '',
                'confirm_password_err' => ''
            ];

            $this->view('users/reset_password', $data);
        }
    }
    // Forgot Password
    public function forgotPassword() {
        // Check for POST
        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            // Sanitize POST data
            $_POST = Security::sanitizePostData($_POST);

            // Init data
            $data = [
                'email' => trim($_POST['email']),
                'email_err' => '',
                'captcha_err' => ''
            ];

            // Validate reCAPTCHA
            if(!$this->validateRecaptcha()) {
                $data['captcha_err'] = 'Please complete the CAPTCHA verification';
                $this->view('users/forgot_password', $data);
                return;
            }

            // Validate Email
            if(empty($data['email'])) {
                $data['email_err'] = 'Please enter email';
            } else {
                // Check rate limiting for password reset requests
                if(!SecurityEnhancements::checkPasswordResetRateLimit($data['email'])) {
                    // Log the rate limit event
                    SecurityEnhancements::logSecurityEvent('password_reset_request', 'Password reset rate limit exceeded for email: ' . $data['email']);

                    // For security reasons, we don't want to reveal rate limiting
                    flash('password_reset', 'If your email is registered, you will receive a password reset link', 'bg-green-100 text-green-800');
                    redirect('users/login');
                }

                // Check if email exists
                if(!$this->userModel->findUserByEmail($data['email'])) {
                    // For security reasons, we don't want to reveal if an email exists or not
                    // So we'll show a success message regardless
                    flash('password_reset', 'If your email is registered, you will receive a password reset link', 'bg-green-100 text-green-800');
                    redirect('users/login');
                }
            }

            // Make sure errors are empty
            if(empty($data['email_err']) && empty($data['captcha_err'])) {
                // Get user details
                $user = $this->userModel->getUserByEmail($data['email']);

                // Create password reset token
                $token = $this->userModel->createPasswordResetToken($data['email']);

                if($token) {
                    // Log the password reset request
                    SecurityEnhancements::logSecurityEvent('password_reset_request', 'Password reset requested for user ID: ' . $user->id, $user->id);

                    // Send password reset email
                    if(sendPasswordResetEmail($data['email'], $user->name, $token)) {
                        flash('password_reset', 'Password reset link has been sent to your email', 'bg-green-100 text-green-800');
                        redirect('users/login');
                    } else {
                        flash('password_reset', 'Failed to send password reset email. Please try again later.', 'bg-red-100 text-red-800');
                        redirect('users/forgotPassword');
                    }
                } else {
                    flash('password_reset', 'Something went wrong. Please try again later.', 'bg-red-100 text-red-800');
                    redirect('users/forgotPassword');
                }
            } else {
                // Load view with errors
                $this->view('users/forgot_password', $data);
            }
        } else {
            // Init data
            $data = [
                'email' => '',
                'email_err' => '',
                'captcha_err' => ''
            ];

            // Load view
            $this->view('users/forgot_password', $data);
        }
    }

    /**
     * Validate Google reCAPTCHA response
     *
     * @return bool True if valid, false otherwise
     */
    private function validateRecaptcha() {
        // Skip validation if no reCAPTCHA response
        if(!isset($_POST['g-recaptcha-response'])) {
            return false;
        }

        $recaptchaResponse = $_POST['g-recaptcha-response'];

        // Verify with Google
        $url = 'https://www.google.com/recaptcha/api/siteverify';
        $data = [
            'secret' => RECAPTCHA_SECRET_KEY,
            'response' => $recaptchaResponse,
            'remoteip' => $_SERVER['REMOTE_ADDR']
        ];

        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data)
            ]
        ];

        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        $response = json_decode($result);

        return $response->success;
    }

    // Reset Password with token
    public function resetPasswordWithToken() {
        // Check if token and email are provided in URL
        if(isset($_GET['token']) && isset($_GET['email'])) {
            $token = $_GET['token'];
            $email = $_GET['email'];

            // Verify token
            if(!$this->userModel->verifyPasswordResetToken($email, $token)) {
                flash('password_reset', 'Invalid or expired password reset link', 'bg-red-100 text-red-800');
                redirect('users/login');
            }

            // If token is valid and it's a GET request, show the reset password form
            if($_SERVER['REQUEST_METHOD'] == 'GET') {
                $data = [
                    'token' => $token,
                    'email' => $email,
                    'password' => '',
                    'confirm_password' => '',
                    'password_err' => '',
                    'confirm_password_err' => ''
                ];

                $this->view('users/reset_password_form', $data);
            } elseif($_SERVER['REQUEST_METHOD'] == 'POST') {
                // Process form
                // Sanitize POST data
                $_POST = Security::sanitizePostData($_POST);

                // Init data
                $data = [
                    'token' => $token,
                    'email' => $email,
                    'password' => trim($_POST['password']),
                    'confirm_password' => trim($_POST['confirm_password']),
                    'password_err' => '',
                    'confirm_password_err' => ''
                ];

                // Validate Password
                if(empty($data['password'])) {
                    $data['password_err'] = 'Please enter password';
                } elseif(strlen($data['password']) < 8) {
                    $data['password_err'] = 'Password must be at least 8 characters';
                } elseif(!preg_match('/[A-Z]/', $data['password'])) {
                    $data['password_err'] = 'Password must contain at least one uppercase letter';
                } elseif(!preg_match('/[a-z]/', $data['password'])) {
                    $data['password_err'] = 'Password must contain at least one lowercase letter';
                } elseif(!preg_match('/[0-9]/', $data['password'])) {
                    $data['password_err'] = 'Password must contain at least one number';
                } elseif(!preg_match('/[^A-Za-z0-9]/', $data['password'])) {
                    $data['password_err'] = 'Password must contain at least one special character';
                }

                // Validate Confirm Password
                if(empty($data['confirm_password'])) {
                    $data['confirm_password_err'] = 'Please confirm password';
                } else {
                    if($data['password'] != $data['confirm_password']) {
                        $data['confirm_password_err'] = 'Passwords do not match';
                    }
                }

                // Make sure errors are empty
                if(empty($data['password_err']) && empty($data['confirm_password_err'])) {
                    // Hash Password
                    $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);

                    // Update Password
                    if($this->userModel->resetPasswordByEmail($email, $hashedPassword)) {
                        // Mark token as used
                        $this->userModel->markTokenAsUsed($email, $token);

                        // Get user ID for logging
                        $user = $this->userModel->getUserByEmail($email);
                        if($user) {
                            // Log the password reset success
                            SecurityEnhancements::logSecurityEvent('password_reset_success', 'Password reset completed successfully', $user->id);
                        }

                        flash('password_reset', 'Your password has been reset successfully. You can now log in with your new password.', 'bg-green-100 text-green-800');
                        redirect('users/login');
                    } else {
                        flash('password_reset', 'Something went wrong. Please try again.', 'bg-red-100 text-red-800');
                        redirect('users/resetPasswordWithToken?token=' . $token . '&email=' . urlencode($email));
                    }
                } else {
                    // Load view with errors
                    $this->view('users/reset_password_form', $data);
                }
            }
        } else {
            redirect('users/login');
        }
    }
}