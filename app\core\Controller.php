<?php
/**
 * Base Controller Class
 *
 * This is the base controller class that all other controllers extend.
 * It provides common functionality for loading models and views.
 *
 * @package AssetVisibility
 * <AUTHOR> Visibility Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
class Controller {

    /**
     * Load a model
     *
     * Dynamically loads and instantiates a model class.
     *
     * @param string $model The name of the model to load
     * @return object The instantiated model object
     * @throws Exception If the model file doesn't exist
     * @since 1.0.0
     */
    public function model($model) {
        // Require model file
        $modelPath = '../app/models/' . $model . '.php';

        if (!file_exists($modelPath)) {
            throw new Exception("Model file not found: {$modelPath}");
        }

        require_once $modelPath;

        // Instantiate model
        return new $model();
    }

    /**
     * Load a view
     *
     * Loads a view file and passes data to it. Automatically adds CSRF token
     * to the data array for form security.
     *
     * @param string $view The view file path (relative to views directory)
     * @param array $data Associative array of data to pass to the view
     * @return void
     * @throws Exception If the view file doesn't exist
     * @since 1.0.0
     */
    public function view($view, $data = []) {
        // Add CSRF token to data array for forms
        if (!isset($data['csrf_token'])) {
            $data['csrf_token'] = Security::generateCsrfToken();
        }

        // Check for view file
        $viewPath = '../app/views/' . $view . '.php';
        if(file_exists($viewPath)) {
            require_once $viewPath;
        } else {
            // View does not exist
            throw new Exception("View does not exist: {$viewPath}");
        }
    }

    /**
     * Sanitize POST data to prevent XSS attacks
     *
     * Uses the Security class to sanitize input data and prevent
     * cross-site scripting (XSS) attacks.
     *
     * @param array $data The POST data to sanitize
     * @return array The sanitized data
     * @since 1.0.0
     */
    protected function sanitizePostData($data) {
        return Security::sanitizeInput($data);
    }

    /**
     * Validate CSRF token
     *
     * Validates a CSRF token to prevent cross-site request forgery attacks.
     * Automatically redirects on validation failure.
     *
     * @param string $token The token to validate
     * @return bool True if the token is valid, false otherwise
     * @since 1.0.0
     */
    protected function validateCsrfToken($token) {
        // Debug log
        error_log("validateCsrfToken called with token: " . substr($token, 0, 10) . "...");

        if (!Security::validateCsrfToken($token)) {
            // Invalid CSRF token
            flash('error', 'Invalid security token. Please try again.', 'alert alert-danger');

            // Get the current controller and method from the URL
            $url = isset($_GET['url']) ? $_GET['url'] : '';
            $urlParts = explode('/', $url);
            $controller = isset($urlParts[0]) ? $urlParts[0] : '';
            $method = isset($urlParts[1]) ? $urlParts[1] : '';

            error_log("CSRF validation failed. URL: " . $url . ", Controller: " . $controller . ", Method: " . $method);

            // If we're in the import method, redirect back to the import page
            if ($controller == 'assets' && $method == 'import') {
                error_log("Redirecting back to import page");
                redirect('assets/import');
            } else {
                // Otherwise redirect to the controller's index page
                error_log("Redirecting to controller index page: " . $controller);
                redirect($controller);
            }
            return false;
        }
        return true;
    }
}
