<?php
/**
 * PDO Database Class
 *
 * Provides a secure database abstraction layer using PDO with prepared statements.
 * Includes features for pagination, parameter binding, and SQL injection protection.
 *
 * @package AssetVisibility
 * <AUTHOR> Visibility Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
class Database {
    /**
     * Database host
     * @var string
     */
    private $host = DB_HOST;

    /**
     * Database username
     * @var string
     */
    private $user = DB_USER;

    /**
     * Database password
     * @var string
     */
    private $pass = DB_PASS;

    /**
     * Database name
     * @var string
     */
    private $dbname = DB_NAME;

    /**
     * PDO database handle
     * @var PDO
     */
    private $dbh;

    /**
     * PDO statement
     * @var PDOStatement
     */
    private $stmt;

    /**
     * Error message
     * @var string
     */
    private $error;

    /**
     * Property to store parameter bindings for reuse
     * @var array
     */
    private $paramBindings = [];

    /**
     * Constructor
     *
     * Establishes database connection using PDO with error handling.
     *
     * @throws PDOException If database connection fails
     * @since 1.0.0
     */
    public function __construct() {
        // Set DSN
        $dsn = 'mysql:host=' . $this->host . ';dbname=' . $this->dbname;
        $options = array(
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        );

        // Create PDO instance
        try {
            $this->dbh = new PDO($dsn, $this->user, $this->pass, $options);
        } catch(PDOException $e) {
            $this->error = $e->getMessage();
            echo $this->error;
        }
    }

    // Prepare statement with query
    public function query($sql) {
        // Clear previous bindings when starting a new query
        $this->paramBindings = [];

        // Validate SQL query to prevent SQL injection
        if ($this->validateSqlQuery($sql)) {
            $this->stmt = $this->dbh->prepare($sql);
        } else {
            $this->error = "Potentially unsafe SQL query detected";
            error_log("Potentially unsafe SQL query detected: " . $sql);
            die($this->error);
        }
    }

    // Validate SQL query for potential SQL injection
    private function validateSqlQuery($sql) {
        // Basic validation - just check for multiple statements
        // This is a simplified version to avoid blocking legitimate queries
        if (preg_match('/;(?!\s*$)/', $sql)) {
            // Multiple SQL statements detected
            return false;
        }

        // Allow all other queries - we're using prepared statements which provide protection
        return true;
    }

    // Bind values
    public function bind($param, $value, $type = null) {
        // Store the binding for later use in getTotalCount
        $this->paramBindings[$param] = [$value, $type];

        if(is_null($type)) {
            switch(true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }

        $this->stmt->bindValue($param, $value, $type);
    }

    // Execute the prepared statement
    public function execute() {
        return $this->stmt->execute();
    }

    // Get result set as array of objects
    public function resultSet() {
        $this->execute();
        return $this->stmt->fetchAll(PDO::FETCH_OBJ);
    }

    // Get single record as object
    public function single() {
        $this->execute();
        return $this->stmt->fetch(PDO::FETCH_OBJ);
    }

    // Get row count
    public function rowCount() {
        return $this->stmt->rowCount();
    }

    // Get last insert ID
    public function lastInsertId() {
        return $this->dbh->lastInsertId();
    }

    // Get paginated result set
    public function paginatedResultSet($page = 1, $perPage = 10) {
        // Calculate offset
        $offset = ($page - 1) * $perPage;

        // Store the original query
        $originalQuery = $this->stmt->queryString;

        // Add LIMIT clause to the query
        $paginatedQuery = $originalQuery . " LIMIT :offset, :perPage";

        // Save current bindings
        $currentBindings = $this->paramBindings;

        // Prepare the new query
        $this->stmt = $this->dbh->prepare($paginatedQuery);

        // Re-apply all the original bindings
        foreach ($currentBindings as $param => $binding) {
            $value = $binding[0];
            $type = $binding[1];

            if(is_null($type)) {
                switch(true) {
                    case is_int($value):
                        $type = PDO::PARAM_INT;
                        break;
                    case is_bool($value):
                        $type = PDO::PARAM_BOOL;
                        break;
                    case is_null($value):
                        $type = PDO::PARAM_NULL;
                        break;
                    default:
                        $type = PDO::PARAM_STR;
                }
            }

            $this->stmt->bindValue($param, $value, $type);
        }

        // Add pagination parameters
        $this->stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $this->stmt->bindValue(':perPage', $perPage, PDO::PARAM_INT);

        // Add pagination bindings to the stored bindings
        $this->paramBindings[':offset'] = [$offset, PDO::PARAM_INT];
        $this->paramBindings[':perPage'] = [$perPage, PDO::PARAM_INT];

        // Execute and return results
        $this->execute();
        return $this->stmt->fetchAll(PDO::FETCH_OBJ);
    }

    // Get total count for pagination
    public function getTotalCount($countQuery) {
        // Create a new PDO statement for the count query
        $countStmt = $this->dbh->prepare($countQuery);

        // Apply the same bindings to the count query
        foreach ($this->paramBindings as $param => $binding) {
            $value = $binding[0];
            $type = $binding[1];

            if(is_null($type)) {
                switch(true) {
                    case is_int($value):
                        $type = PDO::PARAM_INT;
                        break;
                    case is_bool($value):
                        $type = PDO::PARAM_BOOL;
                        break;
                    case is_null($value):
                        $type = PDO::PARAM_NULL;
                        break;
                    default:
                        $type = PDO::PARAM_STR;
                }
            }

            $countStmt->bindValue($param, $value, $type);
        }

        // Execute the count query
        $countStmt->execute();

        // Fetch the result
        $result = $countStmt->fetch(PDO::FETCH_OBJ);

        // Return the total count
        return $result->total;
    }

    /**
     * Get the PDO instance for direct database operations
     *
     * @return PDO|null The PDO instance or null if not available
     */
    public function getPDO() {
        return $this->dbh;
    }
}
