<?php
/**
 * Security Enhancements Helper
 * Contains methods for enhanced security features
 */
class SecurityEnhancements {
    private static $db;

    // Maximum failed login attempts before account lockout
    const MAX_LOGIN_ATTEMPTS = 5;

    // Lockout duration in minutes
    const LOCKOUT_DURATION = 30;

    // Rate limit for password reset requests (max requests per hour)
    const PASSWORD_RESET_RATE_LIMIT = 3;

    // Rate limit for import operations (max imports per hour)
    const IMPORT_RATE_LIMIT = 5;

    // Maximum allowed file size for imports in bytes (10MB)
    const MAX_IMPORT_FILE_SIZE = ********;

    // Remember me token expiry in days
    const REMEMBER_ME_EXPIRY = 30;

    /**
     * Initialize the database connection
     */
    private static function initDb() {
        if (!isset(self::$db)) {
            self::$db = new Database();
        }
    }

    /**
     * Log a security event
     *
     * @param string $eventType The type of security event
     * @param string $description Description of the event
     * @param int|null $userId User ID (if applicable)
     * @return bool True if logged successfully, false otherwise
     */
    public static function logSecurityEvent($eventType, $description, $userId = null) {
        self::initDb();

        $ipAddress = self::getIpAddress();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

        self::$db->query('INSERT INTO security_logs (user_id, ip_address, user_agent, event_type, description)
                          VALUES (:user_id, :ip_address, :user_agent, :event_type, :description)');

        self::$db->bind(':user_id', $userId);
        self::$db->bind(':ip_address', $ipAddress);
        self::$db->bind(':user_agent', $userAgent);
        self::$db->bind(':event_type', $eventType);
        self::$db->bind(':description', $description);

        return self::$db->execute();
    }

    /**
     * Record a login attempt
     *
     * @param string $email Email address used in the attempt
     * @param bool $success Whether the login was successful
     * @param int|null $userId User ID (if successful)
     * @return bool True if recorded successfully, false otherwise
     */
    public static function recordLoginAttempt($email, $success, $userId = null) {
        self::initDb();

        $ipAddress = self::getIpAddress();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

        self::$db->query('INSERT INTO login_attempts (user_id, ip_address, user_agent, email, success)
                          VALUES (:user_id, :ip_address, :user_agent, :email, :success)');

        self::$db->bind(':user_id', $userId);
        self::$db->bind(':ip_address', $ipAddress);
        self::$db->bind(':user_agent', $userAgent);
        self::$db->bind(':email', $email);
        self::$db->bind(':success', $success);

        return self::$db->execute();
    }

    /**
     * Check if an account is locked
     *
     * @param int $userId User ID to check
     * @return bool True if account is locked, false otherwise
     */
    public static function isAccountLocked($userId) {
        self::initDb();

        self::$db->query('SELECT account_locked, locked_until FROM users WHERE id = :id');
        self::$db->bind(':id', $userId);

        $user = self::$db->single();

        if (!$user) {
            return false;
        }

        // If account is locked and the lockout period hasn't expired
        if ($user->account_locked && $user->locked_until > date('Y-m-d H:i:s')) {
            return true;
        }

        // If account is locked but the lockout period has expired, unlock it
        if ($user->account_locked && $user->locked_until <= date('Y-m-d H:i:s')) {
            self::unlockAccount($userId);
            return false;
        }

        return false;
    }

    /**
     * Increment failed login attempts and lock account if necessary
     *
     * @param int $userId User ID
     * @return bool True if account was locked, false otherwise
     */
    public static function incrementFailedLoginAttempts($userId) {
        self::initDb();

        // Get current failed attempts
        self::$db->query('SELECT failed_login_attempts FROM users WHERE id = :id');
        self::$db->bind(':id', $userId);

        $user = self::$db->single();

        if (!$user) {
            return false;
        }

        $attempts = $user->failed_login_attempts + 1;

        // Update failed attempts
        self::$db->query('UPDATE users SET failed_login_attempts = :attempts, last_login_attempt = NOW() WHERE id = :id');
        self::$db->bind(':attempts', $attempts);
        self::$db->bind(':id', $userId);
        self::$db->execute();

        // Lock account if max attempts reached
        if ($attempts >= self::MAX_LOGIN_ATTEMPTS) {
            return self::lockAccount($userId);
        }

        return false;
    }

    /**
     * Reset failed login attempts counter
     *
     * @param int $userId User ID
     * @return bool True if reset successfully, false otherwise
     */
    public static function resetFailedLoginAttempts($userId) {
        self::initDb();

        self::$db->query('UPDATE users SET failed_login_attempts = 0, last_login_attempt = NOW() WHERE id = :id');
        self::$db->bind(':id', $userId);

        return self::$db->execute();
    }

    /**
     * Lock a user account
     *
     * @param int $userId User ID to lock
     * @param string $reason Reason for lockout
     * @return bool True if locked successfully, false otherwise
     */
    public static function lockAccount($userId, $reason = 'Too many failed login attempts') {
        self::initDb();

        // Calculate lockout expiry time
        $lockedUntil = date('Y-m-d H:i:s', strtotime('+' . self::LOCKOUT_DURATION . ' minutes'));

        // Update user record
        self::$db->query('UPDATE users SET account_locked = 1, locked_until = :locked_until WHERE id = :id');
        self::$db->bind(':locked_until', $lockedUntil);
        self::$db->bind(':id', $userId);

        $result = self::$db->execute();

        if ($result) {
            // Record in account_lockouts table
            self::$db->query('INSERT INTO account_lockouts (user_id, locked_until, reason) VALUES (:user_id, :locked_until, :reason)');
            self::$db->bind(':user_id', $userId);
            self::$db->bind(':locked_until', $lockedUntil);
            self::$db->bind(':reason', $reason);
            self::$db->execute();

            // Log security event
            self::logSecurityEvent('account_lockout', $reason, $userId);
        }

        return $result;
    }

    /**
     * Unlock a user account
     *
     * @param int $userId User ID to unlock
     * @return bool True if unlocked successfully, false otherwise
     */
    public static function unlockAccount($userId) {
        self::initDb();

        self::$db->query('UPDATE users SET account_locked = 0, locked_until = NULL, failed_login_attempts = 0 WHERE id = :id');
        self::$db->bind(':id', $userId);

        $result = self::$db->execute();

        if ($result) {
            // Log security event
            self::logSecurityEvent('account_unlock', 'Account unlocked', $userId);
        }

        return $result;
    }

    /**
     * Check if password reset requests are within rate limits
     *
     * @param string $email Email address
     * @return bool True if within rate limits, false otherwise
     */
    public static function checkPasswordResetRateLimit($email) {
        self::initDb();

        $ipAddress = self::getIpAddress();

        // Count requests in the last hour
        self::$db->query('SELECT COUNT(*) as count FROM password_reset_rate_limits
                          WHERE (email = :email OR ip_address = :ip_address)
                          AND request_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)');

        self::$db->bind(':email', $email);
        self::$db->bind(':ip_address', $ipAddress);

        $result = self::$db->single();

        // If within rate limit, record this request
        if ($result->count < self::PASSWORD_RESET_RATE_LIMIT) {
            self::recordPasswordResetRequest($email);
            return true;
        }

        return false;
    }

    /**
     * Record a password reset request
     *
     * @param string $email Email address
     * @return bool True if recorded successfully, false otherwise
     */
    private static function recordPasswordResetRequest($email) {
        self::initDb();

        $ipAddress = self::getIpAddress();

        self::$db->query('INSERT INTO password_reset_rate_limits (email, ip_address) VALUES (:email, :ip_address)');
        self::$db->bind(':email', $email);
        self::$db->bind(':ip_address', $ipAddress);

        return self::$db->execute();
    }

    /**
     * Create a remember me token
     *
     * @param int $userId User ID
     * @return array|bool Token data if created successfully, false otherwise
     */
    public static function createRememberMeToken($userId) {
        self::initDb();

        // Generate random tokens
        $selector = bin2hex(random_bytes(16));
        $validator = bin2hex(random_bytes(32));

        // Hash the validator for storage
        $hashedValidator = password_hash($validator, PASSWORD_DEFAULT);

        // Set expiry date
        $expiry = date('Y-m-d H:i:s', strtotime('+' . self::REMEMBER_ME_EXPIRY . ' days'));

        // Store in database
        self::$db->query('INSERT INTO remember_me_tokens (user_id, selector, validator, expiry)
                          VALUES (:user_id, :selector, :validator, :expiry)');

        self::$db->bind(':user_id', $userId);
        self::$db->bind(':selector', $selector);
        self::$db->bind(':validator', $hashedValidator);
        self::$db->bind(':expiry', $expiry);

        if (self::$db->execute()) {
            // Return the token data for cookie creation
            return [
                'selector' => $selector,
                'validator' => $validator,
                'expiry' => $expiry
            ];
        }

        return false;
    }

    /**
     * Verify a remember me token
     *
     * @param string $selector Token selector
     * @param string $validator Token validator
     * @return int|bool User ID if token is valid, false otherwise
     */
    public static function verifyRememberMeToken($selector, $validator) {
        self::initDb();

        // Get token from database
        self::$db->query('SELECT * FROM remember_me_tokens WHERE selector = :selector AND expiry > NOW()');
        self::$db->bind(':selector', $selector);

        $token = self::$db->single();

        if (!$token) {
            return false;
        }

        // Verify the validator
        if (password_verify($validator, $token->validator)) {
            return $token->user_id;
        }

        return false;
    }

    /**
     * Delete a remember me token
     *
     * @param string $selector Token selector
     * @return bool True if deleted successfully, false otherwise
     */
    public static function deleteRememberMeToken($selector) {
        self::initDb();

        self::$db->query('DELETE FROM remember_me_tokens WHERE selector = :selector');
        self::$db->bind(':selector', $selector);

        return self::$db->execute();
    }

    /**
     * Delete all remember me tokens for a user
     *
     * @param int $userId User ID
     * @return bool True if deleted successfully, false otherwise
     */
    public static function deleteAllRememberMeTokens($userId) {
        self::initDb();

        self::$db->query('DELETE FROM remember_me_tokens WHERE user_id = :user_id');
        self::$db->bind(':user_id', $userId);

        return self::$db->execute();
    }

    /**
     * Get the client IP address
     *
     * @return string IP address
     */
    private static function getIpAddress() {
        // Check for proxy
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
        }

        return $ip;
    }

    /**
     * Check if import operations are within rate limits
     *
     * @param int $userId User ID
     * @return bool True if within rate limits, false otherwise
     */
    public static function checkImportRateLimit($userId) {
        self::initDb();

        $ipAddress = self::getIpAddress();

        // Count imports in the last hour
        self::$db->query('SELECT COUNT(*) as count FROM import_rate_limits
                          WHERE (user_id = :user_id OR ip_address = :ip_address)
                          AND import_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)');

        self::$db->bind(':user_id', $userId);
        self::$db->bind(':ip_address', $ipAddress);

        $result = self::$db->single();

        // If within rate limit
        return ($result->count < self::IMPORT_RATE_LIMIT);
    }

    /**
     * Record an import operation
     *
     * @param int $userId User ID
     * @param string $fileName Name of the imported file
     * @param int $fileSize Size of the imported file in bytes
     * @param bool $success Whether the import was successful
     * @return bool True if recorded successfully, false otherwise
     */
    public static function recordImportOperation($userId, $fileName, $fileSize, $success = true) {
        self::initDb();

        $ipAddress = self::getIpAddress();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

        self::$db->query('INSERT INTO import_rate_limits (user_id, ip_address, user_agent, file_name, file_size, success)
                          VALUES (:user_id, :ip_address, :user_agent, :file_name, :file_size, :success)');

        self::$db->bind(':user_id', $userId);
        self::$db->bind(':ip_address', $ipAddress);
        self::$db->bind(':user_agent', $userAgent);
        self::$db->bind(':file_name', $fileName);
        self::$db->bind(':file_size', $fileSize);
        self::$db->bind(':success', $success);

        return self::$db->execute();
    }

    /**
     * Validate file size
     *
     * @param int $fileSize Size of the file in bytes
     * @return bool True if file size is valid, false otherwise
     */
    public static function validateFileSize($fileSize) {
        return ($fileSize > 0 && $fileSize <= self::MAX_IMPORT_FILE_SIZE);
    }

    /**
     * Validate CSV file content
     *
     * @param string $filePath Path to the CSV file
     * @return bool|array True if valid, array of errors if invalid
     */
    public static function validateCsvContent($filePath) {
        $errors = [];

        // Debug log
        error_log("Validating CSV content for file: " . $filePath);

        // Check if file exists
        if (!file_exists($filePath)) {
            error_log("CSV validation error: File not found: " . $filePath);
            $errors[] = "File not found";
            return $errors;
        }

        // Open the file
        $handle = fopen($filePath, 'r');
        if (!$handle) {
            error_log("CSV validation error: Could not open file: " . $filePath);
            $errors[] = "Could not open file";
            return $errors;
        }

        // Check if file is empty
        $firstRow = fgetcsv($handle);
        if ($firstRow === false) {
            fclose($handle);
            error_log("CSV validation error: File is empty");
            $errors[] = "File is empty";
            return $errors;
        }

        // Log first row for debugging
        error_log("CSV validation: First row has " . count($firstRow) . " columns");
        error_log("CSV validation: First row content: " . implode(", ", array_map(function($cell) {
            return substr($cell, 0, 20) . (strlen($cell) > 20 ? "..." : "");
        }, $firstRow)));

        // Check for minimum number of columns (based on the expected format)
        if (count($firstRow) < 3) {
            error_log("CSV validation error: Insufficient columns: " . count($firstRow));
            $errors[] = "Invalid CSV format: insufficient columns";
        }

        // Check for potential CSV injection in the first few rows
        rewind($handle);
        $rowCount = 0;
        $maxRowsToCheck = 10; // Check first 10 rows

        while (($row = fgetcsv($handle)) !== false && $rowCount < $maxRowsToCheck) {
            foreach ($row as $cell) {
                // Check for formula injection (only check for = at the beginning, allow +/- for numbers)
                if (preg_match('/^[\=]/', $cell)) {
                    // Log the cell content for debugging
                    error_log("CSV validation: Potential formula injection detected in cell: " . substr($cell, 0, 50));
                    $errors[] = "Potential formula injection detected";
                    break 2; // Break out of both loops
                }

                // Check for script tags or suspicious HTML
                if (preg_match('/<script|<iframe|javascript:|data:|vbscript:/i', $cell)) {
                    // Log the cell content for debugging
                    error_log("CSV validation: Potential malicious HTML content detected in cell: " . substr($cell, 0, 50));
                    $errors[] = "Potential malicious content detected";
                    break 2; // Break out of both loops
                }
            }
            $rowCount++;
        }

        fclose($handle);

        // Log validation result
        if (empty($errors)) {
            error_log("CSV validation: File passed validation");
        } else {
            error_log("CSV validation: File failed validation with errors: " . implode(", ", $errors));
        }

        return empty($errors) ? true : $errors;
    }
}
