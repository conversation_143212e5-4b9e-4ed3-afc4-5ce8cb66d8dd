<?php
/**
 * Email Helper
 * Contains functions for sending emails using <PERSON><PERSON><PERSON>ailer
 */

// PHPMailer classes
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;
use P<PERSON><PERSON>ailer\PHPMailer\Exception;

/**
 * Send an email using <PERSON><PERSON><PERSON>ail<PERSON> with SMTP
 *
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $body Email body (HTML)
 * @param string $altBody Plain text alternative body
 * @return bool True if email sent successfully, false otherwise
 */
function sendEmail($to, $subject, $body, $altBody = '') {
    // Create a new PHPMailer instance
    $mail = new PHPMailer(true);

    try {
        // Set timeout settings to prevent hanging
        $mail->Timeout = 30; // SMTP timeout in seconds
        $mail->SMTPKeepAlive = false; // Don't keep connection alive

        // Server settings
        $mail->SMTPDebug = MAIL_DEBUG ? SMTP::DEBUG_SERVER : SMTP::DEBUG_OFF;
        $mail->isSMTP();
        $mail->Host       = SMTP_HOST_GMAIL;
        $mail->SMTPAuth   = SMTP_AUTH;
        $mail->Username   = SMTP_USERNAME;
        $mail->Password   = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_SECURE === 'tls' ? PHPMailer::ENCRYPTION_STARTTLS : PHPMailer::ENCRYPTION_SMTPS;
        $mail->Port       = SMTP_PORT_GMAIL;

        // Additional SMTP options for better compatibility
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // Recipients
        $mail->setFrom(MAIL_FROM_EMAIL, MAIL_FROM_NAME);
        $mail->addAddress($to);
        $mail->addReplyTo(MAIL_FROM_EMAIL, MAIL_FROM_NAME);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body    = $body;
        $mail->AltBody = $altBody ?: strip_tags($body);

        $mail->send();
        return true;
    } catch (Exception $e) {
        $errorMessage = "Email could not be sent. Mailer Error: {$mail->ErrorInfo}";
        error_log($errorMessage);

        if (MAIL_DEBUG) {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px;'>";
            echo "<strong>Email Error:</strong> {$mail->ErrorInfo}<br>";
            echo "<strong>Exception:</strong> {$e->getMessage()}<br>";
            echo "<strong>Exception Code:</strong> {$e->getCode()}<br>";
            echo "<strong>SMTP Host:</strong> " . SMTP_HOST . "<br>";
            echo "<strong>SMTP Port:</strong> " . SMTP_PORT . "<br>";
            echo "<strong>SMTP Security:</strong> " . SMTP_SECURE . "<br>";
            echo "<strong>SMTP Username:</strong> " . SMTP_USERNAME . "<br>";
            echo "<strong>SMTP Auth:</strong> " . (SMTP_AUTH ? 'Yes' : 'No') . "<br>";
            echo "<strong>PHP Version:</strong> " . phpversion() . "<br>";
            echo "<strong>OpenSSL:</strong> " . (extension_loaded('openssl') ? 'Enabled' : 'Disabled') . "<br>";
            echo "</div>";
        }
        return false;
    }
}

/**
 * Fallback email function using PHP's built-in mail()
 *
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $body Email body (HTML)
 * @param string $altBody Plain text alternative body
 * @return bool True if email sent successfully, false otherwise
 */
function sendEmailFallback($to, $subject, $body, $altBody = '') {
    $headers = array();
    $headers[] = 'MIME-Version: 1.0';
    $headers[] = 'Content-type: text/html; charset=UTF-8';
    $headers[] = 'From: ' . MAIL_FROM_NAME . ' <' . MAIL_FROM_EMAIL . '>';
    $headers[] = 'Reply-To: ' . MAIL_FROM_EMAIL;
    $headers[] = 'X-Mailer: PHP/' . phpversion();

    $headerString = implode("\r\n", $headers);

    return mail($to, $subject, $body, $headerString);
}

/**
 * Smart email sending function that tries SMTP first, then fallback
 *
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $body Email body (HTML)
 * @param string $altBody Plain text alternative body
 * @return bool True if email sent successfully, false otherwise
 */
function sendEmailSmart($to, $subject, $body, $altBody = '') {
    // Try SMTP first
    if (sendEmail($to, $subject, $body, $altBody)) {
        return true;
    }

    // If SMTP fails, try fallback method
    error_log("SMTP failed, trying fallback mail() function");
    return sendEmailFallback($to, $subject, $body, $altBody);
}

/**
 * Send a password reset email
 *
 * @param string $to Recipient email address
 * @param string $name Recipient name
 * @param string $token Password reset token
 * @return bool True if email sent successfully, false otherwise
 */
function sendPasswordResetEmail($to, $name, $token) {
    $resetUrl = URLROOT . '/users/resetPasswordWithToken?token=' . $token . '&email=' . urlencode($to);

    $subject = SITENAME . ' - Password Reset Request';

    $body = '
    <html>
    <head>
        <title>Password Reset</title>
    </head>
    <body>
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
            <h2 style="color: #3b82f6;">Password Reset Request</h2>
            <p>Hello ' . htmlspecialchars($name) . ',</p>
            <p>We received a request to reset your password for your account at ' . SITENAME . '.</p>
            <p>Please click the button below to reset your password:</p>
            <p style="text-align: center;">
                <a href="' . $resetUrl . '" style="display: inline-block; padding: 10px 20px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 5px;">Reset Password</a>
            </p>
            <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
            <p>This link will expire in 1 hour for security reasons.</p>
            <p>Regards,<br>The ' . SITENAME . ' Team</p>
        </div>
    </body>
    </html>';

    $altBody = "Hello " . $name . ",\n\n" .
               "We received a request to reset your password for your account at " . SITENAME . ".\n\n" .
               "Please visit the following link to reset your password:\n" .
               $resetUrl . "\n\n" .
               "If you did not request a password reset, please ignore this email or contact support if you have concerns.\n\n" .
               "This link will expire in 1 hour for security reasons.\n\n" .
               "Regards,\nThe " . SITENAME . " Team";

    return sendEmail($to, $subject, $body, $altBody);
}
