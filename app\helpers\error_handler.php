<?php
/**
 * Custom error handler for the application
 * Logs errors to the database and displays user-friendly error messages
 */

// Set error reporting level
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', APPROOT . '/logs/php_errors.log');

/**
 * Custom error handler function
 *
 * @param int $errno Error number
 * @param string $errstr Error message
 * @param string $errfile File where the error occurred
 * @param int $errline Line number where the error occurred
 * @return bool
 */
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    // Don't log suppressed errors
    if (error_reporting() === 0) {
        return false;
    }

    // Map PHP error constants to our error levels
    $errorLevels = [
        E_ERROR => 'error',
        E_WARNING => 'warning',
        E_PARSE => 'error',
        E_NOTICE => 'info',
        E_CORE_ERROR => 'error',
        E_CORE_WARNING => 'warning',
        E_COMPILE_ERROR => 'error',
        E_COMPILE_WARNING => 'warning',
        E_USER_ERROR => 'error',
        E_USER_WARNING => 'warning',
        E_USER_NOTICE => 'info',
        E_STRICT => 'info',
        E_RECOVERABLE_ERROR => 'error',
        E_DEPRECATED => 'info',
        E_USER_DEPRECATED => 'info'
    ];

    $level = isset($errorLevels[$errno]) ? $errorLevels[$errno] : 'error';

    // Get stack trace
    $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
    array_shift($trace); // Remove the call to this function from the stack trace
    $traceString = '';
    foreach ($trace as $i => $t) {
        $traceString .= "#$i " . (isset($t['file']) ? $t['file'] : '<unknown file>');
        $traceString .= "(" . (isset($t['line']) ? $t['line'] : '<unknown line>') . "): ";
        $traceString .= (isset($t['class']) ? $t['class'] . $t['type'] : '');
        $traceString .= $t['function'] . "()\n";
    }

    // Log to PHP error log
    error_log("[$level] $errstr in $errfile on line $errline");

    // Log to database if possible
    try {
        // Create ErrorLog model instance
        $errorLogModel = new ErrorLog();
        
        // Log error to database
        $errorLogModel->logError(
            $level,
            $errstr,
            [
                'error_number' => $errno,
                'error_type' => get_error_type($errno)
            ],
            $errfile,
            $errline,
            $traceString
        );
    } catch (Exception $e) {
        // If we can't log to the database, just log to the PHP error log
        error_log("Failed to log error to database: " . $e->getMessage());
    }

    // Don't execute PHP's internal error handler
    return true;
}

/**
 * Custom exception handler function
 *
 * @param Exception $exception The exception object
 * @return void
 */
function customExceptionHandler($exception) {
    $errstr = $exception->getMessage();
    $errfile = $exception->getFile();
    $errline = $exception->getLine();
    $trace = $exception->getTraceAsString();

    // Log to PHP error log
    error_log("Uncaught Exception: $errstr in $errfile on line $errline");

    // Log to database if possible
    try {
        // Create ErrorLog model instance
        $errorLogModel = new ErrorLog();
        
        // Log error to database
        $errorLogModel->logError(
            'error',
            "Uncaught Exception: $errstr",
            [
                'exception_class' => get_class($exception)
            ],
            $errfile,
            $errline,
            $trace
        );
    } catch (Exception $e) {
        // If we can't log to the database, just log to the PHP error log
        error_log("Failed to log exception to database: " . $e->getMessage());
    }

    // Display user-friendly error page
    if (ENVIRONMENT === 'development') {
        // In development, show detailed error information
        echo "<h1>Application Error</h1>";
        echo "<p>An error occurred in the application.</p>";
        echo "<h2>Error Details</h2>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($errstr) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($errfile) . "</p>";
        echo "<p><strong>Line:</strong> " . $errline . "</p>";
        echo "<h2>Stack Trace</h2>";
        echo "<pre>" . htmlspecialchars($trace) . "</pre>";
    } else {
        // In production, show a generic error message
        include APPROOT . '/views/pages/error.php';
    }

    exit(1);
}

/**
 * Get error type name from error number
 *
 * @param int $errno Error number
 * @return string Error type name
 */
function get_error_type($errno) {
    $errorTypes = [
        E_ERROR => 'E_ERROR',
        E_WARNING => 'E_WARNING',
        E_PARSE => 'E_PARSE',
        E_NOTICE => 'E_NOTICE',
        E_CORE_ERROR => 'E_CORE_ERROR',
        E_CORE_WARNING => 'E_CORE_WARNING',
        E_COMPILE_ERROR => 'E_COMPILE_ERROR',
        E_COMPILE_WARNING => 'E_COMPILE_WARNING',
        E_USER_ERROR => 'E_USER_ERROR',
        E_USER_WARNING => 'E_USER_WARNING',
        E_USER_NOTICE => 'E_USER_NOTICE',
        E_STRICT => 'E_STRICT',
        E_RECOVERABLE_ERROR => 'E_RECOVERABLE_ERROR',
        E_DEPRECATED => 'E_DEPRECATED',
        E_USER_DEPRECATED => 'E_USER_DEPRECATED',
        E_ALL => 'E_ALL'
    ];

    return isset($errorTypes[$errno]) ? $errorTypes[$errno] : 'UNKNOWN';
}

// Set custom error and exception handlers
set_error_handler('customErrorHandler');
set_exception_handler('customExceptionHandler');

// Register shutdown function to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error !== null && ($error['type'] === E_ERROR || $error['type'] === E_PARSE || $error['type'] === E_COMPILE_ERROR)) {
        // Call the custom error handler
        customErrorHandler($error['type'], $error['message'], $error['file'], $error['line']);
        
        // Display user-friendly error page
        if (ENVIRONMENT === 'development') {
            // In development, show detailed error information
            echo "<h1>Fatal Error</h1>";
            echo "<p>A fatal error occurred in the application.</p>";
            echo "<h2>Error Details</h2>";
            echo "<p><strong>Message:</strong> " . htmlspecialchars($error['message']) . "</p>";
            echo "<p><strong>File:</strong> " . htmlspecialchars($error['file']) . "</p>";
            echo "<p><strong>Line:</strong> " . $error['line'] . "</p>";
        } else {
            // In production, show a generic error message
            include APPROOT . '/views/pages/error.php';
        }
    }
});
