<?php
/**
 * Output Helper
 * Contains functions for escaping output to prevent XSS attacks
 */

/**
 * Escape HTML output to prevent XSS attacks
 * 
 * @param string $output The string to escape
 * @return string The escaped string
 */
function e($output) {
    return Security::escapeOutput($output);
}

/**
 * Safely output HTML content
 * 
 * @param string $output The string to output
 * @return void
 */
function safe_echo($output) {
    echo e($output);
}

/**
 * Safely output HTML content with line breaks
 * 
 * @param string $output The string to output
 * @return void
 */
function safe_echo_nl2br($output) {
    echo nl2br(e($output));
}

/**
 * Safely output HTML content with no escaping (for trusted HTML)
 * Use with extreme caution - only for content that is guaranteed to be safe
 * 
 * @param string $output The string to output
 * @return void
 */
function safe_echo_raw($output) {
    echo $output;
}
