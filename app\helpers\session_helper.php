<?php
/**
 * Session Helper Functions
 *
 * Provides session management, authentication, and permission checking
 * functionality for the Asset Visibility application.
 *
 * @package AssetVisibility
 * <AUTHOR> Visibility Development Team
 * @version 1.0.0
 * @since 1.0.0
 */

session_start();

/**
 * Flash message helper
 *
 * Manages flash messages using SweetAlert2 for user notifications.
 * Supports setting and displaying messages with different alert types.
 *
 * @param string $name The name/key for the flash message
 * @param string $message The message content (empty to display existing message)
 * @param string $class CSS class for styling (determines alert type)
 * @return void
 * @since 1.0.0
 */
function flash($name = '', $message = '', $class = 'bg-green-100 text-green-800') {
    // Debug log to track flash message calls
    error_log("Flash called with name: '$name', message: '$message', class: '$class'");

    if(!empty($name)) {
        // Setting a flash message
        if(!empty($message) && empty($_SESSION[$name])) {
            // Clear any existing flash message with this name
            if(!empty($_SESSION[$name])) {
                unset($_SESSION[$name]);
            }

            if(!empty($_SESSION[$name . '_class'])) {
                unset($_SESSION[$name . '_class']);
            }

            // Set the flash message
            $_SESSION[$name] = $message;
            $_SESSION[$name . '_class'] = $class;
            error_log("Flash message set: '$name' = '$message'");
        }
        // Displaying a flash message
        elseif(empty($message) && !empty($_SESSION[$name])) {
            $class = !empty($_SESSION[$name . '_class']) ? $_SESSION[$name . '_class'] : '';
            $message = $_SESSION[$name];

            error_log("Displaying flash message: '$name' = '$message'");

            // Map alert classes to SweetAlert2 types
            $icon = 'success';
            $position = 'top-end';
            $timer = 3000;

            // Determine icon based on class
            if (strpos($class, 'alert-danger') !== false || strpos($class, 'bg-red') !== false) {
                $icon = 'error';
            } elseif (strpos($class, 'alert-warning') !== false || strpos($class, 'bg-yellow') !== false) {
                $icon = 'warning';
            } elseif (strpos($class, 'alert-info') !== false || strpos($class, 'bg-blue') !== false) {
                $icon = 'info';
            }

            // For more important messages (errors and warnings), use center position and longer timer
            if ($icon === 'error' || $icon === 'warning') {
                $position = 'center';
                $timer = 5000;
            }

            // Output SweetAlert2 script
            echo '<script>
                document.addEventListener("DOMContentLoaded", function() {
                    if (typeof Swal !== "undefined") {
                        Swal.fire({
                            title: "",
                            text: "' . addslashes($message) . '",
                            icon: "' . $icon . '",
                            toast: ' . ($position === 'top-end' ? 'true' : 'false') . ',
                            position: "' . $position . '",
                            showConfirmButton: ' . ($position === 'center' ? 'true' : 'false') . ',
                            timer: ' . $timer . ',
                            timerProgressBar: true
                        });
                    } else {
                        console.log("SweetAlert2 not loaded. Message: ' . addslashes($message) . '");
                        alert("' . addslashes($message) . '");
                    }
                });
            </script>';

            // Clear the flash message after displaying it
            unset($_SESSION[$name]);
            unset($_SESSION[$name . '_class']);
            error_log("Flash message cleared: '$name'");
        }
        // Special case: Check for form errors that might be persisting
        elseif($name === 'maintenance_message' && empty($message) && empty($_SESSION[$name])) {
            // Check for any form error session variables that might be persisting
            foreach($_SESSION as $key => $value) {
                if(strpos($key, '_err') !== false) {
                    error_log("Found persisting error session variable: $key = $value");
                    // Clear any form error session variables
                    unset($_SESSION[$key]);
                    error_log("Cleared persisting error session variable: $key");
                }
            }
        }
    }
}

/**
 * Check if user is logged in
 *
 * Checks both session data and remember me cookies to determine
 * if a user is authenticated.
 *
 * @return bool True if user is logged in, false otherwise
 * @since 1.0.0
 */
function isLoggedIn() {
    // Check if user is logged in via session
    if(isset($_SESSION['user_id'])) {
        return true;
    }

    // Check if user has a remember me cookie
    if(isset($_COOKIE['remember_me'])) {
        $cookieParts = explode(':', $_COOKIE['remember_me']);

        // Verify cookie format
        if(count($cookieParts) == 2) {
            $selector = $cookieParts[0];
            $validator = $cookieParts[1];

            // Load User model
            $userModel = new User();

            // Verify the token
            $userId = $userModel->verifyRememberMeToken($selector, $validator);

            if($userId) {
                // Get user data
                $user = $userModel->getUserById($userId);

                if($user) {
                    // Create session
                    $_SESSION['user_id'] = $user->id;
                    $_SESSION['user_email'] = $user->email;
                    $_SESSION['user_name'] = $user->name;
                    $_SESSION['user_role'] = $user->role;

                    // Log the auto-login event
                    SecurityEnhancements::logSecurityEvent('login', 'User auto-logged in via remember me cookie', $user->id);

                    return true;
                }
            } else {
                // Invalid token, delete the cookie
                $params = [
                    'expires' => time() - 3600, // Set expiry in the past
                    'path' => '/',
                    'domain' => '',
                    'secure' => COOKIE_SECURE,
                    'httponly' => COOKIE_HTTP_ONLY,
                    'samesite' => COOKIE_SAME_SITE
                ];

                setcookie('remember_me', '', $params);
            }
        }
    }

    return false;
}

/**
 * Check if user is an administrator
 *
 * Checks both the new role-based system and legacy session role
 * for backward compatibility.
 *
 * @return bool True if user is an administrator, false otherwise
 * @since 1.0.0
 */
function isAdmin() {
    // First check if the user has the Administrator role in the new system
    if(hasRole('Administrator')) {
        return true;
    }

    // Legacy method - check the session role for backward compatibility
    if(isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') {
        return true;
    }

    return false;
}

/**
 * Check if the current user has a specific permission
 *
 * @param string $permission Permission name to check
 * @return bool True if the user has the permission, false otherwise
 */
function hasPermission($permission) {
    // If not logged in, no permissions
    if(!isLoggedIn()) {
        return false;
    }

    // Check if we need to refresh the permission cache
    if(!isset($_SESSION['permissions_last_checked'])) {
        $_SESSION['permissions_last_checked'] = time();
        clearPermissionCache(); // Force refresh on first check
    } else {
        // Check if any of the user's roles have been updated since we last checked
        $userModel = new User();
        $roles = $userModel->getUserRoles($_SESSION['user_id']);

        foreach($roles as $role) {
            $roleUpdatedTime = strtotime($role->updated_at);
            if($roleUpdatedTime > $_SESSION['permissions_last_checked']) {
                // Role was updated, clear the cache
                clearPermissionCache();
                $_SESSION['permissions_last_checked'] = time();
                break;
            }
        }
    }

    // Always check from database first to ensure accuracy
    $userModel = new User();
    $hasPermissionFromDB = $userModel->hasPermission($_SESSION['user_id'], $permission);

    // Update cache with current permission status
    if(!isset($_SESSION['user_permissions'])) {
        $_SESSION['user_permissions'] = [];
    }

    // Cache the result (both true and false)
    $_SESSION['user_permissions'][$permission] = $hasPermissionFromDB;

    return $hasPermissionFromDB;
}

/**
 * Check if the current user has a specific role
 *
 * @param string $role Role name to check
 * @return bool True if the user has the role, false otherwise
 */
function hasRole($role) {
    // If not logged in, no roles
    if(!isLoggedIn()) {
        return false;
    }

    // Check if we need to refresh the role cache (same logic as in hasPermission)
    if(!isset($_SESSION['permissions_last_checked'])) {
        $_SESSION['permissions_last_checked'] = time();
        clearPermissionCache(); // Force refresh on first check
    }

    // Always check from database first to ensure accuracy
    $userModel = new User();
    $hasRoleFromDB = $userModel->hasRole($_SESSION['user_id'], $role);

    // Update cache with current role status
    if(!isset($_SESSION['user_roles'])) {
        $_SESSION['user_roles'] = [];
    }

    // Cache the result (both true and false)
    $_SESSION['user_roles'][$role] = $hasRoleFromDB;

    return $hasRoleFromDB;
}

/**
 * Clear the permission and role cache
 *
 * Clears the current user's cached permissions and roles.
 * Call this when user permissions or roles are updated.
 *
 * @return void
 * @since 1.0.0
 */
function clearPermissionCache() {
    if(isset($_SESSION['user_permissions'])) {
        unset($_SESSION['user_permissions']);
    }

    if(isset($_SESSION['user_roles'])) {
        unset($_SESSION['user_roles']);
    }

    // Update the last checked timestamp
    $_SESSION['permissions_last_checked'] = time();
}

/**
 * Clear permission cache for all users by updating a global timestamp
 *
 * Forces all users to refresh their permissions on next check by
 * updating a global cache timestamp file.
 *
 * @return void
 * @since 1.0.0
 */
function clearGlobalPermissionCache() {
    // Store a global timestamp that all users will check against
    $cacheFile = APPROOT . '/cache/permissions_updated.txt';

    // Create cache directory if it doesn't exist
    $cacheDir = dirname($cacheFile);
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }

    // Write current timestamp to file
    file_put_contents($cacheFile, time());

    // Also clear current user's cache
    clearPermissionCache();
}

/**
 * Redirect to a specific page
 *
 * Performs HTTP redirect to the specified page with proper URL construction.
 * Handles both relative and absolute URLs correctly.
 *
 * @param string $page The page/URL to redirect to
 * @return void This function exits the script after redirect
 * @since 1.0.0
 */
function redirect($page) {
    // Debug log with backtrace to see where the redirect is being called from
    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
    $caller = isset($backtrace[1]) ? $backtrace[1]['file'] . ':' . $backtrace[1]['line'] : 'unknown';
    error_log("Redirect called with page: " . $page . " from " . $caller);

    // Remove any leading slashes from $page to ensure consistent URL construction
    $page = ltrim($page, '/');

    // Check if $page already contains the URLROOT to prevent double URL issue
    if (strpos($page, URLROOT) === 0) {
        // $page already contains URLROOT, use it directly
        $redirectUrl = $page;
        error_log("Using direct URL: " . $redirectUrl);
    } else {
        // Normal case - prepend URLROOT
        $redirectUrl = URLROOT . '/' . $page;
        error_log("Using constructed URL: " . $redirectUrl);
    }

    // Set the Location header
    header('Location: ' . $redirectUrl);

    // Exit to ensure the script stops after redirection
    exit();
}
