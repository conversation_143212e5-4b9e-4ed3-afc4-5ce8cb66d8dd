<?php
/**
 * Asset Model
 *
 * Handles all asset-related database operations including CRUD operations,
 * search functionality, import/export, and asset history tracking.
 *
 * @package AssetVisibility
 * <AUTHOR> Visibility Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
class Asset {
    /**
     * Database connection instance
     * @var Database
     */
    private $db;

    /**
     * Constructor
     *
     * Initializes the Asset model with database connection.
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->db = new Database;
    }

    /**
     * Get all assets with pagination and sorting
     *
     * Retrieves assets from the database with support for pagination,
     * sorting, and field validation to prevent SQL injection.
     *
     * @param int $page Current page number (default: 1)
     * @param int $perPage Number of assets per page (default: 10)
     * @param string $sortField Field to sort by (default: 'id')
     * @param string $sortOrder Sort order ASC or DESC (default: 'DESC')
     * @return array Array containing assets, pagination info, and sort parameters
     * @since 1.0.0
     */
    public function getAssets($page = 1, $perPage = 10, $sortField = 'id', $sortOrder = 'DESC') {
        // First, get the total count
        $totalCount = $this->getTotalAssetsCount();

        // Validate sort field to prevent SQL injection
        $allowedSortFields = [
            'id', 'computer_host_name', 'equipment_type', 'employee_name',
            'serial_number', 'acquisition_date', 'inventory_date', 'site_name'
        ];

        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'id';
        }

        // Validate sort order
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';

        // Then, get the actual assets with pagination and sorting
        $this->db->query("SELECT * FROM assets ORDER BY $sortField $sortOrder");

        // Get paginated results
        $assets = $this->db->paginatedResultSet($page, $perPage);

        return [
            'assets' => $assets,
            'total' => $totalCount,
            'page' => $page,
            'perPage' => $perPage,
            'lastPage' => ceil($totalCount / $perPage),
            'sortField' => $sortField,
            'sortOrder' => $sortOrder
        ];
    }

    /**
     * Get asset by ID
     *
     * Retrieves a single asset record by its unique identifier.
     *
     * @param int $id The asset ID
     * @return object|false Asset object if found, false otherwise
     * @since 1.0.0
     */
    public function getAssetById($id) {
        $this->db->query('SELECT * FROM assets WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    /**
     * Add a new asset
     *
     * Creates a new asset record with all provided data and handles
     * tag associations. Returns the new asset ID on success.
     *
     * @param array $data Associative array of asset data
     * @return int|false Asset ID if successful, false otherwise
     * @since 1.0.0
     */
    public function addAsset($data) {
        $this->db->query('INSERT INTO assets (inventory_date, site_name, employee_name, active_directory_name, position, program_section, computer_host_name, equipment_type, acquisition_type, operating_system, administration_type, xdr_installed, device_custodian, remarks, par_number, serial_number, acquisition_date, estimated_useful_life)
                          VALUES (:inventory_date, :site_name, :employee_name, :active_directory_name, :position, :program_section, :computer_host_name, :equipment_type, :acquisition_type, :operating_system, :administration_type, :xdr_installed, :device_custodian, :remarks, :par_number, :serial_number, :acquisition_date, :estimated_useful_life)');

        // Bind values
        $this->db->bind(':inventory_date', $data['inventory_date']);
        $this->db->bind(':site_name', $data['site_name']);
        $this->db->bind(':employee_name', $data['employee_name']);
        $this->db->bind(':active_directory_name', $data['active_directory_name']);
        $this->db->bind(':position', $data['position']);
        $this->db->bind(':program_section', $data['program_section']);
        $this->db->bind(':computer_host_name', $data['computer_host_name']);
        $this->db->bind(':equipment_type', $data['equipment_type']);
        $this->db->bind(':acquisition_type', $data['acquisition_type']);
        $this->db->bind(':operating_system', $data['operating_system']);
        $this->db->bind(':administration_type', $data['administration_type']);
        $this->db->bind(':xdr_installed', $data['xdr_installed']);
        $this->db->bind(':device_custodian', $data['device_custodian']);
        $this->db->bind(':remarks', $data['remarks']);
        $this->db->bind(':par_number', $data['par_number']);
        $this->db->bind(':serial_number', $data['serial_number']);
        $this->db->bind(':acquisition_date', $data['acquisition_date']);
        $this->db->bind(':estimated_useful_life', $data['estimated_useful_life']);

        // Execute
        if($this->db->execute()) {
            $assetId = $this->db->lastInsertId();

            // Add tags if provided
            if(!empty($data['tags'])) {
                require_once __DIR__ . '/Tag.php';
                $tagModel = new Tag();

                foreach($data['tags'] as $tagId) {
                    $tagModel->addTagToAsset($assetId, $tagId);
                }
            }

            return $assetId;
        } else {
            return false;
        }
    }

    /**
     * Update an existing asset
     *
     * Updates asset data and tracks changes in asset history.
     * Handles tag updates and maintains audit trail.
     *
     * @param array $data Associative array of asset data including id
     * @return bool True if update successful, false otherwise
     * @since 1.0.0
     */
    public function updateAsset($data) {
        // First get the current asset data to compare changes
        $currentAsset = $this->getAssetById($data['id']);

        $this->db->query('UPDATE assets SET
                          inventory_date = :inventory_date,
                          site_name = :site_name,
                          employee_name = :employee_name,
                          active_directory_name = :active_directory_name,
                          position = :position,
                          program_section = :program_section,
                          computer_host_name = :computer_host_name,
                          equipment_type = :equipment_type,
                          acquisition_type = :acquisition_type,
                          operating_system = :operating_system,
                          administration_type = :administration_type,
                          xdr_installed = :xdr_installed,
                          device_custodian = :device_custodian,
                          remarks = :remarks,
                          par_number = :par_number,
                          serial_number = :serial_number,
                          acquisition_date = :acquisition_date,
                          estimated_useful_life = :estimated_useful_life
                          WHERE id = :id');

        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':inventory_date', $data['inventory_date']);
        $this->db->bind(':site_name', $data['site_name']);
        $this->db->bind(':employee_name', $data['employee_name']);
        $this->db->bind(':active_directory_name', $data['active_directory_name']);
        $this->db->bind(':position', $data['position']);
        $this->db->bind(':program_section', $data['program_section']);
        $this->db->bind(':computer_host_name', $data['computer_host_name']);
        $this->db->bind(':equipment_type', $data['equipment_type']);
        $this->db->bind(':acquisition_type', $data['acquisition_type']);
        $this->db->bind(':operating_system', $data['operating_system']);
        $this->db->bind(':administration_type', $data['administration_type']);
        $this->db->bind(':xdr_installed', $data['xdr_installed']);
        $this->db->bind(':device_custodian', $data['device_custodian']);
        $this->db->bind(':remarks', $data['remarks']);
        $this->db->bind(':par_number', $data['par_number']);
        $this->db->bind(':serial_number', $data['serial_number']);
        $this->db->bind(':acquisition_date', $data['acquisition_date']);
        $this->db->bind(':estimated_useful_life', $data['estimated_useful_life']);

        // Execute the update
        if($this->db->execute()) {
            // Track changes by comparing old and new values
            $this->trackChanges($currentAsset, $data);

            // Update tags if provided
            if(isset($data['tags'])) {
                require_once __DIR__ . '/Tag.php';
                $tagModel = new Tag();

                // Remove all existing tags
                $tagModel->removeAllTagsFromAsset($data['id']);

                // Add new tags
                if(!empty($data['tags'])) {
                    foreach($data['tags'] as $tagId) {
                        $tagModel->addTagToAsset($data['id'], $tagId);
                    }
                }

                // Add a history record for tag changes
                $assetHistory = new AssetHistory();
                $assetHistory->addRecord(
                    $data['id'],
                    'Tags',
                    'Tags updated',
                    'Tags updated',
                    isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null
                );
            }

            return true;
        } else {
            return false;
        }
    }

    /**
     * Track changes between old and new asset data
     *
     * Compares old and new asset data and creates history records for any changes.
     * This method is called internally during asset updates.
     *
     * @param object $oldData The original asset data
     * @param array $newData The updated asset data
     * @return void
     * @since 1.0.0
     */
    private function trackChanges($oldData, $newData) {
        // Initialize AssetHistory model
        require_once __DIR__ . '/AssetHistory.php';
        $assetHistory = new AssetHistory();

        // Get current user ID from session
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

        // Fields to track changes for
        $fieldsToTrack = [
            'inventory_date' => 'Inventory Date',
            'site_name' => 'Site Name',
            'employee_name' => 'Employee Name',
            'active_directory_name' => 'Active Directory Name',
            'position' => 'Position',
            'program_section' => 'Program Section',
            'computer_host_name' => 'Computer Host Name',
            'equipment_type' => 'Equipment Type',
            'acquisition_type' => 'Acquisition Type',
            'operating_system' => 'Operating System',
            'administration_type' => 'Administration Type',
            'xdr_installed' => 'XDR Installed',
            'device_custodian' => 'Device Custodian',
            'remarks' => 'Remarks',
            'par_number' => 'PAR Number',
            'serial_number' => 'Serial Number',
            'acquisition_date' => 'Acquisition Date',
            'estimated_useful_life' => 'Estimated Useful Life'
        ];

        // Compare each field and record changes
        foreach ($fieldsToTrack as $field => $displayName) {
            $oldValue = $oldData->$field;
            $newValue = $newData[$field];

            // Only record if there's a change
            if ($oldValue != $newValue) {
                $assetHistory->addRecord(
                    $newData['id'],
                    $displayName,
                    $oldValue,
                    $newValue,
                    $userId
                );
            }
        }
    }

    /**
     * Delete asset
     *
     * Removes an asset from the database by its ID.
     *
     * @param int $id The asset ID to delete
     * @return bool True if deletion successful, false otherwise
     * @since 1.0.0
     */
    public function deleteAsset($id) {
        $this->db->query('DELETE FROM assets WHERE id = :id');
        // Bind values
        $this->db->bind(':id', $id);

        // Execute
        if($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Basic search assets with pagination and sorting
     *
     * Performs a basic search across multiple asset fields including tags.
     * Supports pagination and sorting with SQL injection protection.
     *
     * @param string $term Search term to match against asset fields
     * @param int $page Current page number (default: 1)
     * @param int $perPage Number of assets per page (default: 10)
     * @param string $sortField Field to sort by (default: 'id')
     * @param string $sortOrder Sort order ASC or DESC (default: 'DESC')
     * @return array Array containing search results, pagination info, and sort parameters
     * @since 1.0.0
     */
    public function searchAssets($term, $page = 1, $perPage = 10, $sortField = 'id', $sortOrder = 'DESC') {
        // First, get the total count
        $countQuery = 'SELECT COUNT(DISTINCT a.id) as total FROM assets a
                       LEFT JOIN asset_tags at ON a.id = at.asset_id
                       LEFT JOIN tags t ON at.tag_id = t.id
                       WHERE a.site_name LIKE :term
                       OR a.employee_name LIKE :term
                       OR a.computer_host_name LIKE :term
                       OR a.equipment_type LIKE :term
                       OR a.serial_number LIKE :term
                       OR a.par_number LIKE :term
                       OR t.name LIKE :term';

        $this->db->query($countQuery);
        $this->db->bind(':term', '%' . $term . '%');
        $result = $this->db->single();
        $totalCount = $result->total;

        // Validate sort field to prevent SQL injection
        $allowedSortFields = [
            'id', 'computer_host_name', 'equipment_type', 'employee_name',
            'serial_number', 'acquisition_date', 'inventory_date', 'site_name'
        ];

        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'id';
        }

        // Validate sort order
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';

        // Then, get the actual assets with pagination and sorting
        $this->db->query('SELECT DISTINCT a.* FROM assets a
                          LEFT JOIN asset_tags at ON a.id = at.asset_id
                          LEFT JOIN tags t ON at.tag_id = t.id
                          WHERE a.site_name LIKE :term
                          OR a.employee_name LIKE :term
                          OR a.computer_host_name LIKE :term
                          OR a.equipment_type LIKE :term
                          OR a.serial_number LIKE :term
                          OR a.par_number LIKE :term
                          OR t.name LIKE :term
                          ORDER BY a.' . $sortField . ' ' . $sortOrder);

        $this->db->bind(':term', '%' . $term . '%');

        // Get paginated results
        $assets = $this->db->paginatedResultSet($page, $perPage);

        return [
            'assets' => $assets,
            'total' => $totalCount,
            'page' => $page,
            'perPage' => $perPage,
            'lastPage' => ceil($totalCount / $perPage),
            'sortField' => $sortField,
            'sortOrder' => $sortOrder
        ];
    }

    /**
     * Advanced search assets with multiple criteria and pagination
     *
     * Performs advanced search with multiple filter criteria including date ranges,
     * specific field matches, and tag filtering. Dynamically builds SQL queries.
     *
     * @param array $params Search parameters including filters and search terms
     * @param int $page Current page number (default: 1)
     * @param int $perPage Number of assets per page (default: 10)
     * @return array Array containing search results and pagination info
     * @since 1.0.0
     */
    public function advancedSearchAssets($params, $page = 1, $perPage = 10) {
        // Start building the query
        $sql = 'SELECT DISTINCT a.* FROM assets a';
        $countSql = 'SELECT COUNT(DISTINCT a.id) as total FROM assets a';

        $bindings = [];

        // Add join for tag filtering if needed
        if (!empty($params['tag_id'])) {
            $joinClause = ' INNER JOIN asset_tags at ON a.id = at.asset_id
                           INNER JOIN tags t ON at.tag_id = t.id AND t.id = :tag_id';
            $sql .= $joinClause;
            $countSql .= $joinClause;
            $bindings[':tag_id'] = $params['tag_id'];
        } else {
            // Always join with tags for searching by tag name
            $joinClause = ' LEFT JOIN asset_tags at ON a.id = at.asset_id
                           LEFT JOIN tags t ON at.tag_id = t.id';
            $sql .= $joinClause;
            $countSql .= $joinClause;
        }

        $whereClause = ' WHERE 1=1';
        $sql .= $whereClause;
        $countSql .= $whereClause;

        // Add search term if provided (searches across multiple fields)
        if (!empty($params['term'])) {
            $termClause = ' AND (
                a.site_name LIKE :term
                OR a.employee_name LIKE :term
                OR a.computer_host_name LIKE :term
                OR a.equipment_type LIKE :term
                OR a.serial_number LIKE :term
                OR a.par_number LIKE :term
                OR a.active_directory_name LIKE :term
                OR a.position LIKE :term
                OR a.program_section LIKE :term
                OR a.operating_system LIKE :term
                OR a.administration_type LIKE :term
                OR a.device_custodian LIKE :term
                OR a.remarks LIKE :term
                OR t.name LIKE :term
            )';
            $sql .= $termClause;
            $countSql .= $termClause;
            $bindings[':term'] = '%' . $params['term'] . '%';
        }

        // Add specific field filters
        $exactMatchFields = [
            'site_name', 'employee_name', 'computer_host_name', 'equipment_type',
            'acquisition_type', 'operating_system', 'administration_type',
            'xdr_installed', 'device_custodian', 'program_section'
        ];

        foreach ($exactMatchFields as $field) {
            if (!empty($params[$field])) {
                $fieldClause = " AND a.$field = :$field";
                $sql .= $fieldClause;
                $countSql .= $fieldClause;
                $bindings[":$field"] = $params[$field];
            }
        }

        // Add date range filters
        if (!empty($params['acquisition_date_from'])) {
            $dateClause = ' AND a.acquisition_date >= :acquisition_date_from';
            $sql .= $dateClause;
            $countSql .= $dateClause;
            $bindings[':acquisition_date_from'] = $params['acquisition_date_from'];
        }

        if (!empty($params['acquisition_date_to'])) {
            $dateClause = ' AND a.acquisition_date <= :acquisition_date_to';
            $sql .= $dateClause;
            $countSql .= $dateClause;
            $bindings[':acquisition_date_to'] = $params['acquisition_date_to'];
        }

        if (!empty($params['inventory_date_from'])) {
            $dateClause = ' AND a.inventory_date >= :inventory_date_from';
            $sql .= $dateClause;
            $countSql .= $dateClause;
            $bindings[':inventory_date_from'] = $params['inventory_date_from'];
        }

        if (!empty($params['inventory_date_to'])) {
            $dateClause = ' AND a.inventory_date <= :inventory_date_to';
            $sql .= $dateClause;
            $countSql .= $dateClause;
            $bindings[':inventory_date_to'] = $params['inventory_date_to'];
        }

        // Add sorting
        $sortField = !empty($params['sort_by']) ? $params['sort_by'] : 'id';
        $sortOrder = !empty($params['sort_order']) && strtoupper($params['sort_order']) === 'ASC' ? 'ASC' : 'DESC';

        // Validate sort field to prevent SQL injection
        $allowedSortFields = [
            'id', 'computer_host_name', 'equipment_type', 'employee_name',
            'serial_number', 'acquisition_date', 'inventory_date', 'site_name'
        ];

        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'id';
        }

        // First, get the total count
        $this->db->query($countSql);

        // Bind all parameters for count query
        foreach ($bindings as $param => $value) {
            $this->db->bind($param, $value);
        }

        // Execute count query and get total
        $result = $this->db->single();
        $totalCount = $result->total;

        // Then, get the actual assets with pagination
        $sql .= " ORDER BY a.$sortField $sortOrder";
        $this->db->query($sql);

        // Bind all parameters for main query
        foreach ($bindings as $param => $value) {
            $this->db->bind($param, $value);
        }

        // Get paginated results
        $assets = $this->db->paginatedResultSet($page, $perPage);

        return [
            'assets' => $assets,
            'total' => $totalCount,
            'page' => $page,
            'perPage' => $perPage,
            'lastPage' => ceil($totalCount / $perPage)
        ];
    }

    /**
     * Get unique values for dropdown filters
     *
     * Retrieves distinct values for a specific field to populate dropdown filters.
     * Only returns non-null, non-empty values for security-validated fields.
     *
     * @param string $field The field name to get unique values for
     * @return array Array of objects containing unique field values
     * @since 1.0.0
     */
    public function getUniqueFieldValues($field) {
        $allowedFields = [
            'site_name', 'equipment_type', 'operating_system',
            'administration_type', 'xdr_installed', 'program_section',
            'acquisition_type'
        ];

        if (!in_array($field, $allowedFields)) {
            return [];
        }

        $this->db->query("SELECT DISTINCT $field FROM assets WHERE $field IS NOT NULL AND $field != '' ORDER BY $field ASC");
        return $this->db->resultSet();
    }

    /**
     * Get assets for export (all or filtered by search term)
     *
     * Retrieves all assets or filtered assets for export functionality.
     * Used for CSV export and reporting features.
     *
     * @param string|null $term Optional search term to filter assets
     * @return array Array of asset objects for export
     * @since 1.0.0
     */
    public function getAssetsForExport($term = null) {
        if ($term) {
            // If search term is provided, use the search query
            $this->db->query('SELECT * FROM assets
                              WHERE site_name LIKE :term
                              OR employee_name LIKE :term
                              OR computer_host_name LIKE :term
                              OR equipment_type LIKE :term
                              OR serial_number LIKE :term
                              OR par_number LIKE :term
                              ORDER BY id DESC');
            $this->db->bind(':term', '%' . $term . '%');
        } else {
            // Otherwise get all assets
            $this->db->query('SELECT * FROM assets ORDER BY id DESC');
        }

        return $this->db->resultSet();
    }

    /**
     * Get asset history
     *
     * Retrieves the complete history of changes for a specific asset.
     * Delegates to AssetHistory model for actual data retrieval.
     *
     * @param int $assetId The asset ID to get history for
     * @return array Array of history records for the asset
     * @since 1.0.0
     */
    public function getAssetHistory($assetId) {
        // Initialize AssetHistory model
        require_once __DIR__ . '/AssetHistory.php';
        $assetHistory = new AssetHistory();

        // Get history records for this asset
        return $assetHistory->getAssetHistory($assetId);
    }

    /**
     * Get total count of assets
     *
     * Returns the total number of assets in the database.
     * Used for dashboard statistics and reporting.
     *
     * @return int Total number of assets
     * @since 1.0.0
     */
    public function getAssetCount() {
        $this->db->query('SELECT COUNT(*) as count FROM assets');
        $result = $this->db->single();
        return $result->count;
    }

    /**
     * Import assets from CSV file
     *
     * Processes a CSV file upload and imports asset data into the database.
     * Includes duplicate checking, error handling, and detailed logging.
     *
     * @param array $file File upload array from $_FILES
     * @param int $skipRows Number of header rows to skip (default: 7)
     * @return array Results array containing success/failure counts and error details
     * @since 1.0.0
     */
    public function importFromCSV($file, $skipRows = 7) {
        // Debug log
        error_log("importFromCSV called with file: " . print_r($file, true) . ", skipRows: " . $skipRows);

        // Results tracking
        $results = [
            'success' => 0,
            'failed' => 0,
            'skipped' => 0,
            'duplicates' => 0,
            'errors' => [],
            'duplicate_serials' => []
        ];

        // Check if file parameter is valid
        if (!is_array($file) || !isset($file['tmp_name'])) {
            $error_msg = "Invalid file parameter";
            error_log($error_msg . ": " . print_r($file, true));
            $results['errors'][] = $error_msg;
            return $results;
        }

        // Check if file exists
        if (!file_exists($file['tmp_name'])) {
            $error_msg = "File not found or upload error";
            error_log($error_msg . ": " . $file['tmp_name']);
            $results['errors'][] = $error_msg;
            return $results;
        }

        error_log("File exists at: " . $file['tmp_name'] . ", size: " . filesize($file['tmp_name']));

        // Debug: Check file content preview
        $fileContent = file_get_contents($file['tmp_name']);
        if ($fileContent !== false) {
            $contentPreview = substr($fileContent, 0, 200);
            error_log("CSV file content preview: " . $contentPreview);
        } else {
            error_log("WARNING: Could not read file content for preview");
        }

        // Open the CSV file
        try {
            $handle = fopen($file['tmp_name'], 'r');
            if (!$handle) {
                $error_msg = "Could not open file";
                error_log($error_msg . ": " . $file['tmp_name']);
                $results['errors'][] = $error_msg;
                return $results;
            }
            error_log("Successfully opened file handle");
        } catch (Exception $e) {
            $error_msg = "Exception opening file: " . $e->getMessage();
            error_log($error_msg);
            $results['errors'][] = $error_msg;
            return $results;
        }

        // Debug: Check first few rows of CSV
        error_log("Checking first few rows of CSV before skipping headers:");
        $currentPosition = ftell($handle);
        $rowCount = 0;
        $maxPreviewRows = 3;

        rewind($handle);
        while (($previewRow = fgetcsv($handle)) !== FALSE && $rowCount < $maxPreviewRows) {
            error_log("CSV Row " . $rowCount . ": " . print_r($previewRow, true));
            $rowCount++;
        }

        // Reset file pointer to where it was
        fseek($handle, $currentPosition);

        // Skip header rows
        error_log("Skipping " . $skipRows . " header rows");
        for ($i = 0; $i < $skipRows; $i++) {
            $skippedRow = fgetcsv($handle);
            error_log("Skipped row " . $i . ": " . ($skippedRow ? print_r($skippedRow, true) : "NULL"));
        }

        // Process each row
        $rowNumber = 0;
        while (($row = fgetcsv($handle)) !== FALSE) {
            $rowNumber++;
            error_log("Processing CSV row #" . $rowNumber . ": " . print_r($row, true));

            // Skip empty rows
            if (empty($row[0]) && empty($row[1]) && empty($row[2])) {
                error_log("Skipping empty row #" . $rowNumber);
                $results['skipped']++;
                continue;
            }

            // Check row length
            error_log("Row #" . $rowNumber . " has " . count($row) . " columns");
            if (count($row) < 16) {
                error_log("WARNING: Row #" . $rowNumber . " has fewer than expected columns");
            }

            // Map CSV columns to database fields
            $data = [
                'inventory_date' => !empty($row[0]) ? date('Y-m-d', strtotime($row[0])) : NULL,
                'site_name' => !empty($row[1]) ? $row[1] : NULL,
                'employee_name' => !empty($row[2]) ? $row[2] : NULL,
                'active_directory_name' => !empty($row[3]) ? $row[3] : NULL,
                'position' => !empty($row[4]) ? $row[4] : NULL,
                'program_section' => !empty($row[5]) ? $row[5] : NULL,
                'computer_host_name' => !empty($row[6]) ? $row[6] : NULL,
                'equipment_type' => !empty($row[7]) ? $row[7] : NULL,
                'acquisition_type' => !empty($row[8]) ? $row[8] : NULL,
                'operating_system' => !empty($row[9]) ? $row[9] : NULL,
                'administration_type' => !empty($row[10]) ? $row[10] : NULL,
                'xdr_installed' => !empty($row[11]) ? $row[11] : NULL,
                'device_custodian' => !empty($row[12]) ? $row[12] : NULL,
                'remarks' => !empty($row[13]) ? $row[13] : NULL,
                'par_number' => !empty($row[14]) ? $row[14] : NULL,
                'serial_number' => !empty($row[15]) ? $row[15] : NULL,
                'acquisition_date' => !empty($row[16]) ? date('Y-m-d', strtotime($row[16])) : NULL,
                'estimated_useful_life' => !empty($row[17]) ? date('Y-m-d', strtotime($row[17])) : NULL
            ];

            // Debug: Log mapped data
            error_log("Mapped data for row #" . $rowNumber . ": " . print_r($data, true));

            // Skip if essential fields are missing
            if (empty($data['computer_host_name']) || empty($data['equipment_type']) || empty($data['serial_number'])) {
                error_log("Skipping row #" . $rowNumber . " due to missing essential fields");
                $results['skipped']++;
                continue;
            }

            // Check for duplicate serial number
            if ($this->serialNumberExists($data['serial_number'])) {
                error_log("Skipping row #" . $rowNumber . " due to duplicate serial number: " . $data['serial_number']);
                $results['duplicates']++;
                $results['duplicate_serials'][] = [
                    'serial_number' => $data['serial_number'],
                    'computer_host_name' => $data['computer_host_name'],
                    'equipment_type' => $data['equipment_type']
                ];
                continue;
            }

            try {
                // Prepare SQL statement for this row
                $this->db->query('INSERT INTO assets (inventory_date, site_name, employee_name, active_directory_name, position, program_section, computer_host_name, equipment_type, acquisition_type, operating_system, administration_type, xdr_installed, device_custodian, remarks, par_number, serial_number, acquisition_date, estimated_useful_life)
                                  VALUES (:inventory_date, :site_name, :employee_name, :active_directory_name, :position, :program_section, :computer_host_name, :equipment_type, :acquisition_type, :operating_system, :administration_type, :xdr_installed, :device_custodian, :remarks, :par_number, :serial_number, :acquisition_date, :estimated_useful_life)');

                // Bind values
                $this->db->bind(':inventory_date', $data['inventory_date']);
                $this->db->bind(':site_name', $data['site_name']);
                $this->db->bind(':employee_name', $data['employee_name']);
                $this->db->bind(':active_directory_name', $data['active_directory_name']);
                $this->db->bind(':position', $data['position']);
                $this->db->bind(':program_section', $data['program_section']);
                $this->db->bind(':computer_host_name', $data['computer_host_name']);
                $this->db->bind(':equipment_type', $data['equipment_type']);
                $this->db->bind(':acquisition_type', $data['acquisition_type']);
                $this->db->bind(':operating_system', $data['operating_system']);
                $this->db->bind(':administration_type', $data['administration_type']);
                $this->db->bind(':xdr_installed', $data['xdr_installed']);
                $this->db->bind(':device_custodian', $data['device_custodian']);
                $this->db->bind(':remarks', $data['remarks']);
                $this->db->bind(':par_number', $data['par_number']);
                $this->db->bind(':serial_number', $data['serial_number']);
                $this->db->bind(':acquisition_date', $data['acquisition_date']);
                $this->db->bind(':estimated_useful_life', $data['estimated_useful_life']);

                // Execute the statement
                error_log("Attempting to execute SQL insert for row #" . $rowNumber);
                try {
                    if ($this->db->execute()) {
                        error_log("Successfully inserted asset: {$data['computer_host_name']}");
                        $results['success']++;
                    } else {
                        error_log("Failed to insert asset: {$data['computer_host_name']}. Database error.");
                        $results['failed']++;
                        $results['errors'][] = "Failed to import asset {$data['computer_host_name']}";
                    }
                } catch (PDOException $pdoEx) {
                    error_log("PDO Exception during execute: " . $pdoEx->getMessage() . " - Code: " . $pdoEx->getCode());
                    $results['failed']++;
                    $results['errors'][] = "Error importing asset {$data['computer_host_name']}: " . $pdoEx->getMessage();
                }
            } catch(Exception $e) {
                error_log("Exception during insert: " . $e->getMessage() . " - Trace: " . $e->getTraceAsString());
                $results['failed']++;
                $results['errors'][] = "Error importing asset {$data['computer_host_name']}: " . $e->getMessage();
            }
        }

        // Close the file
        fclose($handle);

        return $results;
    }

    /**
     * Get total assets count
     *
     * Returns the total number of assets in the database with null safety.
     * Alternative method to getAssetCount() with additional error handling.
     *
     * @return int Total number of assets (0 if none found)
     * @since 1.0.0
     */
    public function getTotalAssetsCount() {
        $this->db->query('SELECT COUNT(*) as total FROM assets');
        $result = $this->db->single();
        return isset($result->total) ? $result->total : 0;
    }

    /**
     * Get asset count by type
     *
     * Returns asset counts grouped by equipment type for dashboard analytics.
     * Results are ordered by count in descending order.
     *
     * @return array Array of objects with equipment_type and count
     * @since 1.0.0
     */
    public function getAssetCountByType() {
        $this->db->query('SELECT equipment_type, COUNT(*) as count
                         FROM assets
                         GROUP BY equipment_type
                         ORDER BY count DESC');
        return $this->db->resultSet();
    }

    /**
     * Get asset count by acquisition date (last 12 months)
     *
     * Returns asset acquisition trends for the last 12 months grouped by month.
     * Used for dashboard charts and acquisition analysis.
     *
     * @return array Array of objects with month and count
     * @since 1.0.0
     */
    public function getAssetCountByAcquisitionDate() {
        $this->db->query('SELECT
                            DATE_FORMAT(acquisition_date, "%Y-%m") as month,
                            COUNT(*) as count
                         FROM assets
                         WHERE acquisition_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                         GROUP BY DATE_FORMAT(acquisition_date, "%Y-%m")
                         ORDER BY month ASC');
        return $this->db->resultSet();
    }

    /**
     * Get asset count by employee (top 10)
     *
     * Returns the top 10 employees with the most assigned assets.
     * Excludes null and empty employee names.
     *
     * @return array Array of objects with employee_name and count
     * @since 1.0.0
     */
    public function getAssetCountByEmployee() {
        $this->db->query('SELECT
                            employee_name,
                            COUNT(*) as count
                         FROM assets
                         WHERE employee_name IS NOT NULL AND employee_name != ""
                         GROUP BY employee_name
                         ORDER BY count DESC
                         LIMIT 10');
        return $this->db->resultSet();
    }

    /**
     * Get assets by operating system
     *
     * Returns asset counts grouped by operating system for analytics.
     * Excludes null and empty operating system values.
     *
     * @return array Array of objects with operating_system and count
     * @since 1.0.0
     */
    public function getAssetCountByOS() {
        $this->db->query('SELECT
                            operating_system,
                            COUNT(*) as count
                         FROM assets
                         WHERE operating_system IS NOT NULL AND operating_system != ""
                         GROUP BY operating_system
                         ORDER BY count DESC');
        return $this->db->resultSet();
    }

    // Get assets by site
    public function getAssetCountBySite() {
        $this->db->query('SELECT
                            site_name,
                            COUNT(*) as count
                         FROM assets
                         WHERE site_name IS NOT NULL AND site_name != ""
                         GROUP BY site_name
                         ORDER BY count DESC');
        return $this->db->resultSet();
    }

    // Get multiple assets by IDs
    public function getAssetsByIds($ids) {
        if (empty($ids)) {
            return [];
        }

        // Create placeholders for the IN clause
        $placeholders = implode(',', array_fill(0, count($ids), '?'));

        $this->db->query("SELECT * FROM assets WHERE id IN ($placeholders) ORDER BY id DESC");

        // Bind all IDs
        foreach ($ids as $index => $id) {
            $this->db->bind($index + 1, $id);
        }

        return $this->db->resultSet();
    }

    /**
     * Check if an asset with the given serial number already exists
     *
     * @param string $serialNumber The serial number to check
     * @return bool True if the serial number exists, false otherwise
     */
    public function serialNumberExists($serialNumber) {
        // Skip check if serial number is empty
        if (empty($serialNumber)) {
            return false;
        }

        $this->db->query('SELECT COUNT(*) as count FROM assets WHERE serial_number = :serial_number');
        $this->db->bind(':serial_number', $serialNumber);

        $result = $this->db->single();

        // Log the check for debugging
        error_log("Checking for duplicate serial number: " . $serialNumber . ", Result: " . ($result->count > 0 ? 'Found' : 'Not found'));

        return ($result->count > 0);
    }
}
