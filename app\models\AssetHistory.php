<?php
class AssetHistory {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // Add a record to the asset history
    public function addRecord($assetId, $fieldName, $oldValue, $newValue, $changedBy) {
        $this->db->query('INSERT INTO asset_history (asset_id, field_name, old_value, new_value, changed_by) 
                          VALUES (:asset_id, :field_name, :old_value, :new_value, :changed_by)');
        
        // Bind values
        $this->db->bind(':asset_id', $assetId);
        $this->db->bind(':field_name', $fieldName);
        $this->db->bind(':old_value', $oldValue);
        $this->db->bind(':new_value', $newValue);
        $this->db->bind(':changed_by', $changedBy);
        
        // Execute
        return $this->db->execute();
    }

    // Get history for a specific asset
    public function getAssetHistory($assetId) {
        $this->db->query('SELECT ah.*, u.name as user_name 
                          FROM asset_history ah
                          LEFT JOIN users u ON ah.changed_by = u.id
                          WHERE ah.asset_id = :asset_id
                          ORDER BY ah.changed_at DESC');
        
        $this->db->bind(':asset_id', $assetId);
        
        return $this->db->resultSet();
    }

    // Get recent changes across all assets
    public function getRecentChanges($limit = 10) {
        $this->db->query('SELECT ah.*, a.computer_host_name, u.name as user_name 
                          FROM asset_history ah
                          JOIN assets a ON ah.asset_id = a.id
                          LEFT JOIN users u ON ah.changed_by = u.id
                          ORDER BY ah.changed_at DESC
                          LIMIT :limit');
        
        $this->db->bind(':limit', $limit, PDO::PARAM_INT);
        
        return $this->db->resultSet();
    }
}
