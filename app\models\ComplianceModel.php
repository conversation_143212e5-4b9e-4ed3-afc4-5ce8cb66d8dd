<?php
/**
 * Compliance Model
 *
 * Handles all compliance-related database operations including framework management,
 * control tracking, asset compliance status, and compliance reporting.
 *
 * @package AssetVisibility
 * <AUTHOR> Visibility Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
class ComplianceModel {
    /**
     * Database connection instance
     * @var Database
     */
    private $db;

    /**
     * Constructor
     *
     * Initializes the ComplianceModel with database connection.
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->db = new Database;
    }

    /**
     * Get all compliance frameworks
     *
     * Retrieves all active compliance frameworks ordered by name.
     *
     * @return array Array of framework objects
     * @since 1.0.0
     */
    public function getFrameworks() {
        $this->db->query('SELECT * FROM compliance_frameworks WHERE active = TRUE ORDER BY name ASC');
        return $this->db->resultSet();
    }

    /**
     * Get framework by ID
     *
     * Retrieves a specific compliance framework by its ID.
     *
     * @param int $id The framework ID
     * @return object|false Framework object if found, false otherwise
     * @since 1.0.0
     */
    public function getFrameworkById($id) {
        $this->db->query('SELECT * FROM compliance_frameworks WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    /**
     * Get controls for a framework
     *
     * Retrieves all active compliance controls for a specific framework.
     *
     * @param int $frameworkId The framework ID
     * @return array Array of control objects
     * @since 1.0.0
     */
    public function getControlsByFramework($frameworkId) {
        $this->db->query('SELECT * FROM compliance_controls WHERE framework_id = :framework_id AND active = TRUE ORDER BY control_id ASC');
        $this->db->bind(':framework_id', $frameworkId);
        return $this->db->resultSet();
    }

    /**
     * Get compliance status for an asset
     *
     * Retrieves compliance status for all controls of a specific asset within a framework.
     * Includes control details and assessor information.
     *
     * @param int $assetId The asset ID
     * @param int $frameworkId The framework ID
     * @return array Array of compliance status objects
     * @since 1.0.0
     */
    public function getAssetComplianceStatus($assetId, $frameworkId) {
        $this->db->query('SELECT ac.*, cc.control_id as control_identifier, cc.name as control_name,
                          u.name as assessed_by_name
                          FROM asset_compliance ac
                          JOIN compliance_controls cc ON ac.control_id = cc.id
                          LEFT JOIN users u ON ac.last_assessed_by = u.id
                          WHERE ac.asset_id = :asset_id AND cc.framework_id = :framework_id
                          ORDER BY cc.control_id ASC');
        $this->db->bind(':asset_id', $assetId);
        $this->db->bind(':framework_id', $frameworkId);
        return $this->db->resultSet();
    }

    /**
     * Update asset compliance status
     *
     * Creates or updates compliance status for an asset-control combination.
     * Uses INSERT...ON DUPLICATE KEY UPDATE for upsert functionality.
     *
     * @param array $data Compliance data containing asset_id, control_id, status, evidence, notes, last_assessed_by
     * @return bool True if update successful, false otherwise
     * @since 1.0.0
     */
    public function updateAssetCompliance($data) {
        $this->db->query('INSERT INTO asset_compliance (asset_id, control_id, status, evidence, notes, last_assessed_by, last_assessed_date)
                          VALUES (:asset_id, :control_id, :status, :evidence, :notes, :last_assessed_by, NOW())
                          ON DUPLICATE KEY UPDATE
                          status = :status,
                          evidence = :evidence,
                          notes = :notes,
                          last_assessed_by = :last_assessed_by,
                          last_assessed_date = NOW()');

        $this->db->bind(':asset_id', $data['asset_id']);
        $this->db->bind(':control_id', $data['control_id']);
        $this->db->bind(':status', $data['status']);
        $this->db->bind(':evidence', $data['evidence']);
        $this->db->bind(':notes', $data['notes']);
        $this->db->bind(':last_assessed_by', $data['last_assessed_by']);

        return $this->db->execute();
    }

    /**
     * Get compliance summary for all assets
     *
     * Generates a comprehensive compliance summary showing compliance status
     * counts for each asset within a specific framework.
     *
     * @param int $frameworkId The framework ID
     * @return array Array of asset compliance summary objects
     * @since 1.0.0
     */
    public function getComplianceSummary($frameworkId) {
        $this->db->query('SELECT a.id, a.computer_host_name, a.equipment_type, a.serial_number,
                          COUNT(DISTINCT cc.id) as total_controls,
                          SUM(CASE WHEN ac.status = "compliant" THEN 1 ELSE 0 END) as compliant_count,
                          SUM(CASE WHEN ac.status = "non_compliant" THEN 1 ELSE 0 END) as non_compliant_count,
                          SUM(CASE WHEN ac.status = "not_applicable" THEN 1 ELSE 0 END) as not_applicable_count,
                          SUM(CASE WHEN ac.status = "in_progress" OR ac.status IS NULL THEN 1 ELSE 0 END) as in_progress_count
                          FROM assets a
                          CROSS JOIN compliance_controls cc
                          LEFT JOIN asset_compliance ac ON a.id = ac.asset_id AND cc.id = ac.control_id
                          WHERE cc.framework_id = :framework_id AND cc.active = TRUE
                          GROUP BY a.id, a.computer_host_name, a.equipment_type, a.serial_number
                          ORDER BY compliant_count / total_controls DESC');
        $this->db->bind(':framework_id', $frameworkId);
        return $this->db->resultSet();
    }

    /**
     * Get compliance summary by control
     *
     * Generates a compliance summary showing how many assets are compliant
     * with each control within a specific framework.
     *
     * @param int $frameworkId The framework ID
     * @return array Array of control compliance summary objects
     * @since 1.0.0
     */
    public function getComplianceSummaryByControl($frameworkId) {
        $this->db->query('SELECT cc.id, cc.control_id, cc.name,
                          COUNT(DISTINCT a.id) as total_assets,
                          SUM(CASE WHEN ac.status = "compliant" THEN 1 ELSE 0 END) as compliant_count,
                          SUM(CASE WHEN ac.status = "non_compliant" THEN 1 ELSE 0 END) as non_compliant_count,
                          SUM(CASE WHEN ac.status = "not_applicable" THEN 1 ELSE 0 END) as not_applicable_count,
                          SUM(CASE WHEN ac.status = "in_progress" OR ac.status IS NULL THEN 1 ELSE 0 END) as in_progress_count
                          FROM compliance_controls cc
                          CROSS JOIN assets a
                          LEFT JOIN asset_compliance ac ON cc.id = ac.control_id AND a.id = ac.asset_id
                          WHERE cc.framework_id = :framework_id AND cc.active = TRUE
                          GROUP BY cc.id, cc.control_id, cc.name
                          ORDER BY cc.control_id ASC');
        $this->db->bind(':framework_id', $frameworkId);
        return $this->db->resultSet();
    }

    /**
     * Generate compliance report
     *
     * Creates a comprehensive compliance report for a framework including
     * overall compliance percentage, asset summaries, and control summaries.
     *
     * @param int $frameworkId The framework ID to generate report for
     * @param int $userId The user ID generating the report
     * @return int|false Report ID if successful, false otherwise
     * @since 1.0.0
     */
    public function generateReport($frameworkId, $userId) {
        // Get framework details
        $framework = $this->getFrameworkById($frameworkId);

        // Get compliance summary by asset
        $assetSummary = $this->getComplianceSummary($frameworkId);

        // Get compliance summary by control
        $controlSummary = $this->getComplianceSummaryByControl($frameworkId);

        // Calculate overall compliance percentage
        $totalControls = 0;
        $compliantControls = 0;

        foreach($controlSummary as $control) {
            $totalControls += $control->total_assets;
            $compliantControls += $control->compliant_count;
        }

        $overallCompliance = $totalControls > 0 ? ($compliantControls / $totalControls) * 100 : 0;

        // Prepare report data
        $reportData = [
            'framework' => $framework,
            'generated_date' => date('Y-m-d H:i:s'),
            'overall_compliance' => $overallCompliance,
            'asset_summary' => $assetSummary,
            'control_summary' => $controlSummary
        ];

        // Save report
        $this->db->query('INSERT INTO compliance_reports (framework_id, report_name, report_date, report_data, generated_by)
                          VALUES (:framework_id, :report_name, CURDATE(), :report_data, :generated_by)');

        $this->db->bind(':framework_id', $frameworkId);
        $this->db->bind(':report_name', $framework->name . ' Compliance Report - ' . date('Y-m-d'));
        $this->db->bind(':report_data', json_encode($reportData));
        $this->db->bind(':generated_by', $userId);

        if($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }

    /**
     * Get report by ID
     *
     * Retrieves a compliance report by its ID including framework and user details.
     * Automatically decodes JSON report data.
     *
     * @param int $reportId The report ID
     * @return object|false Report object if found, false otherwise
     * @since 1.0.0
     */
    public function getReportById($reportId) {
        $this->db->query('SELECT cr.*, cf.name as framework_name, u.name as generated_by_name
                          FROM compliance_reports cr
                          LEFT JOIN compliance_frameworks cf ON cr.framework_id = cf.id
                          LEFT JOIN users u ON cr.generated_by = u.id
                          WHERE cr.id = :id');
        $this->db->bind(':id', $reportId);
        $report = $this->db->single();

        if($report) {
            $report->report_data = json_decode($report->report_data);
        }

        return $report;
    }

    /**
     * Get recent reports
     *
     * Retrieves the most recently generated compliance reports with framework
     * and user details, ordered by creation date.
     *
     * @param int $limit Maximum number of reports to return (default: 5)
     * @return array Array of report objects
     * @since 1.0.0
     */
    public function getRecentReports($limit = 5) {
        $this->db->query('SELECT cr.*, cf.name as framework_name, u.name as generated_by_name
                          FROM compliance_reports cr
                          LEFT JOIN compliance_frameworks cf ON cr.framework_id = cf.id
                          LEFT JOIN users u ON cr.generated_by = u.id
                          ORDER BY cr.created_at DESC
                          LIMIT :limit');
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }

    /**
     * Get non-compliant assets
     *
     * Retrieves assets with non-compliant controls, ordered by non-compliance
     * percentage. Only includes assets with at least one non-compliant control.
     *
     * @param int $frameworkId The framework ID
     * @param int $limit Maximum number of assets to return (default: 10)
     * @return array Array of non-compliant asset objects with compliance statistics
     * @since 1.0.0
     */
    public function getNonCompliantAssets($frameworkId, $limit = 10) {
        $this->db->query('SELECT a.id, a.computer_host_name, a.equipment_type, a.serial_number,
                          COUNT(DISTINCT cc.id) as total_controls,
                          SUM(CASE WHEN ac.status = "non_compliant" THEN 1 ELSE 0 END) as non_compliant_count,
                          (SUM(CASE WHEN ac.status = "non_compliant" THEN 1 ELSE 0 END) / COUNT(DISTINCT cc.id)) * 100 as non_compliant_percentage
                          FROM assets a
                          CROSS JOIN compliance_controls cc
                          LEFT JOIN asset_compliance ac ON a.id = ac.asset_id AND cc.id = ac.control_id
                          WHERE cc.framework_id = :framework_id AND cc.active = TRUE
                          GROUP BY a.id, a.computer_host_name, a.equipment_type, a.serial_number
                          HAVING non_compliant_count > 0
                          ORDER BY non_compliant_percentage DESC
                          LIMIT :limit');
        $this->db->bind(':framework_id', $frameworkId);
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }

    /**
     * Get control by ID
     *
     * Retrieves a specific compliance control by its ID including framework details.
     *
     * @param int $controlId The control ID
     * @return object|false Control object with framework details if found, false otherwise
     * @since 1.0.0
     */
    public function getControlById($controlId) {
        $this->db->query('SELECT cc.*, cf.name as framework_name
                          FROM compliance_controls cc
                          JOIN compliance_frameworks cf ON cc.framework_id = cf.id
                          WHERE cc.id = :control_id');
        $this->db->bind(':control_id', $controlId);
        return $this->db->single();
    }

    /**
     * Get compliance status for an asset and control
     *
     * Retrieves the compliance status for a specific asset-control combination.
     *
     * @param int $assetId The asset ID
     * @param int $controlId The control ID
     * @return object|false Compliance status object if found, false otherwise
     * @since 1.0.0
     */
    public function getAssetControlStatus($assetId, $controlId) {
        $this->db->query('SELECT * FROM asset_compliance
                          WHERE asset_id = :asset_id AND control_id = :control_id');
        $this->db->bind(':asset_id', $assetId);
        $this->db->bind(':control_id', $controlId);
        return $this->db->single();
    }
}
