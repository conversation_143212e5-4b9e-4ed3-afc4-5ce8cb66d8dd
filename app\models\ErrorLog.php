<?php
class ErrorLog {
    private $db;

    public function __construct() {
        $this->db = new Database;
        
        // Ensure the error_logs table exists
        $this->ensureErrorLogsTableExists();
    }

    /**
     * Ensure the error_logs table exists
     * This is called once during initialization to prevent repeated table creation
     *
     * @return bool True if the table exists or was created successfully
     */
    public function ensureErrorLogsTableExists() {
        try {
            // Check if the table exists
            $this->db->query("SHOW TABLES LIKE 'error_logs'");
            $tableExists = $this->db->rowCount() > 0;
            error_log("ErrorLog::ensureErrorLogsTableExists - Table exists check: " . ($tableExists ? 'Yes' : 'No'));

            if (!$tableExists) {
                // Create the table
                $sql = "CREATE TABLE IF NOT EXISTS error_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    level VARCHAR(20) NOT NULL,
                    message TEXT NOT NULL,
                    context TEXT,
                    file VARCHAR(255),
                    line INT,
                    trace TEXT,
                    user_id INT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX (level),
                    INDEX (created_at)
                ) ENGINE=InnoDB";

                $this->db->query($sql);
                $result = $this->db->execute();
                error_log("ErrorLog::ensureErrorLogsTableExists - Created table: " . ($result ? 'Success' : 'Failed'));

                return $result;
            }

            return $tableExists;
        } catch (Exception $e) {
            error_log("ErrorLog::ensureErrorLogsTableExists - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Log an error to the database
     *
     * @param string $level Error level (error, warning, info, debug)
     * @param string $message Error message
     * @param array $context Additional context data
     * @param string $file File where the error occurred
     * @param int $line Line number where the error occurred
     * @param string $trace Stack trace
     * @return int|bool The new error log ID or false on failure
     */
    public function logError($level, $message, $context = null, $file = null, $line = null, $trace = null) {
        try {
            $this->db->query('INSERT INTO error_logs (level, message, context, file, line, trace, user_id, ip_address, user_agent)
                              VALUES (:level, :message, :context, :file, :line, :trace, :user_id, :ip_address, :user_agent)');

            $this->db->bind(':level', $level);
            $this->db->bind(':message', $message);
            $this->db->bind(':context', $context ? json_encode($context) : null);
            $this->db->bind(':file', $file);
            $this->db->bind(':line', $line);
            $this->db->bind(':trace', $trace);
            $this->db->bind(':user_id', isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null);
            $this->db->bind(':ip_address', $_SERVER['REMOTE_ADDR'] ?? null);
            $this->db->bind(':user_agent', $_SERVER['HTTP_USER_AGENT'] ?? null);

            if($this->db->execute()) {
                return $this->db->lastInsertId();
            } else {
                return false;
            }
        } catch (Exception $e) {
            error_log("ErrorLog::logError - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all error logs with pagination
     *
     * @param int $limit Number of records to return
     * @param int $offset Offset for pagination
     * @param string $level Filter by error level
     * @param string $search Search term
     * @return array
     */
    public function getErrorLogs($limit = 20, $offset = 0, $level = null, $search = null) {
        try {
            $sql = 'SELECT e.*, u.name as user_name
                    FROM error_logs e
                    LEFT JOIN users u ON e.user_id = u.id
                    WHERE 1=1';
            
            $params = [];
            
            if ($level) {
                $sql .= ' AND e.level = :level';
                $params[':level'] = $level;
            }
            
            if ($search) {
                $sql .= ' AND (e.message LIKE :search OR e.file LIKE :search OR e.context LIKE :search)';
                $params[':search'] = '%' . $search . '%';
            }
            
            $sql .= ' ORDER BY e.created_at DESC LIMIT :limit OFFSET :offset';
            $params[':limit'] = $limit;
            $params[':offset'] = $offset;
            
            $this->db->query($sql);
            
            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("ErrorLog::getErrorLogs - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Count all error logs
     *
     * @param string $level Filter by error level
     * @param string $search Search term
     * @return int
     */
    public function countErrorLogs($level = null, $search = null) {
        try {
            $sql = 'SELECT COUNT(*) as total FROM error_logs WHERE 1=1';
            
            $params = [];
            
            if ($level) {
                $sql .= ' AND level = :level';
                $params[':level'] = $level;
            }
            
            if ($search) {
                $sql .= ' AND (message LIKE :search OR file LIKE :search OR context LIKE :search)';
                $params[':search'] = '%' . $search . '%';
            }
            
            $this->db->query($sql);
            
            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            $result = $this->db->single();
            return $result ? $result->total : 0;
        } catch (Exception $e) {
            error_log("ErrorLog::countErrorLogs - Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get error log by ID
     *
     * @param int $id Error log ID
     * @return object|bool The error log or false on failure
     */
    public function getErrorLogById($id) {
        try {
            $this->db->query('SELECT e.*, u.name as user_name
                              FROM error_logs e
                              LEFT JOIN users u ON e.user_id = u.id
                              WHERE e.id = :id');
            $this->db->bind(':id', $id);
            return $this->db->single();
        } catch (Exception $e) {
            error_log("ErrorLog::getErrorLogById - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete error log by ID
     *
     * @param int $id Error log ID
     * @return bool True on success, false on failure
     */
    public function deleteErrorLog($id) {
        try {
            $this->db->query('DELETE FROM error_logs WHERE id = :id');
            $this->db->bind(':id', $id);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log("ErrorLog::deleteErrorLog - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clear all error logs
     *
     * @return bool True on success, false on failure
     */
    public function clearErrorLogs() {
        try {
            $this->db->query('TRUNCATE TABLE error_logs');
            return $this->db->execute();
        } catch (Exception $e) {
            error_log("ErrorLog::clearErrorLogs - Error: " . $e->getMessage());
            return false;
        }
    }
}
