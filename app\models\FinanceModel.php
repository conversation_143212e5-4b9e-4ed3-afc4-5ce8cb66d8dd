<?php
class FinanceModel {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    /**
     * Add asset cost
     *
     * @param array $data
     * @return bool
     */
    public function addAssetCost($data) {
        $this->db->query('INSERT INTO asset_costs (asset_id, cost_type, amount, description, date_incurred,
                          fiscal_year, budget_category, created_by)
                          VALUES (:asset_id, :cost_type, :amount, :description, :date_incurred,
                          :fiscal_year, :budget_category, :created_by)');

        $this->db->bind(':asset_id', $data['asset_id']);
        $this->db->bind(':cost_type', $data['cost_type']);
        $this->db->bind(':amount', $data['amount']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':date_incurred', $data['date_incurred']);
        $this->db->bind(':fiscal_year', $data['fiscal_year']);
        $this->db->bind(':budget_category', $data['budget_category']);
        $this->db->bind(':created_by', $data['created_by']);

        return $this->db->execute();
    }

    /**
     * Get costs for an asset
     *
     * @param int $assetId
     * @return array
     */
    public function getAssetCosts($assetId) {
        $this->db->query('SELECT ac.*, u.name as created_by_name
                          FROM asset_costs ac
                          LEFT JOIN users u ON ac.created_by = u.id
                          WHERE ac.asset_id = :asset_id
                          ORDER BY ac.date_incurred DESC');
        $this->db->bind(':asset_id', $assetId);
        return $this->db->resultSet();
    }

    /**
     * Get total cost of ownership for an asset
     *
     * @param int $assetId
     * @return array
     */
    public function getTotalCostOfOwnership($assetId) {
        $this->db->query('SELECT
                            SUM(CASE WHEN cost_type = "acquisition" THEN amount ELSE 0 END) as acquisition_cost,
                            SUM(CASE WHEN cost_type = "maintenance" THEN amount ELSE 0 END) as maintenance_cost,
                            SUM(CASE WHEN cost_type = "upgrade" THEN amount ELSE 0 END) as upgrade_cost,
                            SUM(CASE WHEN cost_type = "repair" THEN amount ELSE 0 END) as repair_cost,
                            SUM(CASE WHEN cost_type = "license" THEN amount ELSE 0 END) as license_cost,
                            SUM(CASE WHEN cost_type = "other" THEN amount ELSE 0 END) as other_cost,
                            SUM(amount) as total_cost
                          FROM asset_costs
                          WHERE asset_id = :asset_id');
        $this->db->bind(':asset_id', $assetId);
        return $this->db->single();
    }

    /**
     * Get costs by fiscal year
     *
     * @param int $fiscalYear
     * @return array
     */
    public function getCostsByFiscalYear($fiscalYear = null) {
        if ($fiscalYear === null) {
            // Default to current fiscal year
            $currentMonth = date('n');
            $currentYear = date('Y');
            // Assuming fiscal year starts in July
            $fiscalYear = ($currentMonth >= 7) ? $currentYear : $currentYear - 1;
        }

        $this->db->query('SELECT
                            cost_type,
                            SUM(amount) as total_amount,
                            COUNT(DISTINCT asset_id) as asset_count
                          FROM asset_costs
                          WHERE fiscal_year = :fiscal_year
                          GROUP BY cost_type
                          ORDER BY total_amount DESC');
        $this->db->bind(':fiscal_year', $fiscalYear);
        return $this->db->resultSet();
    }

    /**
     * Get monthly costs for a fiscal year
     *
     * @param int $fiscalYear
     * @return array
     */
    public function getMonthlyCosts($fiscalYear = null) {
        if ($fiscalYear === null) {
            // Default to current fiscal year
            $currentMonth = date('n');
            $currentYear = date('Y');
            // Assuming fiscal year starts in July
            $fiscalYear = ($currentMonth >= 7) ? $currentYear : $currentYear - 1;
        }

        // Calculate fiscal year start and end dates
        $startDate = ($fiscalYear - 1) . '-07-01';
        $endDate = $fiscalYear . '-06-30';

        $this->db->query('SELECT
                            DATE_FORMAT(date_incurred, "%Y-%m") as month,
                            SUM(amount) as total_amount,
                            COUNT(DISTINCT asset_id) as asset_count
                          FROM asset_costs
                          WHERE date_incurred BETWEEN :start_date AND :end_date
                          GROUP BY DATE_FORMAT(date_incurred, "%Y-%m")
                          ORDER BY month ASC');
        $this->db->bind(':start_date', $startDate);
        $this->db->bind(':end_date', $endDate);
        return $this->db->resultSet();
    }

    /**
     * Get costs by equipment type
     *
     * @param int $fiscalYear
     * @return array
     */
    public function getCostsByEquipmentType($fiscalYear = null) {
        if ($fiscalYear === null) {
            // Default to current fiscal year
            $currentMonth = date('n');
            $currentYear = date('Y');
            // Assuming fiscal year starts in July
            $fiscalYear = ($currentMonth >= 7) ? $currentYear : $currentYear - 1;
        }

        $this->db->query('SELECT
                            a.equipment_type,
                            SUM(ac.amount) as total_amount,
                            COUNT(DISTINCT a.id) as asset_count,
                            AVG(ac.amount) as average_cost
                          FROM asset_costs ac
                          JOIN assets a ON ac.asset_id = a.id
                          WHERE ac.fiscal_year = :fiscal_year
                          GROUP BY a.equipment_type
                          ORDER BY total_amount DESC');
        $this->db->bind(':fiscal_year', $fiscalYear);
        return $this->db->resultSet();
    }

    /**
     * Calculate depreciation for an asset
     *
     * @param int $assetId
     * @return array
     */
    public function calculateDepreciation($assetId) {
        // Get asset details
        $this->db->query('SELECT a.*,
                            (SELECT SUM(amount) FROM asset_costs
                             WHERE asset_id = a.id AND cost_type = "acquisition") as acquisition_cost
                          FROM assets a
                          WHERE a.id = :asset_id');
        $this->db->bind(':asset_id', $assetId);
        $asset = $this->db->single();

        if (!$asset || empty($asset->acquisition_cost)) {
            return false;
        }

        // Get depreciation settings for this equipment type
        $this->db->query('SELECT * FROM depreciation_settings WHERE equipment_type = :equipment_type');
        $this->db->bind(':equipment_type', $asset->equipment_type);
        $settings = $this->db->single();

        // Default settings if none found
        if (!$settings) {
            $settings = (object)[
                'useful_life_years' => 5,
                'depreciation_method' => 'straight_line',
                'salvage_value_percentage' => 10
            ];
        }

        $acquisitionCost = $asset->acquisition_cost;
        $salvageValue = $acquisitionCost * ($settings->salvage_value_percentage / 100);
        $depreciableAmount = $acquisitionCost - $salvageValue;
        $usefulLifeYears = max(1, $settings->useful_life_years); // Ensure at least 1 year to prevent division by zero

        // Calculate age in years
        $acquisitionDate = new DateTime($asset->acquisition_date);
        $today = new DateTime();
        $ageInYears = $today->diff($acquisitionDate)->y;
        $ageInYears = min($ageInYears, $usefulLifeYears); // Cap at useful life

        // Calculate current value based on depreciation method
        $currentValue = 0;
        switch ($settings->depreciation_method) {
            case 'straight_line':
                $annualDepreciation = $depreciableAmount / $usefulLifeYears;
                $accumulatedDepreciation = $annualDepreciation * $ageInYears;
                $currentValue = $acquisitionCost - $accumulatedDepreciation;
                break;

            case 'declining_balance':
                $rate = 2 / $usefulLifeYears; // Double declining balance
                $currentValue = $acquisitionCost;
                for ($i = 0; $i < $ageInYears; $i++) {
                    $currentValue -= ($currentValue * $rate);
                }
                break;

            case 'sum_of_years_digits':
                $sumOfYears = ($usefulLifeYears * ($usefulLifeYears + 1)) / 2;
                $accumulatedDepreciation = 0;
                for ($i = 1; $i <= $ageInYears; $i++) {
                    $yearDepreciation = ($depreciableAmount * ($usefulLifeYears - $i + 1)) / $sumOfYears;
                    $accumulatedDepreciation += $yearDepreciation;
                }
                $currentValue = $acquisitionCost - $accumulatedDepreciation;
                break;
        }

        // Ensure current value doesn't go below salvage value
        $currentValue = max($currentValue, $salvageValue);

        // Calculate annual depreciation and remaining value percentage safely
        $annualDepreciation = $depreciableAmount / $usefulLifeYears; // Safe because $usefulLifeYears is at least 1
        $remainingValuePercentage = ($acquisitionCost > 0) ? ($currentValue / $acquisitionCost) * 100 : 0;

        return [
            'acquisition_cost' => $acquisitionCost,
            'salvage_value' => $salvageValue,
            'depreciable_amount' => $depreciableAmount,
            'useful_life_years' => $usefulLifeYears,
            'age_in_years' => $ageInYears,
            'current_value' => $currentValue,
            'depreciation_method' => $settings->depreciation_method,
            'annual_depreciation' => $annualDepreciation,
            'accumulated_depreciation' => $acquisitionCost - $currentValue,
            'remaining_value_percentage' => $remainingValuePercentage
        ];
    }

    /**
     * Generate budget forecast
     *
     * @param int $fiscalYear
     * @return array
     */
    public function generateBudgetForecast($fiscalYear = null) {
        if ($fiscalYear === null) {
            // Default to next fiscal year
            $currentMonth = date('n');
            $currentYear = date('Y');
            // Assuming fiscal year starts in July
            $fiscalYear = ($currentMonth >= 7) ? $currentYear + 1 : $currentYear;
        }

        // Get previous fiscal year for historical data
        $previousFiscalYear = $fiscalYear - 1;

        // Get historical costs by category
        $this->db->query('SELECT
                            cost_type as category,
                            SUM(amount) as historical_amount
                          FROM asset_costs
                          WHERE fiscal_year = :previous_fiscal_year
                          GROUP BY cost_type');
        $this->db->bind(':previous_fiscal_year', $previousFiscalYear);
        $historicalCosts = $this->db->resultSet();

        // Get assets approaching end of life in the forecast fiscal year
        $eolStartDate = ($fiscalYear - 1) . '-07-01';
        $eolEndDate = $fiscalYear . '-06-30';

        $this->db->query('SELECT
                            COUNT(*) as count,
                            SUM((SELECT SUM(amount) FROM asset_costs WHERE asset_id = a.id AND cost_type = "acquisition")) as replacement_cost
                          FROM assets a
                          WHERE a.estimated_useful_life BETWEEN :eol_start_date AND :eol_end_date');
        $this->db->bind(':eol_start_date', $eolStartDate);
        $this->db->bind(':eol_end_date', $eolEndDate);
        $eolAssets = $this->db->single();

        // Generate forecast with 5% increase for maintenance and 10% for upgrades
        $forecast = [];
        foreach ($historicalCosts as $cost) {
            $projectedAmount = $cost->historical_amount;

            // Apply growth factors based on category
            switch ($cost->category) {
                case 'maintenance':
                    $projectedAmount *= 1.05; // 5% increase
                    break;
                case 'upgrade':
                    $projectedAmount *= 1.10; // 10% increase
                    break;
                case 'license':
                    $projectedAmount *= 1.03; // 3% increase
                    break;
            }

            // Calculate growth percentage safely
            $growthPercentage = ($cost->historical_amount > 0)
                ? (($projectedAmount / $cost->historical_amount) - 1) * 100
                : 0;

            $forecast[] = [
                'category' => $cost->category,
                'historical_amount' => $cost->historical_amount,
                'projected_amount' => $projectedAmount,
                'growth_percentage' => $growthPercentage
            ];
        }

        // Add replacement costs for EOL assets
        if ($eolAssets && $eolAssets->count > 0) {
            $forecast[] = [
                'category' => 'replacement',
                'historical_amount' => 0,
                'projected_amount' => $eolAssets->replacement_cost ?? ($eolAssets->count * 1000), // Fallback if no cost data
                'growth_percentage' => 100,
                'eol_assets_count' => $eolAssets->count
            ];
        }

        return $forecast;
    }
}
