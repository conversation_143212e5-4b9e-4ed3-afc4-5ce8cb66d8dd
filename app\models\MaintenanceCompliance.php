<?php
class MaintenanceCompliance {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    /**
     * Get compliance status for all assets
     *
     * @return array
     */
    public function getAllComplianceStatus() {
        try {
            $this->db->query('SELECT c.*, a.computer_host_name, a.equipment_type, g.name as guideline_name, g.importance
                            FROM maintenance_compliance c
                            JOIN assets a ON c.asset_id = a.id
                            JOIN maintenance_guidelines g ON c.guideline_id = g.id
                            ORDER BY c.compliance_status DESC, g.importance DESC, c.next_due_date ASC');
            $result = $this->db->resultSet();

            // Log the number of records retrieved
            error_log("MaintenanceCompliance::getAllComplianceStatus - Retrieved " . count($result) . " compliance status records");

            return $result;
        } catch (Exception $e) {
            error_log("MaintenanceCompliance::getAllComplianceStatus - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get compliance status for a specific asset
     *
     * @param int $assetId
     * @return array
     */
    public function getAssetComplianceStatus($assetId) {
        try {
            $this->db->query('SELECT c.*, g.name as guideline_name, g.description as guideline_description,
                            g.frequency_days, g.importance
                            FROM maintenance_compliance c
                            JOIN maintenance_guidelines g ON c.guideline_id = g.id
                            WHERE c.asset_id = :asset_id
                            ORDER BY c.compliance_status DESC, g.importance DESC, c.next_due_date ASC');
            $this->db->bind(':asset_id', $assetId);
            $result = $this->db->resultSet();

            // Log the number of records retrieved
            error_log("MaintenanceCompliance::getAssetComplianceStatus - Retrieved " . count($result) . " compliance status records for asset ID {$assetId}");

            return $result;
        } catch (Exception $e) {
            error_log("MaintenanceCompliance::getAssetComplianceStatus - Error for asset ID {$assetId}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get compliance status for a specific guideline across all assets
     *
     * @param int $guidelineId
     * @return array
     */
    public function getGuidelineComplianceStatus($guidelineId) {
        try {
            $this->db->query('SELECT c.*, a.computer_host_name, a.equipment_type
                            FROM maintenance_compliance c
                            JOIN assets a ON c.asset_id = a.id
                            WHERE c.guideline_id = :guideline_id
                            ORDER BY c.compliance_status DESC, c.next_due_date ASC');
            $this->db->bind(':guideline_id', $guidelineId);
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("MaintenanceCompliance::getGuidelineComplianceStatus - Error for guideline ID {$guidelineId}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get overdue maintenance tasks
     *
     * @return array
     */
    public function getOverdueMaintenance() {
        try {
            // Get current date for comparison
            $today = date('Y-m-d');

            $this->db->query('SELECT c.*, a.computer_host_name, a.equipment_type, g.name as guideline_name, g.importance
                            FROM maintenance_compliance c
                            JOIN assets a ON c.asset_id = a.id
                            JOIN maintenance_guidelines g ON c.guideline_id = g.id
                            WHERE c.compliance_status = "overdue"
                            AND c.next_due_date < :today
                            ORDER BY g.importance DESC, c.next_due_date ASC');
            $this->db->bind(':today', $today);

            $result = $this->db->resultSet();
            error_log("MaintenanceCompliance::getOverdueMaintenance - Found " . count($result) . " overdue maintenance tasks");
            return $result;
        } catch (Exception $e) {
            error_log("MaintenanceCompliance::getOverdueMaintenance - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get maintenance tasks due soon
     *
     * @param int $daysAhead
     * @return array
     */
    public function getMaintenanceDueSoon($daysAhead = 30) {
        try {
            // Get current date and future date for comparison
            $today = date('Y-m-d');
            $futureDate = date('Y-m-d', strtotime("+$daysAhead days"));

            $this->db->query('SELECT c.*, a.computer_host_name, a.equipment_type, g.name as guideline_name, g.importance
                            FROM maintenance_compliance c
                            JOIN assets a ON c.asset_id = a.id
                            JOIN maintenance_guidelines g ON c.guideline_id = g.id
                            WHERE c.compliance_status = "due_soon"
                            AND c.next_due_date BETWEEN :today AND :future_date
                            ORDER BY g.importance DESC, c.next_due_date ASC');
            $this->db->bind(':today', $today);
            $this->db->bind(':future_date', $futureDate);

            $result = $this->db->resultSet();
            error_log("MaintenanceCompliance::getMaintenanceDueSoon - Found " . count($result) . " maintenance tasks due in the next $daysAhead days");
            return $result;
        } catch (Exception $e) {
            error_log("MaintenanceCompliance::getMaintenanceDueSoon - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get compliant endpoints
     *
     * @return array
     */
    public function getCompliantEndpoints() {
        try {
            $this->db->query('SELECT c.*, a.computer_host_name, a.equipment_type, g.name as guideline_name, g.importance
                            FROM maintenance_compliance c
                            JOIN assets a ON c.asset_id = a.id
                            JOIN maintenance_guidelines g ON c.guideline_id = g.id
                            WHERE c.compliance_status = "compliant"
                            ORDER BY g.importance DESC, c.next_due_date ASC');

            $result = $this->db->resultSet();
            error_log("MaintenanceCompliance::getCompliantEndpoints - Found " . count($result) . " compliant endpoints");
            return $result;
        } catch (Exception $e) {
            error_log("MaintenanceCompliance::getCompliantEndpoints - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update compliance status for an asset and guideline
     *
     * @param int $assetId
     * @param int $guidelineId
     * @param string $status
     * @param string $lastPerformedDate
     * @param string $nextDueDate
     * @return bool
     */
    public function updateComplianceStatus($assetId, $guidelineId, $status, $lastPerformedDate = null, $nextDueDate = null) {
        try {
            $this->db->query('INSERT INTO maintenance_compliance (asset_id, guideline_id, last_performed_date, next_due_date, compliance_status)
                            VALUES (:asset_id, :guideline_id, :last_performed_date, :next_due_date, :compliance_status)
                            ON DUPLICATE KEY UPDATE
                            last_performed_date = :last_performed_date,
                            next_due_date = :next_due_date,
                            compliance_status = :compliance_status,
                            updated_at = NOW()');

            $this->db->bind(':asset_id', $assetId);
            $this->db->bind(':guideline_id', $guidelineId);
            $this->db->bind(':last_performed_date', $lastPerformedDate);
            $this->db->bind(':next_due_date', $nextDueDate);
            $this->db->bind(':compliance_status', $status);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log("MaintenanceCompliance::updateComplianceStatus - Error for asset ID {$assetId}, guideline ID {$guidelineId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Calculate and update compliance status for all assets
     *
     * @return bool
     */
    public function updateAllComplianceStatus() {
        try {
            // Get all assets
            $this->db->query('SELECT id, equipment_type FROM assets');
            $assets = $this->db->resultSet();
            error_log("MaintenanceCompliance::updateAllComplianceStatus - Processing " . count($assets) . " assets");

            // Get all guidelines
            $this->db->query('SELECT id, equipment_type, frequency_days FROM maintenance_guidelines');
            $guidelines = $this->db->resultSet();
            error_log("MaintenanceCompliance::updateAllComplianceStatus - Found " . count($guidelines) . " guidelines");

            // Get all maintenance history and guideline implementations
            $this->db->query('SELECT mh.asset_id, mh.id as maintenance_id, mh.maintenance_type,
                            mh.performed_date as last_performed, mh.status,
                            mgi.guideline_id
                            FROM maintenance_history mh
                            LEFT JOIN maintenance_guideline_implementation mgi ON mh.id = mgi.maintenance_id
                            WHERE mh.status = "completed"
                            ORDER BY mh.performed_date DESC');
            $maintenanceHistory = $this->db->resultSet();

            // Create a lookup for maintenance history by guideline
            $maintenanceLookup = [];
            foreach ($maintenanceHistory as $record) {
                if (!empty($record->guideline_id)) {
                    $key = $record->asset_id . '_' . $record->guideline_id;
                    // Only store the most recent maintenance date for each asset-guideline pair
                    if (!isset($maintenanceLookup[$key]) || strtotime($record->last_performed) > strtotime($maintenanceLookup[$key])) {
                        $maintenanceLookup[$key] = $record->last_performed;
                    }
                }
            }

            error_log("MaintenanceCompliance::updateAllComplianceStatus - Found " . count($maintenanceLookup) . " unique asset-guideline maintenance records");


            // Process each asset
            foreach ($assets as $asset) {
                // Find applicable guidelines for this asset type
                $applicableGuidelines = array_filter($guidelines, function($guideline) use ($asset) {
                    return $guideline->equipment_type == $asset->equipment_type;
                });

                // Update compliance for each applicable guideline
                foreach ($applicableGuidelines as $guideline) {
                    try {
                        // Look for maintenance history for this asset and guideline
                        $maintenanceKey = $asset->id . '_' . $guideline->id;
                        $lastPerformed = isset($maintenanceLookup[$maintenanceKey]) ? $maintenanceLookup[$maintenanceKey] : null;

                        // Calculate next due date and status
                        $nextDueDate = null;
                        $status = 'not_applicable';

                        if ($lastPerformed) {
                            // Calculate next due date based on frequency
                            $nextDueDate = date('Y-m-d', strtotime($lastPerformed . ' + ' . $guideline->frequency_days . ' days'));

                            // Determine status based on next due date
                            $today = date('Y-m-d');
                            $dueSoonDate = date('Y-m-d', strtotime($today . ' + 30 days'));

                            if ($nextDueDate < $today) {
                                $status = 'overdue';
                            } elseif ($nextDueDate <= $dueSoonDate) {
                                $status = 'due_soon';
                            } else {
                                $status = 'compliant';
                            }

                            error_log("MaintenanceCompliance::updateAllComplianceStatus - Asset {$asset->id}, Guideline {$guideline->id}: Last performed {$lastPerformed}, Next due {$nextDueDate}, Status {$status}");
                        } else {
                            // If no maintenance history, set as not applicable
                            error_log("MaintenanceCompliance::updateAllComplianceStatus - Asset {$asset->id}, Guideline {$guideline->id}: No maintenance history found, setting as not_applicable");
                        }

                        // Update compliance status
                        $result = $this->updateComplianceStatus($asset->id, $guideline->id, $status, $lastPerformed, $nextDueDate);
                        if (!$result) {
                            error_log("MaintenanceCompliance::updateAllComplianceStatus - Failed to update compliance status for asset {$asset->id}, guideline {$guideline->id}");
                        }
                    } catch (Exception $e) {
                        error_log("MaintenanceCompliance::updateAllComplianceStatus - Error processing guideline {$guideline->id} for asset {$asset->id}: " . $e->getMessage());
                        // Continue with next guideline
                        continue;
                    }
                }
            }

            return true;
        } catch (Exception $e) {
            error_log("MaintenanceCompliance::updateAllComplianceStatus - Fatal error: " . $e->getMessage());
            // Return true to prevent fatal error on the page
            return true;
        }
    }

    /**
     * Record checklist completion
     *
     * @param int $maintenanceHistoryId
     * @param int $checklistId
     * @param bool $completed
     * @param int $completedBy
     * @param string $notes
     * @return bool
     */
    public function recordChecklistCompletion($maintenanceHistoryId, $checklistId, $completed, $completedBy, $notes = '') {
        $this->db->query('INSERT INTO maintenance_checklist_completion
                          (maintenance_history_id, checklist_id, completed, completed_by, completed_date, notes)
                          VALUES (:maintenance_history_id, :checklist_id, :completed, :completed_by, NOW(), :notes)
                          ON DUPLICATE KEY UPDATE
                          completed = :completed,
                          completed_by = :completed_by,
                          completed_date = NOW(),
                          notes = :notes');

        $this->db->bind(':maintenance_history_id', $maintenanceHistoryId);
        $this->db->bind(':checklist_id', $checklistId);
        $this->db->bind(':completed', $completed ? 1 : 0);
        $this->db->bind(':completed_by', $completedBy);
        $this->db->bind(':notes', $notes);

        return $this->db->execute();
    }

    /**
     * Get checklist completion for a maintenance record
     *
     * @param int $maintenanceHistoryId
     * @return array
     */
    public function getChecklistCompletion($maintenanceHistoryId) {
        $this->db->query('SELECT c.*, cl.description, cl.step_number, cl.is_required, u.name as completed_by_name
                          FROM maintenance_checklist_completion c
                          JOIN maintenance_checklist cl ON c.checklist_id = cl.id
                          LEFT JOIN users u ON c.completed_by = u.id
                          WHERE c.maintenance_history_id = :maintenance_history_id
                          ORDER BY cl.step_number');
        $this->db->bind(':maintenance_history_id', $maintenanceHistoryId);
        return $this->db->resultSet();
    }
}
