<?php
class MaintenanceGuideline {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    /**
     * Get all maintenance guidelines
     *
     * @return array
     */
    public function getAllGuidelines() {
        $this->db->query('SELECT * FROM maintenance_guidelines ORDER BY equipment_type, name');
        return $this->db->resultSet();
    }

    /**
     * Get maintenance guidelines by equipment type
     *
     * @param string $equipmentType
     * @return array
     */
    public function getGuidelinesByEquipmentType($equipmentType) {
        $this->db->query('SELECT * FROM maintenance_guidelines WHERE equipment_type = :equipment_type ORDER BY name');
        $this->db->bind(':equipment_type', $equipmentType);
        return $this->db->resultSet();
    }

    /**
     * Get maintenance guideline by ID
     *
     * @param int $id
     * @return object
     */
    public function getGuidelineById($id) {
        $this->db->query('SELECT * FROM maintenance_guidelines WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    /**
     * Get checklist items for a guideline
     *
     * @param int $guidelineId
     * @return array
     */
    public function getChecklistItems($guidelineId) {
        // Use DISTINCT to ensure we only get unique checklist items
        $this->db->query('SELECT DISTINCT id, guideline_id, step_number, description, is_required, created_at, updated_at
                         FROM maintenance_checklist
                         WHERE guideline_id = :guideline_id
                         ORDER BY step_number');
        $this->db->bind(':guideline_id', $guidelineId);

        $results = $this->db->resultSet();

        // Additional deduplication in PHP to ensure uniqueness by ID
        $uniqueItems = [];
        $uniqueIds = [];

        foreach ($results as $item) {
            if (!in_array($item->id, $uniqueIds)) {
                $uniqueIds[] = $item->id;
                $uniqueItems[] = $item;
            }
        }

        return $uniqueItems;
    }

    /**
     * Add a new maintenance guideline
     *
     * @param array $data
     * @return int|bool The new guideline ID or false on failure
     */
    public function addGuideline($data) {
        $this->db->query('INSERT INTO maintenance_guidelines (name, description, equipment_type, frequency_days, importance)
                          VALUES (:name, :description, :equipment_type, :frequency_days, :importance)');

        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':equipment_type', $data['equipment_type']);
        $this->db->bind(':frequency_days', $data['frequency_days']);
        $this->db->bind(':importance', $data['importance']);

        if($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }

    /**
     * Update a maintenance guideline
     *
     * @param array $data
     * @return bool
     */
    public function updateGuideline($data) {
        $this->db->query('UPDATE maintenance_guidelines
                          SET name = :name,
                              description = :description,
                              equipment_type = :equipment_type,
                              frequency_days = :frequency_days,
                              importance = :importance
                          WHERE id = :id');

        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':equipment_type', $data['equipment_type']);
        $this->db->bind(':frequency_days', $data['frequency_days']);
        $this->db->bind(':importance', $data['importance']);

        return $this->db->execute();
    }

    /**
     * Delete a maintenance guideline
     *
     * @param int $id
     * @return bool
     */
    public function deleteGuideline($id) {
        $this->db->query('DELETE FROM maintenance_guidelines WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    /**
     * Add a checklist item to a guideline
     *
     * @param array $data
     * @return int|bool The new checklist item ID or false on failure
     */
    public function addChecklistItem($data) {
        $this->db->query('INSERT INTO maintenance_checklist (guideline_id, step_number, description, is_required)
                          VALUES (:guideline_id, :step_number, :description, :is_required)');

        $this->db->bind(':guideline_id', $data['guideline_id']);
        $this->db->bind(':step_number', $data['step_number']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':is_required', $data['is_required']);

        if($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }

    /**
     * Update a checklist item
     *
     * @param array $data
     * @return bool
     */
    public function updateChecklistItem($data) {
        $this->db->query('UPDATE maintenance_checklist
                          SET step_number = :step_number,
                              description = :description,
                              is_required = :is_required
                          WHERE id = :id');

        $this->db->bind(':id', $data['id']);
        $this->db->bind(':step_number', $data['step_number']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':is_required', $data['is_required']);

        return $this->db->execute();
    }

    /**
     * Delete a checklist item
     *
     * @param int $id
     * @return bool
     */
    public function deleteChecklistItem($id) {
        $this->db->query('DELETE FROM maintenance_checklist WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    /**
     * Get the next step number for a guideline
     *
     * @param int $guidelineId
     * @return int
     */
    public function getNextStepNumber($guidelineId) {
        $this->db->query('SELECT MAX(step_number) as max_step FROM maintenance_checklist WHERE guideline_id = :guideline_id');
        $this->db->bind(':guideline_id', $guidelineId);
        $result = $this->db->single();
        return ($result->max_step ?? 0) + 1;
    }

    /**
     * Get a specific checklist item by ID and guideline ID
     *
     * @param int $itemId
     * @param int $guidelineId
     * @return object|false
     */
    public function getChecklistItemById($itemId, $guidelineId) {
        $this->db->query('SELECT * FROM maintenance_checklist WHERE id = :id AND guideline_id = :guideline_id');
        $this->db->bind(':id', $itemId);
        $this->db->bind(':guideline_id', $guidelineId);
        return $this->db->single();
    }
}
