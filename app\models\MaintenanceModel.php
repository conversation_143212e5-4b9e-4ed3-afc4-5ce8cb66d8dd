<?php
class MaintenanceModel {
    private $db;

    public function __construct() {
        $this->db = new Database;

        // Ensure the maintenance_guideline_implementation table exists
        $this->ensureGuidelineImplementationTableExists();
    }

    /**
     * Ensure the maintenance_guideline_implementation table exists
     * This is called once during initialization to prevent repeated table creation
     *
     * @return bool True if the table exists or was created successfully
     */
    public function ensureGuidelineImplementationTableExists() {
        try {
            // Use a static flag to prevent multiple creation attempts in the same request
            static $tableChecked = false;
            static $tableStatus = false;

            if ($tableChecked) {
                return $tableStatus;
            }

            // Check if the table exists
            $this->db->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
            $tableExists = $this->db->rowCount() > 0;
            error_log("MaintenanceModel::ensureGuidelineImplementationTableExists - Table exists check: " . ($tableExists ? 'Yes' : 'No'));

            if ($tableExists) {
                $tableChecked = true;
                $tableStatus = true;
                return true;
            }

            // Try direct SQL first (most reliable)
            try {
                // Use ENGINE=InnoDB to ensure transaction support
                // No UNIQUE KEY to allow multiple instances of the same maintenance_id and guideline_id
                // Add foreign key constraints to ensure data integrity
                $sql = "CREATE TABLE IF NOT EXISTS maintenance_guideline_implementation (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    maintenance_id INT NOT NULL,
                    guideline_id INT NOT NULL,
                    implemented_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX (maintenance_id, guideline_id)
                ) ENGINE=InnoDB";

                $this->db->query($sql);
                $result = $this->db->execute();
                error_log("MaintenanceModel::ensureGuidelineImplementationTableExists - Created table: " . ($result ? 'Success' : 'Failed'));

                if (!$result) {
                    // Try a simpler version as a last resort
                    $this->db->query("CREATE TABLE IF NOT EXISTS maintenance_guideline_implementation (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        maintenance_id INT NOT NULL,
                        guideline_id INT NOT NULL,
                        implemented_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )");
                    $result = $this->db->execute();
                    error_log("MaintenanceModel::ensureGuidelineImplementationTableExists - Created simple table: " . ($result ? 'Success' : 'Failed'));
                }
            } catch (Exception $e) {
                error_log("MaintenanceModel::ensureGuidelineImplementationTableExists - Error creating table: " . $e->getMessage());

                // Try a direct PDO query as a last resort
                try {
                    $pdo = $this->db->getPDO();
                    $sql = "CREATE TABLE IF NOT EXISTS maintenance_guideline_implementation (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        maintenance_id INT NOT NULL,
                        guideline_id INT NOT NULL,
                        implemented_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )";
                    $result = $pdo->exec($sql);
                    error_log("MaintenanceModel::ensureGuidelineImplementationTableExists - Direct PDO creation: " . ($result !== false ? 'Success' : 'Failed'));
                } catch (Exception $e2) {
                    error_log("MaintenanceModel::ensureGuidelineImplementationTableExists - Error with direct PDO: " . $e2->getMessage());
                }
            }

            // Verify the table was created
            $this->db->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
            $tableCreated = $this->db->rowCount() > 0;
            error_log("MaintenanceModel::ensureGuidelineImplementationTableExists - Table exists after creation: " . ($tableCreated ? 'Yes' : 'No'));

            if ($tableCreated) {
                // Add an index for better performance (not a unique key)
                try {
                    $this->db->query("ALTER TABLE maintenance_guideline_implementation
                                     ADD INDEX idx_maintenance_guideline (maintenance_id, guideline_id)");
                    $this->db->execute();
                } catch (Exception $e) {
                    // Ignore errors here, the index might already exist or syntax might not be supported
                    error_log("MaintenanceModel::ensureGuidelineImplementationTableExists - Note: Could not add index: " . $e->getMessage());
                }
            }

            $tableChecked = true;
            $tableStatus = $tableCreated;
            return $tableCreated;
        } catch (Exception $e) {
            error_log("MaintenanceModel::ensureGuidelineImplementationTableExists - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get maintenance history for an asset
     *
     * @param int $assetId
     * @return array
     */
    public function getMaintenanceHistory($assetId) {
        $this->db->query('SELECT m.*, u.name as performed_by_name
                          FROM maintenance_history m
                          LEFT JOIN users u ON m.performed_by = u.id
                          WHERE m.asset_id = :asset_id
                          ORDER BY m.performed_date DESC');
        $this->db->bind(':asset_id', $assetId);
        return $this->db->resultSet();
    }

    /**
     * Get maintenance record by ID
     *
     * @param int $id
     * @return object|false
     */
    public function getMaintenanceById($id) {
        $this->db->query('SELECT m.*, u.name as performed_by_name
                          FROM maintenance_history m
                          LEFT JOIN users u ON m.performed_by = u.id
                          WHERE m.id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    /**
     * Get all maintenance history records
     *
     * @param int $limit Optional limit for number of records to return
     * @param int $offset Optional offset for pagination
     * @return array
     */
    public function getAllMaintenanceHistory($limit = null, $offset = 0) {
        $sql = 'SELECT m.*, a.computer_host_name, a.equipment_type, a.serial_number, u.name as performed_by_name
                FROM maintenance_history m
                JOIN assets a ON m.asset_id = a.id
                LEFT JOIN users u ON m.performed_by = u.id
                ORDER BY m.performed_date DESC';

        if ($limit !== null) {
            $sql .= ' LIMIT :limit OFFSET :offset';
        }

        $this->db->query($sql);

        if ($limit !== null) {
            $this->db->bind(':limit', $limit);
            $this->db->bind(':offset', $offset);
        }

        return $this->db->resultSet();
    }

    /**
     * Count all maintenance history records
     *
     * @return int
     */
    public function countAllMaintenanceHistory() {
        $this->db->query('SELECT COUNT(*) as total FROM maintenance_history');
        $result = $this->db->single();
        return $result->total;
    }

    /**
     * Add maintenance record
     *
     * @param array $data
     * @return int|bool The new maintenance ID or false on failure
     */
    public function addMaintenance($data) {
        $this->db->query('INSERT INTO maintenance_history (asset_id, maintenance_type, description, performed_by,
                          performed_date, cost, next_scheduled_date, status)
                          VALUES (:asset_id, :maintenance_type, :description, :performed_by,
                          :performed_date, :cost, :next_scheduled_date, :status)');

        $this->db->bind(':asset_id', $data['asset_id']);
        $this->db->bind(':maintenance_type', $data['maintenance_type']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':performed_by', $data['performed_by']);
        $this->db->bind(':performed_date', $data['performed_date']);
        $this->db->bind(':cost', $data['cost']);
        $this->db->bind(':next_scheduled_date', $data['next_scheduled_date']);
        $this->db->bind(':status', $data['status']);

        if($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }

    /**
     * Get assets due for maintenance
     *
     * @param int $daysAhead Look ahead days
     * @return array
     */
    public function getAssetsDueForMaintenance($daysAhead = 30) {
        $this->db->query('SELECT a.id, a.computer_host_name, a.equipment_type, a.serial_number,
                          m.next_scheduled_date, m.maintenance_type, DATEDIFF(m.next_scheduled_date, CURDATE()) as days_remaining
                          FROM assets a
                          JOIN maintenance_history m ON a.id = m.asset_id
                          WHERE m.next_scheduled_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :days_ahead DAY)
                          AND m.status = "scheduled"
                          ORDER BY m.next_scheduled_date ASC');
        $this->db->bind(':days_ahead', $daysAhead);
        return $this->db->resultSet();
    }

    /**
     * Calculate health score for an asset
     *
     * @param int $assetId
     * @return float
     */
    public function calculateAssetHealthScore($assetId) {
        // Get asset details
        $this->db->query('SELECT * FROM assets WHERE id = :asset_id');
        $this->db->bind(':asset_id', $assetId);
        $asset = $this->db->single();

        if (!$asset) {
            return false;
        }

        // Get maintenance history
        $maintenanceHistory = $this->getMaintenanceHistory($assetId);

        // Get failure history
        $this->db->query('SELECT * FROM asset_failures WHERE asset_id = :asset_id');
        $this->db->bind(':asset_id', $assetId);
        $failures = $this->db->resultSet();

        // Calculate age in days
        $acquisitionDate = new DateTime($asset->acquisition_date);
        $today = new DateTime();
        $ageInDays = $today->diff($acquisitionDate)->days;

        // Calculate expected lifetime in days
        $expectedLifetime = ASSET_LIFETIME; 
        if (!empty($asset->estimated_useful_life)) {
            $eolDate = new DateTime($asset->estimated_useful_life);
            $expectedLifetime = $eolDate->diff($acquisitionDate)->days;
        }

        // Ensure expected lifetime is never zero to prevent division by zero
        $expectedLifetime = max(1, $expectedLifetime);

        // Base score starts at 100 and decreases with age
        $ageScore = 100 - (($ageInDays / $expectedLifetime) * 50); // Age accounts for 50% max
        $ageScore = max(0, min(50, $ageScore)); // Clamp between 0-50

        // Maintenance score - more recent maintenance improves score
        $maintenanceScore = 0;
        if (count($maintenanceHistory) > 0) {
            $lastMaintenance = new DateTime($maintenanceHistory[0]->performed_date);
            $daysSinceLastMaintenance = $today->diff($lastMaintenance)->days;

            // Maintenance within last 90 days is good
            // Ensure we don't divide by zero (though 90 is a constant)
            $maintenanceScore = 25 - (($daysSinceLastMaintenance / 90) * 25);
            $maintenanceScore = max(0, min(25, $maintenanceScore)); // Clamp between 0-25
        }

        // Failure score - more failures decrease score
        $failureScore = 25;
        $failureCount = count($failures);
        if ($failureCount > 0) {
            $failureScore = 25 - ($failureCount * 5); // Each failure reduces by 5 points
            $failureScore = max(0, $failureScore); // Minimum 0
        }

        // Calculate total health score
        $healthScore = $ageScore + $maintenanceScore + $failureScore;

        // Calculate remaining life estimate
        // Division by 100 is safe as it's a constant
        $remainingLifePercentage = $healthScore / 100;
        $estimatedRemainingLife = round($expectedLifetime * $remainingLifePercentage) - $ageInDays;
        $estimatedRemainingLife = max(0, $estimatedRemainingLife);

        // Calculate failure probability (inverse of health score)
        $failureProbability = 100 - $healthScore;

        // Store the calculated metrics
        $this->db->query('INSERT INTO asset_health_metrics (asset_id, health_score, estimated_remaining_life,
                          failure_probability, last_calculated_at)
                          VALUES (:asset_id, :health_score, :estimated_remaining_life,
                          :failure_probability, NOW())
                          ON DUPLICATE KEY UPDATE
                          health_score = :health_score,
                          estimated_remaining_life = :estimated_remaining_life,
                          failure_probability = :failure_probability,
                          last_calculated_at = NOW()');

        $this->db->bind(':asset_id', $assetId);
        $this->db->bind(':health_score', $healthScore);
        $this->db->bind(':estimated_remaining_life', $estimatedRemainingLife);
        $this->db->bind(':failure_probability', $failureProbability);
        $this->db->execute();

        return [
            'health_score' => $healthScore,
            'estimated_remaining_life' => $estimatedRemainingLife,
            'failure_probability' => $failureProbability
        ];
    }

    /**
     * Get assets with health metrics
     *
     * @param int $limit Optional limit for pagination
     * @param int $offset Optional offset for pagination
     * @param string $search Optional search term for filtering
     * @param string $orderBy Optional column to order by
     * @param string $orderDir Optional direction (ASC or DESC)
     * @return array
     */
    public function getAssetsWithHealthMetrics($limit = null, $offset = 0, $search = '', $orderBy = 'health_score', $orderDir = 'ASC') {
        // Base query
        $sql = 'SELECT a.id, a.computer_host_name, a.equipment_type, a.serial_number,
                a.acquisition_date, a.estimated_useful_life,
                h.health_score, h.estimated_remaining_life, h.failure_probability, h.last_calculated_at
                FROM assets a
                LEFT JOIN asset_health_metrics h ON a.id = h.asset_id';

        // Add search condition if provided
        if (!empty($search)) {
            $sql .= ' WHERE a.computer_host_name LIKE :search
                     OR a.equipment_type LIKE :search
                     OR a.serial_number LIKE :search';
        }

        // Add ordering
        $validColumns = ['computer_host_name', 'equipment_type', 'health_score', 'estimated_remaining_life', 'failure_probability'];
        $validDirections = ['ASC', 'DESC'];

        // Validate order column and direction
        if (!in_array($orderBy, $validColumns)) {
            $orderBy = 'health_score';
        }

        if (!in_array(strtoupper($orderDir), $validDirections)) {
            $orderDir = 'ASC';
        }

        // Add ORDER BY clause
        $sql .= ' ORDER BY ' . ($orderBy === 'health_score' ? 'h.health_score' : 'a.' . $orderBy) . ' ' . $orderDir;

        // Add limit if provided
        if ($limit !== null) {
            $sql .= ' LIMIT :limit OFFSET :offset';
        }

        $this->db->query($sql);

        // Bind parameters
        if (!empty($search)) {
            $this->db->bind(':search', '%' . $search . '%');
        }

        if ($limit !== null) {
            $this->db->bind(':limit', $limit);
            $this->db->bind(':offset', $offset);
        }

        return $this->db->resultSet();
    }

    /**
     * Count total assets with health metrics (for pagination)
     *
     * @param string $search Optional search term for filtering
     * @return int
     */
    public function countAssetsWithHealthMetrics($search = '') {
        // Base query
        $sql = 'SELECT COUNT(*) as total FROM assets a LEFT JOIN asset_health_metrics h ON a.id = h.asset_id';

        // Add search condition if provided
        if (!empty($search)) {
            $sql .= ' WHERE a.computer_host_name LIKE :search
                     OR a.equipment_type LIKE :search
                     OR a.serial_number LIKE :search';
        }

        $this->db->query($sql);

        // Bind parameters
        if (!empty($search)) {
            $this->db->bind(':search', '%' . $search . '%');
        }

        $result = $this->db->single();
        return $result ? $result->total : 0;
    }

    /**
     * Get all scheduled maintenance records for an asset
     *
     * @param int $assetId
     * @return array
     */
    public function getAllScheduledMaintenance($assetId) {
        $this->db->query('SELECT id, maintenance_type, next_scheduled_date, status
                          FROM maintenance_history
                          WHERE asset_id = :asset_id
                          AND status = "scheduled"
                          ORDER BY next_scheduled_date ASC');

        $this->db->bind(':asset_id', $assetId);

        return $this->db->resultSet();
    }

    /**
     * Get scheduled maintenance record ID
     *
     * @param int $assetId
     * @param string $maintenanceType
     * @return int|false
     */
    public function getScheduledMaintenanceId($assetId, $maintenanceType) {
        $this->db->query('SELECT id FROM maintenance_history
                          WHERE asset_id = :asset_id
                          AND maintenance_type = :maintenance_type
                          AND status = "scheduled"
                          ORDER BY next_scheduled_date ASC
                          LIMIT 1');

        $this->db->bind(':asset_id', $assetId);
        $this->db->bind(':maintenance_type', $maintenanceType);

        $result = $this->db->single();
        return $result ? $result->id : false;
    }

    /**
     * Update scheduled maintenance record to completed
     *
     * @param int $assetId
     * @param string $maintenanceType
     * @return bool
     */
    public function updateScheduledMaintenanceStatus($assetId, $maintenanceType) {
        // First get the ID for logging
        $maintenanceId = $this->getScheduledMaintenanceId($assetId, $maintenanceType);

        if (!$maintenanceId) {
            error_log("MaintenanceModel::updateScheduledMaintenanceStatus - No scheduled maintenance found for Asset ID: $assetId, Type: $maintenanceType");
            return false;
        }

        error_log("MaintenanceModel::updateScheduledMaintenanceStatus - Updating maintenance ID: $maintenanceId");

        $this->db->query('UPDATE maintenance_history
                          SET status = "completed",
                              performed_date = CURDATE(),
                              updated_at = NOW()
                          WHERE id = :id');

        $this->db->bind(':id', $maintenanceId);

        return $this->db->execute();
    }

    /**
     * Get checklist items for a guideline
     *
     * Note: This method duplicates functionality in MaintenanceGuideline::getChecklistItems
     * Consider using MaintenanceGuideline::getChecklistItems instead to avoid duplication
     *
     * @param int $guidelineId
     * @return array
     */
    public function getChecklistItems($guidelineId) {
        // Log a warning about the duplicate method
        error_log("WARNING: MaintenanceModel::getChecklistItems called - This duplicates MaintenanceGuideline::getChecklistItems");

        // Use the MaintenanceGuideline model to get checklist items to ensure consistency
        $guidelineModel = new MaintenanceGuideline();
        return $guidelineModel->getChecklistItems($guidelineId);
    }

    /**
     * Get completed checklist items for a maintenance record and guideline
     *
     * @param int $maintenanceId
     * @param int $guidelineId
     * @return array
     */
    public function getCompletedChecklistItems($maintenanceId, $guidelineId) {
        error_log("MaintenanceModel::getCompletedChecklistItems - Checking for maintenance ID: $maintenanceId, guideline ID: $guidelineId");

        $this->db->query('SELECT c.*, cl.description, cl.step_number, cl.is_required, u.name as completed_by_name
                          FROM maintenance_checklist_completion c
                          JOIN maintenance_checklist cl ON c.checklist_id = cl.id
                          LEFT JOIN users u ON c.completed_by = u.id
                          WHERE c.maintenance_history_id = :maintenance_id
                          AND cl.guideline_id = :guideline_id
                          AND c.completed = 1
                          ORDER BY cl.step_number');
        $this->db->bind(':maintenance_id', $maintenanceId);
        $this->db->bind(':guideline_id', $guidelineId);
        $results = $this->db->resultSet();

        error_log("MaintenanceModel::getCompletedChecklistItems - Found " . count($results) . " completed items");

        return $results;
    }

    /**
     * Get all completed checklist items for a maintenance record
     *
     * @param int $maintenanceId
     * @return array
     */
    public function getAllCompletedChecklistItems($maintenanceId) {
        error_log("MaintenanceModel::getAllCompletedChecklistItems - Checking for maintenance ID: $maintenanceId");

        $this->db->query('SELECT c.*, cl.guideline_id, cl.description, cl.step_number, cl.is_required, u.name as completed_by_name
                          FROM maintenance_checklist_completion c
                          JOIN maintenance_checklist cl ON c.checklist_id = cl.id
                          LEFT JOIN users u ON c.completed_by = u.id
                          WHERE c.maintenance_history_id = :maintenance_id
                          AND c.completed = 1
                          ORDER BY cl.guideline_id, cl.step_number');
        $this->db->bind(':maintenance_id', $maintenanceId);
        $results = $this->db->resultSet();

        error_log("MaintenanceModel::getAllCompletedChecklistItems - Found " . count($results) . " completed items");

        // Group by guideline ID
        $groupedResults = [];
        foreach ($results as $item) {
            if (!isset($groupedResults[$item->guideline_id])) {
                $groupedResults[$item->guideline_id] = [];
            }
            $groupedResults[$item->guideline_id][] = $item;
        }

        return $groupedResults;
    }

    /**
     * Record checklist completion
     *
     * @param int $maintenanceId
     * @param int $checklistId
     * @param bool $completed
     * @param int $completedBy
     * @param string $notes
     * @return bool
     */
    public function recordChecklistCompletion($maintenanceId, $checklistId, $completed = true, $completedBy = null, $notes = '') {
        $this->db->query('INSERT INTO maintenance_checklist_completion
                          (maintenance_history_id, checklist_id, completed, completed_by, completed_date, notes)
                          VALUES (:maintenance_history_id, :checklist_id, :completed, :completed_by, NOW(), :notes)
                          ON DUPLICATE KEY UPDATE
                          completed = :completed,
                          completed_by = :completed_by,
                          completed_date = NOW(),
                          notes = :notes');

        $this->db->bind(':maintenance_history_id', $maintenanceId);
        $this->db->bind(':checklist_id', $checklistId);
        $this->db->bind(':completed', $completed ? 1 : 0);
        $this->db->bind(':completed_by', $completedBy);
        $this->db->bind(':notes', $notes);

        return $this->db->execute();
    }

    /**
     * Log guideline implementation for a maintenance record
     *
     * @param int $maintenanceId
     * @param int $guidelineId
     * @return bool
     */
    public function logGuidelineImplementation($maintenanceId, $guidelineId) {
        error_log("MaintenanceModel::logGuidelineImplementation - Starting with maintenanceId: $maintenanceId, guidelineId: $guidelineId");

        // Validate input parameters
        if (!$maintenanceId || !$guidelineId) {
            error_log("MaintenanceModel::logGuidelineImplementation - Invalid parameters: maintenanceId=$maintenanceId, guidelineId=$guidelineId");
            return false;
        }

        // Check if the tables exist
        try {
            // First check if the maintenance_history record exists
            $this->db->query('SELECT * FROM maintenance_history WHERE id = :id');
            $this->db->bind(':id', $maintenanceId);
            $maintenanceRecord = $this->db->single();
            $maintenanceExists = ($maintenanceRecord !== false);
            error_log("MaintenanceModel::logGuidelineImplementation - Maintenance record exists: " . ($maintenanceExists ? 'Yes' : 'No'));

            // Check if the guideline exists
            $this->db->query('SELECT * FROM maintenance_guidelines WHERE id = :id');
            $this->db->bind(':id', $guidelineId);
            $guidelineRecord = $this->db->single();
            $guidelineExists = ($guidelineRecord !== false);
            error_log("MaintenanceModel::logGuidelineImplementation - Guideline record exists: " . ($guidelineExists ? 'Yes' : 'No'));

            if (!$maintenanceExists) {
                error_log("MaintenanceModel::logGuidelineImplementation - Cannot proceed: Maintenance record #$maintenanceId not found.");
                return false;
            }

            if (!$guidelineExists) {
                error_log("MaintenanceModel::logGuidelineImplementation - Cannot proceed: Guideline #$guidelineId not found.");
                return false;
            }
        } catch (Exception $e) {
            error_log("MaintenanceModel::logGuidelineImplementation - Error checking records: " . $e->getMessage());
            // Continue anyway - the table creation and insertion will handle any issues
        }

        // The table should already exist from the constructor
        // Just verify it exists
        try {
            $this->db->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
            $tableExists = $this->db->rowCount() > 0;
            error_log("MaintenanceModel::logGuidelineImplementation - Table exists check: " . ($tableExists ? 'Yes' : 'No'));

            if (!$tableExists) {
                // Call the method to ensure the table exists
                $this->ensureGuidelineImplementationTableExists();
            }
        } catch (Exception $e) {
            error_log("MaintenanceModel::logGuidelineImplementation - Error checking table: " . $e->getMessage());
        }

        // Check if the record already exists
        try {
            $this->db->query('SELECT COUNT(*) as count FROM maintenance_guideline_implementation
                              WHERE maintenance_id = :maintenance_id AND guideline_id = :guideline_id');
            $this->db->bind(':maintenance_id', $maintenanceId);
            $this->db->bind(':guideline_id', $guidelineId);
            $result = $this->db->single();
            $existingCount = $result ? $result->count : 0;
            error_log("MaintenanceModel::logGuidelineImplementation - Existing record count: $existingCount");
        } catch (Exception $e) {
            error_log("MaintenanceModel::logGuidelineImplementation - Error checking existing record: " . $e->getMessage());
            $existingCount = 0;
        }

        // Always insert a new record (no ON DUPLICATE KEY UPDATE since we want multiple instances)
        try {
            $this->db->query('INSERT INTO maintenance_guideline_implementation (maintenance_id, guideline_id, implemented_date)
                              VALUES (:maintenance_id, :guideline_id, NOW())');
            $this->db->bind(':maintenance_id', $maintenanceId);
            $this->db->bind(':guideline_id', $guidelineId);
            $insertResult = $this->db->execute();

            error_log("MaintenanceModel::logGuidelineImplementation - Insert result: " . ($insertResult ? 'Success' : 'Failed'));

            if (!$insertResult) {
                error_log("MaintenanceModel::logGuidelineImplementation - Insert failed. Trying direct query.");

                // Try a direct query as a last resort
                try {
                    $this->db->query("INSERT INTO maintenance_guideline_implementation (maintenance_id, guideline_id, implemented_date)
                                     VALUES ($maintenanceId, $guidelineId, NOW())");
                    $insertResult = $this->db->execute();
                    error_log("MaintenanceModel::logGuidelineImplementation - Direct query result: " . ($insertResult ? 'Success' : 'Failed'));
                } catch (Exception $e2) {
                    error_log("MaintenanceModel::logGuidelineImplementation - Error with direct query: " . $e2->getMessage());
                }
            }
        } catch (Exception $e) {
            error_log("MaintenanceModel::logGuidelineImplementation - Error inserting record: " . $e->getMessage());
            $insertResult = false;
        }

        // Verify the record was inserted by checking if the count increased
        try {
            $this->db->query('SELECT COUNT(*) as count FROM maintenance_guideline_implementation
                              WHERE maintenance_id = :maintenance_id AND guideline_id = :guideline_id');
            $this->db->bind(':maintenance_id', $maintenanceId);
            $this->db->bind(':guideline_id', $guidelineId);
            $result = $this->db->single();
            $newCount = $result ? $result->count : 0;
            error_log("MaintenanceModel::logGuidelineImplementation - New record count: $newCount");

            // Check if the count increased (we had $existingCount before)
            if ($newCount > $existingCount) {
                error_log("MaintenanceModel::logGuidelineImplementation - Record count increased from $existingCount to $newCount, returning success");
                return true;
            } else if ($newCount > 0) {
                // If we have at least one record, consider it a partial success
                error_log("MaintenanceModel::logGuidelineImplementation - Record exists but count didn't increase, returning partial success");
                return true;
            }
        } catch (Exception $e) {
            error_log("MaintenanceModel::logGuidelineImplementation - Error verifying record: " . $e->getMessage());
        }

        // If we get here, return the result of the insert operation
        return $insertResult;
    }

    /**
     * Update health metrics for all assets
     *
     * @return int Number of assets updated
     */
    public function updateAllHealthMetrics() {
        // Get all asset IDs
        $this->db->query('SELECT id FROM assets');
        $assets = $this->db->resultSet();

        $updatedCount = 0;
        foreach ($assets as $asset) {
            // Calculate and store health metrics for each asset
            $result = $this->calculateAssetHealthScore($asset->id);
            if ($result !== false) {
                $updatedCount++;
            }
        }

        return $updatedCount;
    }

    /**
     * Get implemented guidelines for a maintenance record
     *
     * @param int $maintenanceId
     * @return array
     */
    public function getImplementedGuidelines($maintenanceId) {
        error_log("MaintenanceModel::getImplementedGuidelines - Starting with maintenanceId: $maintenanceId");

        try {
            // The table should already exist from the constructor
            // Just verify it exists
            $this->db->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
            $tableExists = $this->db->rowCount() > 0;
            error_log("MaintenanceModel::getImplementedGuidelines - Table exists: " . ($tableExists ? 'Yes' : 'No'));

            if (!$tableExists) {
                // Call the method to ensure the table exists
                $this->ensureGuidelineImplementationTableExists();
                // If we're creating the table now, there won't be any records yet
                return [];
            }

            // Check if any records exist
            $this->db->query('SELECT COUNT(*) as count FROM maintenance_guideline_implementation WHERE maintenance_id = :maintenance_id');
            $this->db->bind(':maintenance_id', $maintenanceId);
            $result = $this->db->single();
            $count = $result->count;
            error_log("MaintenanceModel::getImplementedGuidelines - Found $count implementation records");

            if ($count == 0) {
                return [];
            }

            // Get the raw records for debugging
            $this->db->query('SELECT * FROM maintenance_guideline_implementation WHERE maintenance_id = :maintenance_id');
            $this->db->bind(':maintenance_id', $maintenanceId);
            $rawRecords = $this->db->resultSet();
            error_log("MaintenanceModel::getImplementedGuidelines - Raw records: " . print_r($rawRecords, true));

            // Get the actual guidelines
            // Use a direct query without GROUP BY to ensure we get all records
            $this->db->query('SELECT i.*, g.id as guideline_id, g.name, g.description, g.equipment_type, g.frequency_days, g.importance, i.implemented_date
                              FROM maintenance_guideline_implementation i
                              JOIN maintenance_guidelines g ON i.guideline_id = g.id
                              WHERE i.maintenance_id = :maintenance_id
                              ORDER BY g.name');

            $this->db->bind(':maintenance_id', $maintenanceId);

            $guidelines = $this->db->resultSet();
            error_log("MaintenanceModel::getImplementedGuidelines - Retrieved " . count($guidelines) . " guidelines");

            if (count($guidelines) > 0) {
                error_log("MaintenanceModel::getImplementedGuidelines - First guideline: " . print_r(get_object_vars($guidelines[0]), true));
            }

            return $guidelines;
        } catch (Exception $e) {
            error_log("MaintenanceModel::getImplementedGuidelines - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get detailed information about implemented guidelines for a maintenance record
     * including checklist items
     *
     * @param int $maintenanceId
     * @return array
     */
    public function getDetailedImplementedGuidelines($maintenanceId) {
        error_log("MaintenanceModel::getDetailedImplementedGuidelines - Starting with maintenanceId: $maintenanceId");

        try {
            // Get basic guideline information
            $guidelines = $this->getImplementedGuidelines($maintenanceId);
            error_log("MaintenanceModel::getDetailedImplementedGuidelines - Got " . count($guidelines) . " basic guidelines");

            if (empty($guidelines)) {
                // Try a direct query as a fallback
                try {
                    $this->db->query('SELECT i.*, g.id as guideline_id, g.name, g.description, g.equipment_type, g.frequency_days, g.importance, i.implemented_date
                                      FROM maintenance_guideline_implementation i
                                      JOIN maintenance_guidelines g ON i.guideline_id = g.id
                                      WHERE i.maintenance_id = :maintenance_id');
                    $this->db->bind(':maintenance_id', $maintenanceId);
                    $guidelines = $this->db->resultSet();
                    error_log("MaintenanceModel::getDetailedImplementedGuidelines - Direct query fallback found " . count($guidelines) . " guidelines");

                    if (empty($guidelines)) {
                        return [];
                    }
                } catch (Exception $e) {
                    error_log("MaintenanceModel::getDetailedImplementedGuidelines - Error in direct query fallback: " . $e->getMessage());
                    return [];
                }
            }

            // Get checklist items for each guideline
            foreach ($guidelines as &$guideline) {
                try {
                    // Check which property contains the guideline ID
                    $guidelineId = $guideline->guideline_id ?? $guideline->id;
                    error_log("MaintenanceModel::getDetailedImplementedGuidelines - Processing guideline ID: $guidelineId");

                    // Add implementation details if not already present
                    if (!isset($guideline->implementation_count)) {
                        $guideline->implementation_count = 1;
                    }

                    if (!isset($guideline->first_implemented_date)) {
                        $guideline->first_implemented_date = $guideline->implemented_date;
                    }

                    if (!isset($guideline->last_implemented_date)) {
                        $guideline->last_implemented_date = $guideline->implemented_date;
                    }

                    // Get all implementation records for this guideline
                    $this->db->query('SELECT i.*, m.maintenance_type, m.description as maintenance_description, m.performed_date, m.status
                                      FROM maintenance_guideline_implementation i
                                      LEFT JOIN maintenance_history m ON i.maintenance_id = m.id
                                      WHERE i.maintenance_id = :maintenance_id AND i.guideline_id = :guideline_id
                                      ORDER BY i.implemented_date DESC');
                    $this->db->bind(':maintenance_id', $maintenanceId);
                    $this->db->bind(':guideline_id', $guidelineId);
                    $implementations = $this->db->resultSet();
                    $guideline->implementations = $implementations;
                    error_log("MaintenanceModel::getDetailedImplementedGuidelines - Found " . count($implementations) . " implementation records");

                    // Check if maintenance_checklist table exists
                    $this->db->query("SHOW TABLES LIKE 'maintenance_checklist'");
                    $tableExists = $this->db->rowCount() > 0;

                    if (!$tableExists) {
                        error_log("MaintenanceModel::getDetailedImplementedGuidelines - maintenance_checklist table doesn't exist");
                        $guideline->checklist_items = [];
                        continue;
                    }

                    $this->db->query('SELECT * FROM maintenance_checklist
                                      WHERE guideline_id = :guideline_id
                                      ORDER BY step_number');
                    $this->db->bind(':guideline_id', $guidelineId);
                    $checklist_items = $this->db->resultSet();
                    $guideline->checklist_items = $checklist_items;
                    error_log("MaintenanceModel::getDetailedImplementedGuidelines - Found " . count($checklist_items) . " checklist items");
                } catch (Exception $e) {
                    error_log("MaintenanceModel::getDetailedImplementedGuidelines - Error getting checklist items: " . $e->getMessage());
                    $guideline->checklist_items = [];
                    $guideline->implementations = [];
                }
            }

            return $guidelines;
        } catch (Exception $e) {
            error_log("MaintenanceModel::getDetailedImplementedGuidelines - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get maintenance records with implemented guidelines for an asset
     *
     * @param int $assetId
     * @return array
     */
    public function getMaintenanceHistoryWithGuidelines($assetId) {
        // Get maintenance history
        $maintenanceHistory = $this->getMaintenanceHistory($assetId);

        // Get implemented guidelines for each record
        foreach ($maintenanceHistory as &$record) {
            $record->implemented_guidelines = $this->getImplementedGuidelines($record->id);
        }

        return $maintenanceHistory;
    }

    /**
     * Get all implementations of a specific guideline across different maintenance records
     *
     * @param int $guidelineId
     * @param int $limit Optional limit for number of records to return
     * @param int $offset Optional offset for pagination
     * @return array
     */
    public function getGuidelineImplementations($guidelineId, $limit = null, $offset = 0) {
        try {
            // First, check if the guideline exists
            $this->db->query('SELECT * FROM maintenance_guidelines WHERE id = :guideline_id');
            $this->db->bind(':guideline_id', $guidelineId);
            $guideline = $this->db->single();

            if (!$guideline) {
                error_log("MaintenanceModel::getGuidelineImplementations - Guideline with ID $guidelineId not found");
                return [
                    'guideline' => null,
                    'implementations' => [],
                    'total_count' => 0
                ];
            }

            // Ensure the maintenance_guideline_implementation table exists
            $this->ensureGuidelineImplementationTableExists();

            // Try a direct query to get all implementations for this guideline
            // This approach bypasses the table existence check and uses a direct query
            try {
                // Log the query attempt for debugging
                error_log("MaintenanceModel::getGuidelineImplementations - Attempting to get implementations for guideline ID: $guidelineId");

                // First, check if there are any records for this guideline using a direct query
                $this->db->query('SELECT COUNT(*) as count FROM maintenance_guideline_implementation WHERE guideline_id = :guideline_id');
                $this->db->bind(':guideline_id', $guidelineId);
                $result = $this->db->single();
                $recordCount = $result ? $result->count : 0;
                error_log("MaintenanceModel::getGuidelineImplementations - Found $recordCount records for guideline ID: $guidelineId");

                if ($recordCount == 0) {
                    // No records found, return empty array
                    return [
                        'guideline' => $guideline,
                        'implementations' => [],
                        'total_count' => 0
                    ];
                }
            } catch (Exception $e) {
                error_log("MaintenanceModel::getGuidelineImplementations - Error counting records: " . $e->getMessage());
                // Continue with the query anyway, as the table might exist but be empty
            }

            // Build the SQL query with LEFT JOINs to handle potential missing records
            $sql = 'SELECT i.*, m.asset_id, m.maintenance_type, m.description, m.performed_date, m.status,
                    a.computer_host_name, a.equipment_type, a.serial_number, u.name as performed_by_name
                    FROM maintenance_guideline_implementation i
                    LEFT JOIN maintenance_history m ON i.maintenance_id = m.id
                    LEFT JOIN assets a ON m.asset_id = a.id
                    LEFT JOIN users u ON m.performed_by = u.id
                    WHERE i.guideline_id = :guideline_id
                    ORDER BY i.implemented_date DESC';

            if ($limit !== null) {
                $sql .= ' LIMIT :limit OFFSET :offset';
            }

            $this->db->query($sql);
            $this->db->bind(':guideline_id', $guidelineId);

            if ($limit !== null) {
                $this->db->bind(':limit', $limit);
                $this->db->bind(':offset', $offset);
            }

            $implementations = $this->db->resultSet();
            error_log("MaintenanceModel::getGuidelineImplementations - Query returned " . count($implementations) . " results");

            // If no implementations found, try a fallback approach
            if (empty($implementations)) {
                error_log("MaintenanceModel::getGuidelineImplementations - No implementations found in primary query, trying fallback");

                // Try a direct query to the maintenance_history_guidelines table as a fallback
                try {
                    $this->db->query('SELECT mhg.*, m.asset_id, m.maintenance_type, m.description, m.performed_date, m.status,
                                    a.computer_host_name, a.equipment_type, a.serial_number, u.name as performed_by_name
                                    FROM maintenance_history_guidelines mhg
                                    LEFT JOIN maintenance_history m ON mhg.maintenance_id = m.id
                                    LEFT JOIN assets a ON m.asset_id = a.id
                                    LEFT JOIN users u ON m.performed_by = u.id
                                    WHERE mhg.guideline_id = :guideline_id
                                    ORDER BY m.performed_date DESC');
                    $this->db->bind(':guideline_id', $guidelineId);
                    $fallbackImplementations = $this->db->resultSet();

                    if (!empty($fallbackImplementations)) {
                        error_log("MaintenanceModel::getGuidelineImplementations - Found " . count($fallbackImplementations) . " implementations in fallback query");
                        $implementations = $fallbackImplementations;
                    }
                } catch (Exception $e) {
                    error_log("MaintenanceModel::getGuidelineImplementations - Fallback query error: " . $e->getMessage());
                }
            }

            // Count total implementations for this guideline
            $this->db->query('SELECT COUNT(*) as total FROM maintenance_guideline_implementation WHERE guideline_id = :guideline_id');
            $this->db->bind(':guideline_id', $guidelineId);
            $result = $this->db->single();
            $totalCount = $result ? $result->total : 0;

            // If still no count from main table, try the fallback table
            if ($totalCount == 0) {
                try {
                    $this->db->query('SELECT COUNT(*) as total FROM maintenance_history_guidelines WHERE guideline_id = :guideline_id');
                    $this->db->bind(':guideline_id', $guidelineId);
                    $fallbackResult = $this->db->single();
                    $fallbackCount = $fallbackResult ? $fallbackResult->total : 0;

                    if ($fallbackCount > 0) {
                        error_log("MaintenanceModel::getGuidelineImplementations - Using fallback count: $fallbackCount");
                        $totalCount = $fallbackCount;
                    }
                } catch (Exception $e) {
                    error_log("MaintenanceModel::getGuidelineImplementations - Fallback count error: " . $e->getMessage());
                }
            }

            return [
                'guideline' => $guideline,
                'implementations' => $implementations,
                'total_count' => $totalCount
            ];
        } catch (Exception $e) {
            error_log("MaintenanceModel::getGuidelineImplementations - Error: " . $e->getMessage());
            return [
                'guideline' => null,
                'implementations' => [],
                'total_count' => 0
            ];
        }
    }

    /**
     * Check data integrity of the maintenance_guideline_implementation table
     * Identifies orphaned records (records with no corresponding maintenance_history or guideline)
     *
     * @return array Results of the integrity check
     */
    public function checkDataIntegrity() {
        try {
            // Initialize result array
            $result = [
                'orphaned_records' => [],
                'total_records' => 0,
                'orphaned_count' => 0
            ];

            // Verify the maintenance_guideline_implementation table exists
            $this->db->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
            $tableExists = $this->db->rowCount() > 0;

            if (!$tableExists) {
                // Call the method to ensure the table exists
                $this->ensureGuidelineImplementationTableExists();
                return $result;
            }

            // Get total count of records
            $this->db->query('SELECT COUNT(*) as total FROM maintenance_guideline_implementation');
            $countResult = $this->db->single();
            $result['total_records'] = $countResult ? $countResult->total : 0;

            // Find orphaned records (no corresponding maintenance_history record)
            $this->db->query('SELECT i.*, g.name as guideline_name
                             FROM maintenance_guideline_implementation i
                             LEFT JOIN maintenance_history m ON i.maintenance_id = m.id
                             LEFT JOIN maintenance_guidelines g ON i.guideline_id = g.id
                             WHERE m.id IS NULL OR g.id IS NULL');
            $orphanedRecords = $this->db->resultSet();
            $result['orphaned_records'] = $orphanedRecords;
            $result['orphaned_count'] = count($orphanedRecords);

            error_log("MaintenanceModel::checkDataIntegrity - Found {$result['orphaned_count']} orphaned records out of {$result['total_records']} total records");

            return $result;
        } catch (Exception $e) {
            error_log("MaintenanceModel::checkDataIntegrity - Error: " . $e->getMessage());
            return [
                'orphaned_records' => [],
                'total_records' => 0,
                'orphaned_count' => 0
            ];
        }
    }

    /**
     * Fix orphaned records in the maintenance_guideline_implementation table
     * Removes records with no corresponding maintenance_history or guideline
     *
     * @return array Results of the fix operation
     */
    public function fixOrphanedRecords() {
        try {
            // Initialize result array
            $result = [
                'fixed_records' => [],
                'fixed_count' => 0
            ];

            // Get orphaned records first (for reporting)
            $this->db->query('SELECT i.*, g.name as guideline_name
                             FROM maintenance_guideline_implementation i
                             LEFT JOIN maintenance_history m ON i.maintenance_id = m.id
                             LEFT JOIN maintenance_guidelines g ON i.guideline_id = g.id
                             WHERE m.id IS NULL OR g.id IS NULL');
            $orphanedRecords = $this->db->resultSet();
            $result['fixed_records'] = $orphanedRecords;
            $result['fixed_count'] = count($orphanedRecords);

            // Delete orphaned records
            $this->db->query('DELETE i FROM maintenance_guideline_implementation i
                             LEFT JOIN maintenance_history m ON i.maintenance_id = m.id
                             LEFT JOIN maintenance_guidelines g ON i.guideline_id = g.id
                             WHERE m.id IS NULL OR g.id IS NULL');
            $this->db->execute();

            error_log("MaintenanceModel::fixOrphanedRecords - Deleted {$result['fixed_count']} orphaned records");

            return $result;
        } catch (Exception $e) {
            error_log("MaintenanceModel::fixOrphanedRecords - Error: " . $e->getMessage());
            return [
                'fixed_records' => [],
                'fixed_count' => 0
            ];
        }
    }

    /**
     * Find missing maintenance history records
     * Identifies implementation records where the maintenance_history record is missing
     *
     * @return array Results of the search
     */
    public function findMissingMaintenanceRecords() {
        try {
            // Initialize result array
            $result = [
                'missing_records' => [],
                'total_implementations' => 0,
                'missing_count' => 0
            ];

            // Verify the maintenance_guideline_implementation table exists
            $this->db->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
            $tableExists = $this->db->rowCount() > 0;

            if (!$tableExists) {
                // Call the method to ensure the table exists
                $this->ensureGuidelineImplementationTableExists();
                return $result;
            }

            // Get total count of implementation records
            $this->db->query('SELECT COUNT(*) as total FROM maintenance_guideline_implementation');
            $countResult = $this->db->single();
            $result['total_implementations'] = $countResult ? $countResult->total : 0;

            // Find implementation records with missing maintenance history
            $this->db->query('SELECT i.*, g.name as guideline_name, g.equipment_type
                             FROM maintenance_guideline_implementation i
                             LEFT JOIN maintenance_history m ON i.maintenance_id = m.id
                             JOIN maintenance_guidelines g ON i.guideline_id = g.id
                             WHERE m.id IS NULL');
            $missingRecords = $this->db->resultSet();
            $result['missing_records'] = $missingRecords;
            $result['missing_count'] = count($missingRecords);

            error_log("MaintenanceModel::findMissingMaintenanceRecords - Found {$result['missing_count']} missing maintenance records");

            return $result;
        } catch (Exception $e) {
            error_log("MaintenanceModel::findMissingMaintenanceRecords - Error: " . $e->getMessage());
            return [
                'missing_records' => [],
                'total_implementations' => 0,
                'missing_count' => 0
            ];
        }
    }

    /**
     * Recreate missing maintenance history records
     * Creates new maintenance records based on implementation records
     *
     * @return array Results of the recreation
     */
    public function recreateMissingRecords() {
        try {
            // Initialize result array
            $result = [
                'recreated_records' => [],
                'recreated_count' => 0
            ];

            // Find implementation records with missing maintenance history
            $this->db->query('SELECT i.*, g.name as guideline_name, g.equipment_type, g.id as guideline_id
                             FROM maintenance_guideline_implementation i
                             LEFT JOIN maintenance_history m ON i.maintenance_id = m.id
                             JOIN maintenance_guidelines g ON i.guideline_id = g.id
                             WHERE m.id IS NULL');
            $missingRecords = $this->db->resultSet();

            // Group by maintenance_id to avoid duplicates
            $groupedRecords = [];
            foreach ($missingRecords as $record) {
                if (!isset($groupedRecords[$record->maintenance_id])) {
                    $groupedRecords[$record->maintenance_id] = $record;
                }
            }

            // Recreate each missing maintenance record
            $recreatedRecords = [];
            foreach ($groupedRecords as $maintenanceId => $record) {
                // Find an asset with the matching equipment type
                $this->db->query('SELECT id FROM assets WHERE equipment_type = :equipment_type LIMIT 1');
                $this->db->bind(':equipment_type', $record->equipment_type);
                $asset = $this->db->single();

                if (!$asset) {
                    error_log("MaintenanceModel::recreateMissingRecords - No asset found for equipment type: {$record->equipment_type}");
                    continue;
                }

                // Create a new maintenance record
                $maintenanceData = [
                    'asset_id' => $asset->id,
                    'maintenance_type' => 'preventive', // Default type
                    'description' => "Recreated record for guideline: {$record->guideline_name}",
                    'performed_by' => $_SESSION['user_id'] ?? null,
                    'performed_date' => $record->implemented_date,
                    'cost' => 0,
                    'next_scheduled_date' => null,
                    'status' => 'completed'
                ];

                // Insert the new maintenance record
                $newMaintenanceId = $this->addMaintenance($maintenanceData);

                if ($newMaintenanceId) {
                    // Update the implementation record with the new maintenance_id
                    $this->db->query('UPDATE maintenance_guideline_implementation
                                     SET maintenance_id = :new_id
                                     WHERE maintenance_id = :old_id');
                    $this->db->bind(':new_id', $newMaintenanceId);
                    $this->db->bind(':old_id', $maintenanceId);
                    $this->db->execute();

                    // Add to recreated records
                    $record->new_maintenance_id = $newMaintenanceId;
                    $record->asset_id = $asset->id;
                    $recreatedRecords[] = $record;
                }
            }

            $result['recreated_records'] = $recreatedRecords;
            $result['recreated_count'] = count($recreatedRecords);

            error_log("MaintenanceModel::recreateMissingRecords - Recreated {$result['recreated_count']} maintenance records");

            return $result;
        } catch (Exception $e) {
            error_log("MaintenanceModel::recreateMissingRecords - Error: " . $e->getMessage());
            return [
                'recreated_records' => [],
                'recreated_count' => 0
            ];
        }
    }

    /**
     * Get most frequently implemented guidelines
     * Returns a list of guidelines sorted by implementation count
     *
     * @param int $limit Optional limit for number of records to return (null for all records)
     * @return array List of guidelines with implementation counts
     */
    public function getMostImplementedGuidelines($limit = null) {
        try {
            // Verify the maintenance_guideline_implementation table exists
            $this->db->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
            $tableExists = $this->db->rowCount() > 0;

            if (!$tableExists) {
                // Call the method to ensure the table exists
                $this->ensureGuidelineImplementationTableExists();
                return [];
            }

            // Base query without limit
            $sql = 'SELECT g.id, g.name, g.equipment_type, g.importance, COUNT(i.id) as implementation_count
                   FROM maintenance_guidelines g
                   JOIN maintenance_guideline_implementation i ON g.id = i.guideline_id
                   GROUP BY g.id
                   ORDER BY implementation_count DESC';

            // Add limit if specified
            if ($limit !== null) {
                $sql .= ' LIMIT :limit';
            }

            $this->db->query($sql);

            // Bind limit parameter if specified
            if ($limit !== null) {
                $this->db->bind(':limit', $limit);
            }

            $guidelines = $this->db->resultSet();

            error_log("MaintenanceModel::getMostImplementedGuidelines - Retrieved " . count($guidelines) . " guidelines");

            return $guidelines;
        } catch (Exception $e) {
            error_log("MaintenanceModel::getMostImplementedGuidelines - Error: " . $e->getMessage());
            return [];
        }
    }
}
