<?php
/**
 * Permission Model
 * Handles database operations for permissions
 */
class Permission {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    /**
     * Get all permissions
     * 
     * @return array Array of permission objects
     */
    public function getAllPermissions() {
        $this->db->query('SELECT * FROM permissions ORDER BY category, name');
        return $this->db->resultSet();
    }

    /**
     * Get permissions by category
     * 
     * @param string $category Category name
     * @return array Array of permission objects
     */
    public function getPermissionsByCategory($category) {
        $this->db->query('SELECT * FROM permissions WHERE category = :category ORDER BY name');
        $this->db->bind(':category', $category);
        return $this->db->resultSet();
    }

    /**
     * Get all permission categories
     * 
     * @return array Array of category names
     */
    public function getAllCategories() {
        $this->db->query('SELECT DISTINCT category FROM permissions ORDER BY category');
        $categories = $this->db->resultSet();
        
        $result = [];
        foreach ($categories as $category) {
            $result[] = $category->category;
        }
        
        return $result;
    }

    /**
     * Get permission by ID
     * 
     * @param int $id Permission ID
     * @return object Permission object
     */
    public function getPermissionById($id) {
        $this->db->query('SELECT * FROM permissions WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    /**
     * Get permission by name
     * 
     * @param string $name Permission name
     * @return object Permission object
     */
    public function getPermissionByName($name) {
        $this->db->query('SELECT * FROM permissions WHERE name = :name');
        $this->db->bind(':name', $name);
        return $this->db->single();
    }

    /**
     * Create a new permission
     * 
     * @param array $data Permission data
     * @return bool True if successful, false otherwise
     */
    public function createPermission($data) {
        $this->db->query('INSERT INTO permissions (name, description, category) VALUES (:name, :description, :category)');
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':category', $data['category']);
        
        return $this->db->execute();
    }

    /**
     * Update a permission
     * 
     * @param array $data Permission data
     * @return bool True if successful, false otherwise
     */
    public function updatePermission($data) {
        $this->db->query('UPDATE permissions SET name = :name, description = :description, category = :category WHERE id = :id');
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':category', $data['category']);
        
        return $this->db->execute();
    }

    /**
     * Delete a permission
     * 
     * @param int $id Permission ID
     * @return bool True if successful, false otherwise
     */
    public function deletePermission($id) {
        $this->db->query('DELETE FROM permissions WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }

    /**
     * Get all roles that have a specific permission
     * 
     * @param int $permissionId Permission ID
     * @return array Array of role objects
     */
    public function getRolesWithPermission($permissionId) {
        $this->db->query('SELECT r.* FROM roles r 
                         JOIN role_permissions rp ON r.id = rp.role_id 
                         WHERE rp.permission_id = :permission_id 
                         ORDER BY r.name ASC');
        $this->db->bind(':permission_id', $permissionId);
        
        return $this->db->resultSet();
    }
}
