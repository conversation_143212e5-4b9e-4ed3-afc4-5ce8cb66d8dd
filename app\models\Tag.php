<?php
class Tag {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // Get all tags
    public function getTags() {
        $this->db->query('SELECT t.*, u.name as created_by_name
                          FROM tags t
                          LEFT JOIN users u ON t.created_by = u.id
                          ORDER BY t.name ASC');
        return $this->db->resultSet();
    }

    // Get tag by ID
    public function getTagById($id) {
        $this->db->query('SELECT * FROM tags WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    // Get tag by name
    public function getTagByName($name) {
        $this->db->query('SELECT * FROM tags WHERE name = :name');
        $this->db->bind(':name', $name);
        return $this->db->single();
    }

    // Add tag
    public function addTag($data) {
        $this->db->query('INSERT INTO tags (name, color, created_by) VALUES (:name, :color, :created_by)');

        // Bind values
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':color', $data['color']);
        $this->db->bind(':created_by', $data['created_by']);

        // Execute
        if($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }

    // Update tag
    public function updateTag($data) {
        $this->db->query('UPDATE tags SET name = :name, color = :color WHERE id = :id');

        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':color', $data['color']);

        // Execute
        return $this->db->execute();
    }

    // Delete tag
    public function deleteTag($id) {
        $this->db->query('DELETE FROM tags WHERE id = :id');

        // Bind values
        $this->db->bind(':id', $id);

        // Execute
        return $this->db->execute();
    }

    // Get tags for an asset
    public function getTagsForAsset($assetId) {
        $this->db->query('SELECT t.*
                          FROM tags t
                          JOIN asset_tags at ON t.id = at.tag_id
                          WHERE at.asset_id = :asset_id
                          ORDER BY t.name ASC');

        $this->db->bind(':asset_id', $assetId);
        return $this->db->resultSet();
    }

    // Add tag to asset
    public function addTagToAsset($assetId, $tagId) {
        // Check if the relationship already exists
        $this->db->query('SELECT * FROM asset_tags WHERE asset_id = :asset_id AND tag_id = :tag_id');
        $this->db->bind(':asset_id', $assetId);
        $this->db->bind(':tag_id', $tagId);

        $row = $this->db->single();

        if($row) {
            // Relationship already exists
            return true;
        }

        // Create the relationship
        $this->db->query('INSERT INTO asset_tags (asset_id, tag_id) VALUES (:asset_id, :tag_id)');
        $this->db->bind(':asset_id', $assetId);
        $this->db->bind(':tag_id', $tagId);

        return $this->db->execute();
    }

    // Remove tag from asset
    public function removeTagFromAsset($assetId, $tagId) {
        $this->db->query('DELETE FROM asset_tags WHERE asset_id = :asset_id AND tag_id = :tag_id');
        $this->db->bind(':asset_id', $assetId);
        $this->db->bind(':tag_id', $tagId);

        return $this->db->execute();
    }

    // Remove all tags from an asset
    public function removeAllTagsFromAsset($assetId) {
        $this->db->query('DELETE FROM asset_tags WHERE asset_id = :asset_id');
        $this->db->bind(':asset_id', $assetId);

        return $this->db->execute();
    }

    // Get assets by tag
    public function getAssetsByTag($tagId) {
        $this->db->query('SELECT a.*
                          FROM assets a
                          JOIN asset_tags at ON a.id = at.asset_id
                          WHERE at.tag_id = :tag_id
                          ORDER BY a.id DESC');

        $this->db->bind(':tag_id', $tagId);
        return $this->db->resultSet();
    }

    // Count assets with a specific tag
    public function countAssetsWithTag($tagId) {
        $this->db->query('SELECT COUNT(*) as count FROM asset_tags WHERE tag_id = :tag_id');
        $this->db->bind(':tag_id', $tagId);

        $row = $this->db->single();
        return $row->count;
    }
}
