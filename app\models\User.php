<?php
/**
 * User Model
 *
 * Handles all user-related database operations including authentication,
 * registration, role management, and permission checking.
 *
 * @package AssetVisibility
 * <AUTHOR> Visibility Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
class User {
    /**
     * Database connection instance
     * @var Database
     */
    private $db;

    /**
     * Role model instance for role operations
     * @var Role
     */
    private $roleModel;

    /**
     * Constructor
     *
     * Initializes the User model with database and role model instances.
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->db = new Database;
        $this->roleModel = new Role();
    }

    /**
     * Register a new user
     *
     * Creates a new user account with the provided data. Sets default role
     * and status if not specified.
     *
     * @param array $data User registration data containing name, email, password, role, status
     * @return bool True if registration successful, false otherwise
     * @since 1.0.0
     */
    public function register($data) {
        $this->db->query('INSERT INTO users (name, email, password, role, status) VALUES(:name, :email, :password, :role, :status)');
        // Bind values
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':password', $data['password']);
        $this->db->bind(':role', isset($data['role']) ? $data['role'] : 'user');
        $this->db->bind(':status', isset($data['status']) ? $data['status'] : 'inactive');

        // Execute
        if($this->db->execute()) {
            // The trigger will automatically add the user to the appropriate role
            return true;
        } else {
            return false;
        }
    }

    /**
     * Authenticate user login
     *
     * Verifies user credentials and handles security features like account
     * locking and failed login attempt tracking.
     *
     * @param string $email User's email address
     * @param string $password User's password
     * @return mixed User object on success, 'inactive' if account inactive, 'locked' if locked, false on failure
     * @since 1.0.0
     */
    public function login($email, $password) {
        $this->db->query('SELECT * FROM users WHERE email = :email');
        $this->db->bind(':email', $email);

        $row = $this->db->single();

        // Check if user exists
        if(!$row) {
            return false;
        }

        // Check if user is active
        if($row->status == 'inactive') {
            return 'inactive';
        }

        // Check if account is locked
        if(SecurityEnhancements::isAccountLocked($row->id)) {
            return 'locked';
        }

        $hashed_password = $row->password;
        if(password_verify($password, $hashed_password)) {
            // Reset failed login attempts on successful login
            SecurityEnhancements::resetFailedLoginAttempts($row->id);

            // Log successful login
            SecurityEnhancements::logSecurityEvent('login', 'User logged in successfully', $row->id);
            SecurityEnhancements::recordLoginAttempt($email, true, $row->id);

            return $row;
        } else {
            // Increment failed login attempts
            SecurityEnhancements::incrementFailedLoginAttempts($row->id);

            // Log failed login
            SecurityEnhancements::logSecurityEvent('failed_login', 'Failed login attempt', $row->id);
            SecurityEnhancements::recordLoginAttempt($email, false, $row->id);

            return false;
        }
    }

    /**
     * Find user by email
     *
     * Checks if a user exists with the given email address.
     *
     * @param string $email The email address to search for
     * @return bool True if user exists, false otherwise
     * @since 1.0.0
     */
    public function findUserByEmail($email) {
        $this->db->query('SELECT * FROM users WHERE email = :email');
        // Bind value
        $this->db->bind(':email', $email);

        $this->db->single();

        // Check row
        if($this->db->rowCount() > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Get user by ID
     *
     * Retrieves a user record by their unique ID.
     *
     * @param int $id The user ID
     * @return object|false User object if found, false otherwise
     * @since 1.0.0
     */
    public function getUserById($id) {
        $this->db->query('SELECT * FROM users WHERE id = :id');
        // Bind value
        $this->db->bind(':id', $id);

        $row = $this->db->single();

        return $row;
    }

    /**
     * Get all users
     *
     * Retrieves all users from the database ordered by name.
     *
     * @return array Array of user objects
     * @since 1.0.0
     */
    public function getAllUsers() {
        $this->db->query('SELECT id, name, email, role, status, created_at FROM users ORDER BY name ASC');
        return $this->db->resultSet();
    }

    /**
     * Update user status
     *
     * Updates the status of a user (active/inactive).
     *
     * @param int $id The user ID
     * @param string $status The new status ('active' or 'inactive')
     * @return bool True if update successful, false otherwise
     * @since 1.0.0
     */
    public function updateStatus($id, $status) {
        $this->db->query('UPDATE users SET status = :status WHERE id = :id');
        // Bind values
        $this->db->bind(':id', $id);
        $this->db->bind(':status', $status);

        // Execute
        return $this->db->execute();
    }

    /**
     * Update user role (legacy method)
     *
     * Updates both the users table and user_roles table for backward compatibility.
     * Uses database transactions to ensure data consistency.
     *
     * @param int $id The user ID
     * @param string $role The role name ('admin' or 'user')
     * @return bool True if update successful, false otherwise
     * @throws Exception If database transaction fails
     * @since 1.0.0
     */
    public function updateRole($id, $role) {
        // Start a transaction
        $this->db->query('START TRANSACTION');
        $this->db->execute();

        try {
            // Update the role in the users table (for backward compatibility)
            $this->db->query('UPDATE users SET role = :role WHERE id = :id');
            $this->db->bind(':id', $id);
            $this->db->bind(':role', $role);
            $this->db->execute();

            // Get the corresponding role ID
            $roleId = null;
            if ($role == 'admin') {
                $roleObj = $this->roleModel->getRoleByName('Administrator');
                $roleId = $roleObj->id;
            } else {
                $roleObj = $this->roleModel->getRoleByName('User');
                $roleId = $roleObj->id;
            }

            // Delete existing roles
            $this->db->query('DELETE FROM user_roles WHERE user_id = :user_id');
            $this->db->bind(':user_id', $id);
            $this->db->execute();

            // Add the new role
            $this->db->query('INSERT INTO user_roles (user_id, role_id) VALUES (:user_id, :role_id)');
            $this->db->bind(':user_id', $id);
            $this->db->bind(':role_id', $roleId);
            $this->db->execute();

            // Commit the transaction
            $this->db->query('COMMIT');
            $this->db->execute();

            return true;
        } catch (Exception $e) {
            // Rollback the transaction on error
            $this->db->query('ROLLBACK');
            $this->db->execute();

            error_log('Error updating user role: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Update the legacy role field in the users table
     *
     * Updates only the role field in the users table for backward compatibility
     * with the old role system. Does not affect the user_roles table.
     *
     * @param int $id User ID
     * @param string $role Role value ('admin' or 'user')
     * @return bool True if successful, false otherwise
     * @since 1.0.0
     */
    public function updateLegacyRole($id, $role) {
        $this->db->query('UPDATE users SET role = :role WHERE id = :id');
        $this->db->bind(':id', $id);
        $this->db->bind(':role', $role);

        return $this->db->execute();
    }

    /**
     * Update user password
     *
     * Updates a user's password with the provided hashed password.
     *
     * @param int $id The user ID
     * @param string $password The hashed password
     * @return bool True if update successful, false otherwise
     * @since 1.0.0
     */
    public function updatePassword($id, $password) {
        $this->db->query('UPDATE users SET password = :password WHERE id = :id');
        // Bind values
        $this->db->bind(':id', $id);
        $this->db->bind(':password', $password);

        // Execute
        return $this->db->execute();
    }

    /**
     * Update user information
     *
     * Updates user name and optionally password. Dynamically builds
     * query based on provided data.
     *
     * @param array $data User data containing id, name, and optionally password
     * @return bool True if update successful, false otherwise
     * @since 1.0.0
     */
    public function updateUser($data) {
        // Start with updating the name
        $query = 'UPDATE users SET name = :name';

        // Add password to query if provided
        if(isset($data['password'])) {
            $query .= ', password = :password';
        }

        $query .= ' WHERE id = :id';

        $this->db->query($query);

        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);

        if(isset($data['password'])) {
            $this->db->bind(':password', $data['password']);
        }

        // Execute
        return $this->db->execute();
    }

    /**
     * Get user by email (returns user object)
     *
     * Retrieves a complete user record by email address.
     *
     * @param string $email The email address to search for
     * @return object|false User object if found, false otherwise
     * @since 1.0.0
     */
    public function getUserByEmail($email) {
        $this->db->query('SELECT * FROM users WHERE email = :email');
        // Bind value
        $this->db->bind(':email', $email);

        $row = $this->db->single();

        return $row;
    }

    /**
     * Ensure password reset tokens table exists
     *
     * Creates the password_reset_tokens table if it doesn't exist.
     *
     * @return bool True if table exists or was created successfully, false otherwise
     * @since 1.0.0
     */
    private function ensurePasswordResetTokensTableExists() {
        try {
            // Check if table exists
            $this->db->query("SHOW TABLES LIKE 'password_reset_tokens'");
            $tableExists = $this->db->rowCount() > 0;

            if (!$tableExists) {
                // Create the table
                $sql = "CREATE TABLE IF NOT EXISTS password_reset_tokens (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    email VARCHAR(255) NOT NULL,
                    token VARCHAR(255) NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    used TINYINT(1) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX (email),
                    INDEX (token),
                    INDEX (expires_at)
                ) ENGINE=InnoDB";

                $this->db->query($sql);
                $result = $this->db->execute();
                error_log("User::ensurePasswordResetTokensTableExists - Created table: " . ($result ? 'Success' : 'Failed'));
                return $result;
            }

            return $tableExists;
        } catch (Exception $e) {
            error_log("User::ensurePasswordResetTokensTableExists - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create password reset token
     *
     * Generates a secure token for password reset functionality.
     * Token expires after 1 hour and replaces any existing tokens.
     *
     * @param string $email The email address to create token for
     * @return string|false The generated token if successful, false otherwise
     * @since 1.0.0
     */
    public function createPasswordResetToken($email) {
        // Ensure the table exists
        if (!$this->ensurePasswordResetTokensTableExists()) {
            error_log("User::createPasswordResetToken - Failed to ensure table exists");
            return false;
        }
        // Generate a random token
        $token = bin2hex(random_bytes(32));

        // Set expiration time (1 hour from now)
        $expires = date('Y-m-d H:i:s', time() + 3600);

        // Delete any existing tokens for this email
        $this->db->query('DELETE FROM password_reset_tokens WHERE email = :email');
        $this->db->bind(':email', $email);
        $this->db->execute();

        // Insert new token
        $this->db->query('INSERT INTO password_reset_tokens (email, token, expires_at) VALUES(:email, :token, :expires_at)');
        $this->db->bind(':email', $email);
        $this->db->bind(':token', $token);
        $this->db->bind(':expires_at', $expires);

        if($this->db->execute()) {
            return $token;
        } else {
            return false;
        }
    }

    /**
     * Verify password reset token
     *
     * Validates a password reset token for the given email address.
     * Checks if token exists, is not expired, and hasn't been used.
     *
     * @param string $email The email address associated with the token
     * @param string $token The token to verify
     * @return bool True if token is valid, false otherwise
     * @since 1.0.0
     */
    public function verifyPasswordResetToken($email, $token) {
        // Ensure the table exists
        if (!$this->ensurePasswordResetTokensTableExists()) {
            error_log("User::verifyPasswordResetToken - Failed to ensure table exists");
            return false;
        }

        // Log the verification attempt for debugging
        error_log("User::verifyPasswordResetToken - Verifying token for email: " . $email . ", token: " . substr($token, 0, 10) . "...");

        $this->db->query('SELECT * FROM password_reset_tokens WHERE email = :email AND token = :token AND expires_at > NOW() AND used = 0');
        $this->db->bind(':email', $email);
        $this->db->bind(':token', $token);

        $this->db->single();
        $rowCount = $this->db->rowCount();

        // Log the result for debugging
        error_log("User::verifyPasswordResetToken - Query result: " . ($rowCount > 0 ? 'Found valid token' : 'No valid token found'));

        if ($rowCount === 0) {
            // Check if token exists at all (for debugging)
            $this->db->query('SELECT * FROM password_reset_tokens WHERE email = :email AND token = :token');
            $this->db->bind(':email', $email);
            $this->db->bind(':token', $token);
            $anyResult = $this->db->single();
            $anyRowCount = $this->db->rowCount();

            if ($anyRowCount > 0) {
                error_log("User::verifyPasswordResetToken - Token exists but may be expired or used. Expires: " . $anyResult->expires_at . ", Used: " . $anyResult->used);
            } else {
                error_log("User::verifyPasswordResetToken - Token not found in database");
            }
        }

        return $rowCount > 0;
    }

    /**
     * Mark password reset token as used
     *
     * Marks a password reset token as used to prevent reuse.
     * This is called after a successful password reset.
     *
     * @param string $email The email address associated with the token
     * @param string $token The token to mark as used
     * @return bool True if successfully marked as used, false otherwise
     * @since 1.0.0
     */
    public function markTokenAsUsed($email, $token) {
        $this->db->query('UPDATE password_reset_tokens SET used = 1 WHERE email = :email AND token = :token');
        $this->db->bind(':email', $email);
        $this->db->bind(':token', $token);

        return $this->db->execute();
    }

    /**
     * Reset password by email
     *
     * Updates a user's password using their email address.
     * Used in conjunction with password reset tokens.
     *
     * @param string $email The email address of the user
     * @param string $password The new hashed password
     * @return bool True if password reset successful, false otherwise
     * @since 1.0.0
     */
    public function resetPasswordByEmail($email, $password) {
        $this->db->query('UPDATE users SET password = :password WHERE email = :email');
        $this->db->bind(':email', $email);
        $this->db->bind(':password', $password);

        return $this->db->execute();
    }

    /**
     * Create a remember me token for a user
     *
     * Generates a secure remember me token for persistent login functionality.
     * Delegates to SecurityEnhancements class for token creation.
     *
     * @param int $userId User ID
     * @return array|bool Token data if created successfully, false otherwise
     * @since 1.0.0
     */
    public function createRememberMeToken($userId) {
        return SecurityEnhancements::createRememberMeToken($userId);
    }

    /**
     * Verify a remember me token
     *
     * Validates a remember me token using selector and validator components.
     * Delegates to SecurityEnhancements class for token verification.
     *
     * @param string $selector Token selector
     * @param string $validator Token validator
     * @return int|bool User ID if token is valid, false otherwise
     * @since 1.0.0
     */
    public function verifyRememberMeToken($selector, $validator) {
        return SecurityEnhancements::verifyRememberMeToken($selector, $validator);
    }

    /**
     * Delete a remember me token
     *
     * Removes a specific remember me token using its selector.
     * Delegates to SecurityEnhancements class for token deletion.
     *
     * @param string $selector Token selector
     * @return bool True if deleted successfully, false otherwise
     * @since 1.0.0
     */
    public function deleteRememberMeToken($selector) {
        return SecurityEnhancements::deleteRememberMeToken($selector);
    }

    /**
     * Delete all remember me tokens for a user
     *
     * Removes all remember me tokens associated with a specific user.
     * Useful for logout from all devices functionality.
     *
     * @param int $userId User ID
     * @return bool True if deleted successfully, false otherwise
     * @since 1.0.0
     */
    public function deleteAllRememberMeTokens($userId) {
        return SecurityEnhancements::deleteAllRememberMeTokens($userId);
    }

    /**
     * Get all roles for a user
     *
     * Retrieves all roles assigned to a specific user from the user_roles table.
     * Returns roles ordered by name for consistent display.
     *
     * @param int $userId User ID
     * @return array Array of role objects
     * @since 1.0.0
     */
    public function getUserRoles($userId) {
        $this->db->query('SELECT r.* FROM roles r
                         JOIN user_roles ur ON r.id = ur.role_id
                         WHERE ur.user_id = :user_id
                         ORDER BY r.name ASC');
        $this->db->bind(':user_id', $userId);

        return $this->db->resultSet();
    }

    /**
     * Get primary role for a user (for backward compatibility)
     *
     * Returns the first role assigned to a user for backward compatibility
     * with systems expecting a single role per user.
     *
     * @param int $userId User ID
     * @return object|bool Role object or false if no roles
     * @since 1.0.0
     */
    public function getPrimaryRole($userId) {
        $roles = $this->getUserRoles($userId);

        if (count($roles) > 0) {
            // Return the first role (assumed to be primary)
            return $roles[0];
        }

        return false;
    }

    /**
     * Check if a user has a specific role
     *
     * Verifies if a user is assigned a specific role by either role ID or name.
     * Supports both numeric role IDs and string role names for flexibility.
     *
     * @param int $userId User ID
     * @param int|string $role Role ID or name
     * @return bool True if the user has the role, false otherwise
     * @since 1.0.0
     */
    public function hasRole($userId, $role) {
        if (is_numeric($role)) {
            // Role ID provided
            $this->db->query('SELECT COUNT(*) as count FROM user_roles
                             WHERE user_id = :user_id AND role_id = :role_id');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role_id', $role);
        } else {
            // Role name provided
            $this->db->query('SELECT COUNT(*) as count FROM user_roles ur
                             JOIN roles r ON ur.role_id = r.id
                             WHERE ur.user_id = :user_id AND r.name = :role_name');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role_name', $role);
        }

        $result = $this->db->single();
        return $result->count > 0;
    }

    /**
     * Get all permissions for a user
     *
     * Retrieves all permissions granted to a user through their assigned roles.
     * Returns distinct permissions ordered by category and name.
     *
     * @param int $userId User ID
     * @return array Array of permission objects
     * @since 1.0.0
     */
    public function getUserPermissions($userId) {
        $this->db->query('SELECT DISTINCT p.* FROM permissions p
                         JOIN role_permissions rp ON p.id = rp.permission_id
                         JOIN user_roles ur ON rp.role_id = ur.role_id
                         WHERE ur.user_id = :user_id
                         ORDER BY p.category, p.name');
        $this->db->bind(':user_id', $userId);

        return $this->db->resultSet();
    }

    /**
     * Check if a user has a specific permission
     *
     * Verifies if a user has a specific permission through their assigned roles.
     * Supports both numeric permission IDs and string permission names.
     *
     * @param int $userId User ID
     * @param int|string $permission Permission ID or name
     * @return bool True if the user has the permission, false otherwise
     * @since 1.0.0
     */
    public function hasPermission($userId, $permission) {
        if (is_numeric($permission)) {
            // Permission ID provided
            $this->db->query('SELECT COUNT(*) as count FROM permissions p
                             JOIN role_permissions rp ON p.id = rp.permission_id
                             JOIN user_roles ur ON rp.role_id = ur.role_id
                             WHERE ur.user_id = :user_id AND p.id = :permission_id');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':permission_id', $permission);
        } else {
            // Permission name provided
            $this->db->query('SELECT COUNT(*) as count FROM permissions p
                             JOIN role_permissions rp ON p.id = rp.permission_id
                             JOIN user_roles ur ON rp.role_id = ur.role_id
                             WHERE ur.user_id = :user_id AND p.name = :permission_name');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':permission_name', $permission);
        }

        $result = $this->db->single();
        return $result->count > 0;
    }

    /**
     * Assign roles to a user
     *
     * Replaces all existing roles for a user with the provided role IDs.
     * First removes all current roles, then assigns the new ones.
     *
     * @param int $userId User ID
     * @param array $roleIds Array of role IDs to assign
     * @return bool True if successful, false otherwise
     * @since 1.0.0
     */
    public function assignRoles($userId, $roleIds) {
        // First, remove all existing roles for this user
        $this->db->query('DELETE FROM user_roles WHERE user_id = :user_id');
        $this->db->bind(':user_id', $userId);
        $this->db->execute();

        // Then, add the new roles
        $success = true;
        foreach ($roleIds as $roleId) {
            $this->db->query('INSERT INTO user_roles (user_id, role_id) VALUES (:user_id, :role_id)');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role_id', $roleId);

            if (!$this->db->execute()) {
                $success = false;
            }
        }

        return $success;
    }
}
