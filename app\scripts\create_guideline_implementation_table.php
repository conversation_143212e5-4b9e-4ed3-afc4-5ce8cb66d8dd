<?php
/**
 * Create Guideline Implementation Table Script
 * 
 * This script ensures that the maintenance_guideline_implementation table exists
 * and has the correct structure. It also migrates data from maintenance_history_guidelines
 * if needed.
 */

// Initialize
require_once '../config/config.php';
require_once '../libraries/Database.php';

// Create database instance
$db = new Database();

// Output buffer
$output = [];
$output[] = "=== Maintenance Guideline Implementation Table Migration ===";
$output[] = "Running at: " . date('Y-m-d H:i:s');
$output[] = "";

try {
    // Check if the table exists
    $db->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
    $tableExists = $db->rowCount() > 0;
    
    if (!$tableExists) {
        $output[] = "Table does not exist. Creating the table...";
        
        // Create the table with the correct structure
        $sql = "CREATE TABLE IF NOT EXISTS maintenance_guideline_implementation (
            id INT AUTO_INCREMENT PRIMARY KEY,
            maintenance_id INT NOT NULL,
            guideline_id INT NOT NULL,
            implemented_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_maintenance_guideline (maintenance_id, guideline_id)
        ) ENGINE=InnoDB";
        
        $db->query($sql);
        $result = $db->execute();
        
        if ($result) {
            $output[] = "Table created successfully.";
            
            // Check if the maintenance_history_guidelines table exists (legacy table)
            $db->query("SHOW TABLES LIKE 'maintenance_history_guidelines'");
            $legacyTableExists = $db->rowCount() > 0;
            
            if ($legacyTableExists) {
                $output[] = "Legacy table 'maintenance_history_guidelines' found. Migrating data...";
                
                // Get data from legacy table
                $db->query("SELECT * FROM maintenance_history_guidelines");
                $legacyRecords = $db->resultSet();
                
                if (!empty($legacyRecords)) {
                    $migratedCount = 0;
                    
                    foreach ($legacyRecords as $record) {
                        // Insert into new table
                        $db->query("INSERT INTO maintenance_guideline_implementation 
                                   (maintenance_id, guideline_id, implemented_date) 
                                   VALUES (:maintenance_id, :guideline_id, NOW())");
                        $db->bind(':maintenance_id', $record->maintenance_id);
                        $db->bind(':guideline_id', $record->guideline_id);
                        
                        if ($db->execute()) {
                            $migratedCount++;
                        }
                    }
                    
                    $output[] = "Migrated $migratedCount records from legacy table.";
                } else {
                    $output[] = "No records found in legacy table.";
                }
            } else {
                $output[] = "No legacy table found. No data migration needed.";
            }
        } else {
            $output[] = "Failed to create table.";
        }
    } else {
        $output[] = "Table 'maintenance_guideline_implementation' already exists.";
        
        // Check if the table has the correct structure
        $db->query("SHOW CREATE TABLE maintenance_guideline_implementation");
        $tableDefinition = $db->single();
        $createTableStatement = $tableDefinition->{'Create Table'} ?? '';
        
        // Check if the table has a UNIQUE constraint that should be removed
        if (strpos($createTableStatement, 'UNIQUE KEY') !== false) {
            $output[] = "Table has a UNIQUE KEY constraint that may prevent multiple implementations of the same guideline.";
            $output[] = "Attempting to fix...";
            
            // Get the current data
            $db->query("SELECT * FROM maintenance_guideline_implementation");
            $records = $db->resultSet();
            $output[] = "Backed up " . count($records) . " records.";
            
            // Create a new table with the correct structure
            try {
                $sql = "CREATE TABLE maintenance_guideline_implementation_new (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    maintenance_id INT NOT NULL,
                    guideline_id INT NOT NULL,
                    implemented_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_maintenance_guideline (maintenance_id, guideline_id)
                ) ENGINE=InnoDB";
                $db->query($sql);
                $db->execute();
                $output[] = "Created new table with correct structure.";
                
                // Copy data to the new table
                if (count($records) > 0) {
                    $insertedCount = 0;
                    foreach ($records as $record) {
                        $db->query("INSERT INTO maintenance_guideline_implementation_new 
                                   (maintenance_id, guideline_id, implemented_date) 
                                   VALUES (:maintenance_id, :guideline_id, :implemented_date)");
                        $db->bind(':maintenance_id', $record->maintenance_id);
                        $db->bind(':guideline_id', $record->guideline_id);
                        $db->bind(':implemented_date', $record->implemented_date);
                        
                        if ($db->execute()) {
                            $insertedCount++;
                        }
                    }
                    $output[] = "Copied $insertedCount records to new table.";
                }
                
                // Rename tables
                $db->query("DROP TABLE maintenance_guideline_implementation");
                $db->execute();
                $db->query("RENAME TABLE maintenance_guideline_implementation_new TO maintenance_guideline_implementation");
                $db->execute();
                $output[] = "Replaced old table with new table.";
                
            } catch (Exception $e) {
                $output[] = "Error fixing table structure: " . $e->getMessage();
            }
        } else {
            $output[] = "Table structure looks good (no UNIQUE constraint).";
        }
    }
    
    // Final check to ensure the table exists
    $db->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
    $tableExists = $db->rowCount() > 0;
    
    if ($tableExists) {
        $output[] = "";
        $output[] = "✓ Success: The maintenance_guideline_implementation table is properly set up.";
        $output[] = "You should now be able to view guideline implementation history correctly.";
    } else {
        $output[] = "";
        $output[] = "✗ Error: Failed to create or verify the maintenance_guideline_implementation table.";
        $output[] = "Please contact your system administrator for assistance.";
    }
    
} catch (Exception $e) {
    $output[] = "Database Error: " . $e->getMessage();
}

// Display output
foreach ($output as $line) {
    echo $line . PHP_EOL;
}

// Log output to file
$logFile = '../logs/migrations.log';
file_put_contents($logFile, implode(PHP_EOL, $output) . PHP_EOL . PHP_EOL, FILE_APPEND);

echo PHP_EOL . "Log written to: $logFile" . PHP_EOL;
