<?php require APPROOT . '/views/inc/header.php'; ?>
<a href="<?php echo URLROOT; ?>/assets" class="btn btn-light"><i class="fa fa-backward"></i> Back</a>
<div class="card card-body bg-light mt-5">
    <h2>Add Asset</h2>
    <p>Create a new asset with this form</p>
    <form action="<?php echo URLROOT; ?>/assets/add" method="post">
        <!-- CSRF Token -->
        <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="inventory_date">Inventory Date: <sup>*</sup></label>
                    <input type="date" name="inventory_date" class="form-control form-control-lg <?php echo (!empty($data['inventory_date_err'])) ? 'is-invalid' : ''; ?>" value="<?php echo $data['inventory_date']; ?>" id="inventory_date">
                    <span class="invalid-feedback"><?php echo $data['inventory_date_err']; ?></span>
                </div>
                <div class="form-group mb-3">
                    <label for="site_name">Site Name: <sup>*</sup></label>
                    <input type="text" name="site_name" class="form-control form-control-lg <?php echo (!empty($data['site_name_err'])) ? 'is-invalid' : ''; ?>" value="<?php echo $data['site_name']; ?>">
                    <span class="invalid-feedback"><?php echo $data['site_name_err']; ?></span>
                </div>
                <div class="form-group mb-3">
                    <label for="employee_name">Employee Name: <sup>*</sup></label>
                    <input type="text" name="employee_name" class="form-control form-control-lg <?php echo (!empty($data['employee_name_err'])) ? 'is-invalid' : ''; ?>" value="<?php echo $data['employee_name']; ?>">
                    <span class="invalid-feedback"><?php echo $data['employee_name_err']; ?></span>
                </div>
                <div class="form-group mb-3">
                    <label for="active_directory_name">Active Directory Name:</label>
                    <input type="text" name="active_directory_name" class="form-control form-control-lg" value="<?php echo $data['active_directory_name']; ?>">
                </div>
                <div class="form-group mb-3">
                    <label for="position">Position:</label>
                    <input type="text" name="position" class="form-control form-control-lg" value="<?php echo $data['position']; ?>">
                </div>
                <div class="form-group mb-3">
                    <label for="program_section">Program/Section:</label>
                    <input type="text" name="program_section" class="form-control form-control-lg" value="<?php echo $data['program_section']; ?>">
                </div>
                <div class="form-group mb-3">
                    <label for="computer_host_name">Computer/Host Name: <sup>*</sup></label>
                    <input type="text" name="computer_host_name" class="form-control form-control-lg <?php echo (!empty($data['computer_host_name_err'])) ? 'is-invalid' : ''; ?>" value="<?php echo $data['computer_host_name']; ?>">
                    <span class="invalid-feedback"><?php echo $data['computer_host_name_err']; ?></span>
                </div>
                <div class="form-group mb-3">
                    <label for="equipment_type">Equipment Type: <sup>*</sup></label>
                    <select name="equipment_type" class="form-control form-control-lg <?php echo (!empty($data['equipment_type_err'])) ? 'is-invalid' : ''; ?>">
                        <option value="" <?php echo empty($data['equipment_type']) ? 'selected' : ''; ?>>Select Type</option>
                        <option value="Laptop" <?php echo $data['equipment_type'] == 'Laptop' ? 'selected' : ''; ?>>Laptop</option>
                        <option value="Desktop" <?php echo $data['equipment_type'] == 'Desktop' ? 'selected' : ''; ?>>Desktop</option>
                        <option value="Printer" <?php echo $data['equipment_type'] == 'Printer' ? 'selected' : ''; ?>>Printer</option>
                        <option value="Scanner" <?php echo $data['equipment_type'] == 'Scanner' ? 'selected' : ''; ?>>Scanner</option>
                        <option value="Server" <?php echo $data['equipment_type'] == 'Server' ? 'selected' : ''; ?>>Server</option>
                        <option value="Network Device" <?php echo $data['equipment_type'] == 'Network Device' ? 'selected' : ''; ?>>Network Device</option>
                        <option value="Other" <?php echo $data['equipment_type'] == 'Other' ? 'selected' : ''; ?>>Other</option>
                    </select>
                    <span class="invalid-feedback"><?php echo $data['equipment_type_err']; ?></span>
                </div>
                <div class="form-group mb-3">
                    <label for="acquisition_type">Acquisition Type:</label>
                    <select name="acquisition_type" class="form-control form-control-lg">
                        <option value="" <?php echo empty($data['acquisition_type']) ? 'selected' : ''; ?>>Select Type</option>
                        <option value="DSWD Acquired" <?php echo $data['acquisition_type'] == 'DSWD Acquired' ? 'selected' : ''; ?>>DSWD Acquired</option>
                        <option value="Donated" <?php echo $data['acquisition_type'] == 'Donated' ? 'selected' : ''; ?>>Donated</option>
                        <option value="Leased" <?php echo $data['acquisition_type'] == 'Leased' ? 'selected' : ''; ?>>Leased</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="operating_system">Operating System:</label>
                    <select name="operating_system" class="form-control form-control-lg">
                        <option value="" <?php echo empty($data['operating_system']) ? 'selected' : ''; ?>>Select OS</option>
                        <option value="Windows 10" <?php echo $data['operating_system'] == 'Windows 10' ? 'selected' : ''; ?>>Windows 10</option>
                        <option value="Windows 11" <?php echo $data['operating_system'] == 'Windows 11' ? 'selected' : ''; ?>>Windows 11</option>
                        <option value="Windows 7" <?php echo $data['operating_system'] == 'Windows 7' ? 'selected' : ''; ?>>Windows 7</option>
                        <option value="macOS" <?php echo $data['operating_system'] == 'macOS' ? 'selected' : ''; ?>>macOS</option>
                        <option value="Linux" <?php echo $data['operating_system'] == 'Linux' ? 'selected' : ''; ?>>Linux</option>
                        <option value="Other" <?php echo $data['operating_system'] == 'Other' ? 'selected' : ''; ?>>Other</option>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="administration_type">Administration Type:</label>
                    <select name="administration_type" class="form-control form-control-lg">
                        <option value="" <?php echo empty($data['administration_type']) ? 'selected' : ''; ?>>Select Type</option>
                        <option value="User" <?php echo $data['administration_type'] == 'User' ? 'selected' : ''; ?>>User</option>
                        <option value="Admin" <?php echo $data['administration_type'] == 'Admin' ? 'selected' : ''; ?>>Admin</option>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="xdr_installed">XDR Installed:</label>
                    <select name="xdr_installed" class="form-control form-control-lg">
                        <option value="" <?php echo empty($data['xdr_installed']) ? 'selected' : ''; ?>>Select</option>
                        <option value="Yes" <?php echo $data['xdr_installed'] == 'Yes' ? 'selected' : ''; ?>>Yes</option>
                        <option value="No" <?php echo $data['xdr_installed'] == 'No' ? 'selected' : ''; ?>>No</option>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="device_custodian">Device Custodian:</label>
                    <input type="text" name="device_custodian" class="form-control form-control-lg" value="<?php echo $data['device_custodian']; ?>">
                </div>
                <div class="form-group mb-3">
                    <label for="remarks">Remarks:</label>
                    <textarea name="remarks" class="form-control form-control-lg"><?php echo $data['remarks']; ?></textarea>
                </div>
                <div class="form-group mb-3">
                    <label for="par_number">PAR Number:</label>
                    <input type="text" name="par_number" class="form-control form-control-lg" value="<?php echo $data['par_number']; ?>">
                </div>
                <div class="form-group mb-3">
                    <label for="serial_number">Serial Number: <sup>*</sup></label>
                    <input type="text" name="serial_number" class="form-control form-control-lg <?php echo (!empty($data['serial_number_err'])) ? 'is-invalid' : ''; ?>" value="<?php echo $data['serial_number']; ?>">
                    <span class="invalid-feedback"><?php echo $data['serial_number_err']; ?></span>
                </div>
                <div class="form-group mb-3">
                    <label for="acquisition_date">Acquisition Date:</label>
                    <input type="date" name="acquisition_date" class="form-control form-control-lg" value="<?php echo $data['acquisition_date']; ?>">
                </div>
                <div class="form-group mb-3">
                    <label for="estimated_useful_life">Estimated Useful Life:</label>
                    <input type="date" name="estimated_useful_life" class="form-control form-control-lg" value="<?php echo $data['estimated_useful_life']; ?>">
                </div>

                <!-- Asset Tags -->
                <div class="form-group mb-3">
                    <label for="tags">Tags:</label>
                    <div class="mb-2">
                        <select id="tag-select" class="form-control form-control-lg">
                            <option value="">Select a tag to add</option>
                            <?php foreach($data['available_tags'] as $tag) : ?>
                                <option value="<?php echo $tag->id; ?>" data-color="<?php echo $tag->color; ?>"><?php echo $tag->name; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div id="selected-tags" class="d-flex flex-wrap gap-2 mb-2">
                        <!-- Tags will be added here dynamically -->
                    </div>
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">Click on a tag to remove it</small>
                        <a href="<?php echo URLROOT; ?>/tags" target="_blank" class="text-primary">
                            <i class="fas fa-plus-circle"></i> Manage Tags
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <input type="submit" class="btn btn-success" value="Submit">
    </form>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tag selection functionality
    const tagSelect = document.getElementById('tag-select');
    const selectedTagsContainer = document.getElementById('selected-tags');

    if (tagSelect && selectedTagsContainer) {
        // Add tag when selected from dropdown
        tagSelect.addEventListener('change', function() {
            if (this.value) {
                const tagId = this.value;
                const tagName = this.options[this.selectedIndex].text;
                const tagColor = this.options[this.selectedIndex].getAttribute('data-color');

                // Check if tag is already selected
                if (!document.querySelector(`input[name="tags[]"][value="${tagId}"]`)) {
                    // Create tag element
                    const tagElement = document.createElement('div');
                    tagElement.className = 'tag-item d-inline-flex align-items-center px-3 py-1 rounded-pill me-2 mb-2';
                    tagElement.style.backgroundColor = `${tagColor}20`;
                    tagElement.style.color = tagColor;
                    tagElement.style.border = `1px solid ${tagColor}`;

                    tagElement.innerHTML = `
                        <span>${tagName}</span>
                        <input type="hidden" name="tags[]" value="${tagId}">
                        <button type="button" class="btn-close btn-close-sm ms-2 remove-tag" aria-label="Remove tag"></button>
                    `;

                    // Add tag to container
                    selectedTagsContainer.appendChild(tagElement);

                    // Reset select
                    this.value = '';
                }
            }
        });

        // Remove tag when clicked
        selectedTagsContainer.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-tag')) {
                const tagItem = e.target.closest('.tag-item');
                if (tagItem) {
                    tagItem.remove();
                }
            }
        });
    }
});
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
