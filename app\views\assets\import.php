<?php
require APPROOT . '/views/inc/header.php';
require_once APPROOT . '/../app/helpers/Security.php';
?>
<!-- Include Google reCAPTCHA API -->
<script src="https://www.google.com/recaptcha/api.js" async defer></script>

<!-- <PERSON> Header -->
<div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
    <div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">Import Assets from CSV</h1>
        <p class="text-gray-600">Upload a CSV file to import assets into the system</p>
    </div>
    <div class="mt-4 md:mt-0 space-x-2">
        <a href="<?php echo URLROOT; ?>/assets" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md transition duration-150 ease-in-out shadow-sm">
            <i class="fa fa-backward mr-2"></i> Back to Assets
        </a>
        <!-- <a href="<?php //echo URLROOT; ?>/assets/test_upload" class="inline-flex items-center px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-md transition duration-150 ease-in-out shadow-sm">
            <i class="fas fa-vial mr-2"></i> Test Upload
        </a> -->
    </div>
</div>

<?php if(isset($data['error'])) : ?>
    <div class="bg-red-100 border-l-4 border-red-500 text-red-800 px-4 py-3 rounded-md mb-6 shadow-sm animate-fadeIn">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
            </div>
            <div class="ml-3">
                <p class="font-medium"><?php echo $data['error']; ?></p>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Main Content -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Left Column: Upload Form -->
    <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-file-upload text-blue-500 mr-2"></i> Upload CSV File
                </h2>
            </div>
            <div class="p-6">
                <form action="<?php echo htmlspecialchars(URLROOT . '/assets/import'); ?>" method="post" enctype="multipart/form-data" id="importForm">
                    <!-- CSRF Token -->
                    <input type="hidden" name="csrf_token" value="<?php echo Security::generateCsrfToken(); ?>">

                    <!-- File Upload Area -->
                    <div class="mb-6">
                        <label for="csv_file" class="block text-sm font-medium text-gray-700 mb-2">
                            CSV File <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-blue-400 transition duration-150 ease-in-out">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="csv_file" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                        <span>Upload a file</span>
                                        <input id="csv_file" name="csv_file" type="file" class="sr-only" accept=".csv" required>
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">
                                    CSV up to <?php echo (SecurityEnhancements::MAX_IMPORT_FILE_SIZE / 1048576); ?>MB
                                </p>
                            </div>
                        </div>
                        <div id="file-name" class="mt-2 text-sm text-gray-500 hidden">
                            <span class="font-medium">Selected file:</span> <span id="selected-file-name"></span>
                        </div>
                    </div>

                    <!-- Skip Rows Option -->
                    <div class="mb-6">
                        <label for="skip_rows" class="block text-sm font-medium text-gray-700 mb-2">
                            Skip Header Rows
                        </label>
                        <div class="flex items-center">
                            <input type="number" name="skip_rows" id="skip_rows" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:w-1/4 border-gray-300 rounded-md" value="<?php echo $data['skip_rows']; ?>" min="0">
                            <div class="ml-3">
                                <div class="flex items-center">
                                    <button type="button" id="decrease-rows" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-1 px-2 rounded-l">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <button type="button" id="increase-rows" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-1 px-2 rounded-r">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">
                            Number of header rows to skip in the CSV file. Default is 1 for the standard template.
                        </p>
                    </div>

                    <!-- Google reCAPTCHA -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Security Verification <span class="text-red-500">*</span>
                        </label>
                        <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                        <p class="mt-1 text-sm text-gray-500">
                            Please complete the CAPTCHA verification to proceed with the import.
                        </p>
                    </div>

                    <!-- Preview Button -->
                    <div class="flex justify-between">
                        <button type="button" id="previewBtn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md inline-flex items-center shadow-sm transition duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-eye mr-2"></i> Preview Data
                        </button>

                        <!-- Submit Button -->
                        <button type="submit" id="submitBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md inline-flex items-center shadow-sm transition duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-file-import mr-2"></i> Import Assets
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Right Column: Information Panels -->
    <div class="space-y-6">
        <!-- Format Requirements Panel -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
            <div class="bg-blue-50 px-6 py-4 border-b border-blue-200">
                <h3 class="text-lg font-semibold text-blue-800 flex items-center">
                    <i class="fas fa-info-circle mr-2"></i> CSV Format Requirements
                </h3>
            </div>
            <div class="p-6">
                <p class="mb-4 text-gray-700">The CSV file should have the following columns in order:</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <ol class="list-decimal pl-5 space-y-2 text-gray-700">
                            <li>Date</li>
                            <li>Site Name</li>
                            <li>Employee Name</li>
                            <li>Active Directory Name</li>
                            <li>Position</li>
                            <li>Program/Section</li>
                            <li>Computer/Host Name</li>
                            <li>Type of Equipment</li>
                            <li>Acquisition Type</li>
                        </ol>
                    </div>
                    <div>
                        <ol class="list-decimal pl-5 space-y-2 text-gray-700" start="10">
                            <li>Operating System</li>
                            <li>Administration Type</li>
                            <li>XDR Installed (Yes/No)</li>
                            <li>Device Custodian</li>
                            <li>Remarks</li>
                            <li>PAR Number</li>
                            <li>Serial Number</li>
                            <li>Acquisition Date</li>
                            <li>Estimated Useful Life</li>
                        </ol>
                    </div>
                </div>
                <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-200">
                    <p class="font-bold text-blue-800">Required fields:</p>
                    <ul class="list-disc pl-5 mt-1 text-blue-800">
                        <li>Computer/Host Name</li>
                        <li>Type of Equipment</li>
                        <li>Serial Number</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Security Information Panel -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
            <div class="bg-yellow-50 px-6 py-4 border-b border-yellow-200">
                <h3 class="text-lg font-semibold text-yellow-800 flex items-center">
                    <i class="fas fa-shield-alt mr-2"></i> Security Information
                </h3>
            </div>
            <div class="p-6">
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file-alt text-yellow-500 mt-1"></i>
                        </div>
                        <p class="ml-3 text-gray-700">
                            <span class="font-medium">Maximum file size:</span> <?php echo (SecurityEnhancements::MAX_IMPORT_FILE_SIZE / 1048576); ?>MB
                        </p>
                    </li>
                    <li class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file-csv text-yellow-500 mt-1"></i>
                        </div>
                        <p class="ml-3 text-gray-700">
                            <span class="font-medium">File type:</span> Only CSV files are allowed
                        </p>
                    </li>
                    <li class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-clock text-yellow-500 mt-1"></i>
                        </div>
                        <p class="ml-3 text-gray-700">
                            <span class="font-medium">Rate limit:</span> <?php echo SecurityEnhancements::IMPORT_RATE_LIMIT; ?> imports per hour
                        </p>
                    </li>
                    <li class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-history text-yellow-500 mt-1"></i>
                        </div>
                        <p class="ml-3 text-gray-700">
                            <span class="font-medium">Logging:</span> All import operations are logged for security purposes
                        </p>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- CSV Preview Modal -->
<div id="csvPreviewModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden z-50 items-center justify-center overflow-y-auto" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-screen flex flex-col">
        <!-- Modal Header -->
        <div class="bg-gray-100 px-6 py-4 border-b border-gray-200 rounded-t-lg flex justify-between items-center">
            <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                <i class="fas fa-table text-blue-500 mr-2"></i> CSV Data Preview
            </h3>
            <button id="closePreviewModal" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="p-6 overflow-auto flex-grow">
            <div id="previewLoading" class="flex flex-col items-center justify-center py-12">
                <div class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
                <p class="text-gray-600">Loading preview...</p>
            </div>

            <div id="previewContent" class="hidden">
                <!-- File Info -->
                <div class="mb-6 bg-blue-50 p-4 rounded-md">
                    <div class="flex flex-wrap gap-4">
                        <div>
                            <span class="font-medium text-blue-800">File:</span>
                            <span id="preview-filename" class="text-gray-700"></span>
                        </div>
                        <div>
                            <span class="font-medium text-blue-800">Size:</span>
                            <span id="preview-filesize" class="text-gray-700"></span>
                        </div>
                        <div>
                            <span class="font-medium text-blue-800">Rows:</span>
                            <span id="preview-rowcount" class="text-gray-700"></span>
                        </div>
                        <div>
                            <span class="font-medium text-blue-800">Columns:</span>
                            <span id="preview-colcount" class="text-gray-700"></span>
                        </div>
                    </div>
                </div>

                <!-- Column Mapping -->
                <div class="mb-6">
                    <h4 class="text-lg font-medium text-gray-800 mb-2">Column Mapping</h4>
                    <div class="bg-yellow-50 p-4 rounded-md mb-4">
                        <p class="text-yellow-800">
                            <i class="fas fa-info-circle mr-1"></i>
                            Verify that your CSV columns match the expected order. Required fields are marked with <span class="text-red-500">*</span>
                        </p>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expected Column</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CSV Column</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="column-mapping-body">
                                <!-- Column mapping rows will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Data Preview -->
                <div>
                    <h4 class="text-lg font-medium text-gray-800 mb-2">Data Preview (First 5 Rows)</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50" id="preview-table-head">
                                <!-- Table headers will be inserted here -->
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="preview-table-body">
                                <!-- Table rows will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Validation Results -->
                <div id="validation-results" class="mt-6 hidden">
                    <h4 class="text-lg font-medium text-gray-800 mb-2">Validation Results</h4>
                    <div id="validation-content" class="p-4 rounded-md">
                        <!-- Validation content will be inserted here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gray-100 px-6 py-4 border-t border-gray-200 rounded-b-lg flex justify-between">
            <button id="cancelImport" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md inline-flex items-center transition duration-150 ease-in-out">
                <i class="fas fa-times mr-2"></i> Cancel
            </button>
            <button id="confirmImport" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md inline-flex items-center transition duration-150 ease-in-out">
                <i class="fas fa-check mr-2"></i> Proceed with Import
            </button>
        </div>
    </div>
</div>

<!-- Custom JavaScript for file upload, preview and form interaction -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // File upload handling
    const fileInput = document.getElementById('csv_file');
    const fileNameDisplay = document.getElementById('file-name');
    const selectedFileName = document.getElementById('selected-file-name');
    const uploadArea = fileInput.closest('.border-dashed');
    const previewBtn = document.getElementById('previewBtn');
    const submitBtn = document.getElementById('submitBtn');

    // Modal elements
    const csvPreviewModal = document.getElementById('csvPreviewModal');
    const closePreviewModal = document.getElementById('closePreviewModal');
    const cancelImport = document.getElementById('cancelImport');
    const confirmImport = document.getElementById('confirmImport');
    const previewLoading = document.getElementById('previewLoading');
    const previewContent = document.getElementById('previewContent');

    // Preview data elements
    const previewFilename = document.getElementById('preview-filename');
    const previewFilesize = document.getElementById('preview-filesize');
    const previewRowcount = document.getElementById('preview-rowcount');
    const previewColcount = document.getElementById('preview-colcount');
    const columnMappingBody = document.getElementById('column-mapping-body');
    const previewTableHead = document.getElementById('preview-table-head');
    const previewTableBody = document.getElementById('preview-table-body');
    const validationResults = document.getElementById('validation-results');
    const validationContent = document.getElementById('validation-content');

    // Expected columns (in order)
    const expectedColumns = [
        { name: 'Date', required: false },
        { name: 'Site Name', required: false },
        { name: 'Employee Name', required: false },
        { name: 'Active Directory Name', required: false },
        { name: 'Position', required: false },
        { name: 'Program/Section', required: false },
        { name: 'Computer/Host Name', required: true },
        { name: 'Type of Equipment', required: true },
        { name: 'Acquisition Type', required: false },
        { name: 'Operating System', required: false },
        { name: 'Administration Type', required: false },
        { name: 'XDR Installed (Yes/No)', required: false },
        { name: 'Device Custodian', required: false },
        { name: 'Remarks', required: false },
        { name: 'PAR Number', required: false },
        { name: 'Serial Number', required: true },
        { name: 'Acquisition Date', required: false },
        { name: 'Estimated Useful Life', required: false }
    ];

    // Disable preview and submit buttons initially
    previewBtn.disabled = true;

    // Handle file selection
    fileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            selectedFileName.textContent = this.files[0].name;
            fileNameDisplay.classList.remove('hidden');
            uploadArea.classList.add('border-blue-500');
            uploadArea.classList.remove('border-gray-300', 'border-dashed');

            // Enable preview button
            previewBtn.disabled = false;
        } else {
            fileNameDisplay.classList.add('hidden');
            uploadArea.classList.remove('border-blue-500');
            uploadArea.classList.add('border-gray-300', 'border-dashed');

            // Disable preview button
            previewBtn.disabled = true;
        }
    });

    // Drag and drop functionality
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
        uploadArea.classList.add('border-blue-500');
        uploadArea.classList.remove('border-gray-300');
    }

    function unhighlight() {
        uploadArea.classList.remove('border-blue-500');
        uploadArea.classList.add('border-gray-300');
    }

    uploadArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        fileInput.files = files;

        if (files && files[0]) {
            selectedFileName.textContent = files[0].name;
            fileNameDisplay.classList.remove('hidden');

            // Enable preview button
            previewBtn.disabled = false;
        }
    }

    // Skip rows increment/decrement buttons
    const skipRowsInput = document.getElementById('skip_rows');
    const decreaseRowsBtn = document.getElementById('decrease-rows');
    const increaseRowsBtn = document.getElementById('increase-rows');

    decreaseRowsBtn.addEventListener('click', function() {
        const currentValue = parseInt(skipRowsInput.value) || 0;
        if (currentValue > 0) {
            skipRowsInput.value = currentValue - 1;
        }
    });

    increaseRowsBtn.addEventListener('click', function() {
        const currentValue = parseInt(skipRowsInput.value) || 0;
        skipRowsInput.value = currentValue + 1;
    });

    // CSV Preview functionality
    previewBtn.addEventListener('click', function() {
        if (!fileInput.files || !fileInput.files[0]) {
            alert('Please select a CSV file first');
            return;
        }

        // Show modal
        csvPreviewModal.classList.remove('hidden');
        csvPreviewModal.style.display = 'flex';
        previewLoading.classList.remove('hidden');
        previewContent.classList.add('hidden');

        // Parse CSV file
        parseCSV(fileInput.files[0], parseInt(skipRowsInput.value) || 0);
    });

    // Close modal buttons
    closePreviewModal.addEventListener('click', closeModal);
    cancelImport.addEventListener('click', closeModal);

    function closeModal() {
        csvPreviewModal.classList.add('hidden');
        csvPreviewModal.style.display = 'none';
    }

    // Confirm import button
    confirmImport.addEventListener('click', function() {
        closeModal();

        // Submit the form
        const importForm = document.getElementById('importForm');

        // Check if reCAPTCHA is completed
        const recaptchaResponse = grecaptcha.getResponse();
        if (!recaptchaResponse) {
            alert('Please complete the CAPTCHA verification');
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';

        importForm.submit();
    });

    // Parse CSV function
    function parseCSV(file, skipRows) {
        const reader = new FileReader();

        reader.onload = function(e) {
            const contents = e.target.result;
            const lines = contents.split(/\r\n|\n/);
            const headers = parseCSVLine(lines[skipRows]);
            const data = [];

            // Get data rows (skip header rows + get first 5 rows for preview)
            const maxRows = Math.min(lines.length, skipRows + 6);
            for (let i = skipRows + 1; i < maxRows; i++) {
                if (lines[i].trim() !== '') {
                    data.push(parseCSVLine(lines[i]));
                }
            }

            // Display preview
            displayPreview(file, headers, data, lines.length - skipRows - 1);
        };

        reader.onerror = function() {
            previewLoading.classList.add('hidden');
            alert('Error reading the CSV file');
        };

        reader.readAsText(file);
    }

    // Parse CSV line (handles quoted values with commas)
    function parseCSVLine(line) {
        const result = [];
        let currentValue = '';
        let insideQuotes = false;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];

            if (char === '"') {
                insideQuotes = !insideQuotes;
            } else if (char === ',' && !insideQuotes) {
                result.push(currentValue.trim());
                currentValue = '';
            } else {
                currentValue += char;
            }
        }

        // Add the last value
        result.push(currentValue.trim());

        return result;
    }

    // Display preview function
    function displayPreview(file, headers, data, totalRows) {
        // Update file info
        previewFilename.textContent = file.name;
        previewFilesize.textContent = formatFileSize(file.size);
        previewRowcount.textContent = totalRows;
        previewColcount.textContent = headers.length;

        // Clear previous content
        columnMappingBody.innerHTML = '';
        previewTableHead.innerHTML = '';
        previewTableBody.innerHTML = '';

        // Create column mapping table
        createColumnMapping(headers);

        // Create preview table
        createPreviewTable(headers, data);

        // Validate data
        validateData(headers, data);

        // Hide loading, show content
        previewLoading.classList.add('hidden');
        previewContent.classList.remove('hidden');
    }

    // Create column mapping table
    function createColumnMapping(headers) {
        for (let i = 0; i < expectedColumns.length; i++) {
            const expected = expectedColumns[i];
            const csvColumn = headers[i] || '';

            const row = document.createElement('tr');

            // Expected column
            const expectedCell = document.createElement('td');
            expectedCell.className = 'px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900';
            expectedCell.innerHTML = expected.name + (expected.required ? ' <span class="text-red-500">*</span>' : '');
            row.appendChild(expectedCell);

            // CSV column
            const csvCell = document.createElement('td');
            csvCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
            csvCell.textContent = csvColumn || 'Missing';
            row.appendChild(csvCell);

            // Status
            const statusCell = document.createElement('td');
            statusCell.className = 'px-6 py-4 whitespace-nowrap text-sm';

            if (!csvColumn && expected.required) {
                // Missing required column
                statusCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Missing Required</span>';
            } else if (!csvColumn) {
                // Missing optional column
                statusCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Missing Optional</span>';
            } else {
                // Column present
                statusCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">OK</span>';
            }

            row.appendChild(statusCell);
            columnMappingBody.appendChild(row);
        }
    }

    // Create preview table
    function createPreviewTable(headers, data) {
        // Create table header
        const headerRow = document.createElement('tr');

        headers.forEach((header, index) => {
            const th = document.createElement('th');
            th.className = 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider';

            // Highlight required columns
            const isRequired = expectedColumns[index] && expectedColumns[index].required;
            if (isRequired) {
                th.innerHTML = header + ' <span class="text-red-500">*</span>';
            } else {
                th.textContent = header;
            }

            headerRow.appendChild(th);
        });

        previewTableHead.appendChild(headerRow);

        // Create table rows
        data.forEach(row => {
            const tr = document.createElement('tr');
            tr.className = 'hover:bg-gray-50';

            row.forEach((cell, index) => {
                const td = document.createElement('td');
                td.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
                td.textContent = cell;

                // Highlight empty cells in required columns
                const isRequired = expectedColumns[index] && expectedColumns[index].required;
                if (isRequired && !cell) {
                    td.className += ' bg-red-50 text-red-500';
                }

                tr.appendChild(td);
            });

            previewTableBody.appendChild(tr);
        });
    }

    // Validate data
    function validateData(headers, data) {
        const issues = [];
        let hasErrors = false;

        // Check for missing required columns
        expectedColumns.forEach((expected, index) => {
            if (expected.required && (!headers[index] || headers[index].trim() === '')) {
                issues.push({
                    type: 'error',
                    message: `Missing required column: ${expected.name}`
                });
                hasErrors = true;
            }
        });

        // Check for empty cells in required columns
        data.forEach((row, rowIndex) => {
            expectedColumns.forEach((expected, colIndex) => {
                if (expected.required && (!row[colIndex] || row[colIndex].trim() === '')) {
                    issues.push({
                        type: 'error',
                        message: `Empty value in required column "${expected.name}" at row ${rowIndex + 1}`
                    });
                    hasErrors = true;
                }
            });
        });

        // Display validation results
        if (issues.length > 0) {
            validationResults.classList.remove('hidden');

            if (hasErrors) {
                validationContent.className = 'p-4 rounded-md bg-red-50 border border-red-200';
                validationContent.innerHTML = '<div class="flex items-center mb-3"><i class="fas fa-exclamation-circle text-red-500 mr-2"></i><h5 class="font-medium text-red-800">Validation Errors</h5></div>';
            } else {
                validationContent.className = 'p-4 rounded-md bg-yellow-50 border border-yellow-200';
                validationContent.innerHTML = '<div class="flex items-center mb-3"><i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i><h5 class="font-medium text-yellow-800">Validation Warnings</h5></div>';
            }

            const issuesList = document.createElement('ul');
            issuesList.className = 'list-disc pl-5 space-y-1';

            issues.forEach(issue => {
                const li = document.createElement('li');
                li.className = issue.type === 'error' ? 'text-red-700' : 'text-yellow-700';
                li.textContent = issue.message;
                issuesList.appendChild(li);
            });

            validationContent.appendChild(issuesList);

            // Disable confirm button if there are errors
            if (hasErrors) {
                confirmImport.disabled = true;
                confirmImport.classList.add('opacity-50', 'cursor-not-allowed');
            } else {
                confirmImport.disabled = false;
                confirmImport.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        } else {
            validationResults.classList.add('hidden');
            confirmImport.disabled = false;
            confirmImport.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }

    // Format file size
    function formatFileSize(bytes) {
        if (bytes < 1024) {
            return bytes + ' bytes';
        } else if (bytes < 1048576) {
            return (bytes / 1024).toFixed(1) + ' KB';
        } else {
            return (bytes / 1048576).toFixed(1) + ' MB';
        }
    }

    // Form submission loading state
    const importForm = document.getElementById('importForm');

    importForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate file input
        if (!fileInput.files || !fileInput.files[0]) {
            alert('Please select a CSV file first');
            return false;
        }

        // Show preview instead of direct submission
        previewBtn.click();
    });
});
</script>

<style>
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
.animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
}
</style>

<?php require APPROOT . '/views/inc/footer.php'; ?>
