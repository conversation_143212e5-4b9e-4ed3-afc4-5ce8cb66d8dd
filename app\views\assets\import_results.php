<?php require APPROOT . '/views/inc/header.php'; ?>
<?php
// Debug log to verify data is being passed correctly
error_log("Import Results View - Results data: " . print_r($data['results'] ?? 'No results data', true));
?>
<div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">Import Results</h1>
        <p class="text-gray-600">Summary of the CSV import operation</p>
    </div>
    <div class="mt-4 md:mt-0 space-x-2">
        <a href="<?php echo URLROOT; ?>/assets" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md">
            <i class="fa fa-backward mr-2"></i> Back to Assets
        </a>
        <a href="<?php echo URLROOT; ?>/assets/import" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            <i class="fas fa-file-import mr-2"></i> Import Another File
        </a>
    </div>
</div>

<?php if(isset($data['results']) && is_array($data['results'])): ?>
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow overflow-hidden border-t-4 border-green-500">
        <div class="bg-green-50 px-4 py-2 text-green-700 font-medium">
            Successfully Imported
        </div>
        <div class="p-6 text-center">
            <div class="text-5xl font-bold text-green-600 mb-2"><?php echo $data['results']['success'] ?? 0; ?></div>
            <p class="text-gray-600">Assets were successfully added to the database.</p>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden border-t-4 border-yellow-500">
        <div class="bg-yellow-50 px-4 py-2 text-yellow-700 font-medium">
            Skipped
        </div>
        <div class="p-6 text-center">
            <div class="text-5xl font-bold text-yellow-600 mb-2"><?php echo $data['results']['skipped'] ?? 0; ?></div>
            <p class="text-gray-600">Rows were skipped due to missing required fields.</p>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden border-t-4 border-blue-500">
        <div class="bg-blue-50 px-4 py-2 text-blue-700 font-medium">
            Duplicates
        </div>
        <div class="p-6 text-center">
            <div class="text-5xl font-bold text-blue-600 mb-2"><?php echo $data['results']['duplicates'] ?? 0; ?></div>
            <p class="text-gray-600">Assets were skipped due to duplicate serial numbers.</p>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden border-t-4 border-red-500">
        <div class="bg-red-50 px-4 py-2 text-red-700 font-medium">
            Failed
        </div>
        <div class="p-6 text-center">
            <div class="text-5xl font-bold text-red-600 mb-2"><?php echo $data['results']['failed'] ?? 0; ?></div>
            <p class="text-gray-600">Assets could not be imported due to errors.</p>
        </div>
    </div>
</div>
<?php else: ?>
<div class="bg-white rounded-lg shadow overflow-hidden mb-6">
    <div class="bg-yellow-600 px-6 py-3 text-white">
        <h4 class="font-medium flex items-center">
            <i class="fas fa-exclamation-triangle mr-2"></i> No Import Results
        </h4>
    </div>
    <div class="p-6">
        <p>No import results data is available. Please try importing a file again.</p>
    </div>
</div>
<?php endif; ?>

<?php if(isset($data['results']) && is_array($data['results']) && !empty($data['results']['errors'])) : ?>
    <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
        <div class="bg-red-600 px-6 py-3 text-white">
            <h4 class="font-medium flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i> Error Details
            </h4>
        </div>
        <div class="p-6">
            <ul class="divide-y divide-gray-200">
                <?php foreach($data['results']['errors'] as $error) : ?>
                    <li class="py-3 text-red-600"><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
<?php endif; ?>

<?php if(isset($data['results']) && is_array($data['results']) && !empty($data['results']['duplicate_serials'])) : ?>
    <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
        <div class="bg-blue-600 px-6 py-3 text-white">
            <h4 class="font-medium flex items-center">
                <i class="fas fa-copy mr-2"></i> Duplicate Serial Numbers
            </h4>
        </div>
        <div class="p-6">
            <p class="mb-4">The following assets were not imported because their serial numbers already exist in the database:</p>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Computer Host Name</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipment Type</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['results']['duplicate_serials'] as $duplicate) : ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?php echo htmlspecialchars($duplicate['serial_number']); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo htmlspecialchars($duplicate['computer_host_name']); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo htmlspecialchars($duplicate['equipment_type']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php require APPROOT . '/views/inc/footer.php'; ?>
