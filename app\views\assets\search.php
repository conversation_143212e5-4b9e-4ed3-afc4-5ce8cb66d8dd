<?php
require APPROOT . '/views/inc/header.php';
require_once APPROOT . '/models/Tag.php';
?>
<div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">Search Results</h1>
        <?php if(!isset($data['is_advanced']) || !$data['is_advanced']) : ?>
            <?php if(empty($data['term'])) : ?>
                <p class="text-gray-600">Showing all assets</p>
            <?php else : ?>
                <p class="text-gray-600">Showing results for "<?php echo $data['term']; ?>"</p>
            <?php endif; ?>
        <?php else : ?>
            <p class="text-gray-600">Showing results for advanced search</p>
        <?php endif; ?>
    </div>

    <div class="w-full md:w-auto mt-4 md:mt-0">
        <!-- Basic Search Form with Advanced Search Button -->
        <form action="<?php echo URLROOT; ?>/assets/search" method="GET" id="basic-search-form" class="flex mb-2 relative">
            <div class="relative flex-grow">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
                <input type="text" name="term" id="search-input" class="form-control pl-10 pr-10 py-2 w-full rounded-l-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Search assets..."
                       value="<?php echo isset($data['term']) ? $data['term'] : (isset($data['params']['term']) ? $data['params']['term'] : ''); ?>">
            </div>
            <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-r-md transition-colors" type="submit">
                Search
            </button>
            <button type="button" id="advanced-search-toggle" class="absolute right-[100px] top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600 transition-colors">
                <i class="fas fa-sliders-h"></i>
            </button>
        </form>

        <!-- Advanced Search Modal -->
        <div id="advanced-search-modal" class="fixed inset-0 z-50 hidden">
            <!-- Modal Backdrop -->
            <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" id="modal-backdrop"></div>

            <!-- Modal Content -->
            <div class="fixed inset-0 z-10 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
                    <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl">
                        <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                            <div class="flex items-center justify-between border-b border-gray-200 pb-3 mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Advanced Search</h3>
                                <button type="button" id="close-modal-btn" class="text-gray-400 hover:text-gray-500">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <form action="<?php echo URLROOT; ?>/assets/search" method="GET" id="advanced-search-form">
                                <input type="hidden" name="advanced" value="1">

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <!-- General Search Term -->
                    <div>
                        <label for="adv-term" class="block text-sm font-medium text-gray-700 mb-1">Search Term</label>
                        <input type="text" name="term" id="adv-term" class="form-control w-full rounded-md"
                               placeholder="Search across all fields..."
                               value="<?php echo isset($data['params']['term']) ? $data['params']['term'] : ''; ?>">
                    </div>

                    <!-- Host Name -->
                    <div>
                        <label for="computer_host_name" class="block text-sm font-medium text-gray-700 mb-1">Host Name</label>
                        <input type="text" name="computer_host_name" id="computer_host_name" class="form-control w-full rounded-md"
                               placeholder="Exact host name..."
                               value="<?php echo isset($data['params']['computer_host_name']) ? $data['params']['computer_host_name'] : ''; ?>">
                    </div>

                    <!-- Employee Name -->
                    <div>
                        <label for="employee_name" class="block text-sm font-medium text-gray-700 mb-1">Employee Name</label>
                        <input type="text" name="employee_name" id="employee_name" class="form-control w-full rounded-md"
                               placeholder="Exact employee name..."
                               value="<?php echo isset($data['params']['employee_name']) ? $data['params']['employee_name'] : ''; ?>">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <!-- Equipment Type -->
                    <div>
                        <label for="equipment_type" class="block text-sm font-medium text-gray-700 mb-1">Equipment Type</label>
                        <select name="equipment_type" id="equipment_type" class="form-control w-full rounded-md">
                            <option value="">All Types</option>
                            <?php foreach($data['filter_options']['equipment_types'] as $type) : ?>
                                <option value="<?php echo $type->equipment_type; ?>"
                                        <?php echo (isset($data['params']['equipment_type']) && $data['params']['equipment_type'] == $type->equipment_type) ? 'selected' : ''; ?>>
                                    <?php echo $type->equipment_type; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Site Name -->
                    <div>
                        <label for="site_name" class="block text-sm font-medium text-gray-700 mb-1">Site Name</label>
                        <select name="site_name" id="site_name" class="form-control w-full rounded-md">
                            <option value="">All Sites</option>
                            <?php foreach($data['filter_options']['site_names'] as $site) : ?>
                                <option value="<?php echo $site->site_name; ?>"
                                        <?php echo (isset($data['params']['site_name']) && $data['params']['site_name'] == $site->site_name) ? 'selected' : ''; ?>>
                                    <?php echo $site->site_name; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Operating System -->
                    <div>
                        <label for="operating_system" class="block text-sm font-medium text-gray-700 mb-1">Operating System</label>
                        <select name="operating_system" id="operating_system" class="form-control w-full rounded-md">
                            <option value="">All OS</option>
                            <?php foreach($data['filter_options']['operating_systems'] as $os) : ?>
                                <option value="<?php echo $os->operating_system; ?>"
                                        <?php echo (isset($data['params']['operating_system']) && $data['params']['operating_system'] == $os->operating_system) ? 'selected' : ''; ?>>
                                    <?php echo $os->operating_system; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <!-- Acquisition Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Acquisition Date Range</label>
                        <div class="grid grid-cols-2 gap-2">
                            <div>
                                <input type="date" name="acquisition_date_from" class="form-control w-full rounded-md"
                                       placeholder="From"
                                       value="<?php echo isset($data['params']['acquisition_date_from']) ? $data['params']['acquisition_date_from'] : ''; ?>">
                            </div>
                            <div>
                                <input type="date" name="acquisition_date_to" class="form-control w-full rounded-md"
                                       placeholder="To"
                                       value="<?php echo isset($data['params']['acquisition_date_to']) ? $data['params']['acquisition_date_to'] : ''; ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Inventory Date Range</label>
                        <div class="grid grid-cols-2 gap-2">
                            <div>
                                <input type="date" name="inventory_date_from" class="form-control w-full rounded-md"
                                       placeholder="From"
                                       value="<?php echo isset($data['params']['inventory_date_from']) ? $data['params']['inventory_date_from'] : ''; ?>">
                            </div>
                            <div>
                                <input type="date" name="inventory_date_to" class="form-control w-full rounded-md"
                                       placeholder="To"
                                       value="<?php echo isset($data['params']['inventory_date_to']) ? $data['params']['inventory_date_to'] : ''; ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <!-- Program Section -->
                    <div>
                        <label for="program_section" class="block text-sm font-medium text-gray-700 mb-1">Program Section</label>
                        <select name="program_section" id="program_section" class="form-control w-full rounded-md">
                            <option value="">All Sections</option>
                            <?php foreach($data['filter_options']['program_sections'] as $section) : ?>
                                <option value="<?php echo $section->program_section; ?>"
                                        <?php echo (isset($data['params']['program_section']) && $data['params']['program_section'] == $section->program_section) ? 'selected' : ''; ?>>
                                    <?php echo $section->program_section; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Administration Type -->
                    <div>
                        <label for="administration_type" class="block text-sm font-medium text-gray-700 mb-1">Administration Type</label>
                        <select name="administration_type" id="administration_type" class="form-control w-full rounded-md">
                            <option value="">All Types</option>
                            <?php foreach($data['filter_options']['administration_types'] as $type) : ?>
                                <option value="<?php echo $type->administration_type; ?>"
                                        <?php echo (isset($data['params']['administration_type']) && $data['params']['administration_type'] == $type->administration_type) ? 'selected' : ''; ?>>
                                    <?php echo $type->administration_type; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- XDR Installed -->
                    <div>
                        <label for="xdr_installed" class="block text-sm font-medium text-gray-700 mb-1">XDR Installed</label>
                        <select name="xdr_installed" id="xdr_installed" class="form-control w-full rounded-md">
                            <option value="">All</option>
                            <?php foreach($data['filter_options']['xdr_options'] as $option) : ?>
                                <option value="<?php echo $option->xdr_installed; ?>"
                                        <?php echo (isset($data['params']['xdr_installed']) && $data['params']['xdr_installed'] == $option->xdr_installed) ? 'selected' : ''; ?>>
                                    <?php echo $option->xdr_installed; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Tag Filter -->
                    <div>
                        <label for="tag_id" class="block text-sm font-medium text-gray-700 mb-1">Tag</label>
                        <select name="tag_id" id="tag_id" class="form-control w-full rounded-md">
                            <option value="">All Tags</option>
                            <?php foreach($data['tags'] as $tag) : ?>
                                <option value="<?php echo $tag->id; ?>"
                                        <?php echo (isset($data['params']['tag_id']) && $data['params']['tag_id'] == $tag->id) ? 'selected' : ''; ?>
                                        style="color: <?php echo $tag->color; ?>;">
                                    <?php echo $tag->name; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <!-- Sort Options -->
                    <div>
                        <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                        <select name="sort_by" id="sort_by" class="form-control w-full rounded-md">
                            <option value="id" <?php echo (isset($data['params']['sort_by']) && $data['params']['sort_by'] == 'id') ? 'selected' : ''; ?>>Date Added</option>
                            <option value="computer_host_name" <?php echo (isset($data['params']['sort_by']) && $data['params']['sort_by'] == 'computer_host_name') ? 'selected' : ''; ?>>Host Name</option>
                            <option value="equipment_type" <?php echo (isset($data['params']['sort_by']) && $data['params']['sort_by'] == 'equipment_type') ? 'selected' : ''; ?>>Equipment Type</option>
                            <option value="employee_name" <?php echo (isset($data['params']['sort_by']) && $data['params']['sort_by'] == 'employee_name') ? 'selected' : ''; ?>>Employee Name</option>
                            <option value="serial_number" <?php echo (isset($data['params']['sort_by']) && $data['params']['sort_by'] == 'serial_number') ? 'selected' : ''; ?>>Serial Number</option>
                            <option value="acquisition_date" <?php echo (isset($data['params']['sort_by']) && $data['params']['sort_by'] == 'acquisition_date') ? 'selected' : ''; ?>>Acquisition Date</option>
                            <option value="inventory_date" <?php echo (isset($data['params']['sort_by']) && $data['params']['sort_by'] == 'inventory_date') ? 'selected' : ''; ?>>Inventory Date</option>
                            <option value="site_name" <?php echo (isset($data['params']['sort_by']) && $data['params']['sort_by'] == 'site_name') ? 'selected' : ''; ?>>Site Name</option>
                        </select>
                    </div>

                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
                        <select name="sort_order" id="sort_order" class="form-control w-full rounded-md">
                            <option value="DESC" <?php echo (!isset($data['params']['sort_order']) || $data['params']['sort_order'] == 'DESC') ? 'selected' : ''; ?>>Descending</option>
                            <option value="ASC" <?php echo (isset($data['params']['sort_order']) && $data['params']['sort_order'] == 'ASC') ? 'selected' : ''; ?>>Ascending</option>
                        </select>
                    </div>
                </div>

                <div class="flex justify-between mt-6">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                        <i class="fas fa-search mr-1"></i> Search
                    </button>
                    <div>
                        <button type="reset" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md mr-2 transition-colors">
                            <i class="fas fa-undo mr-1"></i> Reset
                        </button>
                        <button type="button" id="close-modal-btn2" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors">
                            <i class="fas fa-times mr-1"></i> Close
                        </button>
                    </div>
                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Applied Filters (for advanced search) -->
<?php if(isset($data['is_advanced']) && $data['is_advanced'] && !empty($data['params'])) : ?>
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div class="flex justify-between items-center mb-2">
            <h3 class="text-sm font-medium text-blue-800">Applied Filters</h3>
            <a href="<?php echo URLROOT; ?>/assets" class="text-blue-600 hover:text-blue-800 text-sm">
                <i class="fas fa-times-circle"></i> Clear All
            </a>
        </div>
        <div class="flex flex-wrap gap-2">
            <?php
            $filterLabels = [
                'term' => 'Search Term',
                'computer_host_name' => 'Host Name',
                'employee_name' => 'Employee Name',
                'equipment_type' => 'Equipment Type',
                'site_name' => 'Site Name',
                'operating_system' => 'Operating System',
                'program_section' => 'Program Section',
                'administration_type' => 'Administration Type',
                'xdr_installed' => 'XDR Installed',
                'acquisition_date_from' => 'Acquisition Date From',
                'acquisition_date_to' => 'Acquisition Date To',
                'inventory_date_from' => 'Inventory Date From',
                'inventory_date_to' => 'Inventory Date To',
                'tag_id' => 'Tag'
            ];

            foreach($data['params'] as $key => $value) :
                if(in_array($key, ['advanced', 'sort_by', 'sort_order']) || empty($value)) continue;

                // Special handling for tag_id to show tag name and color
                if($key == 'tag_id') {
                    $tagFound = false;
                    foreach($data['tags'] as $tag) {
                        if($tag->id == $value) {
                            $tagFound = true;
                            ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                  style="background-color: <?php echo $tag->color; ?>20; color: <?php echo $tag->color; ?>; border: 1px solid <?php echo $tag->color; ?>;">
                                Tag: <?php echo $tag->name; ?>
                            </span>
                            <?php
                            break;
                        }
                    }
                    if($tagFound) continue;
                }
            ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <?php echo isset($filterLabels[$key]) ? $filterLabels[$key] : $key; ?>: <?php echo $value; ?>
                </span>
            <?php endforeach; ?>

            <?php if(isset($data['params']['sort_by'])) : ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    Sort: <?php echo ucfirst(str_replace('_', ' ', $data['params']['sort_by'])); ?>
                    (<?php echo isset($data['params']['sort_order']) && $data['params']['sort_order'] == 'ASC' ? 'Ascending' : 'Descending'; ?>)
                </span>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<div class="flex flex-wrap gap-2 mb-6">
    <a href="<?php echo URLROOT; ?>/assets" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md">
        <i class="fa fa-backward mr-2"></i> Back to Assets
    </a>

    <?php if(hasPermission('export_assets')) : ?>
        <?php if(isset($data['is_advanced']) && $data['is_advanced'] && !empty($data['params'])) : ?>
            <!-- For advanced search, we need to build the export URL with all parameters -->
            <a href="<?php echo URLROOT; ?>/assets/export/advanced?<?php echo http_build_query($data['params']); ?>"
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md inline-flex items-center">
                <i class="fas fa-file-export mr-2"></i> Export Results
            </a>
        <?php else : ?>
            <!-- For basic search, we can use the existing URL structure -->
            <a href="<?php echo URLROOT; ?>/assets/export/<?php echo urlencode($data['term']); ?>"
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md inline-flex items-center">
                <i class="fas fa-file-export mr-2"></i> Export Results
            </a>
        <?php endif; ?>
    <?php endif; ?>
</div>

<?php if(count($data['assets']) > 0) : ?>
    <div>

        <div class="overflow-x-auto bg-white rounded-lg shadow">
            <table class="table min-w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <?php
                        // Function to generate sort URL
                        function getSortUrl($field, $currentSort, $currentOrder) {
                            $queryParams = $_GET;
                            $queryParams['sort'] = $field;
                            $queryParams['order'] = ($currentSort === $field && $currentOrder === 'ASC') ? 'DESC' : 'ASC';
                            return '?' . http_build_query($queryParams);
                        }

                        // Function to display sort indicator
                        function getSortIndicator($field, $currentSort, $currentOrder) {
                            if ($currentSort === $field) {
                                return $currentOrder === 'ASC' ? ' ↑' : ' ↓';
                            }
                            return '';
                        }
                        ?>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <a href="<?php echo getSortUrl('computer_host_name', $data['sort']['field'], $data['sort']['order']); ?>" class="text-gray-500 hover:text-gray-700">
                                Host Name<?php echo getSortIndicator('computer_host_name', $data['sort']['field'], $data['sort']['order']); ?>
                            </a>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <a href="<?php echo getSortUrl('equipment_type', $data['sort']['field'], $data['sort']['order']); ?>" class="text-gray-500 hover:text-gray-700">
                                Type<?php echo getSortIndicator('equipment_type', $data['sort']['field'], $data['sort']['order']); ?>
                            </a>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <a href="<?php echo getSortUrl('employee_name', $data['sort']['field'], $data['sort']['order']); ?>" class="text-gray-500 hover:text-gray-700">
                                Employee<?php echo getSortIndicator('employee_name', $data['sort']['field'], $data['sort']['order']); ?>
                            </a>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <a href="<?php echo getSortUrl('serial_number', $data['sort']['field'], $data['sort']['order']); ?>" class="text-gray-500 hover:text-gray-700">
                                Serial Number<?php echo getSortIndicator('serial_number', $data['sort']['field'], $data['sort']['order']); ?>
                            </a>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <a href="<?php echo getSortUrl('acquisition_date', $data['sort']['field'], $data['sort']['order']); ?>" class="text-gray-500 hover:text-gray-700">
                                Acquisition Date<?php echo getSortIndicator('acquisition_date', $data['sort']['field'], $data['sort']['order']); ?>
                            </a>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tags</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <?php foreach($data['assets'] as $asset) : ?>
                        <tr class="hover:bg-gray-50">

                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo isset($asset->computer_host_name) ? $asset->computer_host_name : $asset['computer_host_name']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo isset($asset->equipment_type) ? $asset->equipment_type : $asset['equipment_type']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo isset($asset->employee_name) ? $asset->employee_name : $asset['employee_name']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo isset($asset->serial_number) ? $asset->serial_number : $asset['serial_number']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo isset($asset->acquisition_date) ? $asset->acquisition_date : $asset['acquisition_date']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php
                                $tagModel = new Tag();
                                $assetId = isset($asset->id) ? $asset->id : $asset['id'];
                                $assetTags = $tagModel->getTagsForAsset($assetId);
                                if(!empty($assetTags)) :
                                ?>
                                    <div class="flex flex-wrap gap-1">
                                        <?php foreach($assetTags as $tag) : ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                                                style="background-color: <?php echo $tag->color; ?>20; color: <?php echo $tag->color; ?>; border: 1px solid <?php echo $tag->color; ?>;">
                                                <?php echo $tag->name; ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="<?php echo URLROOT; ?>/assets/show/<?php echo isset($asset->id) ? $asset->id : $asset['id']; ?>" class="bg-blue-100 text-blue-600 hover:bg-blue-200 p-2 rounded-md">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo URLROOT; ?>/assets/edit/<?php echo isset($asset->id) ? $asset->id : $asset['id']; ?>" class="bg-yellow-100 text-yellow-600 hover:bg-yellow-200 p-2 rounded-md">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo URLROOT; ?>/assets/delete/<?php echo isset($asset->id) ? $asset->id : $asset['id']; ?>" method="post" class="inline">
                                        <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
                                        <button type="submit" class="bg-red-100 text-red-600 hover:bg-red-200 p-2 rounded-md delete-asset">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
            </tbody>
        </table>

        <!-- Pagination -->
        <?php require APPROOT . '/views/inc/pagination.php'; ?>
    </div>
</div>
<?php else : ?>
    <div class="bg-white rounded-lg shadow p-6">
        <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <h3 class="mt-2 text-xl font-medium text-gray-900">No results found</h3>
            <?php if(empty($data['term'])) : ?>
                <p class="mt-1 text-sm text-gray-500">No assets found in the database.</p>
            <?php else : ?>
                <p class="mt-1 text-sm text-gray-500">No assets found matching "<?php echo $data['term']; ?>"</p>
            <?php endif; ?>
            <div class="mt-6">
                <a href="<?php echo URLROOT; ?>/assets" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    View All Assets
                </a>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php require APPROOT . '/views/inc/footer.php'; ?>
