<?php require APPROOT . '/views/inc/header.php'; ?>
<a href="<?php echo URLROOT; ?>/assets" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md mb-4">
    <i class="fa fa-backward mr-2"></i> Back to Assets
</a>

<div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Asset Details</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Computer/Host Name</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->computer_host_name; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Equipment Type</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->equipment_type; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Serial Number</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->serial_number; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Employee Name</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->employee_name; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Position</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->position; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Program/Section</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->program_section; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Site Name</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->site_name; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Active Directory Name</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->active_directory_name; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Acquisition Type</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->acquisition_type; ?></dd>
                    </div>
                </dl>
            </div>
            <div>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Operating System</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->operating_system; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Administration Type</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->administration_type; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">XDR Installed</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->xdr_installed; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Device Custodian</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->device_custodian; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">PAR Number</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->par_number; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Acquisition Date</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->acquisition_date; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Estimated Useful Life</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->estimated_useful_life; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Inventory Date</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->inventory_date; ?></dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Remarks</dt>
                        <dd class="mt-1 text-base text-gray-900"><?php echo $data['asset']->remarks; ?></dd>
                    </div>

                    <!-- Asset Tags -->
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Tags</dt>
                        <dd class="mt-1">
                            <?php if(!empty($data['tags'])) : ?>
                                <div class="flex flex-wrap gap-2">
                                    <?php foreach($data['tags'] as $tag) : ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                              style="background-color: <?php echo $tag->color; ?>20; color: <?php echo $tag->color; ?>; border: 1px solid <?php echo $tag->color; ?>;">
                                            <?php echo $tag->name; ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            <?php else : ?>
                                <span class="text-gray-500">No tags assigned</span>
                            <?php endif; ?>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
        <div class="mt-6 flex space-x-3">
            <a href="<?php echo URLROOT; ?>/assets/edit/<?php echo $data['asset']->id; ?>" class="inline-flex items-center px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-md">
                <i class="fas fa-edit mr-2"></i> Edit
            </a>
            <a href="<?php echo URLROOT; ?>/tags" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md">
                <i class="fas fa-tags mr-2"></i> Manage Tags
            </a>
        </div>
    </div>
</div>

<!-- Asset History Section -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Change History</h2>

        <?php if(count($data['history']) > 0) : ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Field</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Old Value</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">New Value</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Changed By</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['history'] as $record) : ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <?php echo $record->field_name; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $record->old_value; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $record->new_value; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $record->user_name ?? 'System'; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo date('M j, Y g:i A', strtotime($record->changed_at)); ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else : ?>
            <div class="bg-gray-50 p-4 rounded-md">
                <p class="text-gray-600">No change history available for this asset.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
