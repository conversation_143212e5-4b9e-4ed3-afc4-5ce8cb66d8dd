<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">Test File Upload</h1>
        <p class="text-gray-600">Use this page to test basic file upload functionality</p>
    </div>
    <div class="mt-4 md:mt-0 space-x-2">
        <a href="<?php echo URLROOT; ?>/assets" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md">
            <i class="fa fa-backward mr-2"></i> Back to Assets
        </a>
        <a href="<?php echo URLROOT; ?>/assets/import" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            <i class="fas fa-file-import mr-2"></i> Go to Import
        </a>
    </div>
</div>

<?php if(isset($data['error'])): ?>
<div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
    <p class="font-bold">Error</p>
    <p><?php echo $data['error']; ?></p>
</div>
<?php endif; ?>

<?php if(isset($data['success'])): ?>
<div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
    <p class="font-bold">Success</p>
    <p><?php echo $data['success']; ?></p>
    
    <?php if(isset($data['file_info'])): ?>
    <div class="mt-4">
        <h4 class="font-bold">File Information:</h4>
        <ul class="list-disc pl-5 mt-2">
            <li>Name: <?php echo $data['file_info']['name']; ?></li>
            <li>Size: <?php echo $data['file_info']['size']; ?> bytes</li>
            <li>Type: <?php echo $data['file_info']['type']; ?></li>
        </ul>
        
        <h4 class="font-bold mt-4">Content Preview:</h4>
        <pre class="bg-gray-100 p-4 mt-2 rounded overflow-x-auto"><?php echo htmlspecialchars($data['file_info']['content_preview']); ?></pre>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>

<div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="p-6">
        <form action="<?php echo URLROOT; ?>/assets/test_upload" method="post" enctype="multipart/form-data">
            <!-- CSRF Token -->
            <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
            
            <div class="mb-6">
                <label for="test_file" class="block text-sm font-medium text-gray-700 mb-1">
                    Select File <span class="text-red-500">*</span>
                </label>
                <input type="file" name="test_file" id="test_file" class="form-control" required>
                <p class="mt-1 text-sm text-gray-500">
                    Select any file to test the upload functionality.
                </p>
            </div>
            
            <div class="bg-blue-50 border border-blue-200 text-blue-800 px-6 py-4 rounded-md mb-6">
                <h5 class="font-medium flex items-center mb-3">
                    <i class="fas fa-info-circle mr-2"></i> Upload Test Information
                </h5>
                <p>This page is for testing the basic file upload functionality. It will:</p>
                <ul class="list-disc pl-5 mt-2">
                    <li>Upload the file to the server's temporary directory</li>
                    <li>Verify the file exists at the temporary path</li>
                    <li>Read the file contents</li>
                    <li>Display information about the uploaded file</li>
                </ul>
                <p class="mt-3">This helps diagnose issues with the file upload mechanism.</p>
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md inline-flex items-center">
                <i class="fas fa-upload mr-2"></i> Test Upload
            </button>
        </form>
    </div>
</div>

<div class="mt-8 bg-white rounded-lg shadow overflow-hidden">
    <div class="bg-gray-50 px-6 py-3 border-b">
        <h3 class="text-lg font-medium text-gray-900">PHP File Upload Configuration</h3>
    </div>
    <div class="p-6">
        <table class="min-w-full divide-y divide-gray-200">
            <thead>
                <tr>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Setting</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">upload_max_filesize</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo ini_get('upload_max_filesize'); ?></td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">post_max_size</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo ini_get('post_max_size'); ?></td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">max_file_uploads</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo ini_get('max_file_uploads'); ?></td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">upload_tmp_dir</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo ini_get('upload_tmp_dir') ?: 'Default system temp directory'; ?></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
