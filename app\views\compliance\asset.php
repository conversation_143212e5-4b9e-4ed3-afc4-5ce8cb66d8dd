<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2"><?php echo $data['asset']->computer_host_name; ?></h1>
            <p class="text-gray-600">
                <?php echo $data['asset']->equipment_type; ?> | 
                SN: <?php echo $data['asset']->serial_number; ?> | 
                <?php echo $data['framework']->name; ?> Compliance
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/compliance/framework/<?php echo $data['framework']->id; ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
            <a href="<?php echo URLROOT; ?>/assets/show/<?php echo $data['asset']->id; ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-desktop mr-2"></i> View Asset Details
            </a>
        </div>
    </div>

    <?php flash('compliance_message'); ?>

    <!-- Compliance Status -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Compliance Status</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Control ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Assessed</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach($data['controls_with_status'] as $item) : ?>
                        <?php 
                            $control = $item['control'];
                            $status = $item['status'];
                            
                            $statusClass = 'bg-yellow-100 text-yellow-800';
                            $statusText = 'In Progress';
                            
                            if($status) {
                                switch($status->status) {
                                    case 'compliant':
                                        $statusClass = 'bg-green-100 text-green-800';
                                        $statusText = 'Compliant';
                                        break;
                                    case 'non_compliant':
                                        $statusClass = 'bg-red-100 text-red-800';
                                        $statusText = 'Non-Compliant';
                                        break;
                                    case 'not_applicable':
                                        $statusClass = 'bg-gray-100 text-gray-800';
                                        $statusText = 'Not Applicable';
                                        break;
                                    default:
                                        $statusClass = 'bg-yellow-100 text-yellow-800';
                                        $statusText = 'In Progress';
                                }
                            }
                        ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo $control->control_id; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo $control->name; ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-500"><?php echo substr($control->description, 0, 100); ?><?php echo strlen($control->description) > 100 ? '...' : ''; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $statusClass; ?>">
                                    <?php echo $statusText; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">
                                    <?php if($status && $status->last_assessed_date) : ?>
                                        <?php echo date('M j, Y', strtotime($status->last_assessed_date)); ?>
                                        <div class="text-xs text-gray-400">by <?php echo $status->assessed_by_name ?? 'Unknown'; ?></div>
                                    <?php else : ?>
                                        Not assessed yet
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="<?php echo URLROOT; ?>/compliance/update/<?php echo $data['asset']->id; ?>/<?php echo $control->id; ?>" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-edit mr-1"></i> Update
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
