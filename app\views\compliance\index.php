<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800">Compliance Dashboard</h1>
        <div class="flex space-x-4">
            <a href="<?php echo URLROOT; ?>/assets" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-desktop mr-2"></i> Assets
            </a>
            <?php if($data['selected_framework']) : ?>
                <a href="<?php echo URLROOT; ?>/compliance/generate/<?php echo $data['selected_framework']->id; ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                    <i class="fas fa-file-alt mr-2"></i> Generate Report
                </a>
            <?php endif; ?>
        </div>
    </div>

    <?php flash('compliance_message'); ?>

    <?php if(empty($data['frameworks'])) : ?>
        <div class="bg-white rounded-lg shadow-md p-6 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-xl font-medium text-gray-900">No compliance frameworks found</h3>
            <p class="mt-1 text-sm text-gray-500">Please contact your administrator to set up compliance frameworks.</p>
        </div>
    <?php else : ?>
        <!-- Framework Selector -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="w-full md:w-1/2 mb-4 md:mb-0">
                    <label for="framework" class="block text-sm font-medium text-gray-700 mb-2">Select Compliance Framework</label>
                    <select id="framework" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" onchange="window.location.href='<?php echo URLROOT; ?>/compliance/framework/' + this.value">
                        <?php foreach($data['frameworks'] as $framework) : ?>
                            <option value="<?php echo $framework->id; ?>" <?php echo $data['selected_framework'] && $framework->id == $data['selected_framework']->id ? 'selected' : ''; ?>>
                                <?php echo $framework->name; ?> <?php echo $framework->version ? '(' . $framework->version . ')' : ''; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="w-full md:w-1/2 md:text-right">
                    <?php if($data['selected_framework']) : ?>
                        <h2 class="text-lg font-semibold text-gray-800"><?php echo $data['selected_framework']->name; ?> <?php echo $data['selected_framework']->version ? '(' . $data['selected_framework']->version . ')' : ''; ?></h2>
                        <p class="text-sm text-gray-600"><?php echo $data['selected_framework']->description; ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Compliance Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <?php
                $totalAssets = count($data['compliance_summary']);
                $totalControls = count($data['control_summary']);
                
                $compliantAssets = 0;
                $nonCompliantAssets = 0;
                $inProgressAssets = 0;
                
                foreach($data['compliance_summary'] as $summary) {
                    if($summary->total_controls > 0) {
                        $compliancePercentage = ($summary->compliant_count / $summary->total_controls) * 100;
                        if($compliancePercentage == 100) {
                            $compliantAssets++;
                        } elseif($summary->non_compliant_count > 0) {
                            $nonCompliantAssets++;
                        } else {
                            $inProgressAssets++;
                        }
                    }
                }
                
                $totalCompliance = 0;
                $totalAssessments = 0;
                
                foreach($data['control_summary'] as $control) {
                    $totalAssessments += $control->total_assets;
                    $totalCompliance += $control->compliant_count;
                }
                
                $overallCompliance = $totalAssessments > 0 ? ($totalCompliance / $totalAssessments) * 100 : 0;
            ?>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Overall Compliance</h2>
                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                        <i class="fas fa-check-circle text-blue-500"></i>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="w-full bg-gray-200 rounded-full h-4 mr-2">
                        <?php
                            $barColor = 'bg-red-500';
                            if($overallCompliance >= 90) {
                                $barColor = 'bg-green-500';
                            } elseif($overallCompliance >= 70) {
                                $barColor = 'bg-yellow-500';
                            } elseif($overallCompliance >= 50) {
                                $barColor = 'bg-orange-500';
                            }
                        ?>
                        <div class="<?php echo $barColor; ?> h-4 rounded-full" style="width: <?php echo $overallCompliance; ?>%"></div>
                    </div>
                    <span class="text-xl font-bold"><?php echo round($overallCompliance, 1); ?>%</span>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Compliant Assets</h2>
                    <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                        <i class="fas fa-check text-green-500"></i>
                    </div>
                </div>
                <p class="text-3xl font-bold text-green-500"><?php echo $compliantAssets; ?></p>
                <p class="text-gray-600">of <?php echo $totalAssets; ?> total assets</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Non-Compliant</h2>
                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                        <i class="fas fa-times text-red-500"></i>
                    </div>
                </div>
                <p class="text-3xl font-bold text-red-500"><?php echo $nonCompliantAssets; ?></p>
                <p class="text-gray-600">assets with violations</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">In Progress</h2>
                    <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-500"></i>
                    </div>
                </div>
                <p class="text-3xl font-bold text-yellow-500"><?php echo $inProgressAssets; ?></p>
                <p class="text-gray-600">assets being assessed</p>
            </div>
        </div>

        <!-- Non-Compliant Assets -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">Non-Compliant Assets</h2>
            </div>
            <div class="overflow-x-auto">
                <?php if(count($data['non_compliant_assets']) > 0) : ?>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Non-Compliant Controls</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Compliance %</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach($data['non_compliant_assets'] as $asset) : ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?php echo $asset->computer_host_name; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500"><?php echo $asset->equipment_type; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500"><?php echo $asset->serial_number; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-red-500"><?php echo $asset->non_compliant_count; ?> of <?php echo $asset->total_controls; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php 
                                            $compliancePercentage = 100 - $asset->non_compliant_percentage;
                                            $barColor = 'bg-red-500';
                                            if($compliancePercentage >= 90) {
                                                $barColor = 'bg-green-500';
                                            } elseif($compliancePercentage >= 70) {
                                                $barColor = 'bg-yellow-500';
                                            } elseif($compliancePercentage >= 50) {
                                                $barColor = 'bg-orange-500';
                                            }
                                        ?>
                                        <div class="flex items-center">
                                            <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                                                <div class="<?php echo $barColor; ?> h-2.5 rounded-full" style="width: <?php echo $compliancePercentage; ?>%"></div>
                                            </div>
                                            <span class="text-sm font-medium"><?php echo round($compliancePercentage, 1); ?>%</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="<?php echo URLROOT; ?>/compliance/asset/<?php echo $asset->id; ?>/<?php echo $data['selected_framework']->id; ?>" class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-clipboard-check mr-1"></i> Assess
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else : ?>
                    <div class="p-6 text-center">
                        <p class="text-green-500 font-medium">No non-compliant assets found. Great job!</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Control Compliance -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">Control Compliance</h2>
            </div>
            <div class="overflow-x-auto">
                <?php if(count($data['control_summary']) > 0) : ?>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Control ID</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Compliant</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Non-Compliant</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N/A</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">In Progress</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Compliance %</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach($data['control_summary'] as $control) : ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?php echo $control->control_id; ?></div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900"><?php echo $control->name; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-green-500"><?php echo $control->compliant_count; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-red-500"><?php echo $control->non_compliant_count; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500"><?php echo $control->not_applicable_count; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-yellow-500"><?php echo $control->in_progress_count; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php 
                                            $assessedCount = $control->compliant_count + $control->non_compliant_count + $control->not_applicable_count;
                                            $compliancePercentage = $assessedCount > 0 ? ($control->compliant_count / $assessedCount) * 100 : 0;
                                            $barColor = 'bg-red-500';
                                            if($compliancePercentage >= 90) {
                                                $barColor = 'bg-green-500';
                                            } elseif($compliancePercentage >= 70) {
                                                $barColor = 'bg-yellow-500';
                                            } elseif($compliancePercentage >= 50) {
                                                $barColor = 'bg-orange-500';
                                            }
                                        ?>
                                        <div class="flex items-center">
                                            <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                                                <div class="<?php echo $barColor; ?> h-2.5 rounded-full" style="width: <?php echo $compliancePercentage; ?>%"></div>
                                            </div>
                                            <span class="text-sm font-medium"><?php echo round($compliancePercentage, 1); ?>%</span>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else : ?>
                    <div class="p-6 text-center">
                        <p class="text-gray-500">No control data available.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Reports -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">Recent Reports</h2>
            </div>
            <div class="overflow-x-auto">
                <?php if(count($data['recent_reports']) > 0) : ?>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Framework</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated By</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach($data['recent_reports'] as $report) : ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?php echo $report->report_name; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500"><?php echo $report->framework_name; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500"><?php echo date('M j, Y', strtotime($report->report_date)); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500"><?php echo $report->generated_by_name; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="<?php echo URLROOT; ?>/compliance/report/<?php echo $report->id; ?>" class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-file-alt mr-1"></i> View
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else : ?>
                    <div class="p-6 text-center">
                        <p class="text-gray-500">No reports generated yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
