<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2"><?php echo $data['report']->report_name; ?></h1>
            <p class="text-gray-600">
                Generated on <?php echo date('F j, Y', strtotime($data['report']->report_date)); ?> by <?php echo $data['report']->generated_by_name; ?>
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/compliance" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
            <button onclick="window.print()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-print mr-2"></i> Print Report
            </button>
        </div>
    </div>

    <!-- Report Header -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8 print:shadow-none">
        <div class="flex flex-col md:flex-row justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-800 mb-2"><?php echo $data['report']->framework_name; ?> Compliance Report</h2>
                <p class="text-gray-600">Overall Compliance: 
                    <span class="font-bold 
                        <?php 
                            $overallCompliance = $data['report']->report_data->overall_compliance;
                            if($overallCompliance >= 90) {
                                echo 'text-green-600';
                            } elseif($overallCompliance >= 70) {
                                echo 'text-yellow-600';
                            } else {
                                echo 'text-red-600';
                            }
                        ?>">
                        <?php echo round($overallCompliance, 1); ?>%
                    </span>
                </p>
            </div>
            <div class="mt-4 md:mt-0">
                <p class="text-gray-600">Report Date: <?php echo date('F j, Y', strtotime($data['report']->report_date)); ?></p>
                <p class="text-gray-600">Generated By: <?php echo $data['report']->generated_by_name; ?></p>
            </div>
        </div>
    </div>

    <!-- Compliance Summary -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8 print:shadow-none">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 print:bg-white">
            <h2 class="text-xl font-bold text-gray-800">Compliance Summary by Asset</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50 print:bg-white">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Compliant</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Non-Compliant</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N/A</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">In Progress</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Compliance %</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach($data['report']->report_data->asset_summary as $asset) : ?>
                        <tr class="hover:bg-gray-50 print:hover:bg-white">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo $asset->computer_host_name; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500"><?php echo $asset->equipment_type; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500"><?php echo $asset->serial_number; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-green-500"><?php echo $asset->compliant_count; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-red-500"><?php echo $asset->non_compliant_count; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500"><?php echo $asset->not_applicable_count; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-yellow-500"><?php echo $asset->in_progress_count; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php 
                                    $totalControls = $asset->total_controls;
                                    $applicableControls = $totalControls - $asset->not_applicable_count;
                                    $compliancePercentage = $applicableControls > 0 ? ($asset->compliant_count / $applicableControls) * 100 : 0;
                                    
                                    $textColor = 'text-red-500';
                                    if($compliancePercentage >= 90) {
                                        $textColor = 'text-green-500';
                                    } elseif($compliancePercentage >= 70) {
                                        $textColor = 'text-yellow-500';
                                    }
                                ?>
                                <div class="text-sm font-medium <?php echo $textColor; ?>"><?php echo round($compliancePercentage, 1); ?>%</div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Control Compliance -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8 print:shadow-none">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 print:bg-white">
            <h2 class="text-xl font-bold text-gray-800">Compliance Summary by Control</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50 print:bg-white">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Control ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Compliant</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Non-Compliant</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N/A</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">In Progress</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Compliance %</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach($data['report']->report_data->control_summary as $control) : ?>
                        <tr class="hover:bg-gray-50 print:hover:bg-white">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo $control->control_id; ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo $control->name; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-green-500"><?php echo $control->compliant_count; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-red-500"><?php echo $control->non_compliant_count; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500"><?php echo $control->not_applicable_count; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-yellow-500"><?php echo $control->in_progress_count; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php 
                                    $totalAssets = $control->total_assets;
                                    $applicableAssets = $totalAssets - $control->not_applicable_count;
                                    $compliancePercentage = $applicableAssets > 0 ? ($control->compliant_count / $applicableAssets) * 100 : 0;
                                    
                                    $textColor = 'text-red-500';
                                    if($compliancePercentage >= 90) {
                                        $textColor = 'text-green-500';
                                    } elseif($compliancePercentage >= 70) {
                                        $textColor = 'text-yellow-500';
                                    }
                                ?>
                                <div class="text-sm font-medium <?php echo $textColor; ?>"><?php echo round($compliancePercentage, 1); ?>%</div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Report Footer -->
    <div class="bg-white rounded-lg shadow-md p-6 print:shadow-none">
        <div class="text-center text-gray-500 text-sm">
            <p>This report was generated by the Endpoint Visibility and Insight System (EVIS).</p>
            <p>Report ID: <?php echo $data['report']->id; ?> | Generated on: <?php echo date('F j, Y H:i:s', strtotime($data['report']->created_at)); ?></p>
        </div>
    </div>
</div>

<style type="text/css" media="print">
    @page {
        size: landscape;
    }
    
    body {
        font-size: 12pt;
    }
    
    .container {
        max-width: 100%;
        padding: 0;
    }
    
    .no-print {
        display: none !important;
    }
    
    .print-full-width {
        width: 100% !important;
    }
</style>

<?php require APPROOT . '/views/inc/footer.php'; ?>
