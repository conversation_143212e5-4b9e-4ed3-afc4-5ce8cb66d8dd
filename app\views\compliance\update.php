<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Update Compliance Status</h1>
            <p class="text-gray-600">
                <?php echo $data['asset']->computer_host_name; ?> | 
                <?php echo $data['control']->control_id; ?>: <?php echo $data['control']->name; ?>
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/compliance/asset/<?php echo $data['asset']->id; ?>/<?php echo $data['control']->framework_id; ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Asset
            </a>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Control Details</h2>
        </div>
        <div class="p-6">
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-2"><?php echo $data['control']->control_id; ?>: <?php echo $data['control']->name; ?></h3>
                <p class="text-gray-600 mb-4"><?php echo $data['control']->description; ?></p>
                
                <?php if($data['control']->implementation_guidance) : ?>
                    <div class="mb-4">
                        <h4 class="text-md font-semibold text-gray-700 mb-2">Implementation Guidance</h4>
                        <p class="text-gray-600 bg-gray-50 p-3 rounded"><?php echo $data['control']->implementation_guidance; ?></p>
                    </div>
                <?php endif; ?>
                
                <?php if($data['control']->verification_procedure) : ?>
                    <div>
                        <h4 class="text-md font-semibold text-gray-700 mb-2">Verification Procedure</h4>
                        <p class="text-gray-600 bg-gray-50 p-3 rounded"><?php echo $data['control']->verification_procedure; ?></p>
                    </div>
                <?php endif; ?>
            </div>
            
            <form action="<?php echo URLROOT; ?>/compliance/update/<?php echo $data['asset']->id; ?>/<?php echo $data['control']->id; ?>" method="post" class="mt-6">
                <div class="mb-6">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Compliance Status</label>
                    <select id="status" name="status" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 <?php echo (!empty($data['status_err'])) ? 'border-red-500' : ''; ?>">
                        <option value="compliant" <?php echo $data['status'] == 'compliant' ? 'selected' : ''; ?>>Compliant</option>
                        <option value="non_compliant" <?php echo $data['status'] == 'non_compliant' ? 'selected' : ''; ?>>Non-Compliant</option>
                        <option value="not_applicable" <?php echo $data['status'] == 'not_applicable' ? 'selected' : ''; ?>>Not Applicable</option>
                        <option value="in_progress" <?php echo $data['status'] == 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                    </select>
                    <?php if(!empty($data['status_err'])) : ?>
                        <p class="text-red-500 text-xs mt-1"><?php echo $data['status_err']; ?></p>
                    <?php endif; ?>
                </div>
                
                <div class="mb-6">
                    <label for="evidence" class="block text-sm font-medium text-gray-700 mb-2">Evidence</label>
                    <textarea id="evidence" name="evidence" rows="4" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"><?php echo $data['evidence']; ?></textarea>
                    <p class="text-gray-500 text-xs mt-1">Provide evidence of compliance or details of non-compliance.</p>
                </div>
                
                <div class="mb-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea id="notes" name="notes" rows="3" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"><?php echo $data['notes']; ?></textarea>
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                        <i class="fas fa-save mr-2"></i> Save Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
