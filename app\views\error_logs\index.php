<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Error Logs</h1>
            <p class="text-gray-600">
                View and manage system error logs
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
            <a href="<?php echo URLROOT; ?>/error_logs/test" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-vial mr-2"></i> Test Error Logging
            </a>
            <form action="<?php echo URLROOT; ?>/error_logs/clear" method="POST" class="inline">
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md" onclick="return confirm('Are you sure you want to clear all error logs?')">
                    <i class="fas fa-trash-alt mr-2"></i> Clear All Logs
                </button>
            </form>
        </div>
    </div>

    <?php flash('error_log_message'); ?>

    <!-- Filter Form -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Filter Logs</h2>
        </div>
        <div class="p-6">
            <form action="<?php echo URLROOT; ?>/error_logs" method="GET" class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                <div class="flex-1">
                    <label for="level" class="block text-sm font-medium text-gray-700 mb-2">Error Level</label>
                    <select id="level" name="level" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">All Levels</option>
                        <option value="error" <?php echo $data['level'] == 'error' ? 'selected' : ''; ?>>Error</option>
                        <option value="warning" <?php echo $data['level'] == 'warning' ? 'selected' : ''; ?>>Warning</option>
                        <option value="info" <?php echo $data['level'] == 'info' ? 'selected' : ''; ?>>Info</option>
                        <option value="debug" <?php echo $data['level'] == 'debug' ? 'selected' : ''; ?>>Debug</option>
                    </select>
                </div>
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <input type="text" id="search" name="search" value="<?php echo $data['search']; ?>" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Search in message, file, or context">
                </div>
                <div class="flex items-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                        <i class="fas fa-search mr-2"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Error Logs Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Error Logs</h2>
            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                Total: <?php echo $data['total_records']; ?> logs
            </span>
        </div>
        <div class="overflow-x-auto">
            <?php if(!empty($data['error_logs'])) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['error_logs'] as $log) : ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $log->id; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $levelClass = 'bg-blue-100 text-blue-800';
                                        switch($log->level) {
                                            case 'error':
                                                $levelClass = 'bg-red-100 text-red-800';
                                                break;
                                            case 'warning':
                                                $levelClass = 'bg-yellow-100 text-yellow-800';
                                                break;
                                            case 'info':
                                                $levelClass = 'bg-blue-100 text-blue-800';
                                                break;
                                            case 'debug':
                                                $levelClass = 'bg-gray-100 text-gray-800';
                                                break;
                                        }
                                    ?>
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $levelClass; ?>">
                                        <?php echo ucfirst($log->level); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo substr($log->message, 0, 50) . (strlen($log->message) > 50 ? '...' : ''); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $log->file ? basename($log->file) : 'N/A'; ?>
                                    <?php echo $log->line ? ':' . $log->line : ''; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $log->user_name ?? 'N/A'; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo date('M j, Y g:i A', strtotime($log->created_at)); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo URLROOT; ?>/error_logs/view/<?php echo $log->id; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <form action="<?php echo URLROOT; ?>/error_logs/delete/<?php echo $log->id; ?>" method="POST" class="inline">
                                        <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Are you sure you want to delete this error log?')">
                                            <i class="fas fa-trash-alt"></i> Delete
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <!-- Pagination -->
                <?php if($data['total_pages'] > 1) : ?>
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                Showing <span class="font-medium"><?php echo (($data['current_page'] - 1) * $data['records_per_page']) + 1; ?></span>
                                to <span class="font-medium"><?php echo min($data['current_page'] * $data['records_per_page'], $data['total_records']); ?></span>
                                of <span class="font-medium"><?php echo $data['total_records']; ?></span> results
                            </div>
                            <div class="flex space-x-2">
                                <?php if($data['current_page'] > 1) : ?>
                                    <a href="<?php echo URLROOT; ?>/error_logs/index/<?php echo $data['current_page'] - 1; ?>?level=<?php echo $data['level']; ?>&search=<?php echo $data['search']; ?>" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        Previous
                                    </a>
                                <?php endif; ?>
                                
                                <?php if($data['current_page'] < $data['total_pages']) : ?>
                                    <a href="<?php echo URLROOT; ?>/error_logs/index/<?php echo $data['current_page'] + 1; ?>?level=<?php echo $data['level']; ?>&search=<?php echo $data['search']; ?>" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        Next
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No error logs found.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
