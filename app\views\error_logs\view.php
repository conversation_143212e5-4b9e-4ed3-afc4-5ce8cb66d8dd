<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Error Log Details</h1>
            <p class="text-gray-600">
                Viewing error log #<?php echo $data['error_log']->id; ?>
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/error_logs" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Error Logs
            </a>
            <form action="<?php echo URLROOT; ?>/error_logs/delete/<?php echo $data['error_log']->id; ?>" method="POST" class="inline">
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md" onclick="return confirm('Are you sure you want to delete this error log?')">
                    <i class="fas fa-trash-alt mr-2"></i> Delete Log
                </button>
            </form>
        </div>
    </div>

    <?php flash('error_log_message'); ?>

    <!-- Error Log Details -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Error Information</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Basic Information</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-500">ID:</span>
                            <p class="mt-1"><?php echo $data['error_log']->id; ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Level:</span>
                            <p class="mt-1">
                                <?php
                                    $levelClass = 'bg-blue-100 text-blue-800';
                                    switch($data['error_log']->level) {
                                        case 'error':
                                            $levelClass = 'bg-red-100 text-red-800';
                                            break;
                                        case 'warning':
                                            $levelClass = 'bg-yellow-100 text-yellow-800';
                                            break;
                                        case 'info':
                                            $levelClass = 'bg-blue-100 text-blue-800';
                                            break;
                                        case 'debug':
                                            $levelClass = 'bg-gray-100 text-gray-800';
                                            break;
                                    }
                                ?>
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $levelClass; ?>">
                                    <?php echo ucfirst($data['error_log']->level); ?>
                                </span>
                            </p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Date:</span>
                            <p class="mt-1"><?php echo date('F j, Y g:i:s A', strtotime($data['error_log']->created_at)); ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">User:</span>
                            <p class="mt-1"><?php echo $data['error_log']->user_name ?? 'N/A'; ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">IP Address:</span>
                            <p class="mt-1"><?php echo $data['error_log']->ip_address ?? 'N/A'; ?></p>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Error Details</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Message:</span>
                            <p class="mt-1 p-2 bg-gray-50 rounded border border-gray-200"><?php echo $data['error_log']->message; ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">File:</span>
                            <p class="mt-1"><?php echo $data['error_log']->file ?? 'N/A'; ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Line:</span>
                            <p class="mt-1"><?php echo $data['error_log']->line ?? 'N/A'; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Context and Stack Trace -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Context -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">Context</h2>
            </div>
            <div class="p-6">
                <?php if ($data['error_log']->context): ?>
                    <pre class="bg-gray-50 p-4 rounded border border-gray-200 overflow-x-auto text-sm"><?php echo json_encode(json_decode($data['error_log']->context), JSON_PRETTY_PRINT); ?></pre>
                <?php else: ?>
                    <p class="text-gray-500">No context data available.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Stack Trace -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">Stack Trace</h2>
            </div>
            <div class="p-6">
                <?php if ($data['error_log']->trace): ?>
                    <pre class="bg-gray-50 p-4 rounded border border-gray-200 overflow-x-auto text-sm"><?php echo $data['error_log']->trace; ?></pre>
                <?php else: ?>
                    <p class="text-gray-500">No stack trace available.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- User Agent -->
    <div class="mt-8 bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">User Agent</h2>
        </div>
        <div class="p-6">
            <?php if ($data['error_log']->user_agent): ?>
                <pre class="bg-gray-50 p-4 rounded border border-gray-200 overflow-x-auto text-sm"><?php echo $data['error_log']->user_agent; ?></pre>
            <?php else: ?>
                <p class="text-gray-500">No user agent data available.</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
