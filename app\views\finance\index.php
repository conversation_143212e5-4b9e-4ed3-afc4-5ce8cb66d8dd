<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800">Financial Analytics</h1>
        <div class="flex space-x-4">
            <a href="<?php echo URLROOT; ?>/assets" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-desktop mr-2"></i> Assets
            </a>
            <?php if(isAdmin()) : ?>
                <a href="<?php echo URLROOT; ?>/finance/budget" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                    <i class="fas fa-chart-line mr-2"></i> Budget Forecast
                </a>
            <?php endif; ?>
        </div>
    </div>

    <?php flash('finance_message'); ?>

    <!-- Fiscal Year Summary -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Fiscal Year <?php echo $data['fiscal_year']; ?> Summary</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <?php
                $totalCost = 0;
                $totalAssets = 0;
                foreach($data['costs_by_type'] as $cost) {
                    $totalCost += $cost->total_amount;
                    $totalAssets += $cost->asset_count;
                }
            ?>
            <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-sm text-gray-500 mb-1">Total Expenditure</p>
                <p class="text-2xl font-bold text-gray-800">$<?php echo number_format($totalCost, 2); ?></p>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-sm text-gray-500 mb-1">Assets with Costs</p>
                <p class="text-2xl font-bold text-gray-800"><?php echo $totalAssets; ?></p>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-sm text-gray-500 mb-1">Average Cost per Asset</p>
                <p class="text-2xl font-bold text-gray-800">$<?php echo $totalAssets > 0 ? number_format($totalCost / $totalAssets, 2) : 0; ?></p>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-sm text-gray-500 mb-1">Fiscal Year</p>
                <p class="text-2xl font-bold text-gray-800">FY<?php echo $data['fiscal_year']; ?></p>
                <p class="text-xs text-gray-500">Jul <?php echo $data['fiscal_year'] - 1; ?> - Jun <?php echo $data['fiscal_year']; ?></p>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Costs by Type Chart -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Costs by Type</h2>
            <div class="h-80">
                <canvas id="costsByTypeChart"></canvas>
            </div>
        </div>

        <!-- Monthly Costs Chart -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Monthly Costs</h2>
            <div class="h-80">
                <canvas id="monthlyCostsChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Costs by Equipment Type -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Costs by Equipment Type</h2>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($data['costs_by_equipment_type']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipment Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset Count</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Cost</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average Cost</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">% of Total</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['costs_by_equipment_type'] as $cost) : ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $cost->equipment_type; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500"><?php echo $cost->asset_count; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">$<?php echo number_format($cost->total_amount, 2); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">$<?php echo number_format($cost->average_cost, 2); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        <?php echo number_format(($cost->total_amount / $totalCost) * 100, 1); ?>%
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No cost data available by equipment type.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Budget Forecast -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Budget Forecast for FY<?php echo $data['fiscal_year'] + 1; ?></h2>
            <?php if(isAdmin()) : ?>
                <a href="<?php echo URLROOT; ?>/finance/budget" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-chart-line mr-1"></i> Detailed Forecast
                </a>
            <?php endif; ?>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($data['budget_forecast']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Historical Amount (FY<?php echo $data['fiscal_year']; ?>)</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Projected Amount (FY<?php echo $data['fiscal_year'] + 1; ?>)</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Growth %</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php 
                            $totalHistorical = 0;
                            $totalProjected = 0;
                            foreach($data['budget_forecast'] as $forecast) : 
                                $totalHistorical += $forecast['historical_amount'];
                                $totalProjected += $forecast['projected_amount'];
                        ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo ucfirst($forecast['category']); ?></div>
                                    <?php if(isset($forecast['eol_assets_count'])) : ?>
                                        <div class="text-xs text-gray-500"><?php echo $forecast['eol_assets_count']; ?> assets reaching EOL</div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">$<?php echo number_format($forecast['historical_amount'], 2); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">$<?php echo number_format($forecast['projected_amount'], 2); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php 
                                        $growthClass = 'text-gray-500';
                                        if($forecast['growth_percentage'] > 0) {
                                            $growthClass = 'text-red-500';
                                        } elseif($forecast['growth_percentage'] < 0) {
                                            $growthClass = 'text-green-500';
                                        }
                                    ?>
                                    <div class="text-sm font-medium <?php echo $growthClass; ?>">
                                        <?php echo number_format($forecast['growth_percentage'], 1); ?>%
                                        <?php if($forecast['growth_percentage'] > 0) : ?>
                                            <i class="fas fa-arrow-up ml-1"></i>
                                        <?php elseif($forecast['growth_percentage'] < 0) : ?>
                                            <i class="fas fa-arrow-down ml-1"></i>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        <!-- Total Row -->
                        <tr class="bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-bold text-gray-900">Total</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-bold text-gray-900">$<?php echo number_format($totalHistorical, 2); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-bold text-gray-900">$<?php echo number_format($totalProjected, 2); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php 
                                    $totalGrowth = $totalHistorical > 0 ? (($totalProjected / $totalHistorical) - 1) * 100 : 0;
                                    $growthClass = 'text-gray-500';
                                    if($totalGrowth > 0) {
                                        $growthClass = 'text-red-500';
                                    } elseif($totalGrowth < 0) {
                                        $growthClass = 'text-green-500';
                                    }
                                ?>
                                <div class="text-sm font-bold <?php echo $growthClass; ?>">
                                    <?php echo number_format($totalGrowth, 1); ?>%
                                    <?php if($totalGrowth > 0) : ?>
                                        <i class="fas fa-arrow-up ml-1"></i>
                                    <?php elseif($totalGrowth < 0) : ?>
                                        <i class="fas fa-arrow-down ml-1"></i>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No budget forecast data available.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Costs by Type Chart
    const typeCtx = document.getElementById('costsByTypeChart').getContext('2d');
    const typeChart = new Chart(typeCtx, {
        type: 'doughnut',
        data: {
            labels: <?php echo $data['type_labels']; ?>,
            datasets: [{
                data: <?php echo $data['type_amounts']; ?>,
                backgroundColor: <?php echo $data['type_colors']; ?>,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed !== null) {
                                label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(context.parsed);
                            }
                            return label;
                        }
                    }
                }
            }
        }
    });

    // Monthly Costs Chart
    const monthCtx = document.getElementById('monthlyCostsChart').getContext('2d');
    const monthChart = new Chart(monthCtx, {
        type: 'bar',
        data: {
            labels: <?php echo $data['month_labels']; ?>,
            datasets: [{
                label: 'Monthly Expenditure',
                data: <?php echo $data['month_amounts']; ?>,
                backgroundColor: 'rgba(59, 130, 246, 0.7)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(context.parsed.y);
                            }
                            return label;
                        }
                    }
                }
            }
        }
    });
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
