    </div>
    <footer class="bg-gray-100 border-t border-gray-200 py-6 mt-auto">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="bg-blue-600 p-1 rounded-md mr-2">
                        <i class="fas fa-laptop text-white text-sm"></i>
                    </div>
                    <span class="text-gray-700 font-medium"><?php echo SITENAME; ?></span>
                </div>

                <div class="flex space-x-6 mb-4 md:mb-0">
                    <a href="<?php echo URLROOT; ?>" class="text-gray-600 hover:text-blue-600 transition-colors text-sm">Home</a>
                    <a href="<?php echo URLROOT; ?>/pages/about" class="text-gray-600 hover:text-blue-600 transition-colors text-sm">About</a>
                    <a href="<?php echo URLROOT; ?>/assets" class="text-gray-600 hover:text-blue-600 transition-colors text-sm">Assets</a>
                </div>

                <p class="text-gray-500 text-sm">
                    &copy; <?php echo date('Y'); ?> <?php echo SITENAME; ?>
                </p>
            </div>
        </div>
    </footer>

    <!-- Back to top button -->
    <button id="back-to-top" class="fixed bottom-6 right-6 bg-blue-600 text-white w-10 h-10 rounded-full flex items-center justify-center shadow-lg opacity-0 invisible transition-all duration-300 hover:bg-blue-700">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Global variables for JavaScript -->
    <script>
        // Define URL root for use in JavaScript
        const URLROOT = '<?php echo URLROOT; ?>';
    </script>

    <script src="<?php echo URLROOT; ?>/js/main.js"></script>

    <!-- Pagination length selector functionality -->
    <script>
        // Global function for pagination length selector
        function changePaginationLength(perPage) {
            // Get current URL
            let url = new URL(window.location.href);

            // Update or add the per_page parameter
            url.searchParams.set('per_page', perPage);

            // Reset to page 1 when changing items per page
            url.searchParams.set('page', 1);

            // Navigate to the new URL
            window.location.href = url.toString();
        }

        // Back to top button functionality
        document.addEventListener('DOMContentLoaded', function() {
            const backToTopButton = document.getElementById('back-to-top');

            if (backToTopButton) {
                // Show/hide button based on scroll position
                window.addEventListener('scroll', function() {
                    if (window.pageYOffset > 300) {
                        backToTopButton.classList.remove('opacity-0', 'invisible');
                        backToTopButton.classList.add('opacity-100', 'visible');
                    } else {
                        backToTopButton.classList.remove('opacity-100', 'visible');
                        backToTopButton.classList.add('opacity-0', 'invisible');
                    }
                });

                // Scroll to top when clicked
                backToTopButton.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }
        });
    </script>
</body>
</html>
