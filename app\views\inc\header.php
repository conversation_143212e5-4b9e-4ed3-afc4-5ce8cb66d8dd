<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="description" content="EVIS - Endpoint Visibility and Insight System">
    <meta name="keywords" content="asset management, ICT assets, inventory, tracking">
    <meta name="author" content="<?php echo SITENAME; ?>">

    <!-- Base URL -->
    <base href="<?php echo URLROOT; ?>/">

    <!-- Favicon -->
    <link rel="icon" href="<?php echo URLROOT; ?>/img/favicon/favicon-96x96.png"" type="image/x-icon">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo URLROOT; ?>/css/style.css">
    <link rel="stylesheet" href="<?php echo URLROOT; ?>/css/dropdown-fix.css">

    <!-- Custom JavaScript -->
    <script src="<?php echo URLROOT; ?>/js/tag-manager.js"></script>

    <title><?php echo isset($data['title']) ? $data['title'] . ' | ' . SITENAME : SITENAME; ?></title>

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#6b7280',
                        success: '#10b981',
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        info: '#3b82f6',
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                    boxShadow: {
                        'custom': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style type="text/tailwindcss">
        @layer components {
            .btn {
                @apply px-4 py-2 rounded font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
            }
            .btn-primary {
                @apply bg-primary text-white hover:bg-blue-700 focus:ring-blue-500;
            }
            .btn-secondary {
                @apply bg-secondary text-white hover:bg-gray-700 focus:ring-gray-500;
            }
            .btn-success {
                @apply bg-success text-white hover:bg-green-700 focus:ring-green-500;
            }
            .btn-danger {
                @apply bg-danger text-white hover:bg-red-700 focus:ring-red-500;
            }
            .btn-warning {
                @apply bg-warning text-white hover:bg-yellow-700 focus:ring-yellow-500;
            }
            .btn-info {
                @apply bg-info text-white hover:bg-blue-700 focus:ring-blue-500;
            }
            .btn-light {
                @apply bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-300;
            }
            .btn-dark {
                @apply bg-gray-800 text-white hover:bg-gray-900 focus:ring-gray-700;
            }
            .btn-sm {
                @apply px-2 py-1 text-sm;
            }
            .btn-lg {
                @apply px-6 py-3 text-lg;
            }
            .btn-floating {
                @apply fixed bottom-8 right-8 w-14 h-14 rounded-full flex items-center justify-center bg-primary text-white shadow-lg hover:bg-blue-700 z-50;
            }
            .form-control {
                @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary;
            }
            .form-label {
                @apply block text-sm font-medium text-gray-700 mb-1;
            }
            .form-text {
                @apply mt-1 text-sm text-gray-500;
            }
            .card {
                @apply bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden;
            }
            .card-body {
                @apply p-6;
            }
            .card-title {
                @apply text-xl font-semibold mb-2;
            }
            .card-text {
                @apply text-gray-700;
            }
            .card-header {
                @apply px-6 py-4 bg-gray-50 border-b border-gray-200 font-medium;
            }
            .card-footer {
                @apply px-6 py-4 bg-gray-50 border-t border-gray-200;
            }
            .alert {
                @apply p-4 mb-4 rounded-lg;
            }
            .alert-success {
                @apply bg-green-100 text-green-800;
            }
            .alert-danger {
                @apply bg-red-100 text-red-800;
            }
            .alert-warning {
                @apply bg-yellow-100 text-yellow-800;
            }
            .alert-info {
                @apply bg-blue-100 text-blue-800;
            }
            .table {
                @apply min-w-full divide-y divide-gray-200;
            }
            .table th {
                @apply px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
            }
            .table td {
                @apply px-6 py-4 whitespace-nowrap text-sm text-gray-500;
            }
            .table-striped tr:nth-child(even) {
                @apply bg-gray-50;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex flex-col">
    <?php require APPROOT . '/views/inc/navbar.php'; ?>
    <div class="container mx-auto px-4 py-6 flex-grow">
        <?php flash('asset_message'); ?>
