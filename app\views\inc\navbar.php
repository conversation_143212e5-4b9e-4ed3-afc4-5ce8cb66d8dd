<nav class="bg-gradient-to-r from-blue-800 to-blue-600 text-white shadow-lg mb-6">
    <div class="container mx-auto px-4">
        <div class="flex justify-between h-16">
            <div class="flex items-center">
                <a href="<?php echo URLROOT; ?>" class="flex items-center">
                    <div class="bg-white p-1 rounded-md mr-2">
                        <i class="fas fa-laptop text-blue-600 text-xl"></i>
                    </div>
                    <span class="text-xl font-bold"><?php echo SITENAME; ?></span>
                </a>
                <div class="hidden md:flex ml-10 space-x-6">
                    <a href="<?php echo URLROOT; ?>" class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                        <i class="fas fa-home mr-1.5"></i> Home
                    </a>
                    <?php if(isLoggedIn()) : ?>
                    <a href="<?php echo URLROOT; ?>/dashboard" class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                        <i class="fas fa-tachometer-alt mr-1.5"></i> Dashboard
                    </a>
                    <a href="<?php echo URLROOT; ?>/assets" class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                        <i class="fas fa-laptop mr-1.5"></i> Assets
                    </a>
                    <a href="<?php echo URLROOT; ?>/tags" class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                        <i class="fas fa-tags mr-1.5"></i> Tags
                    </a>
                    <div class="relative group">
                        <button class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                            <i class="fas fa-chart-line mr-1.5"></i> Maintenance <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block dropdown-menu">
                            <a href="<?php echo URLROOT; ?>/maintenance" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-tools mr-2"></i> Predictive Maintenance
                            </a>
                            <a href="<?php echo URLROOT; ?>/maintenance/guidelines" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-clipboard-list mr-2"></i> Maintenance Guidelines
                            </a>
                            <a href="<?php echo URLROOT; ?>/maintenance/monitoring" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-chart-line mr-2"></i> Maintenance Monitoring
                            </a>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                            <i class="fas fa-chart-line mr-1.5"></i> Analytics <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block dropdown-menu">
                            <a href="<?php echo URLROOT; ?>/finance" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-money-bill-wave mr-2"></i> Cost Analysis
                            </a>
                            <a href="<?php echo URLROOT; ?>/compliance" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-clipboard-check mr-2"></i> Compliance
                            </a>
                        </div>
                    </div>
                    <?php if(hasPermission('view_users')) : ?>
                    <a href="<?php echo URLROOT; ?>/users/manage" class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                        <i class="fas fa-users-cog mr-1.5"></i> User Management
                    </a>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="hidden md:flex items-center">
                <?php if(isLoggedIn()) : ?>
                    <a href="<?php echo URLROOT; ?>/users/profile" class="flex items-center bg-blue-700 bg-opacity-50 rounded-full px-4 py-1 mr-4 hover:bg-blue-600 transition-colors">
                        <i class="fas fa-user-circle mr-2 text-blue-300"></i>
                        <span class="text-white"><?php echo $_SESSION['user_name']; ?></span>
                    </a>
                    <a href="<?php echo URLROOT; ?>/users/logout" class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                        <i class="fas fa-sign-out-alt mr-1.5"></i> Logout
                    </a>
                <?php else : ?>
                    <a href="<?php echo URLROOT; ?>/users/register" class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                        <i class="fas fa-user-plus mr-1.5"></i> Register
                    </a>
                    <a href="<?php echo URLROOT; ?>/users/login" class="bg-white text-blue-700 hover:bg-blue-50 px-4 py-2 rounded-md text-sm font-medium ml-4 transition-colors flex items-center shadow-sm">
                        <i class="fas fa-sign-in-alt mr-1.5"></i> Login
                    </a>
                <?php endif; ?>
            </div>

            <!-- Mobile menu button -->
            <div class="flex items-center md:hidden">
                <button type="button" class="mobile-menu-button text-white hover:text-blue-200 focus:outline-none transition-colors" aria-label="Toggle menu">
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state -->
    <div class="mobile-menu hidden md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-blue-700 bg-opacity-90 backdrop-blur-sm">
            <a href="<?php echo URLROOT; ?>" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                <i class="fas fa-home mr-2 w-5 text-center"></i> Home
            </a>
            <?php if(isLoggedIn()) : ?>
            <a href="<?php echo URLROOT; ?>/dashboard" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                <i class="fas fa-tachometer-alt mr-2 w-5 text-center"></i> Dashboard
            </a>
            <a href="<?php echo URLROOT; ?>/assets" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                <i class="fas fa-laptop mr-2 w-5 text-center"></i> Assets
            </a>
            <a href="<?php echo URLROOT; ?>/tags" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                <i class="fas fa-tags mr-2 w-5 text-center"></i> Tags
            </a>
            <div class="border-t border-blue-600 mt-2 pt-2">
                <p class="px-3 text-xs text-blue-300 uppercase font-semibold">Analytics</p>
                <a href="<?php echo URLROOT; ?>/maintenance" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-tools mr-2 w-5 text-center"></i> Predictive Maintenance
                </a>
                <a href="<?php echo URLROOT; ?>/finance" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-money-bill-wave mr-2 w-5 text-center"></i> Cost Analysis
                </a>
                <a href="<?php echo URLROOT; ?>/compliance" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-clipboard-check mr-2 w-5 text-center"></i> Compliance
                </a>
            </div>
            <?php if(hasPermission('view_users')) : ?>
            <a href="<?php echo URLROOT; ?>/users/manage" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                <i class="fas fa-users-cog mr-2 w-5 text-center"></i> User Management
            </a>
            <?php endif; ?>
            <?php endif; ?>

            <?php if(isLoggedIn()) : ?>
                <div class="border-t border-blue-600 pt-4 pb-3">
                    <div class="px-3 mb-2">
                        <div class="flex items-center">
                            <i class="fas fa-user-circle mr-2 text-blue-300 text-xl"></i>
                            <p class="text-white font-medium"><?php echo $_SESSION['user_name']; ?></p>
                        </div>
                    </div>
                    <a href="<?php echo URLROOT; ?>/users/profile" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-user-edit mr-2 w-5 text-center"></i> My Profile
                    </a>
                    <a href="<?php echo URLROOT; ?>/users/logout" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-sign-out-alt mr-2 w-5 text-center"></i> Logout
                    </a>
                </div>
            <?php else : ?>
                <div class="border-t border-blue-600 pt-4 pb-3">
                    <a href="<?php echo URLROOT; ?>/users/register" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-user-plus mr-2 w-5 text-center"></i> Register
                    </a>
                    <a href="<?php echo URLROOT; ?>/users/login" class="text-white hover:bg-blue-600 flex items-center px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-sign-in-alt mr-2 w-5 text-center"></i> Login
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</nav>

<script>
    // Mobile menu toggle and dropdown fix
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile menu toggle
        const mobileMenuButton = document.querySelector('.mobile-menu-button');
        const mobileMenu = document.querySelector('.mobile-menu');

        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
        }

        // Dropdown menu fix
        const dropdownButton = document.querySelector('.relative.group button');
        const dropdownMenu = document.querySelector('.dropdown-menu');

        if (dropdownButton && dropdownMenu) {
            // Add click event for mobile devices
            dropdownButton.addEventListener('click', function(e) {
                e.preventDefault();
                dropdownMenu.classList.toggle('block');
                dropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!dropdownButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
                    dropdownMenu.classList.remove('block');
                    dropdownMenu.classList.add('hidden');
                }
            });
        }
    });
</script>
