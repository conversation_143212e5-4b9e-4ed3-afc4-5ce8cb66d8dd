<?php
/**
 * Pagination Component
 *
 * Displays pagination controls for tables
 *
 * Required parameters in $data:
 * - pagination: array containing:
 *   - total: total number of items
 *   - page: current page number
 *   - perPage: number of items per page
 *   - lastPage: last page number
 */

// Only show pagination if we have more than one page
if (isset($data['pagination']) && $data['pagination']['lastPage'] > 1) :
    $pagination = $data['pagination'];
    $currentPage = $pagination['page'];
    $lastPage = $pagination['lastPage'];

    // Build the query string for pagination links
    $queryParams = $_GET;

    // Function to generate pagination URL
    function paginationUrl($page, $queryParams) {
        $queryParams['page'] = $page;
        return '?' . http_build_query($queryParams);
    }

    // Determine which pages to show (we'll show up to 5 page numbers)
    $startPage = max(1, $currentPage - 2);
    $endPage = min($lastPage, $startPage + 4);

    if ($endPage - $startPage < 4 && $startPage > 1) {
        $startPage = max(1, $endPage - 4);
    }
?>
<div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-4">
    <div class="flex flex-1 justify-between sm:hidden">
        <?php if ($currentPage > 1) : ?>
            <a href="<?php echo paginationUrl($currentPage - 1, $queryParams); ?>" class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Previous</a>
        <?php else : ?>
            <span class="relative inline-flex items-center rounded-md border border-gray-300 bg-gray-100 px-4 py-2 text-sm font-medium text-gray-500 cursor-not-allowed">Previous</span>
        <?php endif; ?>

        <?php if ($currentPage < $lastPage) : ?>
            <a href="<?php echo paginationUrl($currentPage + 1, $queryParams); ?>" class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Next</a>
        <?php else : ?>
            <span class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-gray-100 px-4 py-2 text-sm font-medium text-gray-500 cursor-not-allowed">Next</span>
        <?php endif; ?>
    </div>

    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div class="flex items-center">
            <p class="text-sm text-gray-700 mr-4">
                Showing <span class="font-medium"><?php echo ($currentPage - 1) * $pagination['perPage'] + 1; ?></span> to
                <span class="font-medium"><?php echo min($currentPage * $pagination['perPage'], $pagination['total']); ?></span> of
                <span class="font-medium"><?php echo $pagination['total']; ?></span> results
            </p>

            <!-- Pagination Length Selector -->
            <div class="flex items-center">
                <label for="pagination-length" class="text-sm text-gray-700 mr-2">Show:</label>
                <select id="pagination-length" class="form-select rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                        onchange="changePaginationLength(this.value)">
                    <option value="10" <?php echo $pagination['perPage'] == 10 ? 'selected' : ''; ?>>10</option>
                    <option value="25" <?php echo $pagination['perPage'] == 25 ? 'selected' : ''; ?>>25</option>
                    <option value="50" <?php echo $pagination['perPage'] == 50 ? 'selected' : ''; ?>>50</option>
                    <option value="100" <?php echo $pagination['perPage'] == 100 ? 'selected' : ''; ?>>100</option>
                </select>
            </div>
        </div>

        <div>
            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                <!-- Previous Page -->
                <?php if ($currentPage > 1) : ?>
                    <a href="<?php echo paginationUrl($currentPage - 1, $queryParams); ?>" class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                        <span class="sr-only">Previous</span>
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                        </svg>
                    </a>
                <?php else : ?>
                    <span class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-300 ring-1 ring-inset ring-gray-300 focus:outline-offset-0 cursor-not-allowed">
                        <span class="sr-only">Previous</span>
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                        </svg>
                    </span>
                <?php endif; ?>

                <!-- First Page (if not in view) -->
                <?php if ($startPage > 1) : ?>
                    <a href="<?php echo paginationUrl(1, $queryParams); ?>" class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">1</a>

                    <?php if ($startPage > 2) : ?>
                        <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0">...</span>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- Page Numbers -->
                <?php for ($i = $startPage; $i <= $endPage; $i++) : ?>
                    <?php if ($i == $currentPage) : ?>
                        <span aria-current="page" class="relative z-10 inline-flex items-center bg-blue-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                            <?php echo $i; ?>
                        </span>
                    <?php else : ?>
                        <a href="<?php echo paginationUrl($i, $queryParams); ?>" class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                            <?php echo $i; ?>
                        </a>
                    <?php endif; ?>
                <?php endfor; ?>

                <!-- Last Page (if not in view) -->
                <?php if ($endPage < $lastPage) : ?>
                    <?php if ($endPage < $lastPage - 1) : ?>
                        <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0">...</span>
                    <?php endif; ?>

                    <a href="<?php echo paginationUrl($lastPage, $queryParams); ?>" class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                        <?php echo $lastPage; ?>
                    </a>
                <?php endif; ?>

                <!-- Next Page -->
                <?php if ($currentPage < $lastPage) : ?>
                    <a href="<?php echo paginationUrl($currentPage + 1, $queryParams); ?>" class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                        <span class="sr-only">Next</span>
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                        </svg>
                    </a>
                <?php else : ?>
                    <span class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-300 ring-1 ring-inset ring-gray-300 focus:outline-offset-0 cursor-not-allowed">
                        <span class="sr-only">Next</span>
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                        </svg>
                    </span>
                <?php endif; ?>
            </nav>
        </div>
    </div>
</div>
<?php endif; ?>
