<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Add Maintenance Record</h1>
            <?php if($data['asset']) : ?>
                <p class="text-gray-600">
                    <?php echo $data['asset']->computer_host_name; ?> |
                    <?php echo $data['asset']->equipment_type; ?> |
                    SN: <?php echo $data['asset']->serial_number; ?>
                </p>
            <?php endif; ?>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <?php if($data['asset_id']) : ?>
                <a href="<?php echo URLROOT; ?>/maintenance/history/<?php echo $data['asset_id']; ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                    <i class="fas fa-arrow-left mr-2"></i> Back to History
                </a>
            <?php else : ?>
                <a href="<?php echo URLROOT; ?>/maintenance" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Maintenance Details</h2>
        </div>
        <div class="p-6">
            <?php if($data['asset_id'] && !empty($data['applicable_guidelines'])) : ?>
            <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            <strong>Important:</strong> Don't forget to check the guidelines you want to implement at the bottom of this form.
                        </p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <form action="<?php echo URLROOT; ?>/maintenance/add<?php echo $data['asset_id'] ? '/' . $data['asset_id'] : ''; ?>" method="post">
                <?php if(!$data['asset_id']) : ?>
                    <div class="mb-6">
                        <label for="asset_id" class="block text-sm font-medium text-gray-700 mb-2">Asset</label>
                        <select id="asset_id" name="asset_id" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 <?php echo (!empty($data['asset_id_err'])) ? 'border-red-500' : ''; ?>" required>
                            <option value="">Select Asset</option>
                            <?php foreach($data['assets'] as $asset) : ?>
                                <option value="<?php echo $asset->id; ?>" <?php echo $data['asset_id'] == $asset->id ? 'selected' : ''; ?>>
                                    <?php echo $asset->computer_host_name; ?> (<?php echo $asset->equipment_type; ?>, SN: <?php echo $asset->serial_number; ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if(!empty($data['asset_id_err'])) : ?>
                            <p class="text-red-500 text-xs mt-1"><?php echo $data['asset_id_err']; ?></p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="maintenance_type" class="block text-sm font-medium text-gray-700 mb-2">Maintenance Type</label>
                        <select id="maintenance_type" name="maintenance_type" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 <?php echo (!empty($data['maintenance_type_err'])) ? 'border-red-500' : ''; ?>" required>
                            <option value="">Select Type</option>
                            <option value="preventive" <?php echo $data['maintenance_type'] == 'preventive' ? 'selected' : ''; ?>>Preventive</option>
                            <option value="corrective" <?php echo $data['maintenance_type'] == 'corrective' ? 'selected' : ''; ?>>Corrective</option>
                            <option value="upgrade" <?php echo $data['maintenance_type'] == 'upgrade' ? 'selected' : ''; ?>>Upgrade</option>
                            <option value="inspection" <?php echo $data['maintenance_type'] == 'inspection' ? 'selected' : ''; ?>>Inspection</option>
                        </select>
                        <?php if(!empty($data['maintenance_type_err'])) : ?>
                            <p class="text-red-500 text-xs mt-1"><?php echo $data['maintenance_type_err']; ?></p>
                        <?php endif; ?>
                    </div>

                    <div>
                        <label for="performed_date" class="block text-sm font-medium text-gray-700 mb-2">Date Performed</label>
                        <input type="date" id="performed_date" name="performed_date" value="<?php echo $data['performed_date']; ?>" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 <?php echo (!empty($data['performed_date_err'])) ? 'border-red-500' : ''; ?>" required>
                        <?php if(!empty($data['performed_date_err'])) : ?>
                            <p class="text-red-500 text-xs mt-1"><?php echo $data['performed_date_err']; ?></p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mt-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="4" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 <?php echo (!empty($data['description_err'])) ? 'border-red-500' : ''; ?>" required><?php echo $data['description']; ?></textarea>
                    <?php if(!empty($data['description_err'])) : ?>
                        <p class="text-red-500 text-xs mt-1"><?php echo $data['description_err']; ?></p>
                    <?php endif; ?>
                    <p class="text-gray-500 text-xs mt-1">Provide a detailed description of the maintenance performed.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <label for="cost" class="block text-sm font-medium text-gray-700 mb-2">Cost ($)</label>
                        <input type="number" id="cost" name="cost" value="<?php echo $data['cost']; ?>" step="0.01" min="0" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="status" name="status" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 <?php echo (!empty($data['status_err'])) ? 'border-red-500' : ''; ?>" required>
                            <option value="completed" <?php echo $data['status'] == 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="scheduled" <?php echo $data['status'] == 'scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                            <option value="cancelled" <?php echo $data['status'] == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            <option value="overdue" <?php echo $data['status'] == 'overdue' ? 'selected' : ''; ?>>Overdue</option>
                        </select>
                        <?php if(!empty($data['status_err'])) : ?>
                            <p class="text-red-500 text-xs mt-1"><?php echo $data['status_err']; ?></p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mt-6">
                    <label for="next_scheduled_date" class="block text-sm font-medium text-gray-700 mb-2">Next Scheduled Maintenance (Optional)</label>
                    <input type="date" id="next_scheduled_date" name="next_scheduled_date" value="<?php echo $data['next_scheduled_date']; ?>" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <p class="text-gray-500 text-xs mt-1">When should the next maintenance be performed?</p>
                </div>

                <!-- Maintenance Guidelines -->
                <div class="mt-8 border-t pt-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Maintenance Guidelines <span class="text-sm font-normal text-blue-600">(Part of this form)</span></h2>

                    <?php if($data['asset_id'] && !empty($data['applicable_guidelines'])) : ?>
                        <div class="mb-4">
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">Applicable Guidelines for <?php echo $data['asset']->equipment_type; ?></h3>
                            <p class="text-gray-600 mb-4">Check if these guidelines are being implemented in this maintenance record:</p>

                            <div class="space-y-4">
                                <?php foreach($data['applicable_guidelines'] as $guideline) : ?>
                                    <?php
                                        // Find compliance status for this guideline
                                        $complianceRecord = null;
                                        foreach($data['compliance_status'] as $status) {
                                            if($status->guideline_id == $guideline->id) {
                                                $complianceRecord = $status;
                                                break;
                                            }
                                        }

                                        // Determine status class
                                        $statusClass = 'bg-gray-100';
                                        $statusText = 'Not Implemented';

                                        if($complianceRecord) {
                                            switch($complianceRecord->compliance_status) {
                                                case 'compliant':
                                                    $statusClass = 'bg-green-100 text-green-800';
                                                    $statusText = 'Compliant';
                                                    break;
                                                case 'due_soon':
                                                    $statusClass = 'bg-yellow-100 text-yellow-800';
                                                    $statusText = 'Due Soon';
                                                    break;
                                                case 'overdue':
                                                    $statusClass = 'bg-red-100 text-red-800';
                                                    $statusText = 'Overdue';
                                                    break;
                                            }
                                        }
                                    ?>
                                    <div class="border rounded-lg p-4 <?php echo $statusClass; ?>">
                                        <div class="flex items-start">
                                            <div class="flex-1">
                                                <h4 class="font-semibold"><?php echo $guideline->name; ?></h4>
                                                <p class="text-sm mt-1"><?php echo $guideline->description; ?></p>
                                                <div class="mt-2 text-sm">
                                                    <span class="font-medium">Frequency:</span> Every <?php echo $guideline->frequency_days; ?> days |
                                                    <span class="font-medium">Importance:</span> <?php echo ucfirst($guideline->importance); ?>
                                                </div>
                                                <?php if($complianceRecord && $complianceRecord->last_performed_date) : ?>
                                                    <div class="mt-1 text-sm">
                                                        <span class="font-medium">Last performed:</span> <?php echo date('M j, Y', strtotime($complianceRecord->last_performed_date)); ?> |
                                                        <span class="font-medium">Next due:</span> <?php echo date('M j, Y', strtotime($complianceRecord->next_due_date)); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="ml-4">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $statusClass; ?>">
                                                    <?php echo $statusText; ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="mt-3 flex items-center p-2 bg-blue-50 border border-blue-200 rounded-md">
                                            <input type="checkbox" id="guideline_<?php echo $guideline->id; ?>" name="implemented_guidelines[]" value="<?php echo $guideline->id; ?>" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" onchange="toggleChecklistItems(<?php echo $guideline->id; ?>)">
                                            <label for="guideline_<?php echo $guideline->id; ?>" class="ml-2 block text-sm font-medium text-blue-800">
                                                <i class="fas fa-check-circle mr-1"></i> Mark as implemented in this maintenance record
                                            </label>
                                        </div>

                                        <!-- Checklist Items -->
                                        <?php
                                        // Get checklist items for this guideline
                                        $db = new Database();
                                        $db->query("SELECT * FROM maintenance_checklist WHERE guideline_id = :guideline_id ORDER BY step_number");
                                        $db->bind(':guideline_id', $guideline->id);
                                        $checklistItems = $db->resultSet();

                                        if (!empty($checklistItems)) :
                                            // Check if there are any required items
                                            $hasRequiredItems = false;
                                            foreach ($checklistItems as $item) {
                                                if ($item->is_required) {
                                                    $hasRequiredItems = true;
                                                    break;
                                                }
                                            }
                                        ?>
                                        <div id="checklist_items_<?php echo $guideline->id; ?>" class="mt-3 ml-8 p-3 bg-gray-50 border border-gray-200 rounded-md hidden">
                                            <h5 class="text-sm font-semibold text-gray-700 mb-2">Checklist Items</h5>

                                            <?php if ($hasRequiredItems) : ?>
                                            <div class="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
                                                <i class="fas fa-info-circle mr-1"></i> Required items are automatically checked
                                            </div>
                                            <?php endif; ?>

                                            <div class="space-y-2">
                                                <?php foreach ($checklistItems as $item) : ?>
                                                <div class="flex items-start <?php echo $item->is_required ? 'bg-blue-50 p-1 rounded' : ''; ?>">
                                                    <div class="flex items-center h-5">
                                                        <input type="checkbox" id="checklist_item_<?php echo $item->id; ?>" name="checklist_items[<?php echo $guideline->id; ?>][]" value="<?php echo $item->id; ?>" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                    </div>
                                                    <div class="ml-3 text-sm">
                                                        <label for="checklist_item_<?php echo $item->id; ?>" class="font-medium text-gray-700"><?php echo $item->description; ?></label>
                                                        <?php if ($item->is_required) : ?>
                                                            <span class="ml-1 text-xs text-red-500">(Required)</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php else : ?>
                        <?php if($data['asset_id']) : ?>
                            <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                                <p class="text-yellow-700">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    No maintenance guidelines found for this asset type. Please create guidelines in the
                                    <a href="<?php echo URLROOT; ?>/maintenance/guidelines" class="text-blue-600 hover:underline">Maintenance Guidelines</a> section.
                                </p>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="button" id="validate-form-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md">
                        <i class="fas fa-save mr-2"></i> Save Maintenance Record
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listener to the validate form button
        const validateFormBtn = document.getElementById('validate-form-btn');
        if (validateFormBtn) {
            validateFormBtn.addEventListener('click', function() {
                validateAndSubmitForm();
            });
        }
    });

    function toggleChecklistItems(guidelineId) {
        const checkbox = document.getElementById('guideline_' + guidelineId);
        const checklistItems = document.getElementById('checklist_items_' + guidelineId);

        if (checkbox && checklistItems) {
            if (checkbox.checked) {
                checklistItems.classList.remove('hidden');

                // Automatically check required checklist items
                const requiredCheckboxes = checklistItems.querySelectorAll('input[type="checkbox"]');
                requiredCheckboxes.forEach(item => {
                    // Check if this item has a "Required" label next to it
                    const label = item.parentElement.nextElementSibling;
                    if (label && label.innerHTML.includes('(Required)')) {
                        item.checked = true;
                    }
                });
            } else {
                checklistItems.classList.add('hidden');

                // Uncheck all checklist items when guideline is unchecked
                const checklistCheckboxes = checklistItems.querySelectorAll('input[type="checkbox"]');
                checklistCheckboxes.forEach(item => {
                    item.checked = false;
                });
            }
        }
    }

    function validateAndSubmitForm() {
        // Basic form validation
        const form = document.querySelector('form');
        let isValid = true;
        let errorMessage = '';

        // Check required fields
        const requiredFields = form.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                errorMessage = 'Please fill in all required fields';
                field.classList.add('border-red-500');
            } else {
                field.classList.remove('border-red-500');
            }
        });

        // Check if any guidelines are selected
        const guidelineCheckboxes = form.querySelectorAll('input[name="implemented_guidelines[]"]');
        let anyGuidelineSelected = false;
        let missingRequiredItems = false;
        let missingGuidelineId = null;

        guidelineCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                anyGuidelineSelected = true;

                // Check if all required checklist items for this guideline are checked
                const guidelineId = checkbox.value;
                const checklistContainer = document.getElementById('checklist_items_' + guidelineId);

                if (checklistContainer) {
                    const requiredItems = checklistContainer.querySelectorAll('.flex.items-start.bg-blue-50 input[type="checkbox"]');

                    requiredItems.forEach(item => {
                        if (!item.checked) {
                            missingRequiredItems = true;
                            missingGuidelineId = guidelineId;
                        }
                    });
                }
            }
        });

        // If there are applicable guidelines but none selected, show a warning
        const applicableGuidelinesSection = document.querySelector('.space-y-4');
        if (applicableGuidelinesSection && guidelineCheckboxes.length > 0 && !anyGuidelineSelected) {
            // Use SweetAlert if available, otherwise use confirm
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'No Guidelines Selected',
                    text: 'You haven\'t selected any maintenance guidelines. Are you sure you want to continue?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, continue',
                    cancelButtonText: 'No, I\'ll select guidelines'
                }).then((result) => {
                    if (result.isConfirmed) {
                        submitFormIfValid(form, isValid, errorMessage);
                    }
                });
                return;
            } else if (!confirm('You haven\'t selected any maintenance guidelines. Are you sure you want to continue?')) {
                return;
            }
        }

        // If required checklist items are missing, show an error
        if (missingRequiredItems) {
            // Scroll to the guideline with missing required items
            const guidelineElement = document.getElementById('guideline_' + missingGuidelineId);
            if (guidelineElement) {
                guidelineElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            // Use SweetAlert if available, otherwise use alert
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'Required Items Missing',
                    text: 'Please check all required checklist items for the selected guidelines.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            } else {
                alert('Please check all required checklist items for the selected guidelines.');
            }
            return;
        }

        // If everything is valid, submit the form
        submitFormIfValid(form, isValid, errorMessage);
    }

    function submitFormIfValid(form, isValid, errorMessage) {
        if (isValid) {
            form.submit();
        } else {
            // Use SweetAlert if available, otherwise use alert
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'Form Validation Error',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            } else {
                alert(errorMessage);
            }
        }
    }
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
