<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">All Maintenance History</h1>
            <p class="text-gray-600">
                Showing all maintenance records across all assets
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/add" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-plus mr-2"></i> Add Maintenance Record
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Maintenance History -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Maintenance Records</h2>
            <div class="text-sm text-gray-600">
                Total Records: <?php echo $data['total_records']; ?>
            </div>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($data['maintenance_history']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performed By</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['maintenance_history'] as $record) : ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo date('M j, Y', strtotime($record->performed_date)); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $record->computer_host_name; ?></div>
                                    <div class="text-xs text-gray-500"><?php echo $record->equipment_type; ?> | SN: <?php echo $record->serial_number; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $typeClass = 'bg-blue-100 text-blue-800';
                                        switch($record->maintenance_type) {
                                            case 'preventive':
                                                $typeClass = 'bg-green-100 text-green-800';
                                                break;
                                            case 'corrective':
                                                $typeClass = 'bg-red-100 text-red-800';
                                                break;
                                            case 'upgrade':
                                                $typeClass = 'bg-purple-100 text-purple-800';
                                                break;
                                        }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $typeClass; ?>">
                                        <?php echo ucfirst($record->maintenance_type); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900"><?php echo $record->description; ?></div>

                                    <div class="mt-2">
                                        <?php if(!empty($data['implemented_guidelines'][$record->id])) : ?>
                                            <span class="text-xs font-medium text-gray-500">Guidelines Implemented:</span>
                                            <div class="flex flex-wrap gap-1 mt-1">
                                                <?php foreach($data['implemented_guidelines'][$record->id] as $guideline) : ?>
                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                                        <?php echo $guideline->name; ?>
                                                    </span>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>

                                        <div class="mt-2">
                                            <a href="<?php echo URLROOT; ?>/maintenance/viewRecord/<?php echo $record->id; ?>" class="text-xs text-blue-600 hover:text-blue-800 font-medium">
                                                <i class="fas fa-eye mr-1"></i> View Maintenance Details
                                            </a>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo $record->performed_by_name ?? 'N/A'; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">$<?php echo number_format($record->cost, 2); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $statusClass = 'bg-gray-100 text-gray-800';
                                        switch($record->status) {
                                            case 'completed':
                                                $statusClass = 'bg-green-100 text-green-800';
                                                break;
                                            case 'scheduled':
                                                $statusClass = 'bg-blue-100 text-blue-800';
                                                break;
                                            case 'cancelled':
                                                $statusClass = 'bg-red-100 text-red-800';
                                                break;
                                            case 'overdue':
                                                $statusClass = 'bg-yellow-100 text-yellow-800';
                                                break;
                                        }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $statusClass; ?>">
                                        <?php echo ucfirst($record->status); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo URLROOT; ?>/maintenance/history/<?php echo $record->asset_id; ?>" class="text-blue-600 hover:text-blue-900">
                                        <i class="fas fa-history"></i> Asset History
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <!-- Pagination -->
                <?php if($data['total_pages'] > 1) : ?>
                    <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-700">
                                Showing <span class="font-medium"><?php echo ($data['current_page'] - 1) * $data['records_per_page'] + 1; ?></span> to
                                <span class="font-medium">
                                    <?php echo min($data['current_page'] * $data['records_per_page'], $data['total_records']); ?>
                                </span> of
                                <span class="font-medium"><?php echo $data['total_records']; ?></span> results
                            </div>
                            <div class="flex space-x-2">
                                <?php if($data['current_page'] > 1) : ?>
                                    <a href="<?php echo URLROOT; ?>/maintenance/allHistory/<?php echo $data['current_page'] - 1; ?>" class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        Previous
                                    </a>
                                <?php endif; ?>

                                <?php if($data['current_page'] < $data['total_pages']) : ?>
                                    <a href="<?php echo URLROOT; ?>/maintenance/allHistory/<?php echo $data['current_page'] + 1; ?>" class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        Next
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No maintenance records found.</p>
                    <a href="<?php echo URLROOT; ?>/maintenance/add" class="inline-block mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                        <i class="fas fa-plus mr-2"></i> Add First Maintenance Record
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
