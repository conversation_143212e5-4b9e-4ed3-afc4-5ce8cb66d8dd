<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2"><?php echo $data['asset']->computer_host_name; ?></h1>
            <p class="text-gray-600">
                <?php echo $data['asset']->equipment_type; ?> |
                SN: <?php echo $data['asset']->serial_number; ?> |
                Maintenance Compliance
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance/monitoring" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Monitoring
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/history/<?php echo $data['asset']->id; ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-history mr-2"></i> Maintenance History
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/add/<?php echo $data['asset']->id; ?>" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-plus mr-2"></i> Add Record
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Compliance Status -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Maintenance Compliance Status</h2>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($data['compliance_status']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guideline</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Importance</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Performed</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Due Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['compliance_status'] as $item) : ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $item->guideline_name; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $importanceClass = 'bg-blue-100 text-blue-800';
                                        if($item->importance == 'critical') {
                                            $importanceClass = 'bg-red-100 text-red-800';
                                        } else if($item->importance == 'high') {
                                            $importanceClass = 'bg-orange-100 text-orange-800';
                                        } else if($item->importance == 'low') {
                                            $importanceClass = 'bg-green-100 text-green-800';
                                        }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $importanceClass; ?>">
                                        <?php echo ucfirst($item->importance); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $statusClass = 'bg-gray-100 text-gray-800';
                                        $statusText = 'Not Applicable';

                                        if($item->compliance_status == 'compliant') {
                                            $statusClass = 'bg-green-100 text-green-800';
                                            $statusText = 'Compliant';
                                        } else if($item->compliance_status == 'due_soon') {
                                            $statusClass = 'bg-yellow-100 text-yellow-800';
                                            $statusText = 'Due Soon';
                                        } else if($item->compliance_status == 'overdue') {
                                            $statusClass = 'bg-red-100 text-red-800';
                                            $statusText = 'Overdue';
                                        }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $statusClass; ?>">
                                        <?php echo $statusText; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        <?php echo $item->last_performed_date ? date('M j, Y', strtotime($item->last_performed_date)) : 'Never'; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        <?php echo $item->next_due_date ? date('M j, Y', strtotime($item->next_due_date)) : 'N/A'; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo URLROOT; ?>/maintenance/add/<?php echo $data['asset']->id; ?>" class="text-green-600 hover:text-green-900">
                                        <i class="fas fa-plus"></i> Add Record
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No compliance status records found for this asset.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Maintenance History -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Recent Maintenance History</h2>
            <a href="<?php echo URLROOT; ?>/maintenance/history/<?php echo $data['asset']->id; ?>" class="text-blue-600 hover:text-blue-900">
                <i class="fas fa-external-link-alt mr-1"></i> View Full History
            </a>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($data['maintenance_history']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performed Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php
                        // Show only the 5 most recent records
                        $recentHistory = array_slice($data['maintenance_history'], 0, 5);
                        foreach($recentHistory as $record) :
                        ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo ucfirst($record->maintenance_type); ?></div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900"><?php echo substr($record->description, 0, 100) . (strlen($record->description) > 100 ? '...' : ''); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500"><?php echo date('M j, Y', strtotime($record->performed_date)); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $statusClass = 'bg-blue-100 text-blue-800';
                                        if($record->status == 'completed') {
                                            $statusClass = 'bg-green-100 text-green-800';
                                        } else if($record->status == 'scheduled') {
                                            $statusClass = 'bg-yellow-100 text-yellow-800';
                                        } else if($record->status == 'overdue') {
                                            $statusClass = 'bg-red-100 text-red-800';
                                        } else if($record->status == 'cancelled') {
                                            $statusClass = 'bg-gray-100 text-gray-800';
                                        }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $statusClass; ?>">
                                        <?php echo ucfirst($record->status); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo URLROOT; ?>/maintenance/viewRecord/<?php echo $record->id; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No maintenance history found for this asset.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
