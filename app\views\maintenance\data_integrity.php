<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Data Integrity Check Tool</h1>
            <p class="text-gray-600">
                Identify and fix orphaned records in the maintenance_guideline_implementation table
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/recreateMissingRecords" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-sync mr-2"></i> Recreate Missing Records
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Summary Card -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Data Integrity Summary</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h3 class="text-lg font-semibold text-blue-800 mb-2">Total Records</h3>
                    <p class="text-3xl font-bold text-blue-600"><?php echo $data['total_records']; ?></p>
                    <p class="text-sm text-blue-700 mt-2">Total records in the maintenance_guideline_implementation table</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    <h3 class="text-lg font-semibold text-yellow-800 mb-2">Orphaned Records</h3>
                    <p class="text-3xl font-bold text-yellow-600"><?php echo $data['orphaned_count']; ?></p>
                    <p class="text-sm text-yellow-700 mt-2">Records with missing maintenance history or guideline</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                    <h3 class="text-lg font-semibold text-green-800 mb-2">Healthy Records</h3>
                    <p class="text-3xl font-bold text-green-600"><?php echo $data['total_records'] - $data['orphaned_count']; ?></p>
                    <p class="text-sm text-green-700 mt-2">Records with valid maintenance history and guideline</p>
                </div>
            </div>

            <?php if ($data['orphaned_count'] > 0): ?>
            <div class="mt-6">
                <form action="<?php echo URLROOT; ?>/maintenance/dataIntegrityCheck" method="POST" class="flex justify-center">
                    <input type="hidden" name="fix_orphaned" value="true">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-md">
                        <i class="fas fa-trash-alt mr-2"></i> Delete <?php echo $data['orphaned_count']; ?> Orphaned Records
                    </button>
                </form>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Orphaned Records Table -->
    <?php if (!empty($data['orphaned_records'])): ?>
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Orphaned Records</h2>
            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-semibold">
                <?php echo count($data['orphaned_records']); ?> records
            </span>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Maintenance ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guideline ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guideline Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Implemented Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach($data['orphaned_records'] as $record): ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->id; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->maintenance_id; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->guideline_id; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->guideline_name ?? 'Missing Guideline'; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo isset($record->implemented_date) ? date('M j, Y', strtotime($record->implemented_date)) : 'N/A'; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <?php if (!isset($record->guideline_name)): ?>
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                Missing Guideline
                            </span>
                            <?php else: ?>
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                Missing Maintenance Record
                            </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php elseif ($data['action_taken']): ?>
    <div class="bg-green-50 p-4 rounded-lg border border-green-200 text-center">
        <p class="text-green-700">
            <i class="fas fa-check-circle text-2xl mr-2"></i>
            All orphaned records have been successfully removed.
        </p>
    </div>
    <?php else: ?>
    <div class="bg-green-50 p-4 rounded-lg border border-green-200 text-center">
        <p class="text-green-700">
            <i class="fas fa-check-circle text-2xl mr-2"></i>
            No orphaned records found. Data integrity is good!
        </p>
    </div>
    <?php endif; ?>

    <!-- Fixed Records Table -->
    <?php if (!empty($data['fixed_records'])): ?>
    <div class="mt-8 bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Fixed Records</h2>
            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-semibold">
                <?php echo count($data['fixed_records']); ?> records
            </span>
        </div>
        <div class="p-6">
            <p class="text-green-700 mb-4">
                <i class="fas fa-check-circle mr-2"></i>
                Successfully removed <?php echo count($data['fixed_records']); ?> orphaned records.
            </p>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
