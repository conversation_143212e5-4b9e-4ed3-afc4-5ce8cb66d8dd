<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                <?php echo isset($data['id']) ? 'Edit Checklist Item' : 'Add Checklist Item'; ?>
            </h1>
            <p class="text-gray-600">
                For guideline: <span class="font-semibold"><?php echo $data['guideline']->name; ?></span>
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance/manageChecklist/<?php echo $data['guideline_id']; ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Checklist
            </a>
        </div>
    </div>

    <!-- Checklist Item Form -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">
                <?php echo isset($data['id']) ? 'Edit Checklist Item' : 'Add New Checklist Item'; ?>
            </h2>
        </div>
        <div class="p-6">
            <form id="checklist-form" action="<?php echo URLROOT; ?>/maintenance/editChecklistItem/<?php echo $data['guideline_id']; ?><?php echo isset($data['id']) ? '/' . $data['id'] : ''; ?>" method="POST">
                <div class="grid grid-cols-1 gap-6">
                    <!-- Step Number -->
                    <div>
                        <label for="step_number" class="block text-sm font-medium text-gray-700">Step Number</label>
                        <div class="mt-1">
                            <input type="number" name="step_number" id="step_number" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" value="<?php echo $data['step_number']; ?>" min="1">
                        </div>
                        <p class="mt-1 text-xs text-gray-500">The order in which this step appears in the checklist.</p>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <div class="mt-1">
                            <textarea name="description" id="description" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md <?php echo (!empty($data['description_err'])) ? 'border-red-500' : ''; ?>" placeholder="Enter a clear description of this checklist item"><?php echo $data['description']; ?></textarea>
                        </div>
                        <?php if(!empty($data['description_err'])) : ?>
                            <p class="mt-1 text-xs text-red-500"><?php echo $data['description_err']; ?></p>
                        <?php else : ?>
                            <p class="mt-1 text-xs text-gray-500">Provide a clear, actionable description of what needs to be done.</p>
                        <?php endif; ?>
                    </div>

                    <!-- Is Required -->
                    <div>
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input type="checkbox" name="is_required" id="is_required" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded" <?php echo $data['is_required'] ? 'checked' : ''; ?>>
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="is_required" class="font-medium text-gray-700">Required Item</label>
                                <p class="text-gray-500">Mark this item as required for maintenance completion.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-4">
                        <button type="button" id="submit-checklist-btn" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <?php echo isset($data['id']) ? 'Update Checklist Item' : 'Add Checklist Item'; ?>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // SweetAlert for checklist form submission
        const submitButton = document.getElementById('submit-checklist-btn');
        const form = document.getElementById('checklist-form');

        if (submitButton && form) {
            submitButton.addEventListener('click', function(e) {
                e.preventDefault();

                // Check if description is filled
                const description = document.getElementById('description').value.trim();
                if (!description) {
                    Toast.error('Please enter a description for the checklist item');
                    return;
                }

                const isUpdate = <?php echo isset($data['id']) ? 'true' : 'false'; ?>;
                const actionText = isUpdate ? 'Update' : 'Add';

                Swal.fire({
                    title: `${actionText} Checklist Item?`,
                    text: `Are you sure you want to ${actionText.toLowerCase()} this checklist item?`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3b82f6',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: `Yes, ${actionText.toLowerCase()} it!`,
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Submit the form
                        form.submit();
                    }
                });
            });
        }
    });
</script>
