<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                <?php echo isset($data['id']) ? 'Edit Maintenance Guideline' : 'Add Maintenance Guideline'; ?>
            </h1>
            <?php if(isset($data['id'])): ?>
            <p class="text-gray-600">
                Editing: <span class="font-semibold"><?php echo $data['name']; ?></span>
            </p>
            <?php endif; ?>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance/guidelines" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Guidelines
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Guideline Form -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">
                <?php echo isset($data['id']) ? 'Edit Guideline' : 'Add New Guideline'; ?>
            </h2>
        </div>
        <div class="p-6">
            <form id="guideline-form" action="<?php echo URLROOT; ?>/maintenance/editGuideline<?php echo isset($data['id']) ? '/' . $data['id'] : ''; ?>" method="POST">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Name -->
                    <div class="md:col-span-2">
                        <label for="name" class="block text-sm font-medium text-gray-700">Guideline Name</label>
                        <div class="mt-1">
                            <input type="text" name="name" id="name" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md <?php echo (!empty($data['name_err'])) ? 'border-red-500' : ''; ?>" value="<?php echo $data['name']; ?>" placeholder="Enter guideline name">
                        </div>
                        <?php if(!empty($data['name_err'])) : ?>
                            <p class="mt-1 text-xs text-red-500"><?php echo $data['name_err']; ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <div class="mt-1">
                            <textarea name="description" id="description" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md <?php echo (!empty($data['description_err'])) ? 'border-red-500' : ''; ?>" placeholder="Enter guideline description"><?php echo $data['description']; ?></textarea>
                        </div>
                        <?php if(!empty($data['description_err'])) : ?>
                            <p class="mt-1 text-xs text-red-500"><?php echo $data['description_err']; ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Equipment Type -->
                    <div>
                        <label for="equipment_type" class="block text-sm font-medium text-gray-700">Equipment Type</label>
                        <div class="mt-1">
                            <select name="equipment_type" id="equipment_type" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md <?php echo (!empty($data['equipment_type_err'])) ? 'border-red-500' : ''; ?>">
                                <option value="">Select Equipment Type</option>
                                <?php foreach($data['equipment_types'] as $type): ?>
                                    <option value="<?php echo $type->equipment_type; ?>" <?php echo ($data['equipment_type'] == $type->equipment_type) ? 'selected' : ''; ?>>
                                        <?php echo $type->equipment_type; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php if(!empty($data['equipment_type_err'])) : ?>
                            <p class="mt-1 text-xs text-red-500"><?php echo $data['equipment_type_err']; ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Frequency Days -->
                    <div>
                        <label for="frequency_days" class="block text-sm font-medium text-gray-700">Frequency (Days)</label>
                        <div class="mt-1">
                            <input type="number" name="frequency_days" id="frequency_days" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md <?php echo (!empty($data['frequency_days_err'])) ? 'border-red-500' : ''; ?>" value="<?php echo $data['frequency_days']; ?>" min="1">
                        </div>
                        <?php if(!empty($data['frequency_days_err'])) : ?>
                            <p class="mt-1 text-xs text-red-500"><?php echo $data['frequency_days_err']; ?></p>
                        <?php else : ?>
                            <p class="mt-1 text-xs text-gray-500">How often this maintenance should be performed (in days).</p>
                        <?php endif; ?>
                    </div>

                    <!-- Importance -->
                    <div>
                        <label for="importance" class="block text-sm font-medium text-gray-700">Importance</label>
                        <div class="mt-1">
                            <select name="importance" id="importance" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md <?php echo (!empty($data['importance_err'])) ? 'border-red-500' : ''; ?>">
                                <option value="low" <?php echo ($data['importance'] == 'low') ? 'selected' : ''; ?>>Low</option>
                                <option value="medium" <?php echo ($data['importance'] == 'medium') ? 'selected' : ''; ?>>Medium</option>
                                <option value="high" <?php echo ($data['importance'] == 'high') ? 'selected' : ''; ?>>High</option>
                                <option value="critical" <?php echo ($data['importance'] == 'critical') ? 'selected' : ''; ?>>Critical</option>
                            </select>
                        </div>
                        <?php if(!empty($data['importance_err'])) : ?>
                            <p class="mt-1 text-xs text-red-500"><?php echo $data['importance_err']; ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Submit Button -->
                    <div class="md:col-span-2 pt-4">
                        <button type="button" id="submit-guideline-btn" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <?php echo isset($data['id']) ? 'Update Guideline' : 'Add Guideline'; ?>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // SweetAlert for guideline form submission
        const submitButton = document.getElementById('submit-guideline-btn');
        const form = document.getElementById('guideline-form');

        if (submitButton && form) {
            submitButton.addEventListener('click', function(e) {
                e.preventDefault();

                // Basic validation
                const name = document.getElementById('name').value.trim();
                const description = document.getElementById('description').value.trim();
                const equipmentType = document.getElementById('equipment_type').value.trim();
                const frequencyDays = document.getElementById('frequency_days').value.trim();

                if (!name) {
                    Toast.error('Please enter a guideline name');
                    return;
                }

                if (!description) {
                    Toast.error('Please enter a guideline description');
                    return;
                }

                if (!equipmentType) {
                    Toast.error('Please select an equipment type');
                    return;
                }

                if (!frequencyDays || frequencyDays < 1) {
                    Toast.error('Please enter a valid frequency in days');
                    return;
                }

                const isUpdate = <?php echo isset($data['id']) ? 'true' : 'false'; ?>;
                const actionText = isUpdate ? 'Update' : 'Add';

                Swal.fire({
                    title: `${actionText} Maintenance Guideline?`,
                    text: `Are you sure you want to ${actionText.toLowerCase()} this maintenance guideline?`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3b82f6',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: `Yes, ${actionText.toLowerCase()} it!`,
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Submit the form
                        form.submit();
                    }
                });
            });
        }
    });
</script>
