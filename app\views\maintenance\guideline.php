<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Guideline Details</h1>
            <p class="text-gray-600">
                Viewing details for <span class="font-semibold"><?php echo $data['guideline']->name; ?></span>
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance/guidelines" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Guidelines
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/guidelineImplementations/<?php echo $data['guideline']->id; ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-history mr-2"></i> View Implementation History
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/editGuideline/<?php echo $data['guideline']->id; ?>" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-edit mr-2"></i> Edit Guideline
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Guideline Information -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Guideline Information</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Basic Information</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Name:</span>
                            <p class="mt-1"><?php echo $data['guideline']->name; ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Equipment Type:</span>
                            <p class="mt-1"><?php echo $data['guideline']->equipment_type; ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Frequency:</span>
                            <p class="mt-1">Every <?php echo $data['guideline']->frequency_days; ?> days</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Importance:</span>
                            <?php
                                $importanceClass = 'bg-blue-100 text-blue-800';
                                switch($data['guideline']->importance) {
                                    case 'low':
                                        $importanceClass = 'bg-gray-100 text-gray-800';
                                        break;
                                    case 'medium':
                                        $importanceClass = 'bg-blue-100 text-blue-800';
                                        break;
                                    case 'high':
                                        $importanceClass = 'bg-yellow-100 text-yellow-800';
                                        break;
                                    case 'critical':
                                        $importanceClass = 'bg-red-100 text-red-800';
                                        break;
                                }
                            ?>
                            <p class="mt-1">
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $importanceClass; ?>">
                                    <?php echo ucfirst($data['guideline']->importance); ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Description</h3>
                    <p class="text-gray-700"><?php echo $data['guideline']->description; ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Compliance Status -->
    <?php if(isset($data['compliance_status']) && !empty($data['compliance_status'])) : ?>
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">Compliance Status</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Assets Compliant</h3>
                        <p class="text-3xl font-bold text-green-600"><?php echo $data['compliance_status']->compliant_count ?? 0; ?></p>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Assets Non-Compliant</h3>
                        <p class="text-3xl font-bold text-red-600"><?php echo $data['compliance_status']->non_compliant_count ?? 0; ?></p>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Overall Compliance</h3>
                        <?php
                            $totalAssets = ($data['compliance_status']->compliant_count ?? 0) + ($data['compliance_status']->non_compliant_count ?? 0);
                            $compliancePercentage = $totalAssets > 0 ? round(($data['compliance_status']->compliant_count ?? 0) / $totalAssets * 100) : 0;

                            $complianceClass = 'text-red-600';
                            if($compliancePercentage >= 90) {
                                $complianceClass = 'text-green-600';
                            } else if($compliancePercentage >= 70) {
                                $complianceClass = 'text-yellow-600';
                            } else if($compliancePercentage >= 50) {
                                $complianceClass = 'text-orange-600';
                            }
                        ?>
                        <p class="text-3xl font-bold <?php echo $complianceClass; ?>"><?php echo $compliancePercentage; ?>%</p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Checklist Items -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Maintenance Checklist</h2>
            <a href="<?php echo URLROOT; ?>/maintenance/manageChecklist/<?php echo $data['guideline']->id; ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm">
                <i class="fas fa-tasks mr-2"></i> Manage Checklist Items
            </a>
        </div>
        <div class="p-6">
            <?php if(isset($data['checklist_items']) && !empty($data['checklist_items'])) : ?>
                <ul class="space-y-4">
                    <?php
                    // Create a temporary array to track displayed item IDs
                    $displayedItemIds = [];
                    $displayIndex = 0;

                    // Loop through items and only display each unique ID once
                    foreach($data['checklist_items'] as $item) :
                        // Skip if we've already displayed this item ID
                        if (in_array($item->id, $displayedItemIds)) {
                            continue;
                        }

                        // Add this item ID to our tracking array
                        $displayedItemIds[] = $item->id;
                        $displayIndex++;
                    ?>
                        <li class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                    <span class="text-blue-800 font-semibold"><?php echo $displayIndex; ?></span>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-800"><?php echo $item->description; ?></h4>
                                    <?php if(!empty($item->details)) : ?>
                                        <p class="text-sm text-gray-600 mt-1"><?php echo $item->details; ?></p>
                                    <?php endif; ?>
                                    <div class="mt-1">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $item->is_required ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $item->is_required ? 'Required' : 'Optional'; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php else : ?>
                <div class="text-center py-8">
                    <p class="text-gray-500 mb-4">No checklist items available for this guideline.</p>
                    <a href="<?php echo URLROOT; ?>/maintenance/editChecklistItem/<?php echo $data['guideline']->id; ?>" class="add-first-item-btn inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                        <i class="fas fa-plus mr-2"></i> Add First Checklist Item
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // SweetAlert for "Add First Checklist Item" button
        const addFirstItemBtn = document.querySelector('.add-first-item-btn');
        if (addFirstItemBtn) {
            addFirstItemBtn.addEventListener('click', function(e) {
                e.preventDefault();

                const href = this.getAttribute('href');

                Swal.fire({
                    title: 'Add First Checklist Item?',
                    text: 'You will be redirected to the form to add your first checklist item.',
                    icon: 'info',
                    showCancelButton: true,
                    confirmButtonColor: '#3b82f6',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Yes, proceed',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = href;
                    }
                });
            });
        }

        // SweetAlert for "Manage Checklist Items" button
        const manageChecklistBtn = document.querySelector('a[href*="/maintenance/manageChecklist/"]');
        if (manageChecklistBtn) {
            manageChecklistBtn.addEventListener('click', function(e) {
                e.preventDefault();

                const href = this.getAttribute('href');

                Swal.fire({
                    title: 'Manage Checklist Items',
                    text: 'You will be redirected to the checklist management page.',
                    icon: 'info',
                    showCancelButton: true,
                    confirmButtonColor: '#4f46e5',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Yes, proceed',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = href;
                    }
                });
            });
        }
    });
</script>
