<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Guideline Implementation History</h1>
            <p class="text-gray-600">
                Viewing all implementations of <span class="font-semibold"><?php echo $data['guideline']->name; ?></span>
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance/guidelines" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Guidelines
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/guideline/<?php echo $data['guideline']->id; ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-info-circle mr-2"></i> Guideline Details
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Guideline Information -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Guideline Information</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Basic Information</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Name:</span>
                            <p class="mt-1"><?php echo $data['guideline']->name; ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Equipment Type:</span>
                            <p class="mt-1"><?php echo $data['guideline']->equipment_type; ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Frequency:</span>
                            <p class="mt-1">Every <?php echo $data['guideline']->frequency_days; ?> days</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Importance:</span>
                            <?php
                                $importanceClass = 'bg-blue-100 text-blue-800';
                                switch($data['guideline']->importance) {
                                    case 'low':
                                        $importanceClass = 'bg-gray-100 text-gray-800';
                                        break;
                                    case 'medium':
                                        $importanceClass = 'bg-blue-100 text-blue-800';
                                        break;
                                    case 'high':
                                        $importanceClass = 'bg-yellow-100 text-yellow-800';
                                        break;
                                    case 'critical':
                                        $importanceClass = 'bg-red-100 text-red-800';
                                        break;
                                }
                            ?>
                            <p class="mt-1">
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $importanceClass; ?>">
                                    <?php echo ucfirst($data['guideline']->importance); ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Description</h3>
                    <p class="text-gray-700"><?php echo $data['guideline']->description; ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Implementation History -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Implementation History</h2>
            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                Total: <?php echo $data['total_count']; ?> implementations
            </span>
        </div>
        <div class="overflow-x-auto">
            <?php if(!empty($data['implementations'])) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Maintenance Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performed By</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['implementations'] as $implementation) : ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <?php echo isset($implementation->implemented_date) ? date('M j, Y', strtotime($implementation->implemented_date)) : 'N/A'; ?>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <?php echo isset($implementation->implemented_date) ? date('h:i A', strtotime($implementation->implemented_date)) : ''; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if (isset($implementation->computer_host_name) && isset($implementation->equipment_type)): ?>
                                    <div class="text-sm font-medium text-gray-900">
                                        <?php echo $implementation->computer_host_name; ?>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <?php echo $implementation->equipment_type; ?> | SN: <?php echo $implementation->serial_number ?? 'N/A'; ?>
                                    </div>
                                    <?php else: ?>
                                    <div class="text-sm text-gray-500">Asset information unavailable</div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if (isset($implementation->maintenance_type)): ?>
                                    <?php
                                        $typeClass = 'bg-blue-100 text-blue-800';
                                        switch($implementation->maintenance_type) {
                                            case 'preventive':
                                                $typeClass = 'bg-green-100 text-green-800';
                                                break;
                                            case 'corrective':
                                                $typeClass = 'bg-red-100 text-red-800';
                                                break;
                                            case 'upgrade':
                                                $typeClass = 'bg-purple-100 text-purple-800';
                                                break;
                                        }
                                    ?>
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $typeClass; ?>">
                                        <?php echo ucfirst($implementation->maintenance_type); ?>
                                    </span>
                                    <?php else: ?>
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        Unknown
                                    </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if (isset($implementation->status)): ?>
                                    <?php
                                        $statusClass = 'bg-gray-100 text-gray-800';
                                        switch($implementation->status) {
                                            case 'completed':
                                                $statusClass = 'bg-green-100 text-green-800';
                                                break;
                                            case 'scheduled':
                                                $statusClass = 'bg-blue-100 text-blue-800';
                                                break;
                                            case 'cancelled':
                                                $statusClass = 'bg-red-100 text-red-800';
                                                break;
                                            case 'overdue':
                                                $statusClass = 'bg-yellow-100 text-yellow-800';
                                                break;
                                        }
                                    ?>
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $statusClass; ?>">
                                        <?php echo ucfirst($implementation->status); ?>
                                    </span>
                                    <?php else: ?>
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        Unknown
                                    </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $implementation->performed_by_name ?? 'N/A'; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <?php if (isset($implementation->maintenance_id)): ?>
                                    <a href="<?php echo URLROOT; ?>/maintenance/viewRecord/<?php echo $implementation->maintenance_id; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-eye"></i> View Record
                                    </a>
                                    <?php endif; ?>

                                    <?php if (isset($implementation->asset_id)): ?>
                                    <a href="<?php echo URLROOT; ?>/assets/show/<?php echo $implementation->asset_id; ?>" class="text-indigo-600 hover:text-indigo-900">
                                        <i class="fas fa-desktop"></i> View Asset
                                    </a>
                                    <?php endif; ?>

                                    <?php if (!isset($implementation->maintenance_id) && !isset($implementation->asset_id)): ?>
                                    <span class="text-gray-400">No actions available</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <!-- Pagination -->
                <?php if($data['total_pages'] > 1) : ?>
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                Showing <span class="font-medium"><?php echo (($data['current_page'] - 1) * $data['records_per_page']) + 1; ?></span>
                                to <span class="font-medium"><?php echo min($data['current_page'] * $data['records_per_page'], $data['total_count']); ?></span>
                                of <span class="font-medium"><?php echo $data['total_count']; ?></span> results
                            </div>
                            <div class="flex space-x-2">
                                <?php if($data['current_page'] > 1) : ?>
                                    <a href="<?php echo URLROOT; ?>/maintenance/guidelineImplementations/<?php echo $data['guideline']->id; ?>/<?php echo $data['current_page'] - 1; ?>" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        Previous
                                    </a>
                                <?php endif; ?>

                                <?php if($data['current_page'] < $data['total_pages']) : ?>
                                    <a href="<?php echo URLROOT; ?>/maintenance/guidelineImplementations/<?php echo $data['guideline']->id; ?>/<?php echo $data['current_page'] + 1; ?>" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        Next
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500 mb-4">No implementation records found for this guideline.</p>

                    <?php if(isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin'): ?>
                    <div class="mt-4">
                        <p class="text-gray-600 mb-2">This could be due to a database issue with the implementation records table.</p>
                        <a href="<?php echo URLROOT; ?>/maintenance/runMigration/<?php echo $data['guideline']->id; ?>"
                           class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md inline-block">
                            <i class="fas fa-database mr-2"></i> Run Database Migration
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
