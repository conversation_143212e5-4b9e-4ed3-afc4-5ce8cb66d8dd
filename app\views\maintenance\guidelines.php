<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800">Maintenance Guidelines</h1>
        <div class="flex space-x-4">
            <a href="<?php echo URLROOT; ?>/maintenance" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/monitoring" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-chart-line mr-2"></i> Monitoring
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/editGuideline" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-plus mr-2"></i> Add Guideline
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Guidelines Overview -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Guidelines Overview</h2>
        <p class="text-gray-600 mb-4">
            Maintenance guidelines define the recommended maintenance procedures for different types of equipment.
            Each guideline includes a checklist of steps to follow when performing maintenance.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <div class="flex items-center mb-2">
                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                        <i class="fas fa-clipboard-list text-blue-500"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800">Total Guidelines</h3>
                </div>
                <p class="text-3xl font-bold text-blue-600"><?php echo count($data['guidelines']); ?></p>
            </div>

            <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                <div class="flex items-center mb-2">
                    <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                        <i class="fas fa-laptop text-green-500"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800">Equipment Types</h3>
                </div>
                <?php
                    $equipmentTypes = [];
                    foreach($data['guidelines'] as $guideline) {
                        if(!in_array($guideline->equipment_type, $equipmentTypes)) {
                            $equipmentTypes[] = $guideline->equipment_type;
                        }
                    }
                ?>
                <p class="text-3xl font-bold text-green-600"><?php echo count($equipmentTypes); ?></p>
            </div>

            <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                <div class="flex items-center mb-2">
                    <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                        <i class="fas fa-exclamation-triangle text-purple-500"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800">Critical Guidelines</h3>
                </div>
                <?php
                    $criticalCount = 0;
                    foreach($data['guidelines'] as $guideline) {
                        if($guideline->importance == 'critical') {
                            $criticalCount++;
                        }
                    }
                ?>
                <p class="text-3xl font-bold text-purple-600"><?php echo $criticalCount; ?></p>
            </div>
        </div>
    </div>

    <!-- Guidelines List -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Maintenance Guidelines</h2>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($data['guidelines']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipment Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Frequency</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Importance</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['guidelines'] as $guideline) : ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $guideline->name; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500"><?php echo $guideline->equipment_type; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        <?php
                                            if($guideline->frequency_days < 30) {
                                                echo $guideline->frequency_days . ' days';
                                            } else if($guideline->frequency_days < 365) {
                                                echo floor($guideline->frequency_days / 30) . ' months';
                                            } else {
                                                echo floor($guideline->frequency_days / 365) . ' years';
                                            }
                                        ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $importanceClass = 'bg-blue-100 text-blue-800';
                                        if($guideline->importance == 'critical') {
                                            $importanceClass = 'bg-red-100 text-red-800';
                                        } else if($guideline->importance == 'high') {
                                            $importanceClass = 'bg-orange-100 text-orange-800';
                                        } else if($guideline->importance == 'low') {
                                            $importanceClass = 'bg-green-100 text-green-800';
                                        }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $importanceClass; ?>">
                                        <?php echo ucfirst($guideline->importance); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo URLROOT; ?>/maintenance/guideline/<?php echo $guideline->id; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="<?php echo URLROOT; ?>/maintenance/guidelineImplementations/<?php echo $guideline->id; ?>" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                        <i class="fas fa-history"></i> History
                                    </a>
                                    <a href="<?php echo URLROOT; ?>/maintenance/editGuideline/<?php echo $guideline->id; ?>" class="text-green-600 hover:text-green-900 mr-3">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <button type="button" class="text-red-600 hover:text-red-900 border-0 bg-transparent cursor-pointer delete-guideline-btn"
                                        data-id="<?php echo $guideline->id; ?>"
                                        data-name="<?php echo htmlspecialchars($guideline->name); ?>">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No maintenance guidelines available. <a href="<?php echo URLROOT; ?>/maintenance/editGuideline" class="text-blue-600 hover:underline">Add one now</a>.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all delete buttons
    const deleteButtons = document.querySelectorAll('.delete-guideline-btn');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const guidelineId = this.getAttribute('data-id');
            const guidelineName = this.getAttribute('data-name');

            Swal.fire({
                title: 'Delete Guideline?',
                html: `Are you sure you want to delete the guideline <strong>${guidelineName}</strong>?<br><br>This action cannot be undone.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Create and submit a form programmatically
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `${URLROOT}/maintenance/deleteGuideline/${guidelineId}`;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
