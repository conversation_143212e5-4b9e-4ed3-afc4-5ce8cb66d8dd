<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2"><?php echo $data['asset']->computer_host_name; ?> Maintenance History</h1>
            <p class="text-gray-600">
                <?php echo $data['asset']->equipment_type; ?> |
                SN: <?php echo $data['asset']->serial_number; ?>
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/allHistory" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-history mr-2"></i> All History
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/add/<?php echo $data['asset']->id; ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-plus mr-2"></i> Add Maintenance Record
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Asset Health Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">Health Score</h2>
                <?php
                    $healthScore = $data['health_metrics']['health_score'];
                    $bgColor = 'bg-red-100';
                    $textColor = 'text-red-500';

                    if($healthScore >= 90) {
                        $bgColor = 'bg-green-100';
                        $textColor = 'text-green-500';
                    } elseif($healthScore >= 70) {
                        $bgColor = 'bg-yellow-100';
                        $textColor = 'text-yellow-500';
                    } elseif($healthScore >= 50) {
                        $bgColor = 'bg-orange-100';
                        $textColor = 'text-orange-500';
                    }
                ?>
                <div class="w-10 h-10 rounded-full <?php echo $bgColor; ?> flex items-center justify-center">
                    <i class="fas fa-heartbeat <?php echo $textColor; ?>"></i>
                </div>
            </div>
            <p class="text-3xl font-bold <?php echo $textColor; ?>"><?php echo round($healthScore, 1); ?>%</p>
            <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                <div class="<?php echo str_replace('text', 'bg', $textColor); ?> h-2.5 rounded-full" style="width: <?php echo $healthScore; ?>%"></div>
            </div>
            <p class="text-gray-600 mt-2">Overall asset health assessment</p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">Estimated Life</h2>
                <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                    <i class="fas fa-calendar-alt text-blue-500"></i>
                </div>
            </div>
            <?php
                $remainingDays = $data['health_metrics']['estimated_remaining_life'];
                $remainingText = '';

                if($remainingDays > 365) {
                    $years = floor($remainingDays / 365);
                    $months = floor(($remainingDays % 365) / 30);
                    $remainingText = $years . ' year' . ($years > 1 ? 's' : '') . ', ' . $months . ' month' . ($months > 1 ? 's' : '');
                } elseif($remainingDays > 30) {
                    $months = floor($remainingDays / 30);
                    $days = $remainingDays % 30;
                    $remainingText = $months . ' month' . ($months > 1 ? 's' : '') . ', ' . $days . ' day' . ($days > 1 ? 's' : '');
                } else {
                    $remainingText = $remainingDays . ' day' . ($remainingDays > 1 ? 's' : '');
                }
            ?>
            <p class="text-3xl font-bold text-blue-500"><?php echo $remainingText; ?></p>
            <p class="text-gray-600 mt-2">Estimated remaining useful life</p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">Failure Probability</h2>
                <?php
                    $failureProb = $data['health_metrics']['failure_probability'];
                    $fpBgColor = 'bg-green-100';
                    $fpTextColor = 'text-green-500';

                    if($failureProb >= 60) {
                        $fpBgColor = 'bg-red-100';
                        $fpTextColor = 'text-red-500';
                    } elseif($failureProb >= 30) {
                        $fpBgColor = 'bg-yellow-100';
                        $fpTextColor = 'text-yellow-500';
                    }
                ?>
                <div class="w-10 h-10 rounded-full <?php echo $fpBgColor; ?> flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle <?php echo $fpTextColor; ?>"></i>
                </div>
            </div>
            <p class="text-3xl font-bold <?php echo $fpTextColor; ?>"><?php echo round($failureProb, 1); ?>%</p>
            <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                <div class="<?php echo str_replace('text', 'bg', $fpTextColor); ?> h-2.5 rounded-full" style="width: <?php echo $failureProb; ?>%"></div>
            </div>
            <p class="text-gray-600 mt-2">Probability of failure in next 90 days</p>
        </div>
    </div>

    <!-- Maintenance History -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Maintenance History</h2>
            <a href="<?php echo URLROOT; ?>/maintenance/add/<?php echo $data['asset']->id; ?>" class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-plus mr-1"></i> Add Record
            </a>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($data['maintenance_history']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performed By</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Scheduled</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['maintenance_history'] as $record) : ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo date('M j, Y', strtotime($record->performed_date)); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $typeClass = 'bg-blue-100 text-blue-800';
                                        switch($record->maintenance_type) {
                                            case 'preventive':
                                                $typeClass = 'bg-green-100 text-green-800';
                                                break;
                                            case 'corrective':
                                                $typeClass = 'bg-red-100 text-red-800';
                                                break;
                                            case 'upgrade':
                                                $typeClass = 'bg-purple-100 text-purple-800';
                                                break;
                                        }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $typeClass; ?>">
                                        <?php echo ucfirst($record->maintenance_type); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900"><?php echo $record->description; ?></div>

                                    <div class="mt-2">
                                        <?php if(!empty($data['implemented_guidelines'][$record->id])) : ?>
                                            <span class="text-xs font-medium text-gray-500">Guidelines Implemented:</span>
                                            <div class="flex flex-wrap gap-1 mt-1">
                                                <?php foreach($data['implemented_guidelines'][$record->id] as $guideline) : ?>
                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                                        <?php echo $guideline->name; ?>
                                                    </span>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>

                                        <div class="mt-2">
                                            <a href="<?php echo URLROOT; ?>/maintenance/viewRecord/<?php echo $record->id; ?>" class="text-xs text-blue-600 hover:text-blue-800 font-medium">
                                                <i class="fas fa-eye mr-1"></i> View Maintenance Details
                                            </a>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo $record->performed_by_name ?? 'N/A'; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">$<?php echo number_format($record->cost, 2); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $statusClass = 'bg-gray-100 text-gray-800';
                                        switch($record->status) {
                                            case 'completed':
                                                $statusClass = 'bg-green-100 text-green-800';
                                                break;
                                            case 'scheduled':
                                                $statusClass = 'bg-blue-100 text-blue-800';
                                                break;
                                            case 'cancelled':
                                                $statusClass = 'bg-red-100 text-red-800';
                                                break;
                                            case 'overdue':
                                                $statusClass = 'bg-yellow-100 text-yellow-800';
                                                break;
                                        }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $statusClass; ?>">
                                        <?php echo ucfirst($record->status); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <?php echo $record->next_scheduled_date ? date('M j, Y', strtotime($record->next_scheduled_date)) : 'N/A'; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No maintenance records found for this asset.</p>
                    <a href="<?php echo URLROOT; ?>/maintenance/add/<?php echo $data['asset']->id; ?>" class="inline-block mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                        <i class="fas fa-plus mr-2"></i> Add First Maintenance Record
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Maintenance Recommendations -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Maintenance Recommendations</h2>

        <?php if($data['health_metrics']['health_score'] < 70) : ?>
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            This asset's health score is below recommended levels. Consider scheduling preventive maintenance soon.
                        </p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if($data['health_metrics']['failure_probability'] > 30) : ?>
            <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-red-700">
                            Elevated failure probability detected. Recommend thorough inspection and preventive maintenance.
                        </p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if(!empty($data['applicable_guidelines'])) : ?>
            <div class="mt-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Maintenance Guidelines Compliance</h3>
                <div class="space-y-4 mt-3">
                    <?php foreach($data['applicable_guidelines'] as $guideline) : ?>
                        <?php
                            // Find compliance status for this guideline
                            $complianceRecord = null;
                            foreach($data['compliance_status'] as $status) {
                                if($status->guideline_id == $guideline->id) {
                                    $complianceRecord = $status;
                                    break;
                                }
                            }

                            // Determine status class
                            $statusClass = 'bg-gray-100';
                            $statusText = 'Not Implemented';
                            $borderClass = 'border-gray-200';

                            if($complianceRecord) {
                                switch($complianceRecord->compliance_status) {
                                    case 'compliant':
                                        $statusClass = 'bg-green-100 text-green-800';
                                        $statusText = 'Compliant';
                                        $borderClass = 'border-green-200';
                                        break;
                                    case 'due_soon':
                                        $statusClass = 'bg-yellow-100 text-yellow-800';
                                        $statusText = 'Due Soon';
                                        $borderClass = 'border-yellow-200';
                                        break;
                                    case 'overdue':
                                        $statusClass = 'bg-red-100 text-red-800';
                                        $statusText = 'Overdue';
                                        $borderClass = 'border-red-200';
                                        break;
                                }
                            }
                        ?>
                        <div class="border rounded-lg p-3 <?php echo $borderClass; ?>">
                            <div class="flex items-start justify-between">
                                <div>
                                    <h4 class="font-semibold"><?php echo $guideline->name; ?></h4>
                                    <p class="text-sm mt-1 text-gray-600"><?php echo $guideline->description; ?></p>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $statusClass; ?>">
                                    <?php echo $statusText; ?>
                                </span>
                            </div>

                            <?php if($complianceRecord && $complianceRecord->last_performed_date) : ?>
                                <div class="mt-2 text-sm text-gray-600">
                                    <span class="font-medium">Last performed:</span> <?php echo date('M j, Y', strtotime($complianceRecord->last_performed_date)); ?> |
                                    <span class="font-medium">Next due:</span> <?php echo date('M j, Y', strtotime($complianceRecord->next_due_date)); ?>
                                </div>
                            <?php else : ?>
                                <div class="mt-2 text-sm text-gray-600">
                                    <span class="font-medium">Frequency:</span> Every <?php echo $guideline->frequency_days; ?> days
                                </div>
                            <?php endif; ?>

                            <?php if($complianceRecord && $complianceRecord->compliance_status == 'overdue') : ?>
                                <div class="mt-2">
                                    <a href="<?php echo URLROOT; ?>/maintenance/add/<?php echo $data['asset']->id; ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        <i class="fas fa-plus mr-1"></i> Schedule Maintenance
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php else : ?>
            <div class="mt-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Recommended Maintenance Schedule</h3>
                <ul class="list-disc pl-5 space-y-2 text-gray-600">
                    <li>Perform preventive maintenance every 90 days</li>
                    <li>Check for software updates monthly</li>
                    <li>Inspect hardware components quarterly</li>
                    <li>Run diagnostics tests monthly</li>
                </ul>

                <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p class="text-yellow-700">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        No maintenance guidelines found for this asset type. Please create guidelines in the
                        <a href="<?php echo URLROOT; ?>/maintenance/guidelines" class="text-blue-600 hover:underline">Maintenance Guidelines</a> section.
                    </p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
