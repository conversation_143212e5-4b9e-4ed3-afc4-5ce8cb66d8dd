<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800">Predictive Maintenance</h1>
        <div class="flex flex-wrap space-x-2 md:space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/assets" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-md mb-2">
                <i class="fas fa-desktop mr-1"></i> Assets
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/guidelines" class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md mb-2">
                <i class="fas fa-clipboard-list mr-1"></i> Guidelines
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/monitoring" class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded-md mb-2">
                <i class="fas fa-chart-line mr-1"></i> Monitoring
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/allHistory" class="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-2 rounded-md mb-2">
                <i class="fas fa-history mr-1"></i> All History
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/add" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md mb-2">
                <i class="fas fa-plus mr-1"></i> Add Record
            </a>
            <?php if (isAdmin()): ?>
            <div class="dropdown relative inline-block">
                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded-md mb-2 dropdown-toggle">
                    <i class="fas fa-tools mr-1"></i> Tools <i class="fas fa-caret-down ml-1"></i>
                </button>
                <div class="dropdown-menu hidden absolute right-0 mt-2 bg-white rounded-md shadow-lg z-10 w-48">
                    <a href="<?php echo URLROOT; ?>/maintenance/dataIntegrityCheck" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">
                        <i class="fas fa-database mr-2"></i> Data Integrity
                    </a>
                    <a href="<?php echo URLROOT; ?>/maintenance/recreateMissingRecords" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">
                        <i class="fas fa-sync mr-2"></i> Recreate Records
                    </a>
                    <a href="<?php echo URLROOT; ?>/maintenance/recalculateHealthMetrics" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">
                        <i class="fas fa-heartbeat mr-2"></i> Recalculate Health Metrics
                    </a>
                    <a href="<?php echo URLROOT; ?>/error_logs" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">
                        <i class="fas fa-exclamation-triangle mr-2"></i> Error Logs
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <style>
    .dropdown:hover .dropdown-menu {
        display: block;
    }
    </style>

    <?php flash('maintenance_message'); ?>

    <!-- Health Score Overview -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">Critical Assets</h2>
                <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-500"></i>
                </div>
            </div>
            <p class="text-3xl font-bold text-red-500">
                <?php
                    $criticalCount = 0;
                    foreach($data['assets_health'] as $asset) {
                        if($asset->health_score < 40) {
                            $criticalCount++;
                        }
                    }
                    echo $criticalCount;
                ?>
            </p>
            <p class="text-gray-600">Assets with health score below 40%</p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">Maintenance Due</h2>
                <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center">
                    <i class="fas fa-calendar-alt text-yellow-500"></i>
                </div>
            </div>
            <p class="text-3xl font-bold text-yellow-500">
                <?php echo count($data['assets_due']); ?>
            </p>
            <p class="text-gray-600">Assets due for maintenance in next 30 days</p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">Average Health</h2>
                <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                    <i class="fas fa-heartbeat text-blue-500"></i>
                </div>
            </div>
            <p class="text-3xl font-bold text-blue-500">
                <?php
                    $totalScore = 0;
                    $validCount = 0;
                    foreach($data['assets_health'] as $asset) {
                        // Only include assets with valid health scores (not NULL and greater than 0)
                        if (isset($asset->health_score) && $asset->health_score > 0) {
                            $totalScore += $asset->health_score;
                            $validCount++;
                        }
                    }

                    // Only calculate average if we have valid scores
                    if ($validCount > 0) {
                        $avgScore = round($totalScore / $validCount, 1);
                        echo $avgScore . '%';

                        // Set appropriate color based on average score
                        $textColorClass = 'text-blue-500';
                        if ($avgScore < 40) {
                            $textColorClass = 'text-red-500';
                        } elseif ($avgScore < 70) {
                            $textColorClass = 'text-yellow-500';
                        } elseif ($avgScore >= 90) {
                            $textColorClass = 'text-green-500';
                        }

                        // Update the text color dynamically
                        echo "<script>document.currentScript.parentElement.className = 'text-3xl font-bold $textColorClass';</script>";
                    } else {
                        echo 'N/A';
                    }
                ?>
            </p>
            <p class="text-gray-600">Average health score across all assets</p>
        </div>
    </div>

    <!-- Assets Due for Maintenance -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Assets Due for Maintenance</h2>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($data['assets_due']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Maintenance Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days Remaining</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['assets_due'] as $asset) : ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $asset->computer_host_name; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500"><?php echo $asset->equipment_type; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500"><?php echo $asset->serial_number; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500"><?php echo ucfirst($asset->maintenance_type); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500"><?php echo date('M j, Y', strtotime($asset->next_scheduled_date)); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $daysRemaining = $asset->days_remaining;
                                        $textColor = 'text-green-500';
                                        if($daysRemaining <= 7) {
                                            $textColor = 'text-red-500';
                                        } else if($daysRemaining <= 14) {
                                            $textColor = 'text-yellow-500';
                                        }
                                    ?>
                                    <div class="text-sm font-medium <?php echo $textColor; ?>"><?php echo $daysRemaining; ?> days</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo URLROOT; ?>/maintenance/history/<?php echo $asset->id; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-history"></i> History
                                    </a>
                                    <a href="<?php echo URLROOT; ?>/maintenance/add/<?php echo $asset->id; ?>" class="text-green-600 hover:text-green-900 mr-3">
                                        <i class="fas fa-plus"></i> Add Record
                                    </a>
                                    <button
                                        class="text-purple-600 hover:text-purple-900 border-0 bg-transparent cursor-pointer mark-complete-btn"
                                        data-asset-id="<?php echo $asset->id; ?>"
                                        data-asset-name="<?php echo $asset->computer_host_name; ?>"
                                        data-maintenance-type="<?php echo $asset->maintenance_type; ?>">
                                        <i class="fas fa-check-circle"></i> Mark Complete
                                    </button>
                                    <!-- Direct link option (always visible) -->
                                    <a href="<?php echo URLROOT; ?>/maintenance/complete/<?php echo $asset->id; ?>/<?php echo urlencode($asset->maintenance_type); ?>"
                                       class="text-purple-600 hover:text-purple-900 ml-3"
                                       title="Alternative method to mark as completed">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No assets due for maintenance in the next 30 days.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Asset Health Metrics -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex flex-col md:flex-row justify-between items-start md:items-center">
            <h2 class="text-xl font-bold text-gray-800 mb-4 md:mb-0">Asset Health Metrics</h2>

            <!-- Search and Filter Form -->
            <form action="<?php echo URLROOT; ?>/maintenance" method="GET" class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2 w-full md:w-auto">
                <div class="flex-grow">
                    <input type="text" name="search" value="<?php echo isset($data['search']) ? htmlspecialchars($data['search']) : ''; ?>"
                           placeholder="Search assets..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="flex space-x-2">
                    <select name="order_by" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="computer_host_name" <?php echo $data['order_by'] == 'computer_host_name' ? 'selected' : ''; ?>>Asset Name</option>
                        <option value="equipment_type" <?php echo $data['order_by'] == 'equipment_type' ? 'selected' : ''; ?>>Type</option>
                        <option value="health_score" <?php echo $data['order_by'] == 'health_score' ? 'selected' : ''; ?>>Health Score</option>
                    </select>

                    <select name="order_dir" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="ASC" <?php echo $data['order_dir'] == 'ASC' ? 'selected' : ''; ?>>Ascending</option>
                        <option value="DESC" <?php echo $data['order_dir'] == 'DESC' ? 'selected' : ''; ?>>Descending</option>
                    </select>

                    <select name="per_page" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="10" <?php echo $data['records_per_page'] == 10 ? 'selected' : ''; ?>>10 per page</option>
                        <option value="25" <?php echo $data['records_per_page'] == 25 ? 'selected' : ''; ?>>25 per page</option>
                        <option value="50" <?php echo $data['records_per_page'] == 50 ? 'selected' : ''; ?>>50 per page</option>
                        <option value="100" <?php echo $data['records_per_page'] == 100 ? 'selected' : ''; ?>>100 per page</option>
                    </select>

                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-search mr-1"></i> Search
                    </button>
                </div>
            </form>
        </div>

        <div class="overflow-x-auto">
            <?php if(count($data['assets_health']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="<?php echo URLROOT; ?>/maintenance?search=<?php echo urlencode($data['search']); ?>&order_by=computer_host_name&order_dir=<?php echo $data['order_by'] == 'computer_host_name' && $data['order_dir'] == 'ASC' ? 'DESC' : 'ASC'; ?>&per_page=<?php echo $data['records_per_page']; ?>"
                                   class="flex items-center hover:text-gray-700">
                                    Asset
                                    <?php if($data['order_by'] == 'computer_host_name'): ?>
                                        <i class="fas fa-sort-<?php echo $data['order_dir'] == 'ASC' ? 'up' : 'down'; ?> ml-1"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="<?php echo URLROOT; ?>/maintenance?search=<?php echo urlencode($data['search']); ?>&order_by=equipment_type&order_dir=<?php echo $data['order_by'] == 'equipment_type' && $data['order_dir'] == 'ASC' ? 'DESC' : 'ASC'; ?>&per_page=<?php echo $data['records_per_page']; ?>"
                                   class="flex items-center hover:text-gray-700">
                                    Type
                                    <?php if($data['order_by'] == 'equipment_type'): ?>
                                        <i class="fas fa-sort-<?php echo $data['order_dir'] == 'ASC' ? 'up' : 'down'; ?> ml-1"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="<?php echo URLROOT; ?>/maintenance?search=<?php echo urlencode($data['search']); ?>&order_by=health_score&order_dir=<?php echo $data['order_by'] == 'health_score' && $data['order_dir'] == 'ASC' ? 'DESC' : 'ASC'; ?>&per_page=<?php echo $data['records_per_page']; ?>"
                                   class="flex items-center hover:text-gray-700">
                                    Overall Health
                                    <?php if($data['order_by'] == 'health_score'): ?>
                                        <i class="fas fa-sort-<?php echo $data['order_dir'] == 'ASC' ? 'up' : 'down'; ?> ml-1"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['assets_health'] as $asset) : ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $asset->computer_host_name; ?></div>
                                    <div class="text-xs text-gray-500">SN: <?php echo $asset->serial_number; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500"><?php echo $asset->equipment_type; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $healthScore = $asset->health_score ?? 0;
                                        $bgColor = 'bg-green-500';
                                        $statusText = 'Good';
                                        if($healthScore < 40) {
                                            $bgColor = 'bg-red-500';
                                            $statusText = 'Critical';
                                        } else if($healthScore < 70) {
                                            $bgColor = 'bg-yellow-500';
                                            $statusText = 'Warning';
                                        }
                                    ?>
                                    <div class="flex items-center">
                                        <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                                            <div class="<?php echo $bgColor; ?> h-2.5 rounded-full" style="width: <?php echo $healthScore; ?>%"></div>
                                        </div>
                                        <span class="text-sm font-medium"><?php echo round($healthScore, 1); ?>%</span>
                                        <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?php echo $healthScore < 40 ? 'bg-red-100 text-red-800' : ($healthScore < 70 ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'); ?>">
                                            <?php echo $statusText; ?>
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo URLROOT; ?>/maintenance/history/<?php echo $asset->id; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-history"></i> History
                                    </a>
                                    <a href="<?php echo URLROOT; ?>/maintenance/add/<?php echo $asset->id; ?>" class="text-green-600 hover:text-green-900">
                                        <i class="fas fa-plus"></i> Add Record
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <!-- Pagination -->
                <?php if($data['total_pages'] > 1) : ?>
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <div class="text-sm text-gray-700 mb-4 md:mb-0">
                            Showing <span class="font-medium"><?php echo (($data['current_page'] - 1) * $data['records_per_page']) + 1; ?></span>
                            to <span class="font-medium"><?php echo min($data['current_page'] * $data['records_per_page'], $data['total_count']); ?></span>
                            of <span class="font-medium"><?php echo $data['total_count']; ?></span> assets
                        </div>
                        <div class="flex flex-wrap justify-center space-x-1">
                            <?php if($data['current_page'] > 1) : ?>
                                <a href="<?php echo URLROOT; ?>/maintenance/index/1?search=<?php echo urlencode($data['search']); ?>&order_by=<?php echo $data['order_by']; ?>&order_dir=<?php echo $data['order_dir']; ?>&per_page=<?php echo $data['records_per_page']; ?>"
                                   class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                                <a href="<?php echo URLROOT; ?>/maintenance/index/<?php echo $data['current_page'] - 1; ?>?search=<?php echo urlencode($data['search']); ?>&order_by=<?php echo $data['order_by']; ?>&order_dir=<?php echo $data['order_dir']; ?>&per_page=<?php echo $data['records_per_page']; ?>"
                                   class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            <?php endif; ?>

                            <?php
                            // Calculate range of page numbers to display
                            $startPage = max(1, $data['current_page'] - 2);
                            $endPage = min($data['total_pages'], $data['current_page'] + 2);

                            // Always show first page
                            if ($startPage > 1) {
                                echo '<a href="' . URLROOT . '/maintenance/index/1?search=' . urlencode($data['search']) . '&order_by=' . $data['order_by'] . '&order_dir=' . $data['order_dir'] . '&per_page=' . $data['records_per_page'] . '"
                                      class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">1</a>';

                                if ($startPage > 2) {
                                    echo '<span class="px-3 py-1 text-gray-500">...</span>';
                                }
                            }

                            // Page links
                            for ($i = $startPage; $i <= $endPage; $i++) {
                                $activeClass = $i == $data['current_page'] ? 'bg-blue-50 border-blue-500 text-blue-600' : 'bg-white text-gray-700 hover:bg-gray-50';
                                echo '<a href="' . URLROOT . '/maintenance/index/' . $i . '?search=' . urlencode($data['search']) . '&order_by=' . $data['order_by'] . '&order_dir=' . $data['order_dir'] . '&per_page=' . $data['records_per_page'] . '"
                                      class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium ' . $activeClass . '">' . $i . '</a>';
                            }

                            // Always show last page
                            if ($endPage < $data['total_pages']) {
                                if ($endPage < $data['total_pages'] - 1) {
                                    echo '<span class="px-3 py-1 text-gray-500">...</span>';
                                }

                                echo '<a href="' . URLROOT . '/maintenance/index/' . $data['total_pages'] . '?search=' . urlencode($data['search']) . '&order_by=' . $data['order_by'] . '&order_dir=' . $data['order_dir'] . '&per_page=' . $data['records_per_page'] . '"
                                      class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">' . $data['total_pages'] . '</a>';
                            }
                            ?>

                            <?php if($data['current_page'] < $data['total_pages']) : ?>
                                <a href="<?php echo URLROOT; ?>/maintenance/index/<?php echo $data['current_page'] + 1; ?>?search=<?php echo urlencode($data['search']); ?>&order_by=<?php echo $data['order_by']; ?>&order_dir=<?php echo $data['order_dir']; ?>&per_page=<?php echo $data['records_per_page']; ?>"
                                   class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                                <a href="<?php echo URLROOT; ?>/maintenance/index/<?php echo $data['total_pages']; ?>?search=<?php echo urlencode($data['search']); ?>&order_by=<?php echo $data['order_by']; ?>&order_dir=<?php echo $data['order_dir']; ?>&per_page=<?php echo $data['records_per_page']; ?>"
                                   class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            <?php else : ?>
                <div class="p-6 text-center">
                    <?php if(!empty($data['search'])) : ?>
                        <p class="text-gray-500">No assets found matching your search criteria.</p>
                        <a href="<?php echo URLROOT; ?>/maintenance" class="mt-2 inline-block text-blue-600 hover:underline">Clear search</a>
                    <?php else : ?>
                        <p class="text-gray-500">No asset health metrics available.</p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>


    <!-- Maintenance Instructions -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">How to Mark Maintenance as Completed</h2>

        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-blue-700">
                        You can now quickly mark maintenance tasks as completed directly from the dashboard using the "Mark Complete" button. This will automatically create a maintenance record and remove the task from the dashboard.
                    </p>
                </div>
            </div>
        </div>

        <div class="space-y-6">
            <div class="flex">
                <div class="flex-shrink-0 h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
                    1
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800">Quick Completion</h3>
                    <p class="text-gray-600 mt-1">Click the "Mark Complete" button next to the asset in the "Assets Due for Maintenance" table to instantly mark it as completed.</p>
                </div>
            </div>

            <div class="flex">
                <div class="flex-shrink-0 h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
                    2
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800">Manual Entry (Alternative)</h3>
                    <p class="text-gray-600 mt-1">For more detailed maintenance records, click "Add Record" and fill in the maintenance details manually.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SweetAlert2 Script for Mark Complete Button -->
<script>
// Define URLROOT for JavaScript
const URLROOT = '<?php echo URLROOT; ?>';

document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all Mark Complete buttons
    const markCompleteButtons = document.querySelectorAll('.mark-complete-btn');

    markCompleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const assetId = this.getAttribute('data-asset-id');
            const assetName = this.getAttribute('data-asset-name');
            const maintenanceType = this.getAttribute('data-maintenance-type');

            Swal.fire({
                title: 'Mark Maintenance as Completed?',
                html: `Are you sure you want to mark the <strong>${maintenanceType}</strong> maintenance for <strong>${assetName}</strong> as completed?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#10b981',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, mark as completed',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Processing...',
                        html: 'Creating maintenance record...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Log for debugging
                    console.log(`Redirecting to: ${URLROOT}/maintenance/complete/${assetId}/${encodeURIComponent(maintenanceType)}`);

                    try {
                        // Redirect to the complete action with URL-encoded maintenance type
                        window.location.href = `${URLROOT}/maintenance/complete/${assetId}/${encodeURIComponent(maintenanceType)}`;
                    } catch (error) {
                        console.error('Error during redirect:', error);
                        Swal.fire({
                            title: 'Error',
                            text: 'An error occurred while processing your request. Please try again.',
                            icon: 'error'
                        });
                    }
                }
            });
        });
    });
});
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
