<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Manage Checklist Items</h1>
            <p class="text-gray-600">
                Managing checklist for <span class="font-semibold"><?php echo $data['guideline']->name; ?></span>
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance/guideline/<?php echo $data['guideline']->id; ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Guideline
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/editChecklistItem/<?php echo $data['guideline']->id; ?>" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-plus mr-2"></i> Add Checklist Item
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Checklist Items Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Checklist Items</h2>
        </div>
        <div class="p-6">
            <?php if(isset($data['checklist_items']) && !empty($data['checklist_items'])) : ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Step #
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Description
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Required
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php
                            // Create a temporary array to track displayed item IDs
                            $displayedItemIds = [];

                            // Loop through items and only display each unique ID once
                            foreach($data['checklist_items'] as $item) :
                                // Skip if we've already displayed this item ID
                                if (in_array($item->id, $displayedItemIds)) {
                                    continue;
                                }

                                // Add this item ID to our tracking array
                                $displayedItemIds[] = $item->id;
                            ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo $item->step_number; ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500">
                                        <?php echo $item->description; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $item->is_required ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $item->is_required ? 'Required' : 'Optional'; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="<?php echo URLROOT; ?>/maintenance/editChecklistItem/<?php echo $data['guideline']->id; ?>/<?php echo $item->id; ?>" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <form class="inline delete-checklist-form" action="<?php echo URLROOT; ?>/maintenance/deleteChecklistItem/<?php echo $data['guideline']->id; ?>/<?php echo $item->id; ?>" method="POST">
                                            <button type="button" class="delete-checklist-btn text-red-600 hover:text-red-900 border-0 bg-transparent cursor-pointer" data-item-description="<?php echo htmlspecialchars($item->description); ?>">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else : ?>
                <div class="text-center py-8">
                    <p class="text-gray-500 mb-4">No checklist items available for this guideline.</p>
                    <a href="<?php echo URLROOT; ?>/maintenance/editChecklistItem/<?php echo $data['guideline']->id; ?>" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                        <i class="fas fa-plus mr-2"></i> Add First Checklist Item
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Guideline Information -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mt-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Guideline Information</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Basic Information</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Name:</span>
                            <p class="mt-1"><?php echo $data['guideline']->name; ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Equipment Type:</span>
                            <p class="mt-1"><?php echo $data['guideline']->equipment_type; ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Frequency:</span>
                            <p class="mt-1">Every <?php echo $data['guideline']->frequency_days; ?> days</p>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Description</h3>
                    <p class="text-gray-700"><?php echo $data['guideline']->description; ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // SweetAlert for delete checklist item
        const deleteButtons = document.querySelectorAll('.delete-checklist-btn');
        if (deleteButtons.length > 0) {
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();

                    const form = this.closest('.delete-checklist-form');
                    const itemDescription = this.getAttribute('data-item-description');

                    Swal.fire({
                        title: 'Delete Checklist Item?',
                        html: `Are you sure you want to delete the checklist item:<br><strong>${itemDescription}</strong>?`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#ef4444',
                        cancelButtonColor: '#6b7280',
                        confirmButtonText: 'Yes, delete it!',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Show success message
                            Toast.success('Checklist item deleted successfully');

                            // Submit the form after a short delay to show the success message
                            setTimeout(() => {
                                form.submit();
                            }, 1000);
                        }
                    });
                });
            });
        }
    });
</script>
