<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800">Maintenance Monitoring</h1>
        <div class="flex space-x-4">
            <a href="<?php echo URLROOT; ?>/maintenance" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/guidelines" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-clipboard-list mr-2"></i> Guidelines
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Compliance Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">Compliant</h2>
                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                    <i class="fas fa-check text-green-500"></i>
                </div>
            </div>
            <p class="text-3xl font-bold text-green-500">
                <?php echo $data['compliant_count']; ?>
            </p>
            <p class="text-gray-600">Endpoints meeting all guidelines</p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">Due Soon</h2>
                <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-500"></i>
                </div>
            </div>
            <p class="text-3xl font-bold text-yellow-500">
                <?php echo $data['due_soon_count']; ?>
            </p>
            <p class="text-gray-600">Maintenance due in next 30 days</p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">Overdue</h2>
                <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-500"></i>
                </div>
            </div>
            <p class="text-3xl font-bold text-red-500">
                <?php echo $data['overdue_count']; ?>
            </p>
            <p class="text-gray-600">Maintenance past due date</p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">Not Applicable</h2>
                <div class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fas fa-minus-circle text-gray-500"></i>
                </div>
            </div>
            <p class="text-3xl font-bold text-gray-500">
                <?php echo $data['not_applicable_count']; ?>
            </p>
            <p class="text-gray-600">Guidelines not applicable to endpoint</p>
        </div>
    </div>

    <!-- Compliance Progress -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Compliance Progress</h2>

        <?php
            // Calculate total compliance records
            $totalCompliance = $data['compliant_count'] + $data['due_soon_count'] + $data['overdue_count'] + $data['not_applicable_count'];

            // Calculate percentages, ensuring they add up to 100%
            if ($totalCompliance > 0) {
                // First calculate raw percentages
                $compliantPercent = ($data['compliant_count'] / $totalCompliance) * 100;
                $dueSoonPercent = ($data['due_soon_count'] / $totalCompliance) * 100;
                $overduePercent = ($data['overdue_count'] / $totalCompliance) * 100;
                $notApplicablePercent = ($data['not_applicable_count'] / $totalCompliance) * 100;

                // Calculate total of raw percentages
                $totalPercent = $compliantPercent + $dueSoonPercent + $overduePercent + $notApplicablePercent;

                // Adjust if there's any rounding error
                if ($totalPercent != 100) {
                    $adjustment = (100 - $totalPercent) / 4; // Distribute evenly
                    $compliantPercent += $adjustment;
                    $dueSoonPercent += $adjustment;
                    $overduePercent += $adjustment;
                    $notApplicablePercent += $adjustment;
                }

                // Round to integers for display
                $compliantPercent = round($compliantPercent);
                $dueSoonPercent = round($dueSoonPercent);
                $overduePercent = round($overduePercent);
                $notApplicablePercent = round($notApplicablePercent);

                // Final adjustment to ensure exactly 100%
                $sum = $compliantPercent + $dueSoonPercent + $overduePercent + $notApplicablePercent;
                if ($sum != 100) {
                    // Add the difference to the largest category
                    $diff = 100 - $sum;
                    $max = max($compliantPercent, $dueSoonPercent, $overduePercent, $notApplicablePercent);

                    if ($max == $compliantPercent) {
                        $compliantPercent += $diff;
                    } elseif ($max == $dueSoonPercent) {
                        $dueSoonPercent += $diff;
                    } elseif ($max == $overduePercent) {
                        $overduePercent += $diff;
                    } else {
                        $notApplicablePercent += $diff;
                    }
                }
            } else {
                // If no compliance records, set all to 0
                $compliantPercent = 0;
                $dueSoonPercent = 0;
                $overduePercent = 0;
                $notApplicablePercent = 0;
            }
        ?>

        <div class="w-full bg-gray-200 rounded-full h-4 mb-2">
            <div class="flex h-4 rounded-full overflow-hidden">
                <div class="bg-green-500 h-4" style="width: <?php echo $compliantPercent; ?>%"></div>
                <div class="bg-yellow-500 h-4" style="width: <?php echo $dueSoonPercent; ?>%"></div>
                <div class="bg-red-500 h-4" style="width: <?php echo $overduePercent; ?>%"></div>
                <div class="bg-gray-400 h-4" style="width: <?php echo $notApplicablePercent; ?>%"></div>
            </div>
        </div>

        <div class="flex flex-wrap justify-between text-sm mt-2">
            <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span>Compliant (<?php echo $compliantPercent; ?>%)</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                <span>Due Soon (<?php echo $dueSoonPercent; ?>%)</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span>Overdue (<?php echo $overduePercent; ?>%)</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-gray-400 rounded-full mr-2"></div>
                <span>Not Applicable (<?php echo $notApplicablePercent; ?>%)</span>
            </div>
        </div>
    </div>

    <!-- Overdue Maintenance -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Overdue Maintenance</h2>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($data['overdue_maintenance']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guideline</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Performed</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Importance</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['overdue_maintenance'] as $item) : ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $item->computer_host_name; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500"><?php echo $item->equipment_type; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo $item->guideline_name; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        <?php echo $item->last_performed_date ? date('M j, Y', strtotime($item->last_performed_date)) : 'Never'; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-red-600">
                                        <?php echo date('M j, Y', strtotime($item->next_due_date)); ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $importanceClass = 'bg-blue-100 text-blue-800';
                                        if($item->importance == 'critical') {
                                            $importanceClass = 'bg-red-100 text-red-800';
                                        } else if($item->importance == 'high') {
                                            $importanceClass = 'bg-orange-100 text-orange-800';
                                        } else if($item->importance == 'low') {
                                            $importanceClass = 'bg-green-100 text-green-800';
                                        }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $importanceClass; ?>">
                                        <?php echo ucfirst($item->importance); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo URLROOT; ?>/maintenance/assetCompliance/<?php echo $item->asset_id; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="<?php echo URLROOT; ?>/maintenance/add/<?php echo $item->asset_id; ?>" class="text-green-600 hover:text-green-900">
                                        <i class="fas fa-plus"></i> Add Record
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No overdue maintenance tasks.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Maintenance Due Soon -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Maintenance Due Soon</h2>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($data['maintenance_due_soon']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guideline</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Performed</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Importance</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['maintenance_due_soon'] as $item) : ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $item->computer_host_name; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500"><?php echo $item->equipment_type; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo $item->guideline_name; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        <?php echo $item->last_performed_date ? date('M j, Y', strtotime($item->last_performed_date)) : 'Never'; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-yellow-600">
                                        <?php echo date('M j, Y', strtotime($item->next_due_date)); ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $importanceClass = 'bg-blue-100 text-blue-800';
                                        if($item->importance == 'critical') {
                                            $importanceClass = 'bg-red-100 text-red-800';
                                        } else if($item->importance == 'high') {
                                            $importanceClass = 'bg-orange-100 text-orange-800';
                                        } else if($item->importance == 'low') {
                                            $importanceClass = 'bg-green-100 text-green-800';
                                        }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $importanceClass; ?>">
                                        <?php echo ucfirst($item->importance); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo URLROOT; ?>/maintenance/assetCompliance/<?php echo $item->asset_id; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="<?php echo URLROOT; ?>/maintenance/add/<?php echo $item->asset_id; ?>" class="text-green-600 hover:text-green-900">
                                        <i class="fas fa-plus"></i> Add Record
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No maintenance tasks due soon.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Compliant Endpoints -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Compliant Endpoints</h2>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($data['compliant_endpoints']) > 0) : ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guideline</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Performed</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Due Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Importance</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach($data['compliant_endpoints'] as $item) : ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $item->computer_host_name; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500"><?php echo $item->equipment_type; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo $item->guideline_name; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        <?php echo $item->last_performed_date ? date('M j, Y', strtotime($item->last_performed_date)) : 'Never'; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-green-600">
                                        <?php echo date('M j, Y', strtotime($item->next_due_date)); ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $importanceClass = 'bg-blue-100 text-blue-800';
                                        if($item->importance == 'critical') {
                                            $importanceClass = 'bg-red-100 text-red-800';
                                        } else if($item->importance == 'high') {
                                            $importanceClass = 'bg-orange-100 text-orange-800';
                                        } else if($item->importance == 'low') {
                                            $importanceClass = 'bg-green-100 text-green-800';
                                        }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $importanceClass; ?>">
                                        <?php echo ucfirst($item->importance); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo URLROOT; ?>/maintenance/assetCompliance/<?php echo $item->asset_id; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="<?php echo URLROOT; ?>/maintenance/add/<?php echo $item->asset_id; ?>" class="text-green-600 hover:text-green-900">
                                        <i class="fas fa-plus"></i> Add Record
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else : ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500">No compliant endpoints found.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
