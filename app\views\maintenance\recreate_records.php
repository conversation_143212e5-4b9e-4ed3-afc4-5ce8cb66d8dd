<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Recreate Missing Maintenance Records</h1>
            <p class="text-gray-600">
                Recreate maintenance history records based on implementation records
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/dataIntegrityCheck" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-database mr-2"></i> Data Integrity Check
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Summary Card -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Missing Records Summary</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h3 class="text-lg font-semibold text-blue-800 mb-2">Total Implementations</h3>
                    <p class="text-3xl font-bold text-blue-600"><?php echo $data['total_implementations']; ?></p>
                    <p class="text-sm text-blue-700 mt-2">Total records in the maintenance_guideline_implementation table</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    <h3 class="text-lg font-semibold text-yellow-800 mb-2">Missing Records</h3>
                    <p class="text-3xl font-bold text-yellow-600"><?php echo $data['missing_count']; ?></p>
                    <p class="text-sm text-yellow-700 mt-2">Implementation records with missing maintenance history</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                    <h3 class="text-lg font-semibold text-green-800 mb-2">Valid Records</h3>
                    <p class="text-3xl font-bold text-green-600"><?php echo $data['total_implementations'] - $data['missing_count']; ?></p>
                    <p class="text-sm text-green-700 mt-2">Implementation records with valid maintenance history</p>
                </div>
            </div>

            <?php if ($data['missing_count'] > 0): ?>
            <div class="mt-6">
                <form action="<?php echo URLROOT; ?>/maintenance/recreateMissingRecords" method="POST" class="flex justify-center">
                    <input type="hidden" name="recreate_records" value="true">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md">
                        <i class="fas fa-sync mr-2"></i> Recreate <?php echo $data['missing_count']; ?> Missing Records
                    </button>
                </form>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Missing Records Table -->
    <?php if (!empty($data['missing_records'])): ?>
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Missing Maintenance Records</h2>
            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-semibold">
                <?php echo count($data['missing_records']); ?> records
            </span>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Maintenance ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guideline</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipment Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Implemented Date</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach($data['missing_records'] as $record): ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->id; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->maintenance_id; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->guideline_name; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->equipment_type; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo isset($record->implemented_date) ? date('M j, Y', strtotime($record->implemented_date)) : 'N/A'; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php elseif ($data['action_taken']): ?>
    <div class="bg-green-50 p-4 rounded-lg border border-green-200 text-center">
        <p class="text-green-700">
            <i class="fas fa-check-circle text-2xl mr-2"></i>
            All missing maintenance records have been successfully recreated.
        </p>
    </div>
    <?php else: ?>
    <div class="bg-green-50 p-4 rounded-lg border border-green-200 text-center">
        <p class="text-green-700">
            <i class="fas fa-check-circle text-2xl mr-2"></i>
            No missing maintenance records found. All implementation records have valid maintenance history!
        </p>
    </div>
    <?php endif; ?>

    <!-- Recreated Records Table -->
    <?php if (!empty($data['recreated_records'])): ?>
    <div class="mt-8 bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Recreated Records</h2>
            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-semibold">
                <?php echo count($data['recreated_records']); ?> records
            </span>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Old Maintenance ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">New Maintenance ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guideline</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach($data['recreated_records'] as $record): ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->maintenance_id; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->new_maintenance_id; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->guideline_name; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $record->asset_id; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="<?php echo URLROOT; ?>/maintenance/viewRecord/<?php echo $record->new_maintenance_id; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                <i class="fas fa-eye"></i> View Record
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
