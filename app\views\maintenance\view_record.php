<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Maintenance Record Details</h1>
            <p class="text-gray-600">
                Viewing maintenance record for <?php echo $data['asset']->computer_host_name; ?> (<?php echo $data['asset']->equipment_type; ?>)
            </p>
        </div>
        <div class="flex space-x-4 mt-4 md:mt-0">
            <a href="<?php echo URLROOT; ?>/maintenance/history/<?php echo $data['asset']->id; ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-arrow-left mr-2"></i> Back to Asset History
            </a>
            <a href="<?php echo URLROOT; ?>/maintenance/allHistory" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md">
                <i class="fas fa-history mr-2"></i> All History
            </a>
        </div>
    </div>

    <?php flash('maintenance_message'); ?>

    <!-- Debug Information (Temporary) -->
    <?php if(isset($_GET['debug']) && $_GET['debug'] == 1): ?>
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
        <h3 class="text-lg font-semibold text-yellow-800 mb-2">Debug Information</h3>
        <div>
            <p><strong>Maintenance ID:</strong> <?php echo $data['maintenance_record']->id; ?></p>
            <p><strong>Completed Checklist Items:</strong> <?php echo isset($data['completed_checklist_items']) ? 'Available' : 'Not Available'; ?></p>
            <?php if(isset($data['completed_checklist_items'])): ?>
                <p><strong>Number of Guideline IDs with Completed Items:</strong> <?php echo count($data['completed_checklist_items']); ?></p>
                <p><strong>Guideline IDs with Completed Items:</strong> <?php echo implode(', ', array_keys($data['completed_checklist_items'])); ?></p>

                <?php foreach($data['completed_checklist_items'] as $guidelineId => $items): ?>
                    <div class="mt-2 p-2 bg-white rounded border border-yellow-200">
                        <p><strong>Guideline ID <?php echo $guidelineId; ?>:</strong> <?php echo count($items); ?> completed items</p>
                        <ul class="list-disc pl-5">
                            <?php foreach($items as $item): ?>
                                <li><?php echo $item->description; ?> (Completed: <?php echo date('Y-m-d', strtotime($item->completed_date)); ?>)</li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Maintenance Record Details -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Maintenance Record</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Basic Information</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Date Performed:</span>
                            <p class="mt-1"><?php echo date('F j, Y', strtotime($data['maintenance_record']->performed_date)); ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Maintenance Type:</span>
                            <?php
                                $typeClass = 'bg-blue-100 text-blue-800';
                                switch($data['maintenance_record']->maintenance_type) {
                                    case 'preventive':
                                        $typeClass = 'bg-green-100 text-green-800';
                                        break;
                                    case 'corrective':
                                        $typeClass = 'bg-red-100 text-red-800';
                                        break;
                                    case 'upgrade':
                                        $typeClass = 'bg-purple-100 text-purple-800';
                                        break;
                                }
                            ?>
                            <p class="mt-1">
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $typeClass; ?>">
                                    <?php echo ucfirst($data['maintenance_record']->maintenance_type); ?>
                                </span>
                            </p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Status:</span>
                            <?php
                                $statusClass = 'bg-gray-100 text-gray-800';
                                switch($data['maintenance_record']->status) {
                                    case 'completed':
                                        $statusClass = 'bg-green-100 text-green-800';
                                        break;
                                    case 'scheduled':
                                        $statusClass = 'bg-blue-100 text-blue-800';
                                        break;
                                    case 'cancelled':
                                        $statusClass = 'bg-red-100 text-red-800';
                                        break;
                                    case 'overdue':
                                        $statusClass = 'bg-yellow-100 text-yellow-800';
                                        break;
                                }
                            ?>
                            <p class="mt-1">
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $statusClass; ?>">
                                    <?php echo ucfirst($data['maintenance_record']->status); ?>
                                </span>
                            </p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Performed By:</span>
                            <p class="mt-1"><?php echo $data['maintenance_record']->performed_by_name ?? 'N/A'; ?></p>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Additional Details</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Description:</span>
                            <p class="mt-1"><?php echo $data['maintenance_record']->description; ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Cost:</span>
                            <p class="mt-1">$<?php echo number_format($data['maintenance_record']->cost, 2); ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Next Scheduled Date:</span>
                            <p class="mt-1">
                                <?php echo $data['maintenance_record']->next_scheduled_date ? date('F j, Y', strtotime($data['maintenance_record']->next_scheduled_date)) : 'N/A'; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Implemented Guidelines -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Implemented Guidelines</h2>
            <?php
            // Direct database query to get the count
            $db = new Database();
            $db->query("SELECT COUNT(*) as count FROM maintenance_guideline_implementation WHERE maintenance_id = :maintenance_id");
            $db->bind(':maintenance_id', $data['maintenance_record']->id);
            $countResult = $db->single();
            $guidelineCount = $countResult->count;
            ?>
            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                Total: <?php echo $guidelineCount; ?> guidelines
            </span>
        </div>
        <div class="p-6">
            <?php
            // Direct database query to get the guidelines
            $db = new Database();
            $db->query("SELECT * FROM maintenance_guideline_implementation WHERE maintenance_id = :maintenance_id");
            $db->bind(':maintenance_id', $data['maintenance_record']->id);
            $implementedGuidelines = $db->resultSet();

            // Get the guideline details
            $guidelineDetails = [];
            if (!empty($implementedGuidelines)) {
                foreach ($implementedGuidelines as $implementation) {
                    $db->query("SELECT * FROM maintenance_guidelines WHERE id = :guideline_id");
                    $db->bind(':guideline_id', $implementation->guideline_id);
                    $guideline = $db->single();
                    if ($guideline) {
                        // Add implementation date to the guideline object
                        $guideline->implemented_date = $implementation->implemented_date;
                        $guideline->guideline_id = $guideline->id;
                        $guideline->checklist_items = [];
                        $guidelineDetails[] = $guideline;
                    }
                }
            }
            ?>

            <?php if(!empty($guidelineDetails)) : ?>
                <ul class="space-y-6">
                    <?php foreach($guidelineDetails as $index => $guideline) : ?>
                        <li class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                    <span class="text-blue-800 font-semibold"><?php echo $index + 1; ?></span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex justify-between items-start">
                                        <h4 class="font-semibold text-gray-800"><?php echo $guideline->name; ?></h4>
                                        <div class="flex space-x-2">
                                            <a href="<?php echo URLROOT; ?>/maintenance/guideline/<?php echo $guideline->guideline_id; ?>" class="text-xs text-blue-600 hover:text-blue-900">
                                                <i class="fas fa-info-circle"></i> Details
                                            </a>
                                            <a href="<?php echo URLROOT; ?>/maintenance/guidelineImplementations/<?php echo $guideline->guideline_id; ?>" class="text-xs text-indigo-600 hover:text-indigo-900">
                                                <i class="fas fa-history"></i> View All Implementations
                                            </a>
                                        </div>
                                    </div>

                                    <div class="mt-2">
                                        <p class="text-sm text-gray-600"><?php echo $guideline->description; ?></p>
                                    </div>

                                    <div class="mt-3 grid grid-cols-1 md:grid-cols-4 gap-4 text-xs text-gray-500">
                                        <div>
                                            <span class="font-medium">Equipment Type:</span>
                                            <p><?php echo $guideline->equipment_type; ?></p>
                                        </div>
                                        <div>
                                            <span class="font-medium">Frequency:</span>
                                            <p>Every <?php echo $guideline->frequency_days; ?> days</p>
                                        </div>
                                        <div>
                                            <span class="font-medium">Importance:</span>
                                            <?php
                                                $importanceClass = 'bg-blue-100 text-blue-800';
                                                switch($guideline->importance) {
                                                    case 'low':
                                                        $importanceClass = 'bg-gray-100 text-gray-800';
                                                        break;
                                                    case 'medium':
                                                        $importanceClass = 'bg-blue-100 text-blue-800';
                                                        break;
                                                    case 'high':
                                                        $importanceClass = 'bg-yellow-100 text-yellow-800';
                                                        break;
                                                    case 'critical':
                                                        $importanceClass = 'bg-red-100 text-red-800';
                                                        break;
                                                }
                                            ?>
                                            <p>
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $importanceClass; ?>">
                                                    <?php echo ucfirst($guideline->importance); ?>
                                                </span>
                                            </p>
                                        </div>
                                        <div>
                                            <span class="font-medium">Implementation Date:</span>
                                            <p><?php echo date('M j, Y', strtotime($guideline->implemented_date)); ?></p>
                                        </div>
                                    </div>

                                    <!-- Completed Checklist Items -->
                                    <?php
                                    // Get completed checklist items for this guideline
                                    $completedItems = [];
                                    $guidelineId = $guideline->guideline_id ?? $guideline->id;

                                    if (isset($data['completed_checklist_items'][$guidelineId])) {
                                        $completedItems = $data['completed_checklist_items'][$guidelineId];
                                    }

                                    if (!empty($completedItems)) :
                                    ?>
                                    <div class="mt-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <h5 class="text-sm font-semibold text-gray-700">Completed Checklist Items (<?php echo count($completedItems); ?>)</h5>
                                            <button type="button" class="text-xs text-blue-600 hover:text-blue-900 focus:outline-none"
                                                    onclick="toggleChecklist('completed-checklist-<?php echo $guidelineId; ?>')">
                                                <i class="fas fa-chevron-down"></i> Show/Hide
                                            </button>
                                        </div>
                                        <div id="completed-checklist-<?php echo $guidelineId; ?>" class="">
                                            <ul class="space-y-2 bg-white rounded border border-gray-200 p-3">
                                                <?php foreach($completedItems as $item) : ?>
                                                <li class="text-xs text-gray-700 flex items-start">
                                                    <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-2">
                                                        <i class="fas fa-check text-green-600 text-xs"></i>
                                                    </div>
                                                    <div>
                                                        <p class="font-medium"><?php echo $item->description; ?></p>
                                                        <p class="text-gray-500 mt-1">
                                                            <span class="text-xs">Completed on: <?php echo date('M j, Y', strtotime($item->completed_date)); ?></span>
                                                            <?php if(!empty($item->completed_by_name)) : ?>
                                                            <span class="text-xs ml-2">by: <?php echo $item->completed_by_name; ?></span>
                                                            <?php endif; ?>
                                                        </p>
                                                    </div>
                                                </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php else: ?>
                <div class="flex flex-col items-center justify-center py-8">
                    <div class="bg-gray-100 rounded-full p-3 mb-4">
                        <i class="fas fa-clipboard-list text-gray-500 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">No Guidelines Implemented</h3>
                    <p class="text-gray-500 text-center max-w-md">
                        No maintenance guidelines have been implemented for this record yet. Guidelines help track specific maintenance tasks performed on this asset.
                    </p>
                    <?php if(isset($_SESSION['user_id']) && $_SESSION['user_role'] == 'admin'): ?>
                        <a href="<?php echo URLROOT; ?>/maintenance/guidelines" class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-plus mr-2"></i> View Available Guidelines
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add JavaScript for toggling sections -->
    <script>
        function toggleImplementations(id) {
            const element = document.getElementById(id);
            if (element) {
                element.classList.toggle('hidden');
            }
        }

        function toggleChecklist(id) {
            const element = document.getElementById(id);
            if (element) {
                element.classList.toggle('hidden');
            }
        }
    </script>

    <!-- Asset Information -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Asset Information</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <span class="text-sm font-medium text-gray-500">Host Name:</span>
                    <p class="mt-1"><?php echo $data['asset']->computer_host_name; ?></p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Equipment Type:</span>
                    <p class="mt-1"><?php echo $data['asset']->equipment_type; ?></p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Serial Number:</span>
                    <p class="mt-1"><?php echo $data['asset']->serial_number; ?></p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Site Name:</span>
                    <p class="mt-1"><?php echo $data['asset']->site_name ?? 'N/A'; ?></p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Program Section:</span>
                    <p class="mt-1"><?php echo $data['asset']->program_section ?? 'N/A'; ?></p>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Status:</span>
                    <p class="mt-1"><?php echo isset($data['asset']->status) ? ucfirst($data['asset']->status) : 'N/A'; ?></p>
                </div>
            </div>
            <div class="mt-4">
                <a href="<?php echo URLROOT; ?>/assets/show/<?php echo $data['asset']->id; ?>" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-external-link-alt mr-1"></i> View Full Asset Details
                </a>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
