<?php require APPROOT . '/views/inc/header.php'; ?>

<!-- Hero Section -->
<section class="relative bg-gradient-to-r from-blue-800 via-blue-700 to-blue-600 text-white overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
            <defs>
                <pattern id="circuit" width="100" height="100" patternUnits="userSpaceOnUse">
                    <path d="M0 50 H30 M70 50 H100 M50 0 V30 M50 70 V100" stroke="white" stroke-width="2" fill="none" />
                    <circle cx="50" cy="50" r="10" fill="white" opacity="0.3" />
                    <circle cx="50" cy="50" r="5" fill="white" opacity="0.6" />
                </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#circuit)" />
        </svg>
    </div>

    <div class="container mx-auto px-4 py-20 md:py-32 relative z-10">
        <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-10 md:mb-0">
                <div class="inline-block px-3 py-1 bg-blue-900 bg-opacity-50 rounded-full text-blue-100 text-sm font-semibold mb-6">
                    <?php if(isLoggedIn()): ?>
                        <span class="flex items-center">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                            Welcome back, <?php echo $_SESSION['user_name']; ?>
                        </span>
                    <?php else: ?>
                        <span class="flex items-center">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-2"></span>
                            Asset Management Made Simple
                        </span>
                    <?php endif; ?>
                </div>
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                    <?php echo $data['title']; ?>
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-xl">
                    <?php echo $data['description']; ?>
                </p>

                <?php if(isLoggedIn()): ?>
                    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 mb-8">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-3xl font-bold"><?php echo $data['asset_count']; ?></div>
                                <div class="text-blue-200 text-sm">Total Assets</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="text-blue-200 text-sm">Asset Analytics</div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <p class="text-lg text-blue-200 mb-8">
                        Streamline your asset management with our powerful, easy-to-use platform
                    </p>
                <?php endif; ?>

                <div class="flex flex-wrap gap-4">
                    <?php if(!isLoggedIn()) : ?>
                        <a href="<?php echo URLROOT; ?>/users/login" class="px-8 py-3 bg-white text-blue-700 rounded-lg font-medium hover:bg-blue-50 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                            <i class="fas fa-sign-in-alt mr-2"></i> Login
                        </a>
                        <a href="<?php echo URLROOT; ?>/users/register" class="px-8 py-3 bg-transparent border-2 border-white text-white rounded-lg font-medium hover:bg-white/10 transition-colors">
                            <i class="fas fa-user-plus mr-2"></i> Register
                        </a>
                    <?php else : ?>
                        <a href="<?php echo URLROOT; ?>/dashboard" class="px-8 py-3 bg-white text-blue-700 rounded-lg font-medium hover:bg-blue-50 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                        <a href="<?php echo URLROOT; ?>/assets" class="px-8 py-3 bg-transparent border-2 border-white text-white rounded-lg font-medium hover:bg-white/10 transition-colors">
                            <i class="fas fa-laptop mr-2"></i> View Assets
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="md:w-1/2">
                <div class="relative">
                    <!-- Hero Image -->
                    <div class="bg-white p-3 rounded-lg shadow-2xl transform rotate-2">
                        <img src="https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                             alt="Asset Management Dashboard"
                             class="rounded w-full h-auto shadow-inner"
                             style="max-height: 400px; object-fit: cover;">
                    </div>
                    <!-- Decorative Elements -->
                    <div class="absolute -bottom-6 -left-6 w-24 h-24 bg-yellow-400 rounded-lg shadow-lg transform -rotate-6 opacity-80"></div>
                    <div class="absolute -top-6 -right-6 w-20 h-20 bg-blue-500 rounded-full shadow-lg opacity-80"></div>

                    <!-- Floating Icons -->
                    <div class="absolute top-1/4 -left-10 bg-white p-3 rounded-lg shadow-lg transform -rotate-6">
                        <i class="fas fa-laptop text-blue-600 text-2xl"></i>
                    </div>
                    <div class="absolute bottom-1/4 -right-10 bg-white p-3 rounded-lg shadow-lg transform rotate-6">
                        <i class="fas fa-server text-blue-600 text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Wave Separator -->
    <div class="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" fill="#f9fafb">
            <path d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
        </svg>
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <span class="inline-block px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-4">POWERFUL CAPABILITIES</span>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Everything You Need for Asset Management</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">Our comprehensive platform provides all the tools necessary to efficiently track, monitor, and manage your organization's valuable ICT assets.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
            <!-- Feature 1 -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all hover:shadow-xl hover:-translate-y-2">
                <div class="p-1 bg-gradient-to-r from-blue-500 to-blue-700"></div>
                <div class="p-8">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                        <i class="fas fa-history text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Change History Tracking</h3>
                    <p class="text-gray-600 mb-6">Keep a detailed record of all changes made to your assets, including who made the changes and when.</p>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Complete audit trail
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            User accountability
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Field-level tracking
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Feature 2 -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all hover:shadow-xl hover:-translate-y-2">
                <div class="p-1 bg-gradient-to-r from-blue-500 to-blue-700"></div>
                <div class="p-8">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                        <i class="fas fa-file-export text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Data Import & Export</h3>
                    <p class="text-gray-600 mb-6">Seamlessly import and export asset data for integration with other systems and reporting.</p>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            CSV import/export
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Bulk operations
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Custom data filtering
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Feature 3 -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all hover:shadow-xl hover:-translate-y-2">
                <div class="p-1 bg-gradient-to-r from-blue-500 to-blue-700"></div>
                <div class="p-8">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                        <i class="fas fa-chart-pie text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Dashboard Analytics</h3>
                    <p class="text-gray-600 mb-6">Gain valuable insights with visual analytics and reports on your asset inventory.</p>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Visual data representation
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Asset distribution charts
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Trend analysis
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Asset Management Process Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <span class="inline-block px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-4">STREAMLINED WORKFLOW</span>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">How Our Asset Management Works</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">Our system simplifies the entire asset management lifecycle with an intuitive, user-friendly process.</p>
        </div>

        <div class="relative">
            <!-- Connection Line (Desktop) -->
            <div class="hidden md:block absolute top-24 left-0 right-0 h-1 bg-blue-200 z-0"></div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 relative z-10">
                <!-- Step 1 -->
                <div class="bg-white p-6 rounded-xl shadow-md text-center relative">
                    <div class="w-20 h-20 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 border-4 border-white">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Register & Setup</h3>
                    <p class="text-gray-600">Create your account and configure your organization's asset categories and fields.</p>
                </div>

                <!-- Step 2 -->
                <div class="bg-white p-6 rounded-xl shadow-md text-center relative">
                    <div class="w-20 h-20 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 border-4 border-white">
                        <i class="fas fa-file-import"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Add & Import Assets</h3>
                    <p class="text-gray-600">Add assets individually or import in bulk from CSV files with our easy import tool.</p>
                </div>

                <!-- Step 3 -->
                <div class="bg-white p-6 rounded-xl shadow-md text-center relative">
                    <div class="w-20 h-20 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 border-4 border-white">
                        <i class="fas fa-history"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Track Changes</h3>
                    <p class="text-gray-600">Monitor all changes to your assets with our detailed history tracking system.</p>
                </div>

                <!-- Step 4 -->
                <div class="bg-white p-6 rounded-xl shadow-md text-center relative">
                    <div class="w-20 h-20 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 border-4 border-white">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Analyze & Report</h3>
                    <p class="text-gray-600">Generate insightful reports and visualize your asset data with our dashboard analytics.</p>
                </div>
            </div>
        </div>

        <!-- Additional Info -->
        <div class="mt-16 bg-blue-50 rounded-xl p-8 shadow-inner">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/4 mb-6 md:mb-0 text-center">
                    <i class="fas fa-lightbulb text-5xl text-yellow-500 mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-900">Did You Know?</h3>
                </div>
                <div class="md:w-3/4 md:pl-8 md:border-l border-blue-200">
                    <p class="text-lg text-gray-700">Our asset management system now includes a comprehensive change history feature that tracks every modification made to your assets. This provides complete accountability and helps with compliance requirements.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-blue-700 to-indigo-800 text-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
            <defs>
                <pattern id="dots" width="30" height="30" patternUnits="userSpaceOnUse">
                    <circle cx="15" cy="15" r="2" fill="white" />
                </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#dots)" />
        </svg>
    </div>

    <div class="container mx-auto px-4 text-center relative z-10">
        <div class="inline-block px-4 py-1 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-white text-sm font-semibold mb-6">
            <?php if(isLoggedIn()) : ?>
                ENHANCE YOUR ASSET MANAGEMENT EXPERIENCE
            <?php else : ?>
                START MANAGING YOUR ASSETS TODAY
            <?php endif; ?>
        </div>

        <h2 class="text-3xl md:text-4xl font-bold mb-6">Take Control of Your ICT Assets</h2>
        <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Explore all the features of the EVIS - the system that optimize the tracking, monitoring, and bring efficient management of endpoints.
        </p>

        <div class="flex flex-wrap justify-center gap-4">
            <?php if(!isLoggedIn()) : ?>
                <a href="<?php echo URLROOT; ?>/users/register" class="px-8 py-4 bg-white text-blue-700 rounded-lg font-medium hover:bg-blue-50 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center">
                    <i class="fas fa-rocket mr-2"></i> Get Started Now
                </a>
                <a href="<?php echo URLROOT; ?>/pages/about" class="px-8 py-4 bg-transparent border-2 border-white text-white rounded-lg font-medium hover:bg-white/10 transition-colors flex items-center">
                    <i class="fas fa-info-circle mr-2"></i> Learn More
                </a>
            <?php else : ?>
                <a href="<?php echo URLROOT; ?>/dashboard" class="px-8 py-4 bg-white text-blue-700 rounded-lg font-medium hover:bg-blue-50 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center">
                    <i class="fas fa-tachometer-alt mr-2"></i> Go to Dashboard
                </a>
                <a href="<?php echo URLROOT; ?>/assets" class="px-8 py-4 bg-transparent border-2 border-white text-white rounded-lg font-medium hover:bg-white/10 transition-colors flex items-center">
                    <i class="fas fa-laptop mr-2"></i> Manage Assets
                </a>
            <?php endif; ?>
        </div>

        <!-- Trust Indicators -->
        <div class="mt-16 flex flex-wrap justify-center gap-8 text-center">
            <div>
                <div class="text-3xl font-bold mb-2">100%</div>
                <div class="text-blue-200">Secure</div>
            </div>
            <div>
                <div class="text-3xl font-bold mb-2">24/7</div>
                <div class="text-blue-200">Access</div>
            </div>
            <div>
                <div class="text-3xl font-bold mb-2">Easy</div>
                <div class="text-blue-200">Setup</div>
            </div>
        </div>
    </div>
</section>

<?php require APPROOT . '/views/inc/footer.php'; ?>
