<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="mb-4 flex justify-between items-center">
    <h1 class="text-2xl font-bold text-gray-800">Add Permission</h1>
    <a href="<?php echo URLROOT; ?>/permissions" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md">
        <i class="fa fa-backward mr-2"></i> Back to Permissions
    </a>
</div>

<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-4 border-b border-gray-200 bg-gray-50">
        <h2 class="text-lg font-semibold text-gray-700">Permission Details</h2>
    </div>
    <div class="p-6">
        <form action="<?php echo URLROOT; ?>/permissions/add" method="POST">
            <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
            
            <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Permission Name</label>
                <input type="text" name="name" id="name" 
                       class="w-full px-3 py-2 border <?php echo (!empty($data['name_err'])) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                       value="<?php echo e($data['name']); ?>" required>
                <span class="text-red-500 text-xs"><?php echo $data['name_err']; ?></span>
                <p class="mt-1 text-xs text-gray-500">Use lowercase with underscores (e.g., manage_users, view_reports)</p>
            </div>
            
            <div class="mb-4">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea name="description" id="description" rows="3" 
                          class="w-full px-3 py-2 border <?php echo (!empty($data['description_err'])) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          required><?php echo e($data['description']); ?></textarea>
                <span class="text-red-500 text-xs"><?php echo $data['description_err']; ?></span>
            </div>
            
            <div class="mb-6">
                <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <div class="relative">
                    <select name="category" id="category" 
                            class="w-full px-3 py-2 border <?php echo (!empty($data['category_err'])) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 appearance-none">
                        <option value="">Select a category</option>
                        <?php foreach($data['categories'] as $category) : ?>
                            <option value="<?php echo e($category); ?>" <?php echo ($data['category'] == $category) ? 'selected' : ''; ?>>
                                <?php echo e($category); ?>
                            </option>
                        <?php endforeach; ?>
                        <option value="new">+ Add New Category</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <i class="fas fa-chevron-down"></i>
                    </div>
                </div>
                <span class="text-red-500 text-xs"><?php echo $data['category_err']; ?></span>
                
                <div id="new-category-container" class="mt-3" style="display: none;">
                    <label for="new_category" class="block text-sm font-medium text-gray-700 mb-1">New Category Name</label>
                    <input type="text" name="new_category" id="new_category" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md">
                    <i class="fas fa-save mr-2"></i> Save Permission
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const categorySelect = document.getElementById('category');
        const newCategoryContainer = document.getElementById('new-category-container');
        const newCategoryInput = document.getElementById('new_category');
        
        categorySelect.addEventListener('change', function() {
            if (this.value === 'new') {
                newCategoryContainer.style.display = 'block';
                newCategoryInput.focus();
            } else {
                newCategoryContainer.style.display = 'none';
                newCategoryInput.value = '';
            }
        });
        
        // Show new category input if "new" is already selected (e.g., on form validation error)
        if (categorySelect.value === 'new') {
            newCategoryContainer.style.display = 'block';
        }
    });
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
