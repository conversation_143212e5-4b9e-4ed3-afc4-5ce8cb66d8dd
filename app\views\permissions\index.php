<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="mb-4 flex justify-between items-center">
    <h1 class="text-2xl font-bold text-gray-800">Permission Management</h1>
    <div>
        <a href="<?php echo URLROOT; ?>/roles" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md mr-2">
            <i class="fas fa-user-tag mr-2"></i> Manage Roles
        </a>
        <a href="<?php echo URLROOT; ?>/users/manage" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md">
            <i class="fas fa-users mr-2"></i> User Management
        </a>
    </div>
</div>

<?php flash('permission_message'); ?>

<div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
        <h2 class="text-lg font-semibold text-gray-700">Permissions</h2>
        <a href="<?php echo URLROOT; ?>/permissions/add" class="inline-flex items-center px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded-md">
            <i class="fas fa-plus mr-1"></i> Add Permission
        </a>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php foreach($data['permissions'] as $permission) : ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">
                                <?php echo e($permission->name); ?>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-500">
                                <?php echo e($permission->description); ?>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <?php echo e($permission->category); ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="<?php echo URLROOT; ?>/permissions/show/<?php echo $permission->id; ?>" class="text-indigo-600 hover:text-indigo-900">
                                    <i class="fas fa-eye"></i>
                                    <span class="sr-only">View</span>
                                </a>
                                
                                <a href="<?php echo URLROOT; ?>/permissions/edit/<?php echo $permission->id; ?>" class="text-yellow-600 hover:text-yellow-900">
                                    <i class="fas fa-edit"></i>
                                    <span class="sr-only">Edit</span>
                                </a>
                                
                                <a href="javascript:void(0);" 
                                   class="text-red-600 hover:text-red-900 delete-permission-btn"
                                   data-permission-id="<?php echo $permission->id; ?>"
                                   data-permission-name="<?php echo e($permission->name); ?>">
                                    <i class="fas fa-trash"></i>
                                    <span class="sr-only">Delete</span>
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                
                <?php if(empty($data['permissions'])) : ?>
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">
                            No permissions found
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Delete Permission Form (Hidden) -->
<form id="delete-permission-form" action="" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
</form>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Delete permission confirmation
        const deleteButtons = document.querySelectorAll('.delete-permission-btn');
        const deleteForm = document.getElementById('delete-permission-form');
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const permissionId = this.getAttribute('data-permission-id');
                const permissionName = this.getAttribute('data-permission-name');
                
                Swal.fire({
                    title: 'Are you sure?',
                    text: `Do you want to delete the permission "${permissionName}"? This action cannot be undone.`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, delete it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        deleteForm.action = `<?php echo URLROOT; ?>/permissions/delete/${permissionId}`;
                        deleteForm.submit();
                    }
                });
            });
        });
    });
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
