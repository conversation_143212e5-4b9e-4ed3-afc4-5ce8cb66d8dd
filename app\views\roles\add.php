<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="mb-4 flex justify-between items-center">
    <h1 class="text-2xl font-bold text-gray-800">Add Role</h1>
    <a href="<?php echo URLROOT; ?>/roles" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md">
        <i class="fa fa-backward mr-2"></i> Back to Roles
    </a>
</div>

<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-4 border-b border-gray-200 bg-gray-50">
        <h2 class="text-lg font-semibold text-gray-700">Role Details</h2>
    </div>
    <div class="p-6">
        <form action="<?php echo URLROOT; ?>/roles/add" method="POST">
            <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
            
            <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
                <input type="text" name="name" id="name" 
                       class="w-full px-3 py-2 border <?php echo (!empty($data['name_err'])) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                       value="<?php echo e($data['name']); ?>" required>
                <span class="text-red-500 text-xs"><?php echo $data['name_err']; ?></span>
            </div>
            
            <div class="mb-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea name="description" id="description" rows="3" 
                          class="w-full px-3 py-2 border <?php echo (!empty($data['description_err'])) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          required><?php echo e($data['description']); ?></textarea>
                <span class="text-red-500 text-xs"><?php echo $data['description_err']; ?></span>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md">
                    <i class="fas fa-save mr-2"></i> Save Role
                </button>
            </div>
        </form>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
