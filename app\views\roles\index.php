<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="mb-4 flex justify-between items-center">
    <h1 class="text-2xl font-bold text-gray-800">Role Management</h1>
    <div>
        <a href="<?php echo URLROOT; ?>/permissions" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md mr-2">
            <i class="fas fa-lock mr-2"></i> Manage Permissions
        </a>
        <a href="<?php echo URLROOT; ?>/users/manage" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md">
            <i class="fas fa-users mr-2"></i> User Management
        </a>
    </div>
</div>

<?php flash('role_message'); ?>

<div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
        <h2 class="text-lg font-semibold text-gray-700">Roles</h2>
        <a href="<?php echo URLROOT; ?>/roles/add" class="inline-flex items-center px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded-md">
            <i class="fas fa-plus mr-1"></i> Add Role
        </a>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        System Role
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php foreach($data['roles'] as $role) : ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">
                                <?php echo e($role->name); ?>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-500">
                                <?php echo e($role->description); ?>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <?php if($role->is_system_role) : ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Yes
                                </span>
                            <?php else : ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    No
                                </span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="<?php echo URLROOT; ?>/roles/show/<?php echo $role->id; ?>" class="text-indigo-600 hover:text-indigo-900">
                                    <i class="fas fa-eye"></i>
                                    <span class="sr-only">View</span>
                                </a>
                                
                                <a href="<?php echo URLROOT; ?>/roles/permissions/<?php echo $role->id; ?>" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-lock"></i>
                                    <span class="sr-only">Permissions</span>
                                </a>
                                
                                <?php if(!$role->is_system_role) : ?>
                                    <a href="<?php echo URLROOT; ?>/roles/edit/<?php echo $role->id; ?>" class="text-yellow-600 hover:text-yellow-900">
                                        <i class="fas fa-edit"></i>
                                        <span class="sr-only">Edit</span>
                                    </a>
                                    
                                    <a href="javascript:void(0);" 
                                       class="text-red-600 hover:text-red-900 delete-role-btn"
                                       data-role-id="<?php echo $role->id; ?>"
                                       data-role-name="<?php echo e($role->name); ?>">
                                        <i class="fas fa-trash"></i>
                                        <span class="sr-only">Delete</span>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                
                <?php if(empty($data['roles'])) : ?>
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">
                            No roles found
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Delete Role Form (Hidden) -->
<form id="delete-role-form" action="" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
</form>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Delete role confirmation
        const deleteButtons = document.querySelectorAll('.delete-role-btn');
        const deleteForm = document.getElementById('delete-role-form');
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const roleId = this.getAttribute('data-role-id');
                const roleName = this.getAttribute('data-role-name');
                
                Swal.fire({
                    title: 'Are you sure?',
                    text: `Do you want to delete the role "${roleName}"? This action cannot be undone.`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, delete it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        deleteForm.action = `<?php echo URLROOT; ?>/roles/delete/${roleId}`;
                        deleteForm.submit();
                    }
                });
            });
        });
    });
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
