<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="mb-4 flex justify-between items-center">
    <h1 class="text-2xl font-bold text-gray-800">Manage Role Permissions</h1>
    <div>
        <a href="<?php echo URLROOT; ?>/roles/show/<?php echo $data['role']->id; ?>" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md mr-2">
            <i class="fas fa-eye mr-2"></i> View Role
        </a>
        <a href="<?php echo URLROOT; ?>/roles" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md">
            <i class="fa fa-backward mr-2"></i> Back to Roles
        </a>
    </div>
</div>

<?php flash('role_message'); ?>

<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-4 border-b border-gray-200 bg-gray-50">
        <h2 class="text-lg font-semibold text-gray-700">Permissions for: <?php echo e($data['role']->name); ?></h2>
        <p class="text-sm text-gray-500 mt-1"><?php echo e($data['role']->description); ?></p>
    </div>
    <div class="p-6">
        <form action="<?php echo URLROOT; ?>/roles/permissions/<?php echo $data['role']->id; ?>" method="POST">
            <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
            
            <div class="mb-4">
                <div class="flex items-center mb-4">
                    <button type="button" id="select-all" class="text-sm text-indigo-600 hover:text-indigo-900 mr-4">
                        <i class="fas fa-check-square mr-1"></i> Select All
                    </button>
                    <button type="button" id="deselect-all" class="text-sm text-indigo-600 hover:text-indigo-900">
                        <i class="fas fa-square mr-1"></i> Deselect All
                    </button>
                </div>
                
                <?php foreach($data['categories'] as $category) : ?>
                    <div class="mb-6">
                        <h3 class="text-md font-medium text-gray-700 mb-2 border-b pb-1"><?php echo e($category); ?></h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <?php foreach($data['allPermissions'] as $permission) : ?>
                                <?php if($permission->category == $category) : ?>
                                    <div class="flex items-start">
                                        <div class="flex items-center h-5">
                                            <input id="permission-<?php echo $permission->id; ?>" 
                                                   name="permissions[]" 
                                                   type="checkbox" 
                                                   value="<?php echo $permission->id; ?>" 
                                                   class="permission-checkbox focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                                                   <?php echo in_array($permission->id, $data['rolePermissionIds']) ? 'checked' : ''; ?>>
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="permission-<?php echo $permission->id; ?>" class="font-medium text-gray-700">
                                                <?php echo e($permission->name); ?>
                                            </label>
                                            <p class="text-gray-500"><?php echo e($permission->description); ?></p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md">
                    <i class="fas fa-save mr-2"></i> Save Permissions
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllBtn = document.getElementById('select-all');
        const deselectAllBtn = document.getElementById('deselect-all');
        const checkboxes = document.querySelectorAll('.permission-checkbox');
        
        selectAllBtn.addEventListener('click', function() {
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        });
        
        deselectAllBtn.addEventListener('click', function() {
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        });
    });
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
