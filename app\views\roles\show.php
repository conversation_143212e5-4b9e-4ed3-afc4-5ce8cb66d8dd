<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="mb-4 flex justify-between items-center">
    <h1 class="text-2xl font-bold text-gray-800">Role Details</h1>
    <a href="<?php echo URLROOT; ?>/roles" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md">
        <i class="fa fa-backward mr-2"></i> Back to Roles
    </a>
</div>

<?php flash('role_message'); ?>

<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Role Information -->
    <div class="md:col-span-1">
        <div class="bg-white rounded-lg shadow-md overflow-hidden h-full">
            <div class="p-4 border-b border-gray-200 bg-gray-50">
                <h2 class="text-lg font-semibold text-gray-700">Role Information</h2>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Name</h3>
                    <p class="mt-1 text-sm text-gray-900"><?php echo e($data['role']->name); ?></p>
                </div>
                
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Description</h3>
                    <p class="mt-1 text-sm text-gray-900"><?php echo e($data['role']->description); ?></p>
                </div>
                
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">System Role</h3>
                    <p class="mt-1">
                        <?php if($data['role']->is_system_role) : ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Yes
                            </span>
                        <?php else : ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                No
                            </span>
                        <?php endif; ?>
                    </p>
                </div>
                
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Created At</h3>
                    <p class="mt-1 text-sm text-gray-900"><?php echo date('F j, Y, g:i a', strtotime($data['role']->created_at)); ?></p>
                </div>
                
                <div class="mt-6 flex space-x-3">
                    <a href="<?php echo URLROOT; ?>/roles/permissions/<?php echo $data['role']->id; ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
                        <i class="fas fa-lock mr-2"></i> Manage Permissions
                    </a>
                    
                    <?php if(!$data['role']->is_system_role) : ?>
                        <a href="<?php echo URLROOT; ?>/roles/edit/<?php echo $data['role']->id; ?>" class="inline-flex items-center px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-md">
                            <i class="fas fa-edit mr-2"></i> Edit
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Permissions -->
    <div class="md:col-span-1">
        <div class="bg-white rounded-lg shadow-md overflow-hidden h-full">
            <div class="p-4 border-b border-gray-200 bg-gray-50">
                <h2 class="text-lg font-semibold text-gray-700">Permissions</h2>
            </div>
            <div class="p-6">
                <?php if(empty($data['permissions'])) : ?>
                    <p class="text-sm text-gray-500">No permissions assigned to this role.</p>
                <?php else : ?>
                    <ul class="divide-y divide-gray-200">
                        <?php foreach($data['permissions'] as $permission) : ?>
                            <li class="py-2">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($permission->name); ?></p>
                                        <p class="text-xs text-gray-500"><?php echo e($permission->description); ?></p>
                                    </div>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
                
                <div class="mt-4">
                    <a href="<?php echo URLROOT; ?>/roles/permissions/<?php echo $data['role']->id; ?>" class="text-sm text-indigo-600 hover:text-indigo-900">
                        <i class="fas fa-cog mr-1"></i> Manage permissions
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Users with this role -->
    <div class="md:col-span-1">
        <div class="bg-white rounded-lg shadow-md overflow-hidden h-full">
            <div class="p-4 border-b border-gray-200 bg-gray-50">
                <h2 class="text-lg font-semibold text-gray-700">Users with this Role</h2>
            </div>
            <div class="p-6">
                <?php if(empty($data['users'])) : ?>
                    <p class="text-sm text-gray-500">No users have this role.</p>
                <?php else : ?>
                    <ul class="divide-y divide-gray-200">
                        <?php foreach($data['users'] as $user) : ?>
                            <li class="py-2">
                                <div class="flex items-center">
                                    <i class="fas fa-user text-gray-400 mr-2"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($user->name); ?></p>
                                        <p class="text-xs text-gray-500"><?php echo e($user->email); ?></p>
                                    </div>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
                
                <div class="mt-4">
                    <a href="<?php echo URLROOT; ?>/users/manage" class="text-sm text-indigo-600 hover:text-indigo-900">
                        <i class="fas fa-users mr-1"></i> Manage users
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
