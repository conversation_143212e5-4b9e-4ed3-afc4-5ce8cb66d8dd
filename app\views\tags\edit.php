<?php require APPROOT . '/views/inc/header.php'; ?>
<div class="mb-4">
    <a href="<?php echo URLROOT; ?>/tags" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md">
        <i class="fa fa-backward mr-2"></i> Back to Tags
    </a>
</div>

<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-4 border-b border-gray-200 bg-gray-50">
        <h2 class="text-lg font-semibold text-gray-700">Edit Tag</h2>
        <p class="text-sm text-gray-600">Update tag information.</p>
    </div>
    
    <div class="p-6">
        <form action="<?php echo URLROOT; ?>/tags/edit/<?php echo $data['id']; ?>" method="post">
            <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Tag Name</label>
                <input type="text" name="name" id="name" class="form-control w-full rounded-md <?php echo (!empty($data['name_err'])) ? 'border-red-500' : ''; ?>" value="<?php echo $data['name']; ?>" placeholder="Enter tag name">
                <span class="text-red-500 text-xs mt-1"><?php echo $data['name_err']; ?></span>
            </div>
            
            <div class="mb-6">
                <label for="color" class="block text-sm font-medium text-gray-700 mb-1">Tag Color</label>
                <div class="flex items-center">
                    <input type="color" name="color" id="color" class="h-10 w-10 rounded-md border border-gray-300 mr-2" value="<?php echo $data['color']; ?>">
                    <input type="text" id="color-hex" class="form-control rounded-md" value="<?php echo $data['color']; ?>" readonly>
                </div>
                <p class="text-xs text-gray-500 mt-1">Choose a color for the tag.</p>
            </div>
            
            <div class="flex justify-between">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                    <i class="fas fa-save mr-1"></i> Update Tag
                </button>
                <a href="<?php echo URLROOT; ?>/tags" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                    <i class="fas fa-times mr-1"></i> Cancel
                </a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const colorInput = document.getElementById('color');
    const colorHexInput = document.getElementById('color-hex');
    
    // Update hex input when color changes
    colorInput.addEventListener('input', function() {
        colorHexInput.value = this.value;
    });
});
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
