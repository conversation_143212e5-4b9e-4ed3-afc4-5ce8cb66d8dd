<?php require APPROOT . '/views/inc/header.php'; ?>
<div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-800 mb-4 md:mb-0">Manage Tags</h1>
    <div>
        <a href="<?php echo URLROOT; ?>/assets" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md mr-2">
            <i class="fa fa-backward mr-2"></i> Back to Assets
        </a>
        <a href="<?php echo URLROOT; ?>/tags/add" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            <i class="fas fa-plus mr-2"></i> Add Tag
        </a>
    </div>
</div>

<?php flash('tag_message'); ?>

<?php if(count($data['tags']) > 0) : ?>
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tag</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Color</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach($data['tags'] as $tag) : ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: <?php echo $tag->color; ?>"></span>
                                    <span class="text-sm font-medium text-gray-900"><?php echo $tag->name; ?></span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-500"><?php echo $tag->color; ?></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-500"><?php echo $tag->created_by_name ?? 'System'; ?></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-500"><?php echo date('M j, Y g:i A', strtotime($tag->created_at)); ?></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="<?php echo URLROOT; ?>/tags/edit/<?php echo $tag->id; ?>" class="text-yellow-600 hover:text-yellow-900">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo URLROOT; ?>/tags/delete/<?php echo $tag->id; ?>" method="post" class="inline delete-tag-form">
                                        <button type="submit" class="text-red-600 hover:text-red-900 bg-transparent border-0 p-0">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
<?php else : ?>
    <div class="bg-white rounded-lg shadow-md p-6 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
        </svg>
        <h3 class="mt-2 text-xl font-medium text-gray-900">No tags found</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating a new tag.</p>
        <div class="mt-6">
            <a href="<?php echo URLROOT; ?>/tags/add" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i> Add Tag
            </a>
        </div>
    </div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Confirm delete with SweetAlert2
    const deleteTagForms = document.querySelectorAll('.delete-tag-form');
    if (deleteTagForms) {
        deleteTagForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#ef4444',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Yes, delete it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        this.submit();
                    }
                });
            });
        });
    }
});
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
