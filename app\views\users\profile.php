<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="max-w-4xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">My Profile</h1>
        <a href="<?php echo URLROOT; ?>" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md inline-flex items-center">
            <i class="fa fa-backward mr-2"></i> Back
        </a>
    </div>

    <?php flash('user_message'); ?>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- User Information -->
                <div>
                    <h2 class="text-xl font-semibold mb-4 text-gray-700 border-b pb-2">Account Information</h2>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm text-gray-500">Email</p>
                            <p class="font-medium"><?php echo $data['email']; ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Role</p>
                            <p class="font-medium capitalize"><?php echo $data['role']; ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Status</p>
                            <p class="font-medium capitalize"><?php echo $data['status']; ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Member Since</p>
                            <p class="font-medium"><?php echo date('F j, Y', strtotime($data['created_at'])); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Edit Profile Form -->
                <div>
                    <h2 class="text-xl font-semibold mb-4 text-gray-700 border-b pb-2">Edit Profile</h2>
                    <form action="<?php echo URLROOT; ?>/users/profile" method="post">
                        <div class="mb-4">
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                            <input type="text" name="name" id="name" class="form-control <?php echo (!empty($data['name_err'])) ? 'border-red-500' : ''; ?>" value="<?php echo $data['name']; ?>">
                            <span class="text-red-500 text-xs"><?php echo $data['name_err']; ?></span>
                        </div>

                        <h3 class="text-lg font-medium mb-3 mt-6 text-gray-700 border-b pb-2">Change Password</h3>
                        <p class="text-sm text-gray-500 mb-4">Leave blank if you don't want to change your password</p>

                        <div class="mb-4">
                            <label for="current_password" class="block text-sm font-medium text-gray-700 mb-1">Current Password</label>
                            <input type="password" name="current_password" id="current_password" class="form-control <?php echo (!empty($data['current_password_err'])) ? 'border-red-500' : ''; ?>">
                            <span class="text-red-500 text-xs"><?php echo $data['current_password_err']; ?></span>
                        </div>

                        <div class="mb-4">
                            <label for="new_password" class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                            <input type="password" name="new_password" id="new_password" class="form-control <?php echo (!empty($data['new_password_err'])) ? 'border-red-500' : ''; ?>">
                            <span class="text-red-500 text-xs"><?php echo $data['new_password_err']; ?></span>
                        </div>

                        <div class="mb-4">
                            <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
                            <input type="password" name="confirm_password" id="confirm_password" class="form-control <?php echo (!empty($data['confirm_password_err'])) ? 'border-red-500' : ''; ?>">
                            <span class="text-red-500 text-xs"><?php echo $data['confirm_password_err']; ?></span>
                        </div>

                        <div class="mt-6">
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                                <i class="fas fa-save mr-2"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
