<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
            <h2 class="text-3xl font-extrabold text-gray-900 mb-2">Create an account</h2>
            <p class="text-gray-600">Join us to start managing your assets</p>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow-xl sm:rounded-lg sm:px-10 border border-gray-200">
                <form action="<?php echo URLROOT; ?>/users/register" method="post" class="space-y-6">
                    <!-- Name Field -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            Full Name <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input
                                type="text"
                                name="name"
                                id="name"
                                class="pl-10 block w-full border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 py-2 px-3 <?php echo (!empty($data['name_err'])) ? 'border-red-300 text-red-900 placeholder-red-300 focus:outline-none focus:ring-red-500 focus:border-red-500' : 'border-gray-300'; ?>"
                                placeholder="John Doe"
                                value="<?php echo $data['name']; ?>"
                            >
                            <?php if(!empty($data['name_err'])) : ?>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php if(!empty($data['name_err'])) : ?>
                            <p class="mt-2 text-sm text-red-600"><?php echo $data['name_err']; ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Email Field -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">
                            Email address <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                </svg>
                            </div>
                            <input
                                type="email"
                                name="email"
                                id="email"
                                class="pl-10 block w-full border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 py-2 px-3 <?php echo (!empty($data['email_err'])) ? 'border-red-300 text-red-900 placeholder-red-300 focus:outline-none focus:ring-red-500 focus:border-red-500' : 'border-gray-300'; ?>"
                                placeholder="<EMAIL>"
                                value="<?php echo $data['email']; ?>"
                            >
                            <?php if(!empty($data['email_err'])) : ?>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php if(!empty($data['email_err'])) : ?>
                            <p class="mt-2 text-sm text-red-600"><?php echo $data['email_err']; ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Password Field -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">
                            Password <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input
                                type="password"
                                name="password"
                                id="password"
                                class="pl-10 block w-full border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 py-2 px-3 <?php echo (!empty($data['password_err'])) ? 'border-red-300 text-red-900 placeholder-red-300 focus:outline-none focus:ring-red-500 focus:border-red-500' : 'border-gray-300'; ?>"
                                value="<?php echo $data['password']; ?>"
                                onkeyup="checkPasswordStrength(this.value)"
                            >
                            <?php if(!empty($data['password_err'])) : ?>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php if(!empty($data['password_err'])) : ?>
                            <p class="mt-2 text-sm text-red-600"><?php echo $data['password_err']; ?></p>
                        <?php else : ?>
                            <!-- Password Strength Indicator -->
                            <div class="mt-2">
                                <div class="flex items-center">
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-red-500 h-2.5 rounded-full" id="password-strength-meter" style="width: 0%"></div>
                                    </div>
                                    <span class="ml-2 text-sm" id="password-strength-text">Weak</span>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">Password must be at least 8 characters and include uppercase, lowercase, number, and special character.</p>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Confirm Password Field -->
                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700">
                            Confirm Password <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input
                                type="password"
                                name="confirm_password"
                                id="confirm_password"
                                class="pl-10 block w-full border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 py-2 px-3 <?php echo (!empty($data['confirm_password_err'])) ? 'border-red-300 text-red-900 placeholder-red-300 focus:outline-none focus:ring-red-500 focus:border-red-500' : 'border-gray-300'; ?>"
                                value="<?php echo $data['confirm_password']; ?>"
                            >
                            <?php if(!empty($data['confirm_password_err'])) : ?>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php if(!empty($data['confirm_password_err'])) : ?>
                            <p class="mt-2 text-sm text-red-600"><?php echo $data['confirm_password_err']; ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Terms and Conditions -->
                    <!-- <div class="flex items-center">
                        <input id="terms" name="terms" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="terms" class="ml-2 block text-sm text-gray-900">
                            I agree to the <a href="#" class="text-blue-600 hover:text-blue-500">Terms of Service</a>
                            and <a href="#" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>
                        </label>
                    </div> -->

                    <div>
                        <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Create Account
                        </button>
                    </div>
                </form>

                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">
                    Already have an account?
                    <a href="<?php echo URLROOT; ?>/users/login" class="font-medium text-blue-600 hover:text-blue-500">
                        Sign in
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Password Strength Checker Script -->
<script>
function checkPasswordStrength(password) {
    // Initialize variables
    let strength = 0;
    let meter = document.getElementById('password-strength-meter');
    let text = document.getElementById('password-strength-text');

    // Check password length
    if (password.length >= 8) {
        strength += 1;
    }

    // Check for uppercase letters
    if (password.match(/[A-Z]/)) {
        strength += 1;
    }

    // Check for lowercase letters
    if (password.match(/[a-z]/)) {
        strength += 1;
    }

    // Check for numbers
    if (password.match(/[0-9]/)) {
        strength += 1;
    }

    // Check for special characters
    if (password.match(/[^A-Za-z0-9]/)) {
        strength += 1;
    }

    // Update the strength meter
    switch (strength) {
        case 0:
            meter.style.width = '0%';
            meter.className = 'bg-gray-200 h-2.5 rounded-full';
            text.textContent = '';
            break;
        case 1:
            meter.style.width = '20%';
            meter.className = 'bg-red-500 h-2.5 rounded-full';
            text.textContent = 'Very Weak';
            text.className = 'ml-2 text-sm text-red-500';
            break;
        case 2:
            meter.style.width = '40%';
            meter.className = 'bg-orange-500 h-2.5 rounded-full';
            text.textContent = 'Weak';
            text.className = 'ml-2 text-sm text-orange-500';
            break;
        case 3:
            meter.style.width = '60%';
            meter.className = 'bg-yellow-500 h-2.5 rounded-full';
            text.textContent = 'Medium';
            text.className = 'ml-2 text-sm text-yellow-600';
            break;
        case 4:
            meter.style.width = '80%';
            meter.className = 'bg-blue-500 h-2.5 rounded-full';
            text.textContent = 'Strong';
            text.className = 'ml-2 text-sm text-blue-500';
            break;
        case 5:
            meter.style.width = '100%';
            meter.className = 'bg-green-500 h-2.5 rounded-full';
            text.textContent = 'Very Strong';
            text.className = 'ml-2 text-sm text-green-500';
            break;
    }
}
</script>

<?php require APPROOT . '/views/inc/footer.php'; ?>
