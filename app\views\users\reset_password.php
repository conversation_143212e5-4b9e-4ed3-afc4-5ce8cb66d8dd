<?php require APPROOT . '/views/inc/header.php'; ?>

<div class="mb-4 flex justify-between items-center">
    <h1 class="text-2xl font-bold text-gray-800">Reset Password</h1>
    <a href="<?php echo URLROOT; ?>/users/manage" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md">
        <i class="fa fa-backward mr-2"></i> Back to User Management
    </a>
</div>

<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-4 border-b border-gray-200 bg-gray-50">
        <h2 class="text-lg font-semibold text-gray-700">Reset Password for <?php echo $data['user']->name; ?></h2>
    </div>
    
    <div class="p-6">
        <form action="<?php echo URLROOT; ?>/users/resetPassword/<?php echo $data['id']; ?>" method="post">
            <div class="mb-4">
                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                <input type="password" name="password" id="password" 
                       class="block w-full px-3 py-2 border <?php echo (!empty($data['password_err'])) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                <span class="text-red-500 text-xs"><?php echo $data['password_err']; ?></span>
            </div>
            
            <div class="mb-6">
                <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
                <input type="password" name="confirm_password" id="confirm_password" 
                       class="block w-full px-3 py-2 border <?php echo (!empty($data['confirm_password_err'])) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                <span class="text-red-500 text-xs"><?php echo $data['confirm_password_err']; ?></span>
            </div>
            
            <div class="flex items-center justify-between">
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Reset Password
                </button>
                <a href="<?php echo URLROOT; ?>/users/manage" class="text-sm text-gray-600 hover:text-gray-900">Cancel</a>
            </div>
        </form>
    </div>
</div>

<?php require APPROOT . '/views/inc/footer.php'; ?>
