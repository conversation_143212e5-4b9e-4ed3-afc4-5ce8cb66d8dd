<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Connection Check</h1>";

// Load config
echo "Loading configuration...<br>";
try {
    require_once 'app/config/config.php';
    echo "Configuration loaded successfully.<br>";
    echo "DB_HOST: " . DB_HOST . "<br>";
    echo "DB_NAME: " . DB_NAME . "<br>";
    echo "DB_USER: " . DB_USER . "<br>";
    echo "DB_PASS: " . (empty(DB_PASS) ? "empty" : "set") . "<br>";
} catch (Exception $e) {
    echo "Error loading configuration: " . $e->getMessage() . "<br>";
    exit;
}

// Try to connect to the database
echo "<h2>Attempting database connection...</h2>";
try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<strong style='color:green'>Connected to database successfully.</strong><br>";

    // Check if assets table exists and has data
    echo "<h2>Checking assets table...</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'assets'");
    if ($stmt->rowCount() == 0) {
        echo "<strong style='color:red'>Assets table does not exist!</strong><br>";
        exit;
    }

    echo "Assets table exists. Checking for data...<br>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM assets");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Number of assets in database: <strong>" . $result['count'] . "</strong><br>";

    if ($result['count'] > 0) {
        // Get a sample of assets
        $stmt = $pdo->query("SELECT * FROM assets LIMIT 5");
        $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<h3>Sample Assets:</h3>";
        echo "<pre>";
        print_r($assets);
        echo "</pre>";
    } else {
        echo "<strong style='color:red'>No assets found in the database.</strong><br>";
        echo "This explains why no data is shown in the table.";
    }

} catch(PDOException $e) {
    echo "<strong style='color:red'>Connection failed: " . $e->getMessage() . "</strong>";
}
?>
