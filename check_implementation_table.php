<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=asset_visibility', 'root', '');
    
    // Check if table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
    echo 'Table exists: ' . ($stmt->rowCount() > 0 ? 'Yes' : 'No') . '<br>';
    
    if ($stmt->rowCount() > 0) {
        // Get table structure
        $stmt = $pdo->query('DESCRIBE maintenance_guideline_implementation');
        echo "<h3>Table structure:</h3>";
        echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . ($row['Null'] == 'YES' ? 'NULL' : 'NOT NULL') . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check if there are any records
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM maintenance_guideline_implementation");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo '<br>Record count: ' . $result['count'] . '<br>';
        
        if ($result['count'] > 0) {
            // Get sample records
            $stmt = $pdo->query("SELECT * FROM maintenance_guideline_implementation LIMIT 5");
            echo "<h3>Sample records:</h3>";
            echo "<table border='1'><tr>";
            
            // Get column names
            $columns = [];
            for ($i = 0; $i < $stmt->columnCount(); $i++) {
                $col = $stmt->getColumnMeta($i);
                $columns[] = $col['name'];
                echo "<th>" . $col['name'] . "</th>";
            }
            echo "</tr>";
            
            // Get data
            while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr>";
                foreach ($columns as $column) {
                    echo "<td>" . $row[$column] . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
            
            // Check if there are any records with specific guideline_id
            $stmt = $pdo->query("SELECT DISTINCT guideline_id FROM maintenance_guideline_implementation");
            echo "<h3>Distinct guideline_id values:</h3>";
            echo "<ul>";
            while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<li>guideline_id: " . $row['guideline_id'] . "</li>";
            }
            echo "</ul>";
        }
    }
} catch (PDOException $e) {
    echo 'Error: ' . $e->getMessage();
}
?>
