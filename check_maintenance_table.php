<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Maintenance Guideline Implementation Table Check</h1>";

// Load config
require_once 'app/config/config.php';

try {
    // Connect to database
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color:green'>Connected to database successfully.</p>";

    // Check if maintenance_guideline_implementation table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color:red'>maintenance_guideline_implementation table does not exist!</p>";

        // Create the table
        echo "<p>Attempting to create the table...</p>";
        $sql = "CREATE TABLE IF NOT EXISTS maintenance_guideline_implementation (
            id INT AUTO_INCREMENT PRIMARY KEY,
            maintenance_id INT NOT NULL,
            guideline_id INT NOT NULL,
            implemented_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (maintenance_id, guideline_id)
        )";
        $pdo->exec($sql);
        echo "<p style='color:green'>Table created successfully.</p>";
    } else {
        echo "<p style='color:green'>maintenance_guideline_implementation table exists.</p>";

        // Check for records
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM maintenance_guideline_implementation");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>Number of records: <strong>" . $result['count'] . "</strong></p>";

        if ($result['count'] > 0) {
            // Get sample records
            $stmt = $pdo->query("SELECT * FROM maintenance_guideline_implementation LIMIT 5");
            $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo "<h3>Sample Records:</h3>";
            echo "<pre>";
            print_r($records);
            echo "</pre>";
        } else {
            echo "<p style='color:orange'>No records found in the table.</p>";

            // Check maintenance_history table
            $stmt = $pdo->query("SHOW TABLES LIKE 'maintenance_history'");
            if ($stmt->rowCount() > 0) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM maintenance_history");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                echo "<p>Number of maintenance_history records: <strong>" . $result['count'] . "</strong></p>";

                if ($result['count'] > 0) {
                    $stmt = $pdo->query("SELECT * FROM maintenance_history LIMIT 5");
                    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    echo "<h3>Sample Maintenance History Records:</h3>";
                    echo "<pre>";
                    print_r($records);
                    echo "</pre>";
                }
            }

            // Check maintenance_guidelines table
            $stmt = $pdo->query("SHOW TABLES LIKE 'maintenance_guidelines'");
            if ($stmt->rowCount() > 0) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM maintenance_guidelines");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                echo "<p>Number of maintenance_guidelines records: <strong>" . $result['count'] . "</strong></p>";

                if ($result['count'] > 0) {
                    $stmt = $pdo->query("SELECT * FROM maintenance_guidelines LIMIT 5");
                    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    echo "<h3>Sample Maintenance Guidelines Records:</h3>";
                    echo "<pre>";
                    print_r($records);
                    echo "</pre>";
                }
            }
        }
    }

} catch(PDOException $e) {
    echo "<p style='color:red'>Database error: " . $e->getMessage() . "</p>";
}
?>
