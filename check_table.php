<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=asset_visibility', 'root', '');
    $stmt = $pdo->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
    echo 'Table exists: ' . ($stmt->rowCount() > 0 ? 'Yes' : 'No');
    
    if ($stmt->rowCount() > 0) {
        // Check if there are any records in the table
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM maintenance_guideline_implementation");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo '<br>Record count: ' . $result['count'];
    }
} catch (PDOException $e) {
    echo 'Error: ' . $e->getMessage();
}
?>
