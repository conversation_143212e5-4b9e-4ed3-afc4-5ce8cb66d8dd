<?php
/**
 * Advanced Email Debug Script
 * 
 * This script provides detailed debugging for email configuration issues.
 */

// Set longer execution time for testing
set_time_limit(120);

// Load the application bootstrap
require_once 'app/bootstrap.php';

use P<PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
use P<PERSON>Mailer\PHPMailer\Exception;

echo "<h2>Advanced Email Debug</h2>\n";

// Function to test different SMTP configurations
function testSMTPConfig($host, $port, $username, $password, $secure = 'tls', $auth = true) {
    $mail = new PHPMailer(true);
    
    try {
        // Enable verbose debug output
        $mail->SMTPDebug = SMTP::DEBUG_CONNECTION;
        $mail->Debugoutput = function($str, $level) {
            echo "<div style='background: #f0f0f0; padding: 5px; margin: 2px; font-family: monospace; font-size: 12px;'>";
            echo htmlspecialchars($str);
            echo "</div>";
        };
        
        // Set timeout settings
        $mail->Timeout = 30;
        $mail->SMTPKeepAlive = false;
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = $host;
        $mail->SMTPAuth = $auth;
        if ($auth) {
            $mail->Username = $username;
            $mail->Password = $password;
        }
        $mail->SMTPSecure = $secure === 'tls' ? PHPMailer::ENCRYPTION_STARTTLS : PHPMailer::ENCRYPTION_SMTPS;
        $mail->Port = $port;
        
        // Additional SMTP options for better compatibility
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // Recipients
        $mail->setFrom($username, 'Test Sender');
        $mail->addAddress($username); // Send to self

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'SMTP Test - ' . date('Y-m-d H:i:s');
        $mail->Body = '<h3>SMTP Test Successful!</h3><p>Configuration: ' . $host . ':' . $port . '</p>';

        echo "<h4>Testing: $host:$port with " . ($auth ? 'authentication' : 'no authentication') . "</h4>";
        
        $result = $mail->send();
        
        if ($result) {
            echo "<p style='color: green;'><strong>✓ SUCCESS: Email sent successfully!</strong></p>";
            return true;
        } else {
            echo "<p style='color: red;'><strong>✗ FAILED: Could not send email</strong></p>";
            return false;
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'><strong>✗ EXCEPTION:</strong> {$e->getMessage()}</p>";
        echo "<p><strong>Error Info:</strong> {$mail->ErrorInfo}</p>";
        return false;
    }
}

// Test current configuration first
echo "<h3>Testing Current Configuration</h3>";
echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px;'>";
testSMTPConfig(SMTP_HOST, SMTP_PORT, SMTP_USERNAME, SMTP_PASSWORD, SMTP_SECURE, SMTP_AUTH);
echo "</div>";

// Test different configurations for government email
$testConfigs = [
    [
        'name' => 'Office365 TLS',
        'host' => 'smtp.office365.com',
        'port' => 587,
        'secure' => 'tls'
    ],
    [
        'name' => 'Office365 SSL',
        'host' => 'smtp.office365.com', 
        'port' => 465,
        'secure' => 'ssl'
    ],
    [
        'name' => 'Outlook TLS',
        'host' => 'smtp-mail.outlook.com',
        'port' => 587,
        'secure' => 'tls'
    ],
    [
        'name' => 'Gmail TLS',
        'host' => 'smtp.gmail.com',
        'port' => 587,
        'secure' => 'tls'
    ]
];

echo "<h3>Testing Alternative Configurations</h3>";
foreach ($testConfigs as $config) {
    echo "<h4>{$config['name']}</h4>";
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px;'>";
    testSMTPConfig($config['host'], $config['port'], SMTP_USERNAME, SMTP_PASSWORD, $config['secure'], true);
    echo "</div>";
    echo "<hr>";
}

// Test without authentication
echo "<h3>Testing Without Authentication</h3>";
echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px;'>";
testSMTPConfig(SMTP_HOST, SMTP_PORT, '', '', SMTP_SECURE, false);
echo "</div>";

echo "<h3>System Information</h3>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . phpversion() . "</li>";
echo "<li><strong>OpenSSL:</strong> " . (extension_loaded('openssl') ? 'Enabled' : 'Disabled') . "</li>";
echo "<li><strong>cURL:</strong> " . (extension_loaded('curl') ? 'Enabled' : 'Disabled') . "</li>";
echo "<li><strong>Socket:</strong> " . (extension_loaded('sockets') ? 'Enabled' : 'Disabled') . "</li>";
echo "<li><strong>Stream:</strong> " . (function_exists('stream_socket_client') ? 'Available' : 'Not Available') . "</li>";
echo "</ul>";

echo "<h3>Recommendations</h3>";
echo "<ol>";
echo "<li><strong>Contact IT Department:</strong> Government email servers often have specific requirements</li>";
echo "<li><strong>Check Firewall:</strong> Ensure outbound connections on ports 587 and 465 are allowed</li>";
echo "<li><strong>Verify Credentials:</strong> Make sure username and password are correct</li>";
echo "<li><strong>Try Internal SMTP:</strong> Your organization might have internal mail servers</li>";
echo "<li><strong>Use App Password:</strong> If using Office365, you might need an app-specific password</li>";
echo "</ol>";
?>
