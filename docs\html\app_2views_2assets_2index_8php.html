<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/assets/index.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_4bf277b741c35ce534efde8f7dcf6e83.html">assets</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">index.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ab29771c47555edcc9d91bfbe99f84b63" id="r_ab29771c47555edcc9d91bfbe99f84b63"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>('create_assets')) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>( 'import_assets')) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>('export_assets')) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(count( $data[ 'assets']) &gt; 0)( $field, $currentSort, $currentOrder)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab29771c47555edcc9d91bfbe99f84b63">getSortIndicator</a> ($field, $currentSort, $currentOrder)</td></tr>
<tr class="separator:ab29771c47555edcc9d91bfbe99f84b63"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>($data['filter_options']['equipment_types'] as $type)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20c4a031eb504739e03f6014cdfb0055" id="r_a20c4a031eb504739e03f6014cdfb0055"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a20c4a031eb504739e03f6014cdfb0055">$tagModel</a> = new <a class="el" href="class_tag.html">Tag</a>()</td></tr>
<tr class="separator:a20c4a031eb504739e03f6014cdfb0055"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74cea71d3e2cbaff24dfb33210faa0f7" id="r_a74cea71d3e2cbaff24dfb33210faa0f7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a74cea71d3e2cbaff24dfb33210faa0f7">$assetId</a> = isset($asset-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>) ? $asset-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a> : $asset['<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>']</td></tr>
<tr class="separator:a74cea71d3e2cbaff24dfb33210faa0f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf61640e87147db95185d5d01f3b66d1" id="r_abf61640e87147db95185d5d01f3b66d1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abf61640e87147db95185d5d01f3b66d1">$assetTags</a> = $tagModel-&gt;getTagsForAsset($assetId)</td></tr>
<tr class="separator:abf61640e87147db95185d5d01f3b66d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82cd33ca97ff99f2fcc5e9c81d65251b" id="r_a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82cd33ca97ff99f2fcc5e9c81d65251b">endif</a></td></tr>
<tr class="separator:a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a id="ab29771c47555edcc9d91bfbe99f84b63" name="ab29771c47555edcc9d91bfbe99f84b63"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab29771c47555edcc9d91bfbe99f84b63">&#9670;&#160;</a></span>getSortIndicator()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>( 'create_assets')) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>('import_assets')) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>( 'export_assets')) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(count($data['assets']) &gt; 0)($field, $currentSort, $currentOrder) getSortIndicator </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$field</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$currentSort</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$currentOrder</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a74cea71d3e2cbaff24dfb33210faa0f7" name="a74cea71d3e2cbaff24dfb33210faa0f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a74cea71d3e2cbaff24dfb33210faa0f7">&#9670;&#160;</a></span>$assetId</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$assetId = isset($asset-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>) ? $asset-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a> : $asset['<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>']</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abf61640e87147db95185d5d01f3b66d1" name="abf61640e87147db95185d5d01f3b66d1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf61640e87147db95185d5d01f3b66d1">&#9670;&#160;</a></span>$assetTags</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$assetTags = $tagModel-&gt;getTagsForAsset($assetId)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a20c4a031eb504739e03f6014cdfb0055" name="a20c4a031eb504739e03f6014cdfb0055"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a20c4a031eb504739e03f6014cdfb0055">&#9670;&#160;</a></span>$tagModel</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$tagModel = new <a class="el" href="class_tag.html">Tag</a>()</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a82cd33ca97ff99f2fcc5e9c81d65251b" name="a82cd33ca97ff99f2fcc5e9c81d65251b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82cd33ca97ff99f2fcc5e9c81d65251b">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
