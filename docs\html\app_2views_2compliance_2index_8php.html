<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/compliance/index.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_f53799ec3239dd83ce111fd4501b9606.html">compliance</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">index.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a243e196c10a5fb8d371ea4c420892780" id="r_a243e196c10a5fb8d371ea4c420892780"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a243e196c10a5fb8d371ea4c420892780">(empty( $data[ 'frameworks']))</a> ( $data[ 'frameworks'] as $framework)</td></tr>
<tr class="separator:a243e196c10a5fb8d371ea4c420892780"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a53332b4185c5d728f0f11e2fc73fc6c6" id="r_a53332b4185c5d728f0f11e2fc73fc6c6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a53332b4185c5d728f0f11e2fc73fc6c6">if</a> ( $data[ 'selected_framework'])</td></tr>
<tr class="separator:a53332b4185c5d728f0f11e2fc73fc6c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82cd33ca97ff99f2fcc5e9c81d65251b" id="r_a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82cd33ca97ff99f2fcc5e9c81d65251b">endif</a></td></tr>
<tr class="separator:a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b9d42daa53a6dc9fb6156d46d3f63e6" id="r_a3b9d42daa53a6dc9fb6156d46d3f63e6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3b9d42daa53a6dc9fb6156d46d3f63e6">$totalAssets</a> = count($data['compliance_summary'])</td></tr>
<tr class="separator:a3b9d42daa53a6dc9fb6156d46d3f63e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73dbf60765f3b9036cf082caad668726" id="r_a73dbf60765f3b9036cf082caad668726"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a73dbf60765f3b9036cf082caad668726">$totalControls</a> = count($data['control_summary'])</td></tr>
<tr class="separator:a73dbf60765f3b9036cf082caad668726"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a29a31da736043f70eb4c00d38901e709" id="r_a29a31da736043f70eb4c00d38901e709"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a29a31da736043f70eb4c00d38901e709">$compliantAssets</a> = 0</td></tr>
<tr class="separator:a29a31da736043f70eb4c00d38901e709"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b10ee15f2a50ebb857910457f7909eb" id="r_a2b10ee15f2a50ebb857910457f7909eb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2b10ee15f2a50ebb857910457f7909eb">$nonCompliantAssets</a> = 0</td></tr>
<tr class="separator:a2b10ee15f2a50ebb857910457f7909eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af237d81a9ea4bf557609c9a1ee33febf" id="r_af237d81a9ea4bf557609c9a1ee33febf"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af237d81a9ea4bf557609c9a1ee33febf">$inProgressAssets</a> = 0</td></tr>
<tr class="separator:af237d81a9ea4bf557609c9a1ee33febf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2999f88c51c3f083b4d50815639f5080" id="r_a2999f88c51c3f083b4d50815639f5080"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>($data['compliance_summary'] as $summary)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2999f88c51c3f083b4d50815639f5080">$totalCompliance</a> = 0</td></tr>
<tr class="separator:a2999f88c51c3f083b4d50815639f5080"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bbc899e361b1dc3af1d3e5be47fcbe5" id="r_a0bbc899e361b1dc3af1d3e5be47fcbe5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0bbc899e361b1dc3af1d3e5be47fcbe5">$totalAssessments</a> = 0</td></tr>
<tr class="separator:a0bbc899e361b1dc3af1d3e5be47fcbe5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a123663bab6dc36d847b4fdd422ec78b9" id="r_a123663bab6dc36d847b4fdd422ec78b9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>($data['control_summary'] as $control)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a123663bab6dc36d847b4fdd422ec78b9">$overallCompliance</a> = $totalAssessments &gt; 0 ? ($totalCompliance / $totalAssessments) * 100 : 0</td></tr>
<tr class="separator:a123663bab6dc36d847b4fdd422ec78b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a481f92faa68a232509ff785cf4f70e44" id="r_a481f92faa68a232509ff785cf4f70e44"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a481f92faa68a232509ff785cf4f70e44">$barColor</a> = 'bg-red-500'</td></tr>
<tr class="separator:a481f92faa68a232509ff785cf4f70e44"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2fe92cbc651f6f645279ed84f54274dd" id="r_a2fe92cbc651f6f645279ed84f54274dd"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($overallCompliance &gt;=90) elseif( $overallCompliance &gt;=70)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2fe92cbc651f6f645279ed84f54274dd">elseif</a> ( $overallCompliance &gt;=50)</td></tr>
<tr class="separator:a2fe92cbc651f6f645279ed84f54274dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80e005f3bdab5f2680d6494ec89ff8b3" id="r_a80e005f3bdab5f2680d6494ec89ff8b3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a80e005f3bdab5f2680d6494ec89ff8b3">(count( $data[ 'non_compliant_assets']) &gt; 0)</a> ( $data[ 'non_compliant_assets'] as $asset)</td></tr>
<tr class="separator:a80e005f3bdab5f2680d6494ec89ff8b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d4ce20a31e47694d0105327b8502fe6" id="r_a2d4ce20a31e47694d0105327b8502fe6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2d4ce20a31e47694d0105327b8502fe6">$compliancePercentage</a> = 100 - $asset-&gt;non_compliant_percentage</td></tr>
<tr class="separator:a2d4ce20a31e47694d0105327b8502fe6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88b90db63c2b36b85d371bab0ef3b644" id="r_a88b90db63c2b36b85d371bab0ef3b644"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a88b90db63c2b36b85d371bab0ef3b644">(count( $data[ 'control_summary']) &gt; 0)</a> ( $data[ 'control_summary'] as $control)</td></tr>
<tr class="separator:a88b90db63c2b36b85d371bab0ef3b644"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f027137bc02172b9aa60c1c9f1af7ac" id="r_a3f027137bc02172b9aa60c1c9f1af7ac"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3f027137bc02172b9aa60c1c9f1af7ac">$assessedCount</a> = $control-&gt;compliant_count + $control-&gt;non_compliant_count + $control-&gt;not_applicable_count</td></tr>
<tr class="separator:a3f027137bc02172b9aa60c1c9f1af7ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8b4bb1441c6ab4dcb28a37bc46c8ead" id="r_ae8b4bb1441c6ab4dcb28a37bc46c8ead"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae8b4bb1441c6ab4dcb28a37bc46c8ead">__pad1__</a></td></tr>
<tr class="separator:ae8b4bb1441c6ab4dcb28a37bc46c8ead"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a015fdb3803c848a73f2d486cd919da6e" id="r_a015fdb3803c848a73f2d486cd919da6e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a015fdb3803c848a73f2d486cd919da6e">(count( $data[ 'recent_reports']) &gt; 0)</a> ( $data[ 'recent_reports'] as $report)</td></tr>
<tr class="separator:a015fdb3803c848a73f2d486cd919da6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed2d37b4e8da3f52103ae96ce9d26d82" id="r_aed2d37b4e8da3f52103ae96ce9d26d82"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aed2d37b4e8da3f52103ae96ce9d26d82">__pad2__</a></td></tr>
<tr class="separator:aed2d37b4e8da3f52103ae96ce9d26d82"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a3f027137bc02172b9aa60c1c9f1af7ac" name="a3f027137bc02172b9aa60c1c9f1af7ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3f027137bc02172b9aa60c1c9f1af7ac">&#9670;&#160;</a></span>$assessedCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$assessedCount = $control-&gt;compliant_count + $control-&gt;non_compliant_count + $control-&gt;not_applicable_count</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a481f92faa68a232509ff785cf4f70e44" name="a481f92faa68a232509ff785cf4f70e44"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a481f92faa68a232509ff785cf4f70e44">&#9670;&#160;</a></span>$barColor</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$barColor = 'bg-red-500'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2d4ce20a31e47694d0105327b8502fe6" name="a2d4ce20a31e47694d0105327b8502fe6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d4ce20a31e47694d0105327b8502fe6">&#9670;&#160;</a></span>$compliancePercentage</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$compliancePercentage = 100 - $asset-&gt;non_compliant_percentage</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a29a31da736043f70eb4c00d38901e709" name="a29a31da736043f70eb4c00d38901e709"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a29a31da736043f70eb4c00d38901e709">&#9670;&#160;</a></span>$compliantAssets</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$compliantAssets = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af237d81a9ea4bf557609c9a1ee33febf" name="af237d81a9ea4bf557609c9a1ee33febf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af237d81a9ea4bf557609c9a1ee33febf">&#9670;&#160;</a></span>$inProgressAssets</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$inProgressAssets = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2b10ee15f2a50ebb857910457f7909eb" name="a2b10ee15f2a50ebb857910457f7909eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2b10ee15f2a50ebb857910457f7909eb">&#9670;&#160;</a></span>$nonCompliantAssets</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$nonCompliantAssets = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a123663bab6dc36d847b4fdd422ec78b9" name="a123663bab6dc36d847b4fdd422ec78b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a123663bab6dc36d847b4fdd422ec78b9">&#9670;&#160;</a></span>$overallCompliance</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ( $data[ 'control_summary'] as $control) $overallCompliance = $totalAssessments &gt; 0 ? ($totalCompliance / $totalAssessments) * 100 : 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0bbc899e361b1dc3af1d3e5be47fcbe5" name="a0bbc899e361b1dc3af1d3e5be47fcbe5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0bbc899e361b1dc3af1d3e5be47fcbe5">&#9670;&#160;</a></span>$totalAssessments</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$totalAssessments = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3b9d42daa53a6dc9fb6156d46d3f63e6" name="a3b9d42daa53a6dc9fb6156d46d3f63e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3b9d42daa53a6dc9fb6156d46d3f63e6">&#9670;&#160;</a></span>$totalAssets</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$totalAssets = count($data['compliance_summary'])</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2999f88c51c3f083b4d50815639f5080" name="a2999f88c51c3f083b4d50815639f5080"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2999f88c51c3f083b4d50815639f5080">&#9670;&#160;</a></span>$totalCompliance</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ( $data[ 'compliance_summary'] as $summary) $totalCompliance = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a73dbf60765f3b9036cf082caad668726" name="a73dbf60765f3b9036cf082caad668726"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a73dbf60765f3b9036cf082caad668726">&#9670;&#160;</a></span>$totalControls</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$totalControls = count($data['control_summary'])</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a88b90db63c2b36b85d371bab0ef3b644" name="a88b90db63c2b36b85d371bab0ef3b644"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a88b90db63c2b36b85d371bab0ef3b644">&#9670;&#160;</a></span>(count( $data[ 'control_summary']) &gt; 0)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (count($data['control_summary']) &gt; 0)($data['control_summary'] as $control) </td>
          <td>(</td>
          <td class="paramtype">count( $data[ 'control_summary'])</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a80e005f3bdab5f2680d6494ec89ff8b3" name="a80e005f3bdab5f2680d6494ec89ff8b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a80e005f3bdab5f2680d6494ec89ff8b3">&#9670;&#160;</a></span>(count( $data[ 'non_compliant_assets']) &gt; 0)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (count($data['non_compliant_assets']) &gt; 0)($data['non_compliant_assets'] as $asset) </td>
          <td>(</td>
          <td class="paramtype">count( $data[ 'non_compliant_assets'])</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a015fdb3803c848a73f2d486cd919da6e" name="a015fdb3803c848a73f2d486cd919da6e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a015fdb3803c848a73f2d486cd919da6e">&#9670;&#160;</a></span>(count( $data[ 'recent_reports']) &gt; 0)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (count($data['recent_reports']) &gt; 0)($data['recent_reports'] as $report) </td>
          <td>(</td>
          <td class="paramtype">count( $data[ 'recent_reports'])</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a243e196c10a5fb8d371ea4c420892780" name="a243e196c10a5fb8d371ea4c420892780"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a243e196c10a5fb8d371ea4c420892780">&#9670;&#160;</a></span>(empty( $data[ 'frameworks']))</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (empty($data['frameworks']))($data['frameworks'] as $framework) </td>
          <td>(</td>
          <td class="paramtype">empty( $data[ 'frameworks'])</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae8b4bb1441c6ab4dcb28a37bc46c8ead" name="ae8b4bb1441c6ab4dcb28a37bc46c8ead"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae8b4bb1441c6ab4dcb28a37bc46c8ead">&#9670;&#160;</a></span>__pad1__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad1__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aed2d37b4e8da3f52103ae96ce9d26d82" name="aed2d37b4e8da3f52103ae96ce9d26d82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aed2d37b4e8da3f52103ae96ce9d26d82">&#9670;&#160;</a></span>__pad2__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad2__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2fe92cbc651f6f645279ed84f54274dd" name="a2fe92cbc651f6f645279ed84f54274dd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2fe92cbc651f6f645279ed84f54274dd">&#9670;&#160;</a></span>elseif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $compliancePercentage &gt;=90) elseif($compliancePercentage &gt;=70) elseif($compliancePercentage &gt;=50) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$overallCompliance &gt;=</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a82cd33ca97ff99f2fcc5e9c81d65251b" name="a82cd33ca97ff99f2fcc5e9c81d65251b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82cd33ca97ff99f2fcc5e9c81d65251b">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a53332b4185c5d728f0f11e2fc73fc6c6" name="a53332b4185c5d728f0f11e2fc73fc6c6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a53332b4185c5d728f0f11e2fc73fc6c6">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if($data['selected_framework']) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span>[ 'selected_framework']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
