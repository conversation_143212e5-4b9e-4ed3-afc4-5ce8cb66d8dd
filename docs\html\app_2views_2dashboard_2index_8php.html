<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/dashboard/index.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_09cc42e160634d5cd4e1447314d4efc0.html">dashboard</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">index.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:ae38e77fe8d9d76e30ab5136b42f4f299" id="r_ae38e77fe8d9d76e30ab5136b42f4f299"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae38e77fe8d9d76e30ab5136b42f4f299">if</a> (<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>( 'import_assets'))</td></tr>
<tr class="separator:ae38e77fe8d9d76e30ab5136b42f4f299"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3be9ed6dcbf2db80c191f903270f4731" id="r_a3be9ed6dcbf2db80c191f903270f4731"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3be9ed6dcbf2db80c191f903270f4731">foreach</a> ( $data[ 'assetsByEmployee'] as $employee)</td></tr>
<tr class="separator:a3be9ed6dcbf2db80c191f903270f4731"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e3b8598eb20e81f17f0da0e4d0df1fa" id="r_a5e3b8598eb20e81f17f0da0e4d0df1fa"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5e3b8598eb20e81f17f0da0e4d0df1fa">foreach</a> ( $data[ 'assetsByOS'] as $os)</td></tr>
<tr class="separator:a5e3b8598eb20e81f17f0da0e4d0df1fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3be9ed6dcbf2db80c191f903270f4731" name="a3be9ed6dcbf2db80c191f903270f4731"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3be9ed6dcbf2db80c191f903270f4731">&#9670;&#160;</a></span>foreach <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">foreach($data['assetsByEmployee'] as $employee) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'assetsByEmployee']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a5e3b8598eb20e81f17f0da0e4d0df1fa" name="a5e3b8598eb20e81f17f0da0e4d0df1fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5e3b8598eb20e81f17f0da0e4d0df1fa">&#9670;&#160;</a></span>foreach <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">foreach($data['assetsByOS'] as $os) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'assetsByOS']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae38e77fe8d9d76e30ab5136b42f4f299" name="ae38e77fe8d9d76e30ab5136b42f4f299"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae38e77fe8d9d76e30ab5136b42f4f299">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if(<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>('import_assets')) </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>( 'import_assets')</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
