<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/error_logs/index.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_c09662c790968526f76075857adb0866.html">error_logs</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">index.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a9f27ec358be1812cedbb2e90857a47ee" id="r_a9f27ec358be1812cedbb2e90857a47ee"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($data['error_logs']))($data['error_logs'] as $log)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9f27ec358be1812cedbb2e90857a47ee">$levelClass</a> = 'bg-blue-100 text-blue-800'</td></tr>
<tr class="separator:a9f27ec358be1812cedbb2e90857a47ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4bde7d5ea853e6dda100e3831dc3946b" id="r_a4bde7d5ea853e6dda100e3831dc3946b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4bde7d5ea853e6dda100e3831dc3946b">switch</a> ( $log-&gt;level)</td></tr>
<tr class="separator:a4bde7d5ea853e6dda100e3831dc3946b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a10bb4f97f8b642fa51417e7118eca7c9" id="r_a10bb4f97f8b642fa51417e7118eca7c9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $data[ 'current_page'] &gt; 1) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data['current_page']&lt; $data['total_pages'])&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a10bb4f97f8b642fa51417e7118eca7c9">endif</a></td></tr>
<tr class="separator:a10bb4f97f8b642fa51417e7118eca7c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a9f27ec358be1812cedbb2e90857a47ee" name="a9f27ec358be1812cedbb2e90857a47ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f27ec358be1812cedbb2e90857a47ee">&#9670;&#160;</a></span>$levelClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (!empty( $data[ 'error_logs'])) ( $data[ 'error_logs'] as $log) $levelClass = 'bg-blue-100 text-blue-800'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a10bb4f97f8b642fa51417e7118eca7c9" name="a10bb4f97f8b642fa51417e7118eca7c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a10bb4f97f8b642fa51417e7118eca7c9">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data['current_page'] &gt; 1) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $data[ 'current_page']&lt; $data[ 'total_pages']) endif </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4bde7d5ea853e6dda100e3831dc3946b" name="a4bde7d5ea853e6dda100e3831dc3946b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4bde7d5ea853e6dda100e3831dc3946b">&#9670;&#160;</a></span>switch</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">switch($log-&gt;level) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$log-&gt;</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
