<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/finance/index.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_fda66d57a41ca337700983a36a4d49e3.html">finance</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">index.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a284811b011d52a7f346faf819e079383" id="r_a284811b011d52a7f346faf819e079383"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a284811b011d52a7f346faf819e079383">$totalCost</a> = 0</td></tr>
<tr class="separator:a284811b011d52a7f346faf819e079383"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b9d42daa53a6dc9fb6156d46d3f63e6" id="r_a3b9d42daa53a6dc9fb6156d46d3f63e6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3b9d42daa53a6dc9fb6156d46d3f63e6">$totalAssets</a> = 0</td></tr>
<tr class="separator:a3b9d42daa53a6dc9fb6156d46d3f63e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88bcfdf9ca7cdcc0c0f4ca3cf47f55f8" id="r_a88bcfdf9ca7cdcc0c0f4ca3cf47f55f8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a88bcfdf9ca7cdcc0c0f4ca3cf47f55f8">foreach</a> ( $data[ 'costs_by_type'] as $cost)</td></tr>
<tr class="separator:a88bcfdf9ca7cdcc0c0f4ca3cf47f55f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5553b91b143c1b248fca77c94960999a" id="r_a5553b91b143c1b248fca77c94960999a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5553b91b143c1b248fca77c94960999a">(count( $data[ 'costs_by_equipment_type']) &gt; 0)</a> ( $data[ 'costs_by_equipment_type'] as $cost)</td></tr>
<tr class="separator:a5553b91b143c1b248fca77c94960999a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf1f638188316a0955150f271ea199f9" id="r_adf1f638188316a0955150f271ea199f9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adf1f638188316a0955150f271ea199f9">endforeach</a></td></tr>
<tr class="separator:adf1f638188316a0955150f271ea199f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7cb2fb2d2964b1cf70f9c35f1e3c0af" id="r_aa7cb2fb2d2964b1cf70f9c35f1e3c0af"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa7cb2fb2d2964b1cf70f9c35f1e3c0af">$totalProjected</a> = 0</td></tr>
<tr class="separator:aa7cb2fb2d2964b1cf70f9c35f1e3c0af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f5e7dd6e52a56a405eb9dbfd2677708" id="r_a4f5e7dd6e52a56a405eb9dbfd2677708"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(isset($forecast['eol_assets_count']))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4f5e7dd6e52a56a405eb9dbfd2677708">endif</a></td></tr>
<tr class="separator:a4f5e7dd6e52a56a405eb9dbfd2677708"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26bf6c514c5b5c6daf1475c9e0cad453" id="r_a26bf6c514c5b5c6daf1475c9e0cad453"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a26bf6c514c5b5c6daf1475c9e0cad453">$growthClass</a> = 'text-gray-500'</td></tr>
<tr class="separator:a26bf6c514c5b5c6daf1475c9e0cad453"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af4f64bc5f928cddab2505b3d2fc180bc" id="r_af4f64bc5f928cddab2505b3d2fc180bc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $forecast[ 'growth_percentage'] &gt; 0)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af4f64bc5f928cddab2505b3d2fc180bc">elseif</a> ( $forecast[ 'growth_percentage']&lt; 0)</td></tr>
<tr class="separator:af4f64bc5f928cddab2505b3d2fc180bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a570b1ffaf84608acaa9b662240f9296f" id="r_a570b1ffaf84608acaa9b662240f9296f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a570b1ffaf84608acaa9b662240f9296f">$totalGrowth</a> = $totalHistorical &gt; 0 ? (($totalProjected / $totalHistorical) - 1) * 100 : 0</td></tr>
<tr class="separator:a570b1ffaf84608acaa9b662240f9296f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6df4abe9c77f961baffc617d019af60f" id="r_a6df4abe9c77f961baffc617d019af60f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($totalGrowth &gt; 0)($totalGrowth&lt; 0)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6df4abe9c77f961baffc617d019af60f">else</a></td></tr>
<tr class="separator:a6df4abe9c77f961baffc617d019af60f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a26bf6c514c5b5c6daf1475c9e0cad453" name="a26bf6c514c5b5c6daf1475c9e0cad453"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a26bf6c514c5b5c6daf1475c9e0cad453">&#9670;&#160;</a></span>$growthClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$growthClass = 'text-gray-500'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3b9d42daa53a6dc9fb6156d46d3f63e6" name="a3b9d42daa53a6dc9fb6156d46d3f63e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3b9d42daa53a6dc9fb6156d46d3f63e6">&#9670;&#160;</a></span>$totalAssets</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$totalAssets = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a284811b011d52a7f346faf819e079383" name="a284811b011d52a7f346faf819e079383"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a284811b011d52a7f346faf819e079383">&#9670;&#160;</a></span>$totalCost</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$totalCost = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a570b1ffaf84608acaa9b662240f9296f" name="a570b1ffaf84608acaa9b662240f9296f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a570b1ffaf84608acaa9b662240f9296f">&#9670;&#160;</a></span>$totalGrowth</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$totalGrowth = $totalHistorical &gt; 0 ? (($totalProjected / $totalHistorical) - 1) * 100 : 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa7cb2fb2d2964b1cf70f9c35f1e3c0af" name="aa7cb2fb2d2964b1cf70f9c35f1e3c0af"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa7cb2fb2d2964b1cf70f9c35f1e3c0af">&#9670;&#160;</a></span>$totalProjected</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ( $data[ 'budget_forecast'] as $forecast) $totalProjected = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a5553b91b143c1b248fca77c94960999a" name="a5553b91b143c1b248fca77c94960999a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5553b91b143c1b248fca77c94960999a">&#9670;&#160;</a></span>(count( $data[ 'costs_by_equipment_type']) &gt; 0)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (count($data['costs_by_equipment_type']) &gt; 0)($data['costs_by_equipment_type'] as $cost) </td>
          <td>(</td>
          <td class="paramtype">count( $data[ 'costs_by_equipment_type'])</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6df4abe9c77f961baffc617d019af60f" name="a6df4abe9c77f961baffc617d019af60f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6df4abe9c77f961baffc617d019af60f">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $totalGrowth &gt; 0) ( $totalGrowth&lt; 0) else</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af4f64bc5f928cddab2505b3d2fc180bc" name="af4f64bc5f928cddab2505b3d2fc180bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af4f64bc5f928cddab2505b3d2fc180bc">&#9670;&#160;</a></span>elseif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($totalGrowth &gt; 0) elseif($totalGrowth&lt; 0) </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="adf1f638188316a0955150f271ea199f9" name="adf1f638188316a0955150f271ea199f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adf1f638188316a0955150f271ea199f9">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $forecast[ 'growth_percentage'] &gt; 0) ( $forecast[ 'growth_percentage']&lt; 0) endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4f5e7dd6e52a56a405eb9dbfd2677708" name="a4f5e7dd6e52a56a405eb9dbfd2677708"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f5e7dd6e52a56a405eb9dbfd2677708">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (isset( $forecast[ 'eol_assets_count'])) endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a88bcfdf9ca7cdcc0c0f4ca3cf47f55f8" name="a88bcfdf9ca7cdcc0c0f4ca3cf47f55f8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a88bcfdf9ca7cdcc0c0f4ca3cf47f55f8">&#9670;&#160;</a></span>foreach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">foreach($data['costs_by_type'] as $cost) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'costs_by_type']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
