<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/maintenance/index.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_287ed6d8d174ec1b6d586a434511d951.html">maintenance</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">index.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a2c1bcb77c0686b142a36054fc8c0ff82" id="r_a2c1bcb77c0686b142a36054fc8c0ff82"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2c1bcb77c0686b142a36054fc8c0ff82">$criticalCount</a> = 0</td></tr>
<tr class="separator:a2c1bcb77c0686b142a36054fc8c0ff82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1340fb8e5a9e6a079b46d34d100713fb" id="r_a1340fb8e5a9e6a079b46d34d100713fb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1340fb8e5a9e6a079b46d34d100713fb">foreach</a> ( $data[ 'assets_health'] as $asset)</td></tr>
<tr class="separator:a1340fb8e5a9e6a079b46d34d100713fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a57234fb07677f1698ffd77858ed94f76" id="r_a57234fb07677f1698ffd77858ed94f76"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a57234fb07677f1698ffd77858ed94f76">$totalScore</a> = 0</td></tr>
<tr class="separator:a57234fb07677f1698ffd77858ed94f76"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0564d98bf5dd495a3827a3a9b4cf74b8" id="r_a0564d98bf5dd495a3827a3a9b4cf74b8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0564d98bf5dd495a3827a3a9b4cf74b8">$validCount</a> = 0</td></tr>
<tr class="separator:a0564d98bf5dd495a3827a3a9b4cf74b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9507c0d7c11912b019f9c1de81865e56" id="r_a9507c0d7c11912b019f9c1de81865e56"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>( $data[ 'assets_health'] as $asset) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($validCount &gt; 0)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9507c0d7c11912b019f9c1de81865e56">else</a></td></tr>
<tr class="separator:a9507c0d7c11912b019f9c1de81865e56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a87e6ad6ae335c64338f0832e043b573c" id="r_a87e6ad6ae335c64338f0832e043b573c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a87e6ad6ae335c64338f0832e043b573c">(count( $data[ 'assets_due']) &gt; 0)</a> ( $data[ 'assets_due'] as $asset)</td></tr>
<tr class="separator:a87e6ad6ae335c64338f0832e043b573c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f269df22fd4acadaf5438a2f6994341" id="r_a6f269df22fd4acadaf5438a2f6994341"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6f269df22fd4acadaf5438a2f6994341">$daysRemaining</a> = $asset-&gt;days_remaining</td></tr>
<tr class="separator:a6f269df22fd4acadaf5438a2f6994341"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab99343d4912650d1363355564aa6c179" id="r_ab99343d4912650d1363355564aa6c179"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab99343d4912650d1363355564aa6c179">$textColor</a> = 'text-green-500'</td></tr>
<tr class="separator:ab99343d4912650d1363355564aa6c179"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a810457ca020359cc6ac82360b6524b67" id="r_a810457ca020359cc6ac82360b6524b67"><td class="memItemLeft" align="right" valign="top">if( $daysRemaining&lt;=7) <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a810457ca020359cc6ac82360b6524b67">if</a> ( $daysRemaining&lt;=14)</td></tr>
<tr class="separator:a810457ca020359cc6ac82360b6524b67"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af90db8bde5b9826e549922ed58ea68f6" id="r_af90db8bde5b9826e549922ed58ea68f6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af90db8bde5b9826e549922ed58ea68f6">(count( $data[ 'assets_health']) &gt; 0)</a> ( $data[ 'order_by']=='computer_host_name')</td></tr>
<tr class="separator:af90db8bde5b9826e549922ed58ea68f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a6589cf136d38601621ed34cf1c63b9" id="r_a1a6589cf136d38601621ed34cf1c63b9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1a6589cf136d38601621ed34cf1c63b9">if</a> ( $data[ 'order_by']=='equipment_type')</td></tr>
<tr class="separator:a1a6589cf136d38601621ed34cf1c63b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a976c0aadcca5628cd02713ca4890b341" id="r_a976c0aadcca5628cd02713ca4890b341"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $data[ 'order_by']=='health_score')&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a976c0aadcca5628cd02713ca4890b341">foreach</a> ( $data[ 'assets_health'] as $asset)</td></tr>
<tr class="separator:a976c0aadcca5628cd02713ca4890b341"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ccb94cadb093164e431630a7020d49f" id="r_a1ccb94cadb093164e431630a7020d49f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1ccb94cadb093164e431630a7020d49f">$healthScore</a> = $asset-&gt;health_score ?? 0</td></tr>
<tr class="separator:a1ccb94cadb093164e431630a7020d49f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab82a04539e3f3415ec35adedf87b141f" id="r_ab82a04539e3f3415ec35adedf87b141f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab82a04539e3f3415ec35adedf87b141f">$bgColor</a> = 'bg-green-500'</td></tr>
<tr class="separator:ab82a04539e3f3415ec35adedf87b141f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac51d4079cb386b1268110b732f7f9405" id="r_ac51d4079cb386b1268110b732f7f9405"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac51d4079cb386b1268110b732f7f9405">$statusText</a> = 'Good'</td></tr>
<tr class="separator:ac51d4079cb386b1268110b732f7f9405"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acc352a22ca4439892ce16877974a71b7" id="r_acc352a22ca4439892ce16877974a71b7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data['current_page'] &gt; 1)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acc352a22ca4439892ce16877974a71b7">$startPage</a> = max(1, $data['current_page'] - 2)</td></tr>
<tr class="separator:acc352a22ca4439892ce16877974a71b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae12f8ad07b55a483cd345c1261d56a92" id="r_ae12f8ad07b55a483cd345c1261d56a92"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae12f8ad07b55a483cd345c1261d56a92">$endPage</a> = min($data['total_pages'], $data['current_page'] + 2)</td></tr>
<tr class="separator:ae12f8ad07b55a483cd345c1261d56a92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82cd33ca97ff99f2fcc5e9c81d65251b" id="r_a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $startPage &gt; 1) for($i=$startPage; $i&lt;=$endPage; $i++) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $endPage&lt; $data[ 'total_pages']) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data['current_page']&lt; $data['total_pages'])&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82cd33ca97ff99f2fcc5e9c81d65251b">endif</a></td></tr>
<tr class="separator:a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8b4bb1441c6ab4dcb28a37bc46c8ead" id="r_ae8b4bb1441c6ab4dcb28a37bc46c8ead"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae8b4bb1441c6ab4dcb28a37bc46c8ead">__pad1__</a></td></tr>
<tr class="separator:ae8b4bb1441c6ab4dcb28a37bc46c8ead"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed2d37b4e8da3f52103ae96ce9d26d82" id="r_aed2d37b4e8da3f52103ae96ce9d26d82"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aed2d37b4e8da3f52103ae96ce9d26d82">__pad2__</a></td></tr>
<tr class="separator:aed2d37b4e8da3f52103ae96ce9d26d82"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="ab82a04539e3f3415ec35adedf87b141f" name="ab82a04539e3f3415ec35adedf87b141f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab82a04539e3f3415ec35adedf87b141f">&#9670;&#160;</a></span>$bgColor</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$bgColor = 'bg-green-500'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2c1bcb77c0686b142a36054fc8c0ff82" name="a2c1bcb77c0686b142a36054fc8c0ff82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2c1bcb77c0686b142a36054fc8c0ff82">&#9670;&#160;</a></span>$criticalCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$criticalCount = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6f269df22fd4acadaf5438a2f6994341" name="a6f269df22fd4acadaf5438a2f6994341"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6f269df22fd4acadaf5438a2f6994341">&#9670;&#160;</a></span>$daysRemaining</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$daysRemaining = $asset-&gt;days_remaining</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae12f8ad07b55a483cd345c1261d56a92" name="ae12f8ad07b55a483cd345c1261d56a92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae12f8ad07b55a483cd345c1261d56a92">&#9670;&#160;</a></span>$endPage</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$endPage = min($data['total_pages'], $data['current_page'] + 2)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1ccb94cadb093164e431630a7020d49f" name="a1ccb94cadb093164e431630a7020d49f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1ccb94cadb093164e431630a7020d49f">&#9670;&#160;</a></span>$healthScore</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$healthScore = $asset-&gt;health_score ?? 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="acc352a22ca4439892ce16877974a71b7" name="acc352a22ca4439892ce16877974a71b7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acc352a22ca4439892ce16877974a71b7">&#9670;&#160;</a></span>$startPage</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $data[ 'current_page'] &gt; 1) $startPage = max(1, $data['current_page'] - 2)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac51d4079cb386b1268110b732f7f9405" name="ac51d4079cb386b1268110b732f7f9405"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac51d4079cb386b1268110b732f7f9405">&#9670;&#160;</a></span>$statusText</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$statusText = 'Good'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab99343d4912650d1363355564aa6c179" name="ab99343d4912650d1363355564aa6c179"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab99343d4912650d1363355564aa6c179">&#9670;&#160;</a></span>$textColor</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$textColor = 'text-green-500'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a57234fb07677f1698ffd77858ed94f76" name="a57234fb07677f1698ffd77858ed94f76"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a57234fb07677f1698ffd77858ed94f76">&#9670;&#160;</a></span>$totalScore</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$totalScore = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0564d98bf5dd495a3827a3a9b4cf74b8" name="a0564d98bf5dd495a3827a3a9b4cf74b8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0564d98bf5dd495a3827a3a9b4cf74b8">&#9670;&#160;</a></span>$validCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$validCount = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a87e6ad6ae335c64338f0832e043b573c" name="a87e6ad6ae335c64338f0832e043b573c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a87e6ad6ae335c64338f0832e043b573c">&#9670;&#160;</a></span>(count( $data[ 'assets_due']) &gt; 0)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (count($data['assets_due']) &gt; 0)($data['assets_due'] as $asset) </td>
          <td>(</td>
          <td class="paramtype">count( $data[ 'assets_due'])</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af90db8bde5b9826e549922ed58ea68f6" name="af90db8bde5b9826e549922ed58ea68f6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af90db8bde5b9826e549922ed58ea68f6">&#9670;&#160;</a></span>(count( $data[ 'assets_health']) &gt; 0)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (count($data['assets_health']) &gt; 0)($data['order_by']=='computer_host_name') </td>
          <td>(</td>
          <td class="paramtype">count( $data[ 'assets_health'])</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae8b4bb1441c6ab4dcb28a37bc46c8ead" name="ae8b4bb1441c6ab4dcb28a37bc46c8ead"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae8b4bb1441c6ab4dcb28a37bc46c8ead">&#9670;&#160;</a></span>__pad1__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad1__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aed2d37b4e8da3f52103ae96ce9d26d82" name="aed2d37b4e8da3f52103ae96ce9d26d82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aed2d37b4e8da3f52103ae96ce9d26d82">&#9670;&#160;</a></span>__pad2__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad2__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9507c0d7c11912b019f9c1de81865e56" name="a9507c0d7c11912b019f9c1de81865e56"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9507c0d7c11912b019f9c1de81865e56">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>($data['assets_health'] as $asset) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $validCount &gt; 0) else</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">                        echo <span class="stringliteral">&#39;N/A&#39;</span></div>
</div><!-- fragment -->
</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a82cd33ca97ff99f2fcc5e9c81d65251b" name="a82cd33ca97ff99f2fcc5e9c81d65251b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82cd33ca97ff99f2fcc5e9c81d65251b">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1340fb8e5a9e6a079b46d34d100713fb" name="a1340fb8e5a9e6a079b46d34d100713fb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1340fb8e5a9e6a079b46d34d100713fb">&#9670;&#160;</a></span>foreach <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">foreach($data['assets_health'] as $asset) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'assets_health']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a976c0aadcca5628cd02713ca4890b341" name="a976c0aadcca5628cd02713ca4890b341"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a976c0aadcca5628cd02713ca4890b341">&#9670;&#160;</a></span>foreach <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data['order_by']=='health_score') foreach($data['assets_health'] as $asset) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'assets_health']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1a6589cf136d38601621ed34cf1c63b9" name="a1a6589cf136d38601621ed34cf1c63b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a6589cf136d38601621ed34cf1c63b9">&#9670;&#160;</a></span>if <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if($data['order_by']=='equipment_type') </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span>[ 'order_by']<span class="paramdefsep"> = </span><span class="paramdefval">=&#160;'equipment_type'</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a810457ca020359cc6ac82360b6524b67" name="a810457ca020359cc6ac82360b6524b67"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a810457ca020359cc6ac82360b6524b67">&#9670;&#160;</a></span>if <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if($healthScore&lt; 40) <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> if($healthScore&lt; 70) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$daysRemaining&lt;=</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
