<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/roles/index.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_b7635b3103214860ed22671372c5f3ca.html">roles</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">index.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:ab78450e16a061bbc11f72bf1f8e27d3c" id="r_ab78450e16a061bbc11f72bf1f8e27d3c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab78450e16a061bbc11f72bf1f8e27d3c">if</a> ( $role-&gt;is_system_role)</td></tr>
<tr class="separator:ab78450e16a061bbc11f72bf1f8e27d3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1503edf98562298d8024253085f1f76d" id="r_a1503edf98562298d8024253085f1f76d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(! $role-&gt;is_system_role)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1503edf98562298d8024253085f1f76d">endforeach</a></td></tr>
<tr class="separator:a1503edf98562298d8024253085f1f76d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a585ca23b0e2286d604231a36cf1e8d96" id="r_a585ca23b0e2286d604231a36cf1e8d96"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a585ca23b0e2286d604231a36cf1e8d96">if</a> (empty( $data[ 'roles']))</td></tr>
<tr class="separator:a585ca23b0e2286d604231a36cf1e8d96"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a1503edf98562298d8024253085f1f76d" name="a1503edf98562298d8024253085f1f76d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1503edf98562298d8024253085f1f76d">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (! $role-&gt;is_system_role) endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab78450e16a061bbc11f72bf1f8e27d3c" name="ab78450e16a061bbc11f72bf1f8e27d3c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab78450e16a061bbc11f72bf1f8e27d3c">&#9670;&#160;</a></span>if <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if($role-&gt;is_system_role) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$role-&gt;</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a585ca23b0e2286d604231a36cf1e8d96" name="a585ca23b0e2286d604231a36cf1e8d96"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a585ca23b0e2286d604231a36cf1e8d96">&#9670;&#160;</a></span>if <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if(empty($data['roles'])) </td>
          <td>(</td>
          <td class="paramtype">empty( $data[ 'roles'])</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
