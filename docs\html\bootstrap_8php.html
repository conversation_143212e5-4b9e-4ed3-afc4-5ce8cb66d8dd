<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/bootstrap.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">bootstrap.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a8757a57d09df5349f8b93b2083e29a1e" id="r_a8757a57d09df5349f8b93b2083e29a1e"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8757a57d09df5349f8b93b2083e29a1e">ENVIRONMENT</a> 'development'</td></tr>
<tr class="separator:a8757a57d09df5349f8b93b2083e29a1e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01d711562d54e54d71a9c7244d0acc77" id="r_a01d711562d54e54d71a9c7244d0acc77"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a01d711562d54e54d71a9c7244d0acc77">$logsDir</a> = <a class="el" href="config_8php.html#a10375bcd2448d71fb65abb104c207203">APPROOT</a> . '/logs'</td></tr>
<tr class="separator:a01d711562d54e54d71a9c7244d0acc77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af75becf76e03572890c9841a9295e925" id="r_af75becf76e03572890c9841a9295e925"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af75becf76e03572890c9841a9295e925">if</a> (!file_exists( $logsDir))</td></tr>
<tr class="separator:af75becf76e03572890c9841a9295e925"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a01d711562d54e54d71a9c7244d0acc77" name="a01d711562d54e54d71a9c7244d0acc77"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a01d711562d54e54d71a9c7244d0acc77">&#9670;&#160;</a></span>$logsDir</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$logsDir = <a class="el" href="config_8php.html#a10375bcd2448d71fb65abb104c207203">APPROOT</a> . '/logs'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8757a57d09df5349f8b93b2083e29a1e" name="a8757a57d09df5349f8b93b2083e29a1e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8757a57d09df5349f8b93b2083e29a1e">&#9670;&#160;</a></span>ENVIRONMENT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const ENVIRONMENT 'development'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af75becf76e03572890c9841a9295e925" name="af75becf76e03572890c9841a9295e925"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af75becf76e03572890c9841a9295e925">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype">!</td>          <td class="paramname"><span class="paramname"><em>file_exists</em></span> $logsDir<span class="paramdefsep"> = </span><span class="paramdefval">=&#160;'overdue'</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">            <a class="code hl_variable" href="create__guideline__implementation__table_8php.html#a73004ce9cd673c1bfafd1dc351134797">$output</a>[] = <span class="stringliteral">&quot;Table structure looks good (no UNIQUE constraint).&quot;</span></div>
<div class="ttc" id="acreate__guideline__implementation__table_8php_html_a73004ce9cd673c1bfafd1dc351134797"><div class="ttname"><a href="create__guideline__implementation__table_8php.html#a73004ce9cd673c1bfafd1dc351134797">$output</a></div><div class="ttdeci">$output</div><div class="ttdef"><b>Definition</b> create_guideline_implementation_table.php:18</div></div>
</div><!-- fragment -->
</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
