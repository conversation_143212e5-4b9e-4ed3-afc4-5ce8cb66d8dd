<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: check_db.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">check_db.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:abe4cc9788f52e49485473dc699537388" id="r_abe4cc9788f52e49485473dc699537388"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abe4cc9788f52e49485473dc699537388">try</a></td></tr>
<tr class="separator:abe4cc9788f52e49485473dc699537388"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ee42195f2b26ca51b7b816b4f28113e" id="r_a9ee42195f2b26ca51b7b816b4f28113e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9ee42195f2b26ca51b7b816b4f28113e">catch</a> (Exception $<a class="el" href="output__helper_8php.html#a18d38faad6177eda235a3d9d28572984">e</a>)</td></tr>
<tr class="separator:a9ee42195f2b26ca51b7b816b4f28113e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af27a9140d5f2658693e7fd107f716449" id="r_af27a9140d5f2658693e7fd107f716449"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af27a9140d5f2658693e7fd107f716449">$stmt</a> = $pdo-&gt;query(&quot;SHOW TABLES LIKE 'assets'&quot;)</td></tr>
<tr class="separator:af27a9140d5f2658693e7fd107f716449"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f8cf61e47812c53a24ccf689c122172" id="r_a6f8cf61e47812c53a24ccf689c122172"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6f8cf61e47812c53a24ccf689c122172">if</a> ( $stmt-&gt;rowCount()==0)</td></tr>
<tr class="separator:a6f8cf61e47812c53a24ccf689c122172"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a112ef069ddc0454086e3d1e6d8d55d07" id="r_a112ef069ddc0454086e3d1e6d8d55d07"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a112ef069ddc0454086e3d1e6d8d55d07">$result</a> = $stmt-&gt;fetch(PDO::FETCH_ASSOC)</td></tr>
<tr class="separator:a112ef069ddc0454086e3d1e6d8d55d07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3750c7492008b48abc325797bf4cd5a6" id="r_a3750c7492008b48abc325797bf4cd5a6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($result['count'] &gt; 0)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3750c7492008b48abc325797bf4cd5a6">else</a></td></tr>
<tr class="separator:a3750c7492008b48abc325797bf4cd5a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a112ef069ddc0454086e3d1e6d8d55d07" name="a112ef069ddc0454086e3d1e6d8d55d07"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a112ef069ddc0454086e3d1e6d8d55d07">&#9670;&#160;</a></span>$result</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$result = $stmt-&gt;fetch(PDO::FETCH_ASSOC)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af27a9140d5f2658693e7fd107f716449" name="af27a9140d5f2658693e7fd107f716449"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af27a9140d5f2658693e7fd107f716449">&#9670;&#160;</a></span>$stmt</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$stmt = $pdo-&gt;query(&quot;SHOW TABLES LIKE 'assets'&quot;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9ee42195f2b26ca51b7b816b4f28113e" name="a9ee42195f2b26ca51b7b816b4f28113e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9ee42195f2b26ca51b7b816b4f28113e">&#9670;&#160;</a></span>catch</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">catch(Exception $<a class="el" href="output__helper_8php.html#a18d38faad6177eda235a3d9d28572984">e</a>) </td>
          <td>(</td>
          <td class="paramtype">Exception</td>          <td class="paramname"><span class="paramname"><em>$e</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3750c7492008b48abc325797bf4cd5a6" name="a3750c7492008b48abc325797bf4cd5a6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3750c7492008b48abc325797bf4cd5a6">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $result[ 'count'] &gt; 0) else</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">        echo <span class="stringliteral">&quot;&lt;strong style=&#39;color:red&#39;&gt;No assets found in the database.&lt;/strong&gt;&lt;br&gt;&quot;</span></div>
</div><!-- fragment -->
</div>
</div>
<a id="a6f8cf61e47812c53a24ccf689c122172" name="a6f8cf61e47812c53a24ccf689c122172"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6f8cf61e47812c53a24ccf689c122172">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if($stmt-&gt;rowCount()==0) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$stmt-&gt;</em></span>()<span class="paramdefsep"> = </span><span class="paramdefval">=&#160;0</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abe4cc9788f52e49485473dc699537388" name="abe4cc9788f52e49485473dc699537388"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe4cc9788f52e49485473dc699537388">&#9670;&#160;</a></span>try</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">try</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">    require_once <span class="stringliteral">&#39;app/config/config.php&#39;</span></div>
</div><!-- fragment -->
</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
