<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Compliance Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">Compliance Class Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for Compliance:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_compliance.png" usemap="#Compliance_map" alt=""/>
  <map id="Compliance_map" name="Compliance_map">
<area href="class_controller.html" alt="Controller" shape="rect" coords="0,0,78,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a149eb92716c1084a935e04a8d95f7347" id="r_a149eb92716c1084a935e04a8d95f7347"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a149eb92716c1084a935e04a8d95f7347">index</a> ()</td></tr>
<tr class="separator:a149eb92716c1084a935e04a8d95f7347"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa462bba054828b894e52f915ead6345" id="r_aaa462bba054828b894e52f915ead6345"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaa462bba054828b894e52f915ead6345">framework</a> ($frameworkId)</td></tr>
<tr class="separator:aaa462bba054828b894e52f915ead6345"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9712d2ec27afddb5c060abd82a8fc39" id="r_aa9712d2ec27afddb5c060abd82a8fc39"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa9712d2ec27afddb5c060abd82a8fc39">asset</a> ($assetId, $frameworkId)</td></tr>
<tr class="separator:aa9712d2ec27afddb5c060abd82a8fc39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad98cc30b3386d3b1e3ceda687803ede2" id="r_ad98cc30b3386d3b1e3ceda687803ede2"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad98cc30b3386d3b1e3ceda687803ede2">update</a> ($assetId, $controlId)</td></tr>
<tr class="separator:ad98cc30b3386d3b1e3ceda687803ede2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa33e9836cc6af4b321a1586206e77193" id="r_aa33e9836cc6af4b321a1586206e77193"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa33e9836cc6af4b321a1586206e77193">generate</a> ($frameworkId)</td></tr>
<tr class="separator:aa33e9836cc6af4b321a1586206e77193"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7912ba6d06a4a6cf2a4b9b59a42348a" id="r_ac7912ba6d06a4a6cf2a4b9b59a42348a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac7912ba6d06a4a6cf2a4b9b59a42348a">report</a> ($reportId)</td></tr>
<tr class="separator:ac7912ba6d06a4a6cf2a4b9b59a42348a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_class_controller"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_class_controller')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="class_controller.html">Controller</a></td></tr>
<tr class="memitem:ac531eb761b130b1925a8bae5c33af2fc inherit pub_methods_class_controller" id="r_ac531eb761b130b1925a8bae5c33af2fc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#ac531eb761b130b1925a8bae5c33af2fc">model</a> ($model)</td></tr>
<tr class="separator:ac531eb761b130b1925a8bae5c33af2fc inherit pub_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11f0e20b30b899d00b009a9bb1afe43d inherit pub_methods_class_controller" id="r_a11f0e20b30b899d00b009a9bb1afe43d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#a11f0e20b30b899d00b009a9bb1afe43d">view</a> ($view, $data=[])</td></tr>
<tr class="separator:a11f0e20b30b899d00b009a9bb1afe43d inherit pub_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="inherited" name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pro_methods_class_controller"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pro_methods_class_controller')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="class_controller.html">Controller</a></td></tr>
<tr class="memitem:a0d92de8136cebc006a407442aab9db0a inherit pro_methods_class_controller" id="r_a0d92de8136cebc006a407442aab9db0a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#a0d92de8136cebc006a407442aab9db0a">sanitizePostData</a> ($data)</td></tr>
<tr class="separator:a0d92de8136cebc006a407442aab9db0a inherit pro_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf7b7d5aa2f9ec7a1f79646322121f52 inherit pro_methods_class_controller" id="r_aaf7b7d5aa2f9ec7a1f79646322121f52"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#aaf7b7d5aa2f9ec7a1f79646322121f52">validateCsrfToken</a> ($token)</td></tr>
<tr class="separator:aaf7b7d5aa2f9ec7a1f79646322121f52 inherit pro_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="aa9712d2ec27afddb5c060abd82a8fc39" name="aa9712d2ec27afddb5c060abd82a8fc39"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa9712d2ec27afddb5c060abd82a8fc39">&#9670;&#160;</a></span>asset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">asset </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$frameworkId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>View asset compliance details</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$frameworkId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aaa462bba054828b894e52f915ead6345" name="aaa462bba054828b894e52f915ead6345"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaa462bba054828b894e52f915ead6345">&#9670;&#160;</a></span>framework()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">framework </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$frameworkId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>View compliance dashboard for a specific framework</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$frameworkId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa33e9836cc6af4b321a1586206e77193" name="aa33e9836cc6af4b321a1586206e77193"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa33e9836cc6af4b321a1586206e77193">&#9670;&#160;</a></span>generate()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">generate </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$frameworkId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Generate compliance report</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$frameworkId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a149eb92716c1084a935e04a8d95f7347" name="a149eb92716c1084a935e04a8d95f7347"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a149eb92716c1084a935e04a8d95f7347">&#9670;&#160;</a></span>index()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">index </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p><a class="el" href="class_compliance.html">Compliance</a> dashboard </p>

</div>
</div>
<a id="ac7912ba6d06a4a6cf2a4b9b59a42348a" name="ac7912ba6d06a4a6cf2a4b9b59a42348a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac7912ba6d06a4a6cf2a4b9b59a42348a">&#9670;&#160;</a></span>report()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">report </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$reportId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>View compliance report</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$reportId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ad98cc30b3386d3b1e3ceda687803ede2" name="ad98cc30b3386d3b1e3ceda687803ede2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad98cc30b3386d3b1e3ceda687803ede2">&#9670;&#160;</a></span>update()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">update </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$controlId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update asset compliance status</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$controlId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/controllers/<a class="el" href="_compliance_8php.html">Compliance.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
