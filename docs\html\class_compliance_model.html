<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: ComplianceModel Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">ComplianceModel Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a48611d2953815ea9e440be48270e5b41" id="r_a48611d2953815ea9e440be48270e5b41"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a48611d2953815ea9e440be48270e5b41">getFrameworks</a> ()</td></tr>
<tr class="separator:a48611d2953815ea9e440be48270e5b41"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a6f89d1e69db5f6f7ae1f97dde53d2e" id="r_a2a6f89d1e69db5f6f7ae1f97dde53d2e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2a6f89d1e69db5f6f7ae1f97dde53d2e">getFrameworkById</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a2a6f89d1e69db5f6f7ae1f97dde53d2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeac9b1b5819ae5e2970cfbd1a7597f76" id="r_aeac9b1b5819ae5e2970cfbd1a7597f76"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aeac9b1b5819ae5e2970cfbd1a7597f76">getControlsByFramework</a> ($frameworkId)</td></tr>
<tr class="separator:aeac9b1b5819ae5e2970cfbd1a7597f76"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7feafecf43729d726a17800a2274562f" id="r_a7feafecf43729d726a17800a2274562f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7feafecf43729d726a17800a2274562f">getAssetComplianceStatus</a> ($assetId, $frameworkId)</td></tr>
<tr class="separator:a7feafecf43729d726a17800a2274562f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77ab529e36423aae6bc0a5b8f5cc6b7a" id="r_a77ab529e36423aae6bc0a5b8f5cc6b7a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a77ab529e36423aae6bc0a5b8f5cc6b7a">updateAssetCompliance</a> ($data)</td></tr>
<tr class="separator:a77ab529e36423aae6bc0a5b8f5cc6b7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a814c5d44551a1e675a00c71ca3742297" id="r_a814c5d44551a1e675a00c71ca3742297"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a814c5d44551a1e675a00c71ca3742297">getComplianceSummary</a> ($frameworkId)</td></tr>
<tr class="separator:a814c5d44551a1e675a00c71ca3742297"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06766d64303589b753c200226d796052" id="r_a06766d64303589b753c200226d796052"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a06766d64303589b753c200226d796052">getComplianceSummaryByControl</a> ($frameworkId)</td></tr>
<tr class="separator:a06766d64303589b753c200226d796052"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acc7bd2f33a48df1f96004c6ac83bc452" id="r_acc7bd2f33a48df1f96004c6ac83bc452"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acc7bd2f33a48df1f96004c6ac83bc452">generateReport</a> ($frameworkId, $userId)</td></tr>
<tr class="separator:acc7bd2f33a48df1f96004c6ac83bc452"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7276523dbcf22869eb572eaf69c1e792" id="r_a7276523dbcf22869eb572eaf69c1e792"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7276523dbcf22869eb572eaf69c1e792">getReportById</a> ($reportId)</td></tr>
<tr class="separator:a7276523dbcf22869eb572eaf69c1e792"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f36be93a3051b76fd781c4d27576bae" id="r_a3f36be93a3051b76fd781c4d27576bae"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3f36be93a3051b76fd781c4d27576bae">getRecentReports</a> ($limit=5)</td></tr>
<tr class="separator:a3f36be93a3051b76fd781c4d27576bae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad933a2ed9c35310e79d219954f8fed3e" id="r_ad933a2ed9c35310e79d219954f8fed3e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad933a2ed9c35310e79d219954f8fed3e">getNonCompliantAssets</a> ($frameworkId, $limit=10)</td></tr>
<tr class="separator:ad933a2ed9c35310e79d219954f8fed3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0418c1b96081854db5489ed186ad1337" id="r_a0418c1b96081854db5489ed186ad1337"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0418c1b96081854db5489ed186ad1337">getControlById</a> ($controlId)</td></tr>
<tr class="separator:a0418c1b96081854db5489ed186ad1337"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13e3ddd0e6d5a876f6d7b100a6f24acd" id="r_a13e3ddd0e6d5a876f6d7b100a6f24acd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a13e3ddd0e6d5a876f6d7b100a6f24acd">getAssetControlStatus</a> ($assetId, $controlId)</td></tr>
<tr class="separator:a13e3ddd0e6d5a876f6d7b100a6f24acd"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="acc7bd2f33a48df1f96004c6ac83bc452" name="acc7bd2f33a48df1f96004c6ac83bc452"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acc7bd2f33a48df1f96004c6ac83bc452">&#9670;&#160;</a></span>generateReport()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">generateReport </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$frameworkId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Generate compliance report</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$frameworkId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int Report ID </dd></dl>

</div>
</div>
<a id="a7feafecf43729d726a17800a2274562f" name="a7feafecf43729d726a17800a2274562f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7feafecf43729d726a17800a2274562f">&#9670;&#160;</a></span>getAssetComplianceStatus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetComplianceStatus </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$frameworkId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get compliance status for an asset</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$frameworkId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a13e3ddd0e6d5a876f6d7b100a6f24acd" name="a13e3ddd0e6d5a876f6d7b100a6f24acd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a13e3ddd0e6d5a876f6d7b100a6f24acd">&#9670;&#160;</a></span>getAssetControlStatus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetControlStatus </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$controlId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get compliance status for an asset and control</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$controlId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object </dd></dl>

</div>
</div>
<a id="a814c5d44551a1e675a00c71ca3742297" name="a814c5d44551a1e675a00c71ca3742297"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a814c5d44551a1e675a00c71ca3742297">&#9670;&#160;</a></span>getComplianceSummary()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getComplianceSummary </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$frameworkId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get compliance summary for all assets</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$frameworkId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a06766d64303589b753c200226d796052" name="a06766d64303589b753c200226d796052"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a06766d64303589b753c200226d796052">&#9670;&#160;</a></span>getComplianceSummaryByControl()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getComplianceSummaryByControl </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$frameworkId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get compliance summary by control</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$frameworkId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a0418c1b96081854db5489ed186ad1337" name="a0418c1b96081854db5489ed186ad1337"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0418c1b96081854db5489ed186ad1337">&#9670;&#160;</a></span>getControlById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getControlById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$controlId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get control by ID</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$controlId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object </dd></dl>

</div>
</div>
<a id="aeac9b1b5819ae5e2970cfbd1a7597f76" name="aeac9b1b5819ae5e2970cfbd1a7597f76"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeac9b1b5819ae5e2970cfbd1a7597f76">&#9670;&#160;</a></span>getControlsByFramework()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getControlsByFramework </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$frameworkId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get controls for a framework</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$frameworkId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a2a6f89d1e69db5f6f7ae1f97dde53d2e" name="a2a6f89d1e69db5f6f7ae1f97dde53d2e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a6f89d1e69db5f6f7ae1f97dde53d2e">&#9670;&#160;</a></span>getFrameworkById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getFrameworkById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get framework by ID</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object </dd></dl>

</div>
</div>
<a id="a48611d2953815ea9e440be48270e5b41" name="a48611d2953815ea9e440be48270e5b41"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a48611d2953815ea9e440be48270e5b41">&#9670;&#160;</a></span>getFrameworks()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getFrameworks </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all compliance frameworks</p>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="ad933a2ed9c35310e79d219954f8fed3e" name="ad933a2ed9c35310e79d219954f8fed3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad933a2ed9c35310e79d219954f8fed3e">&#9670;&#160;</a></span>getNonCompliantAssets()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getNonCompliantAssets </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$frameworkId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$limit</em></span><span class="paramdefsep"> = </span><span class="paramdefval">10</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get non-compliant assets</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$frameworkId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$limit</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a3f36be93a3051b76fd781c4d27576bae" name="a3f36be93a3051b76fd781c4d27576bae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3f36be93a3051b76fd781c4d27576bae">&#9670;&#160;</a></span>getRecentReports()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getRecentReports </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$limit</em></span><span class="paramdefsep"> = </span><span class="paramdefval">5</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get recent reports</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$limit</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a7276523dbcf22869eb572eaf69c1e792" name="a7276523dbcf22869eb572eaf69c1e792"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7276523dbcf22869eb572eaf69c1e792">&#9670;&#160;</a></span>getReportById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getReportById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$reportId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get report by ID</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$reportId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object </dd></dl>

</div>
</div>
<a id="a77ab529e36423aae6bc0a5b8f5cc6b7a" name="a77ab529e36423aae6bc0a5b8f5cc6b7a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77ab529e36423aae6bc0a5b8f5cc6b7a">&#9670;&#160;</a></span>updateAssetCompliance()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateAssetCompliance </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update asset compliance status</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/models/<a class="el" href="_compliance_model_8php.html">ComplianceModel.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
