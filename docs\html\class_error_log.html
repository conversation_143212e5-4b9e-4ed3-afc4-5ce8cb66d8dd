<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: ErrorLog Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">ErrorLog Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5a561b39013b248d4be5a42f5fa8f48" id="r_ab5a561b39013b248d4be5a42f5fa8f48"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab5a561b39013b248d4be5a42f5fa8f48">ensureErrorLogsTableExists</a> ()</td></tr>
<tr class="separator:ab5a561b39013b248d4be5a42f5fa8f48"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8d4999f0e2ad3fe19b0bace0e159393" id="r_ac8d4999f0e2ad3fe19b0bace0e159393"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac8d4999f0e2ad3fe19b0bace0e159393">logError</a> ($level, $message, $context=null, $file=null, $line=null, $trace=null)</td></tr>
<tr class="separator:ac8d4999f0e2ad3fe19b0bace0e159393"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f92688780dc6f24f4edc5aefdfd7dfe" id="r_a3f92688780dc6f24f4edc5aefdfd7dfe"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3f92688780dc6f24f4edc5aefdfd7dfe">getErrorLogs</a> ($limit=20, $offset=0, $level=null, $search=null)</td></tr>
<tr class="separator:a3f92688780dc6f24f4edc5aefdfd7dfe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abba5797fcf8825cdb1a7638e090fbb94" id="r_abba5797fcf8825cdb1a7638e090fbb94"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abba5797fcf8825cdb1a7638e090fbb94">countErrorLogs</a> ($level=null, $search=null)</td></tr>
<tr class="separator:abba5797fcf8825cdb1a7638e090fbb94"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3e0cd66d937aa3255f4c60b887f29f3f" id="r_a3e0cd66d937aa3255f4c60b887f29f3f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3e0cd66d937aa3255f4c60b887f29f3f">getErrorLogById</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a3e0cd66d937aa3255f4c60b887f29f3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8faa630c237d7353b199e42831e1f80b" id="r_a8faa630c237d7353b199e42831e1f80b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8faa630c237d7353b199e42831e1f80b">deleteErrorLog</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a8faa630c237d7353b199e42831e1f80b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6979c1ed6251a51c8515d3bfacfb8d02" id="r_a6979c1ed6251a51c8515d3bfacfb8d02"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6979c1ed6251a51c8515d3bfacfb8d02">clearErrorLogs</a> ()</td></tr>
<tr class="separator:a6979c1ed6251a51c8515d3bfacfb8d02"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a6979c1ed6251a51c8515d3bfacfb8d02" name="a6979c1ed6251a51c8515d3bfacfb8d02"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6979c1ed6251a51c8515d3bfacfb8d02">&#9670;&#160;</a></span>clearErrorLogs()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">clearErrorLogs </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Clear all error logs</p>
<dl class="section return"><dt>Returns</dt><dd>bool True on success, false on failure </dd></dl>

</div>
</div>
<a id="abba5797fcf8825cdb1a7638e090fbb94" name="abba5797fcf8825cdb1a7638e090fbb94"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abba5797fcf8825cdb1a7638e090fbb94">&#9670;&#160;</a></span>countErrorLogs()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">countErrorLogs </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$level</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$search</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Count all error logs</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$level</td><td>Filter by error level </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$search</td><td>Search term </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int </dd></dl>

</div>
</div>
<a id="a8faa630c237d7353b199e42831e1f80b" name="a8faa630c237d7353b199e42831e1f80b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8faa630c237d7353b199e42831e1f80b">&#9670;&#160;</a></span>deleteErrorLog()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">deleteErrorLog </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete error log by ID</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td>Error log ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True on success, false on failure </dd></dl>

</div>
</div>
<a id="ab5a561b39013b248d4be5a42f5fa8f48" name="ab5a561b39013b248d4be5a42f5fa8f48"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab5a561b39013b248d4be5a42f5fa8f48">&#9670;&#160;</a></span>ensureErrorLogsTableExists()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ensureErrorLogsTableExists </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Ensure the error_logs table exists This is called once during initialization to prevent repeated table creation</p>
<dl class="section return"><dt>Returns</dt><dd>bool True if the table exists or was created successfully </dd></dl>

</div>
</div>
<a id="a3e0cd66d937aa3255f4c60b887f29f3f" name="a3e0cd66d937aa3255f4c60b887f29f3f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3e0cd66d937aa3255f4c60b887f29f3f">&#9670;&#160;</a></span>getErrorLogById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getErrorLogById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get error log by ID</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td>Error log ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object|bool The error log or false on failure </dd></dl>

</div>
</div>
<a id="a3f92688780dc6f24f4edc5aefdfd7dfe" name="a3f92688780dc6f24f4edc5aefdfd7dfe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3f92688780dc6f24f4edc5aefdfd7dfe">&#9670;&#160;</a></span>getErrorLogs()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getErrorLogs </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$limit</em></span><span class="paramdefsep"> = </span><span class="paramdefval">20</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$offset</em></span><span class="paramdefsep"> = </span><span class="paramdefval">0</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$level</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$search</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all error logs with pagination</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$limit</td><td>Number of records to return </td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$offset</td><td>Offset for pagination </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$level</td><td>Filter by error level </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$search</td><td>Search term </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="ac8d4999f0e2ad3fe19b0bace0e159393" name="ac8d4999f0e2ad3fe19b0bace0e159393"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac8d4999f0e2ad3fe19b0bace0e159393">&#9670;&#160;</a></span>logError()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">logError </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$level</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$message</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$context</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$file</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$line</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$trace</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Log an error to the database</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$level</td><td>Error level (error, warning, info, debug) </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$message</td><td>Error message </td></tr>
    <tr><td class="paramtype">array</td><td class="paramname">$context</td><td>Additional context data </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$file</td><td>File where the error occurred </td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$line</td><td>Line number where the error occurred </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$trace</td><td>Stack trace </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int|bool The new error log ID or false on failure </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/models/<a class="el" href="_error_log_8php.html">ErrorLog.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
