<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: FinanceModel Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">FinanceModel Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c3b28394728db624c6b6602ff462e87" id="r_a1c3b28394728db624c6b6602ff462e87"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1c3b28394728db624c6b6602ff462e87">addAssetCost</a> ($data)</td></tr>
<tr class="separator:a1c3b28394728db624c6b6602ff462e87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a722cf1192462e36e341e8e98d617a024" id="r_a722cf1192462e36e341e8e98d617a024"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a722cf1192462e36e341e8e98d617a024">getAssetCosts</a> ($assetId)</td></tr>
<tr class="separator:a722cf1192462e36e341e8e98d617a024"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e3a0e893bb4973a8ead487c7a2e84e4" id="r_a8e3a0e893bb4973a8ead487c7a2e84e4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e3a0e893bb4973a8ead487c7a2e84e4">getTotalCostOfOwnership</a> ($assetId)</td></tr>
<tr class="separator:a8e3a0e893bb4973a8ead487c7a2e84e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8183489a77f4cdce3e1ec2665909d172" id="r_a8183489a77f4cdce3e1ec2665909d172"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8183489a77f4cdce3e1ec2665909d172">getCostsByFiscalYear</a> ($fiscalYear=null)</td></tr>
<tr class="separator:a8183489a77f4cdce3e1ec2665909d172"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35fd3a47017a454cba53c16cc6d7f6df" id="r_a35fd3a47017a454cba53c16cc6d7f6df"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a35fd3a47017a454cba53c16cc6d7f6df">getMonthlyCosts</a> ($fiscalYear=null)</td></tr>
<tr class="separator:a35fd3a47017a454cba53c16cc6d7f6df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9f0064083dfe65c08b304490432ecb8" id="r_ad9f0064083dfe65c08b304490432ecb8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad9f0064083dfe65c08b304490432ecb8">getCostsByEquipmentType</a> ($fiscalYear=null)</td></tr>
<tr class="separator:ad9f0064083dfe65c08b304490432ecb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6929cf3308d48a3c73d46cdf847a9b0" id="r_ab6929cf3308d48a3c73d46cdf847a9b0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab6929cf3308d48a3c73d46cdf847a9b0">calculateDepreciation</a> ($assetId)</td></tr>
<tr class="separator:ab6929cf3308d48a3c73d46cdf847a9b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afc623d939da88738ae9729d532a45374" id="r_afc623d939da88738ae9729d532a45374"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afc623d939da88738ae9729d532a45374">generateBudgetForecast</a> ($fiscalYear=null)</td></tr>
<tr class="separator:afc623d939da88738ae9729d532a45374"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a1c3b28394728db624c6b6602ff462e87" name="a1c3b28394728db624c6b6602ff462e87"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c3b28394728db624c6b6602ff462e87">&#9670;&#160;</a></span>addAssetCost()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">addAssetCost </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add asset cost</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="ab6929cf3308d48a3c73d46cdf847a9b0" name="ab6929cf3308d48a3c73d46cdf847a9b0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab6929cf3308d48a3c73d46cdf847a9b0">&#9670;&#160;</a></span>calculateDepreciation()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">calculateDepreciation </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Calculate depreciation for an asset</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="afc623d939da88738ae9729d532a45374" name="afc623d939da88738ae9729d532a45374"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afc623d939da88738ae9729d532a45374">&#9670;&#160;</a></span>generateBudgetForecast()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">generateBudgetForecast </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$fiscalYear</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Generate budget forecast</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$fiscalYear</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a722cf1192462e36e341e8e98d617a024" name="a722cf1192462e36e341e8e98d617a024"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a722cf1192462e36e341e8e98d617a024">&#9670;&#160;</a></span>getAssetCosts()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetCosts </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get costs for an asset</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="ad9f0064083dfe65c08b304490432ecb8" name="ad9f0064083dfe65c08b304490432ecb8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad9f0064083dfe65c08b304490432ecb8">&#9670;&#160;</a></span>getCostsByEquipmentType()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getCostsByEquipmentType </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$fiscalYear</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get costs by equipment type</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$fiscalYear</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a8183489a77f4cdce3e1ec2665909d172" name="a8183489a77f4cdce3e1ec2665909d172"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8183489a77f4cdce3e1ec2665909d172">&#9670;&#160;</a></span>getCostsByFiscalYear()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getCostsByFiscalYear </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$fiscalYear</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get costs by fiscal year</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$fiscalYear</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a35fd3a47017a454cba53c16cc6d7f6df" name="a35fd3a47017a454cba53c16cc6d7f6df"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a35fd3a47017a454cba53c16cc6d7f6df">&#9670;&#160;</a></span>getMonthlyCosts()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getMonthlyCosts </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$fiscalYear</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get monthly costs for a fiscal year</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$fiscalYear</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a8e3a0e893bb4973a8ead487c7a2e84e4" name="a8e3a0e893bb4973a8ead487c7a2e84e4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e3a0e893bb4973a8ead487c7a2e84e4">&#9670;&#160;</a></span>getTotalCostOfOwnership()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getTotalCostOfOwnership </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get total cost of ownership for an asset</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/models/<a class="el" href="_finance_model_8php.html">FinanceModel.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
