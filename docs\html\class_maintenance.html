<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Maintenance Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">Maintenance Class Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for Maintenance:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_maintenance.png" usemap="#Maintenance_map" alt=""/>
  <map id="Maintenance_map" name="Maintenance_map">
<area href="class_controller.html" alt="Controller" shape="rect" coords="0,0,85,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a65c682611e5c929cdacbce2ff322c9ce" id="r_a65c682611e5c929cdacbce2ff322c9ce"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a65c682611e5c929cdacbce2ff322c9ce">index</a> ($page=1)</td></tr>
<tr class="separator:a65c682611e5c929cdacbce2ff322c9ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada8a565af34521a04a4acda75a445bcb" id="r_ada8a565af34521a04a4acda75a445bcb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ada8a565af34521a04a4acda75a445bcb">history</a> ($assetId)</td></tr>
<tr class="separator:ada8a565af34521a04a4acda75a445bcb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5af77dc86e75e68b764cd874fa55c960" id="r_a5af77dc86e75e68b764cd874fa55c960"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5af77dc86e75e68b764cd874fa55c960">add</a> ($assetId=null)</td></tr>
<tr class="separator:a5af77dc86e75e68b764cd874fa55c960"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aafa7994a16f9c00e068c54343174ca82" id="r_aafa7994a16f9c00e068c54343174ca82"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aafa7994a16f9c00e068c54343174ca82">recalculateHealthMetrics</a> ()</td></tr>
<tr class="separator:aafa7994a16f9c00e068c54343174ca82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a93f0b59a2513f700476e3f8d01c30860" id="r_a93f0b59a2513f700476e3f8d01c30860"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a93f0b59a2513f700476e3f8d01c30860">complete</a> ($assetId, $maintenanceType)</td></tr>
<tr class="separator:a93f0b59a2513f700476e3f8d01c30860"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad1c0026170594acf2d25446f4e359832" id="r_ad1c0026170594acf2d25446f4e359832"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad1c0026170594acf2d25446f4e359832">guidelines</a> ()</td></tr>
<tr class="separator:ad1c0026170594acf2d25446f4e359832"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf9c215de21af2165cda212eaa586067" id="r_abf9c215de21af2165cda212eaa586067"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abf9c215de21af2165cda212eaa586067">guideline</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:abf9c215de21af2165cda212eaa586067"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a373f7f8cde6c7caec444d7fa1ef459f6" id="r_a373f7f8cde6c7caec444d7fa1ef459f6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a373f7f8cde6c7caec444d7fa1ef459f6">manageChecklist</a> ($guidelineId)</td></tr>
<tr class="separator:a373f7f8cde6c7caec444d7fa1ef459f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80c346d7c2eb6742214d7a2eafbbc6a6" id="r_a80c346d7c2eb6742214d7a2eafbbc6a6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a80c346d7c2eb6742214d7a2eafbbc6a6">editChecklistItem</a> ($guidelineId, $itemId=null)</td></tr>
<tr class="separator:a80c346d7c2eb6742214d7a2eafbbc6a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f368832baf0b4b89a5a716d417333b3" id="r_a7f368832baf0b4b89a5a716d417333b3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7f368832baf0b4b89a5a716d417333b3">deleteChecklistItem</a> ($guidelineId, $itemId)</td></tr>
<tr class="separator:a7f368832baf0b4b89a5a716d417333b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:accb1821e13b0574c994996c403d6e881" id="r_accb1821e13b0574c994996c403d6e881"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#accb1821e13b0574c994996c403d6e881">editGuideline</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>=null)</td></tr>
<tr class="separator:accb1821e13b0574c994996c403d6e881"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afefb31fb1543b011326767baf0878913" id="r_afefb31fb1543b011326767baf0878913"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afefb31fb1543b011326767baf0878913">deleteGuideline</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:afefb31fb1543b011326767baf0878913"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1cefddff0a8d40a82cc589cdf00f5b63" id="r_a1cefddff0a8d40a82cc589cdf00f5b63"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1cefddff0a8d40a82cc589cdf00f5b63">allHistory</a> ($page=1)</td></tr>
<tr class="separator:a1cefddff0a8d40a82cc589cdf00f5b63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac578e27aa90b225d97576caec289c0f9" id="r_ac578e27aa90b225d97576caec289c0f9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac578e27aa90b225d97576caec289c0f9">viewRedirect</a> ($maintenanceId)</td></tr>
<tr class="separator:ac578e27aa90b225d97576caec289c0f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06c923f74980ab773940970cd420c3af" id="r_a06c923f74980ab773940970cd420c3af"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a06c923f74980ab773940970cd420c3af">viewRecord</a> ($maintenanceId)</td></tr>
<tr class="separator:a06c923f74980ab773940970cd420c3af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1f61d95f6b17f24041cf10c08d904a5e" id="r_a1f61d95f6b17f24041cf10c08d904a5e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1f61d95f6b17f24041cf10c08d904a5e">monitoring</a> ()</td></tr>
<tr class="separator:a1f61d95f6b17f24041cf10c08d904a5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a322e3e8f6158aebfc7f0bc7c23f9f93d" id="r_a322e3e8f6158aebfc7f0bc7c23f9f93d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a322e3e8f6158aebfc7f0bc7c23f9f93d">assetCompliance</a> ($assetId)</td></tr>
<tr class="separator:a322e3e8f6158aebfc7f0bc7c23f9f93d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb25853af641d105c381e29e0d2e6409" id="r_adb25853af641d105c381e29e0d2e6409"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adb25853af641d105c381e29e0d2e6409">guidelineImplementations</a> ($guidelineId, $page=1)</td></tr>
<tr class="separator:adb25853af641d105c381e29e0d2e6409"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11fa9a74c36a285318dbac79eda144af" id="r_a11fa9a74c36a285318dbac79eda144af"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a11fa9a74c36a285318dbac79eda144af">dataIntegrityCheck</a> ()</td></tr>
<tr class="separator:a11fa9a74c36a285318dbac79eda144af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9cbd796bd274dad9c2a2ef7eac75dbff" id="r_a9cbd796bd274dad9c2a2ef7eac75dbff"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9cbd796bd274dad9c2a2ef7eac75dbff">recreateMissingRecords</a> ()</td></tr>
<tr class="separator:a9cbd796bd274dad9c2a2ef7eac75dbff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a63340b3b2a8d07f4a03d1dc922530f38" id="r_a63340b3b2a8d07f4a03d1dc922530f38"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a63340b3b2a8d07f4a03d1dc922530f38">runMigration</a> ($guidelineId=null)</td></tr>
<tr class="separator:a63340b3b2a8d07f4a03d1dc922530f38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_class_controller"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_class_controller')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="class_controller.html">Controller</a></td></tr>
<tr class="memitem:ac531eb761b130b1925a8bae5c33af2fc inherit pub_methods_class_controller" id="r_ac531eb761b130b1925a8bae5c33af2fc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#ac531eb761b130b1925a8bae5c33af2fc">model</a> ($model)</td></tr>
<tr class="separator:ac531eb761b130b1925a8bae5c33af2fc inherit pub_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11f0e20b30b899d00b009a9bb1afe43d inherit pub_methods_class_controller" id="r_a11f0e20b30b899d00b009a9bb1afe43d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#a11f0e20b30b899d00b009a9bb1afe43d">view</a> ($view, $data=[])</td></tr>
<tr class="separator:a11f0e20b30b899d00b009a9bb1afe43d inherit pub_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="inherited" name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pro_methods_class_controller"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pro_methods_class_controller')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="class_controller.html">Controller</a></td></tr>
<tr class="memitem:a0d92de8136cebc006a407442aab9db0a inherit pro_methods_class_controller" id="r_a0d92de8136cebc006a407442aab9db0a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#a0d92de8136cebc006a407442aab9db0a">sanitizePostData</a> ($data)</td></tr>
<tr class="separator:a0d92de8136cebc006a407442aab9db0a inherit pro_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf7b7d5aa2f9ec7a1f79646322121f52 inherit pro_methods_class_controller" id="r_aaf7b7d5aa2f9ec7a1f79646322121f52"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_controller.html#aaf7b7d5aa2f9ec7a1f79646322121f52">validateCsrfToken</a> ($token)</td></tr>
<tr class="separator:aaf7b7d5aa2f9ec7a1f79646322121f52 inherit pro_methods_class_controller"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a5af77dc86e75e68b764cd874fa55c960" name="a5af77dc86e75e68b764cd874fa55c960"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5af77dc86e75e68b764cd874fa55c960">&#9670;&#160;</a></span>add()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">add </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add maintenance record</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a1cefddff0a8d40a82cc589cdf00f5b63" name="a1cefddff0a8d40a82cc589cdf00f5b63"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1cefddff0a8d40a82cc589cdf00f5b63">&#9670;&#160;</a></span>allHistory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">allHistory </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$page</em></span><span class="paramdefsep"> = </span><span class="paramdefval">1</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>View all maintenance history</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$page</td><td>Current page number for pagination </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a322e3e8f6158aebfc7f0bc7c23f9f93d" name="a322e3e8f6158aebfc7f0bc7c23f9f93d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a322e3e8f6158aebfc7f0bc7c23f9f93d">&#9670;&#160;</a></span>assetCompliance()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">assetCompliance </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>View asset compliance</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a93f0b59a2513f700476e3f8d01c30860" name="a93f0b59a2513f700476e3f8d01c30860"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a93f0b59a2513f700476e3f8d01c30860">&#9670;&#160;</a></span>complete()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">complete </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceType</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Mark maintenance as completed</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$maintenanceType</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a11fa9a74c36a285318dbac79eda144af" name="a11fa9a74c36a285318dbac79eda144af"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a11fa9a74c36a285318dbac79eda144af">&#9670;&#160;</a></span>dataIntegrityCheck()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">dataIntegrityCheck </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Data Integrity Check Tool Identifies and fixes orphaned records in the maintenance_guideline_implementation table </p>

</div>
</div>
<a id="a7f368832baf0b4b89a5a716d417333b3" name="a7f368832baf0b4b89a5a716d417333b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7f368832baf0b4b89a5a716d417333b3">&#9670;&#160;</a></span>deleteChecklistItem()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">deleteChecklistItem </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$itemId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete a checklist item</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$itemId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="afefb31fb1543b011326767baf0878913" name="afefb31fb1543b011326767baf0878913"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afefb31fb1543b011326767baf0878913">&#9670;&#160;</a></span>deleteGuideline()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">deleteGuideline </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete guideline</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a80c346d7c2eb6742214d7a2eafbbc6a6" name="a80c346d7c2eb6742214d7a2eafbbc6a6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a80c346d7c2eb6742214d7a2eafbbc6a6">&#9670;&#160;</a></span>editChecklistItem()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">editChecklistItem </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$itemId</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add or edit a checklist item</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$itemId</td><td>(optional) </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="accb1821e13b0574c994996c403d6e881" name="accb1821e13b0574c994996c403d6e881"></a>
<h2 class="memtitle"><span class="permalink"><a href="#accb1821e13b0574c994996c403d6e881">&#9670;&#160;</a></span>editGuideline()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">editGuideline </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add or edit guideline</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="abf9c215de21af2165cda212eaa586067" name="abf9c215de21af2165cda212eaa586067"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf9c215de21af2165cda212eaa586067">&#9670;&#160;</a></span>guideline()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">guideline </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>View guideline details</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="adb25853af641d105c381e29e0d2e6409" name="adb25853af641d105c381e29e0d2e6409"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adb25853af641d105c381e29e0d2e6409">&#9670;&#160;</a></span>guidelineImplementations()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">guidelineImplementations </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$page</em></span><span class="paramdefsep"> = </span><span class="paramdefval">1</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>View all implementations of a specific guideline across different maintenance records</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$page</td><td>Current page number for pagination </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ad1c0026170594acf2d25446f4e359832" name="ad1c0026170594acf2d25446f4e359832"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad1c0026170594acf2d25446f4e359832">&#9670;&#160;</a></span>guidelines()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">guidelines </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Guidelines dashboard </p>

</div>
</div>
<a id="ada8a565af34521a04a4acda75a445bcb" name="ada8a565af34521a04a4acda75a445bcb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada8a565af34521a04a4acda75a445bcb">&#9670;&#160;</a></span>history()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">history </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>View maintenance history for an asset</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a65c682611e5c929cdacbce2ff322c9ce" name="a65c682611e5c929cdacbce2ff322c9ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a65c682611e5c929cdacbce2ff322c9ce">&#9670;&#160;</a></span>index()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">index </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$page</em></span><span class="paramdefsep"> = </span><span class="paramdefval">1</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p><a class="el" href="class_maintenance.html">Maintenance</a> dashboard</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$page</td><td>Current page for asset health metrics pagination </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a373f7f8cde6c7caec444d7fa1ef459f6" name="a373f7f8cde6c7caec444d7fa1ef459f6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a373f7f8cde6c7caec444d7fa1ef459f6">&#9670;&#160;</a></span>manageChecklist()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">manageChecklist </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Manage checklist items for a guideline</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a1f61d95f6b17f24041cf10c08d904a5e" name="a1f61d95f6b17f24041cf10c08d904a5e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1f61d95f6b17f24041cf10c08d904a5e">&#9670;&#160;</a></span>monitoring()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">monitoring </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Monitoring dashboard </p>

</div>
</div>
<a id="aafa7994a16f9c00e068c54343174ca82" name="aafa7994a16f9c00e068c54343174ca82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aafa7994a16f9c00e068c54343174ca82">&#9670;&#160;</a></span>recalculateHealthMetrics()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">recalculateHealthMetrics </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Recalculate health metrics for all assets </p>

</div>
</div>
<a id="a9cbd796bd274dad9c2a2ef7eac75dbff" name="a9cbd796bd274dad9c2a2ef7eac75dbff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9cbd796bd274dad9c2a2ef7eac75dbff">&#9670;&#160;</a></span>recreateMissingRecords()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">recreateMissingRecords </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Recreate Missing <a class="el" href="class_maintenance.html">Maintenance</a> Records Recreates maintenance history records based on implementation records </p>

</div>
</div>
<a id="a63340b3b2a8d07f4a03d1dc922530f38" name="a63340b3b2a8d07f4a03d1dc922530f38"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a63340b3b2a8d07f4a03d1dc922530f38">&#9670;&#160;</a></span>runMigration()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">runMigration </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Run <a class="el" href="class_database.html">Database</a> Migration for Guideline Implementation Table Executes the migration script to ensure the table exists and has the correct structure</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td>Optional guideline ID to redirect back to </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a06c923f74980ab773940970cd420c3af" name="a06c923f74980ab773940970cd420c3af"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a06c923f74980ab773940970cd420c3af">&#9670;&#160;</a></span>viewRecord()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">viewRecord </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>View detailed maintenance record with implemented guidelines</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$maintenanceId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ac578e27aa90b225d97576caec289c0f9" name="ac578e27aa90b225d97576caec289c0f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac578e27aa90b225d97576caec289c0f9">&#9670;&#160;</a></span>viewRedirect()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">viewRedirect </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Redirect to viewRecord method This method exists to handle legacy URLs that use /maintenance/view/ instead of /maintenance/viewRecord/</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$maintenanceId</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/controllers/<a class="el" href="_maintenance_8php.html">Maintenance.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
