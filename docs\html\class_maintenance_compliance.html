<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: MaintenanceCompliance Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">MaintenanceCompliance Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae004cfdc78ae9803530098e3b3fa8f34" id="r_ae004cfdc78ae9803530098e3b3fa8f34"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae004cfdc78ae9803530098e3b3fa8f34">getAllComplianceStatus</a> ()</td></tr>
<tr class="separator:ae004cfdc78ae9803530098e3b3fa8f34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38966b8a791ede8f4a33ada2c45900e4" id="r_a38966b8a791ede8f4a33ada2c45900e4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a38966b8a791ede8f4a33ada2c45900e4">getAssetComplianceStatus</a> ($assetId)</td></tr>
<tr class="separator:a38966b8a791ede8f4a33ada2c45900e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae41214459099d198fdbf254d1b6d3ea5" id="r_ae41214459099d198fdbf254d1b6d3ea5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae41214459099d198fdbf254d1b6d3ea5">getGuidelineComplianceStatus</a> ($guidelineId)</td></tr>
<tr class="separator:ae41214459099d198fdbf254d1b6d3ea5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a53eda7cd291800d6597fc9fd393d9cc9" id="r_a53eda7cd291800d6597fc9fd393d9cc9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a53eda7cd291800d6597fc9fd393d9cc9">getOverdueMaintenance</a> ()</td></tr>
<tr class="separator:a53eda7cd291800d6597fc9fd393d9cc9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a78151f12d8c609fb337b7c75b901b09a" id="r_a78151f12d8c609fb337b7c75b901b09a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a78151f12d8c609fb337b7c75b901b09a">getMaintenanceDueSoon</a> ($daysAhead=30)</td></tr>
<tr class="separator:a78151f12d8c609fb337b7c75b901b09a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace44f8eca9de68cedee14e5fecd831a7" id="r_ace44f8eca9de68cedee14e5fecd831a7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ace44f8eca9de68cedee14e5fecd831a7">getCompliantEndpoints</a> ()</td></tr>
<tr class="separator:ace44f8eca9de68cedee14e5fecd831a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acdfc6a30cfae1bf421ac8843f2da48e6" id="r_acdfc6a30cfae1bf421ac8843f2da48e6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acdfc6a30cfae1bf421ac8843f2da48e6">updateComplianceStatus</a> ($assetId, $guidelineId, $status, $lastPerformedDate=null, $nextDueDate=null)</td></tr>
<tr class="separator:acdfc6a30cfae1bf421ac8843f2da48e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81dcd7ed7d6a40beead9f10aff4f7b16" id="r_a81dcd7ed7d6a40beead9f10aff4f7b16"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a81dcd7ed7d6a40beead9f10aff4f7b16">updateAllComplianceStatus</a> ()</td></tr>
<tr class="separator:a81dcd7ed7d6a40beead9f10aff4f7b16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4690ec3327999f4578be947e8809291" id="r_ac4690ec3327999f4578be947e8809291"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac4690ec3327999f4578be947e8809291">recordChecklistCompletion</a> ($maintenanceHistoryId, $checklistId, $completed, $completedBy, $notes='')</td></tr>
<tr class="separator:ac4690ec3327999f4578be947e8809291"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b211ed4bb2c38a6eb9a7a0d6a42afab" id="r_a3b211ed4bb2c38a6eb9a7a0d6a42afab"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3b211ed4bb2c38a6eb9a7a0d6a42afab">getChecklistCompletion</a> ($maintenanceHistoryId)</td></tr>
<tr class="separator:a3b211ed4bb2c38a6eb9a7a0d6a42afab"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ae004cfdc78ae9803530098e3b3fa8f34" name="ae004cfdc78ae9803530098e3b3fa8f34"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae004cfdc78ae9803530098e3b3fa8f34">&#9670;&#160;</a></span>getAllComplianceStatus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAllComplianceStatus </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get compliance status for all assets</p>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a38966b8a791ede8f4a33ada2c45900e4" name="a38966b8a791ede8f4a33ada2c45900e4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a38966b8a791ede8f4a33ada2c45900e4">&#9670;&#160;</a></span>getAssetComplianceStatus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetComplianceStatus </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get compliance status for a specific asset</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a3b211ed4bb2c38a6eb9a7a0d6a42afab" name="a3b211ed4bb2c38a6eb9a7a0d6a42afab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3b211ed4bb2c38a6eb9a7a0d6a42afab">&#9670;&#160;</a></span>getChecklistCompletion()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getChecklistCompletion </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceHistoryId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get checklist completion for a maintenance record</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$maintenanceHistoryId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="ace44f8eca9de68cedee14e5fecd831a7" name="ace44f8eca9de68cedee14e5fecd831a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ace44f8eca9de68cedee14e5fecd831a7">&#9670;&#160;</a></span>getCompliantEndpoints()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getCompliantEndpoints </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get compliant endpoints</p>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="ae41214459099d198fdbf254d1b6d3ea5" name="ae41214459099d198fdbf254d1b6d3ea5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae41214459099d198fdbf254d1b6d3ea5">&#9670;&#160;</a></span>getGuidelineComplianceStatus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getGuidelineComplianceStatus </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get compliance status for a specific guideline across all assets</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a78151f12d8c609fb337b7c75b901b09a" name="a78151f12d8c609fb337b7c75b901b09a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a78151f12d8c609fb337b7c75b901b09a">&#9670;&#160;</a></span>getMaintenanceDueSoon()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getMaintenanceDueSoon </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$daysAhead</em></span><span class="paramdefsep"> = </span><span class="paramdefval">30</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get maintenance tasks due soon</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$daysAhead</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a53eda7cd291800d6597fc9fd393d9cc9" name="a53eda7cd291800d6597fc9fd393d9cc9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a53eda7cd291800d6597fc9fd393d9cc9">&#9670;&#160;</a></span>getOverdueMaintenance()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getOverdueMaintenance </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get overdue maintenance tasks</p>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="ac4690ec3327999f4578be947e8809291" name="ac4690ec3327999f4578be947e8809291"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac4690ec3327999f4578be947e8809291">&#9670;&#160;</a></span>recordChecklistCompletion()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">recordChecklistCompletion </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceHistoryId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$checklistId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$completed</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$completedBy</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$notes</em></span><span class="paramdefsep"> = </span><span class="paramdefval">''</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Record checklist completion</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$maintenanceHistoryId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$checklistId</td><td></td></tr>
    <tr><td class="paramtype">bool</td><td class="paramname">$completed</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$completedBy</td><td></td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$notes</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="a81dcd7ed7d6a40beead9f10aff4f7b16" name="a81dcd7ed7d6a40beead9f10aff4f7b16"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a81dcd7ed7d6a40beead9f10aff4f7b16">&#9670;&#160;</a></span>updateAllComplianceStatus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateAllComplianceStatus </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Calculate and update compliance status for all assets</p>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="acdfc6a30cfae1bf421ac8843f2da48e6" name="acdfc6a30cfae1bf421ac8843f2da48e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acdfc6a30cfae1bf421ac8843f2da48e6">&#9670;&#160;</a></span>updateComplianceStatus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateComplianceStatus </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$status</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$lastPerformedDate</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$nextDueDate</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update compliance status for an asset and guideline</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$status</td><td></td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$lastPerformedDate</td><td></td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$nextDueDate</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/models/<a class="el" href="_maintenance_compliance_8php.html">MaintenanceCompliance.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
