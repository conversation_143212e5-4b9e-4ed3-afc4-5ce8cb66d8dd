<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: MaintenanceGuideline Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">MaintenanceGuideline Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acf5491f2d8fdc88257f5a86d9b858c51" id="r_acf5491f2d8fdc88257f5a86d9b858c51"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acf5491f2d8fdc88257f5a86d9b858c51">getAllGuidelines</a> ()</td></tr>
<tr class="separator:acf5491f2d8fdc88257f5a86d9b858c51"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2cc7d08e605aae76fe2d7fb575741b1" id="r_ad2cc7d08e605aae76fe2d7fb575741b1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad2cc7d08e605aae76fe2d7fb575741b1">getGuidelinesByEquipmentType</a> ($equipmentType)</td></tr>
<tr class="separator:ad2cc7d08e605aae76fe2d7fb575741b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ce317ffbfd16f85d200877dbb74d8ef" id="r_a8ce317ffbfd16f85d200877dbb74d8ef"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8ce317ffbfd16f85d200877dbb74d8ef">getGuidelineById</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a8ce317ffbfd16f85d200877dbb74d8ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe2396d040b8fdb1946a63702f30e67c" id="r_abe2396d040b8fdb1946a63702f30e67c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abe2396d040b8fdb1946a63702f30e67c">getChecklistItems</a> ($guidelineId)</td></tr>
<tr class="separator:abe2396d040b8fdb1946a63702f30e67c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a383b611d9c3469a58ac395f64981c356" id="r_a383b611d9c3469a58ac395f64981c356"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a383b611d9c3469a58ac395f64981c356">addGuideline</a> ($data)</td></tr>
<tr class="separator:a383b611d9c3469a58ac395f64981c356"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb781661c951700724bf44af32d02c21" id="r_abb781661c951700724bf44af32d02c21"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abb781661c951700724bf44af32d02c21">updateGuideline</a> ($data)</td></tr>
<tr class="separator:abb781661c951700724bf44af32d02c21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afefb31fb1543b011326767baf0878913" id="r_afefb31fb1543b011326767baf0878913"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afefb31fb1543b011326767baf0878913">deleteGuideline</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:afefb31fb1543b011326767baf0878913"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa78853d4b8a87ebcd6f89818ac37eaac" id="r_aa78853d4b8a87ebcd6f89818ac37eaac"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa78853d4b8a87ebcd6f89818ac37eaac">addChecklistItem</a> ($data)</td></tr>
<tr class="separator:aa78853d4b8a87ebcd6f89818ac37eaac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d92535f0a290cdb6dae4eaa018c7b37" id="r_a0d92535f0a290cdb6dae4eaa018c7b37"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0d92535f0a290cdb6dae4eaa018c7b37">updateChecklistItem</a> ($data)</td></tr>
<tr class="separator:a0d92535f0a290cdb6dae4eaa018c7b37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67dfc8aaee56ed77b7a15ef682315571" id="r_a67dfc8aaee56ed77b7a15ef682315571"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a67dfc8aaee56ed77b7a15ef682315571">deleteChecklistItem</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a67dfc8aaee56ed77b7a15ef682315571"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a36b79d222bae4ee9ebec107463a840" id="r_a8a36b79d222bae4ee9ebec107463a840"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8a36b79d222bae4ee9ebec107463a840">getNextStepNumber</a> ($guidelineId)</td></tr>
<tr class="separator:a8a36b79d222bae4ee9ebec107463a840"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9237db02c249b6f068d682dd5df1b371" id="r_a9237db02c249b6f068d682dd5df1b371"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9237db02c249b6f068d682dd5df1b371">getChecklistItemById</a> ($itemId, $guidelineId)</td></tr>
<tr class="separator:a9237db02c249b6f068d682dd5df1b371"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="aa78853d4b8a87ebcd6f89818ac37eaac" name="aa78853d4b8a87ebcd6f89818ac37eaac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa78853d4b8a87ebcd6f89818ac37eaac">&#9670;&#160;</a></span>addChecklistItem()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">addChecklistItem </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add a checklist item to a guideline</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int|bool The new checklist item ID or false on failure </dd></dl>

</div>
</div>
<a id="a383b611d9c3469a58ac395f64981c356" name="a383b611d9c3469a58ac395f64981c356"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a383b611d9c3469a58ac395f64981c356">&#9670;&#160;</a></span>addGuideline()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">addGuideline </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add a new maintenance guideline</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int|bool The new guideline ID or false on failure </dd></dl>

</div>
</div>
<a id="a67dfc8aaee56ed77b7a15ef682315571" name="a67dfc8aaee56ed77b7a15ef682315571"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a67dfc8aaee56ed77b7a15ef682315571">&#9670;&#160;</a></span>deleteChecklistItem()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">deleteChecklistItem </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete a checklist item</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="afefb31fb1543b011326767baf0878913" name="afefb31fb1543b011326767baf0878913"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afefb31fb1543b011326767baf0878913">&#9670;&#160;</a></span>deleteGuideline()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">deleteGuideline </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete a maintenance guideline</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="acf5491f2d8fdc88257f5a86d9b858c51" name="acf5491f2d8fdc88257f5a86d9b858c51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acf5491f2d8fdc88257f5a86d9b858c51">&#9670;&#160;</a></span>getAllGuidelines()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAllGuidelines </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all maintenance guidelines</p>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a9237db02c249b6f068d682dd5df1b371" name="a9237db02c249b6f068d682dd5df1b371"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9237db02c249b6f068d682dd5df1b371">&#9670;&#160;</a></span>getChecklistItemById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getChecklistItemById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$itemId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get a specific checklist item by ID and guideline ID</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$itemId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object|false </dd></dl>

</div>
</div>
<a id="abe2396d040b8fdb1946a63702f30e67c" name="abe2396d040b8fdb1946a63702f30e67c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe2396d040b8fdb1946a63702f30e67c">&#9670;&#160;</a></span>getChecklistItems()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getChecklistItems </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get checklist items for a guideline</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a8ce317ffbfd16f85d200877dbb74d8ef" name="a8ce317ffbfd16f85d200877dbb74d8ef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8ce317ffbfd16f85d200877dbb74d8ef">&#9670;&#160;</a></span>getGuidelineById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getGuidelineById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get maintenance guideline by ID</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object </dd></dl>

</div>
</div>
<a id="ad2cc7d08e605aae76fe2d7fb575741b1" name="ad2cc7d08e605aae76fe2d7fb575741b1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad2cc7d08e605aae76fe2d7fb575741b1">&#9670;&#160;</a></span>getGuidelinesByEquipmentType()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getGuidelinesByEquipmentType </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$equipmentType</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get maintenance guidelines by equipment type</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$equipmentType</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a8a36b79d222bae4ee9ebec107463a840" name="a8a36b79d222bae4ee9ebec107463a840"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8a36b79d222bae4ee9ebec107463a840">&#9670;&#160;</a></span>getNextStepNumber()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getNextStepNumber </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get the next step number for a guideline</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int </dd></dl>

</div>
</div>
<a id="a0d92535f0a290cdb6dae4eaa018c7b37" name="a0d92535f0a290cdb6dae4eaa018c7b37"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0d92535f0a290cdb6dae4eaa018c7b37">&#9670;&#160;</a></span>updateChecklistItem()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateChecklistItem </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update a checklist item</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="abb781661c951700724bf44af32d02c21" name="abb781661c951700724bf44af32d02c21"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abb781661c951700724bf44af32d02c21">&#9670;&#160;</a></span>updateGuideline()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateGuideline </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update a maintenance guideline</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/models/<a class="el" href="_maintenance_guideline_8php.html">MaintenanceGuideline.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
