<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: MaintenanceModel Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">MaintenanceModel Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a307b0ec1c1343e097ffa8d0a058cc353" id="r_a307b0ec1c1343e097ffa8d0a058cc353"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a307b0ec1c1343e097ffa8d0a058cc353">ensureGuidelineImplementationTableExists</a> ()</td></tr>
<tr class="separator:a307b0ec1c1343e097ffa8d0a058cc353"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5028d7b9ec8e052e69d9f683e1ebfd07" id="r_a5028d7b9ec8e052e69d9f683e1ebfd07"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5028d7b9ec8e052e69d9f683e1ebfd07">getMaintenanceHistory</a> ($assetId)</td></tr>
<tr class="separator:a5028d7b9ec8e052e69d9f683e1ebfd07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a308b6d3e1f8e959f87bf5ae8f6c93ae6" id="r_a308b6d3e1f8e959f87bf5ae8f6c93ae6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a308b6d3e1f8e959f87bf5ae8f6c93ae6">getMaintenanceById</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a308b6d3e1f8e959f87bf5ae8f6c93ae6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace2e334dc067d1e99ab1cf4bed40359f" id="r_ace2e334dc067d1e99ab1cf4bed40359f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ace2e334dc067d1e99ab1cf4bed40359f">getAllMaintenanceHistory</a> ($limit=null, $offset=0)</td></tr>
<tr class="separator:ace2e334dc067d1e99ab1cf4bed40359f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9447fe3595934e7036eeba1eee31bae" id="r_aa9447fe3595934e7036eeba1eee31bae"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa9447fe3595934e7036eeba1eee31bae">countAllMaintenanceHistory</a> ()</td></tr>
<tr class="separator:aa9447fe3595934e7036eeba1eee31bae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49112508b667299f63c3e43993c89ac1" id="r_a49112508b667299f63c3e43993c89ac1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a49112508b667299f63c3e43993c89ac1">addMaintenance</a> ($data)</td></tr>
<tr class="separator:a49112508b667299f63c3e43993c89ac1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af337d34247442623d3ef21c81c77b5c7" id="r_af337d34247442623d3ef21c81c77b5c7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af337d34247442623d3ef21c81c77b5c7">getAssetsDueForMaintenance</a> ($daysAhead=30)</td></tr>
<tr class="separator:af337d34247442623d3ef21c81c77b5c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afb3f191cdd7cdb9724de3d59626b162a" id="r_afb3f191cdd7cdb9724de3d59626b162a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afb3f191cdd7cdb9724de3d59626b162a">calculateAssetHealthScore</a> ($assetId)</td></tr>
<tr class="separator:afb3f191cdd7cdb9724de3d59626b162a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a234816f2513b32ba0fea57902e2ca897" id="r_a234816f2513b32ba0fea57902e2ca897"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a234816f2513b32ba0fea57902e2ca897">getAssetsWithHealthMetrics</a> ($limit=null, $offset=0, $search='', $orderBy='health_score', $orderDir='ASC')</td></tr>
<tr class="separator:a234816f2513b32ba0fea57902e2ca897"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38aac20b8e7f44f2cf503808243794ec" id="r_a38aac20b8e7f44f2cf503808243794ec"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a38aac20b8e7f44f2cf503808243794ec">countAssetsWithHealthMetrics</a> ($search='')</td></tr>
<tr class="separator:a38aac20b8e7f44f2cf503808243794ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3712d99605d1092dd417d6813adfb838" id="r_a3712d99605d1092dd417d6813adfb838"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3712d99605d1092dd417d6813adfb838">getAllScheduledMaintenance</a> ($assetId)</td></tr>
<tr class="separator:a3712d99605d1092dd417d6813adfb838"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adac8c5043e0bc976a86e615fce9b4a0d" id="r_adac8c5043e0bc976a86e615fce9b4a0d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adac8c5043e0bc976a86e615fce9b4a0d">getScheduledMaintenanceId</a> ($assetId, $maintenanceType)</td></tr>
<tr class="separator:adac8c5043e0bc976a86e615fce9b4a0d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a567f817301870b14b63b9607a8f1c182" id="r_a567f817301870b14b63b9607a8f1c182"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a567f817301870b14b63b9607a8f1c182">updateScheduledMaintenanceStatus</a> ($assetId, $maintenanceType)</td></tr>
<tr class="separator:a567f817301870b14b63b9607a8f1c182"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe2396d040b8fdb1946a63702f30e67c" id="r_abe2396d040b8fdb1946a63702f30e67c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abe2396d040b8fdb1946a63702f30e67c">getChecklistItems</a> ($guidelineId)</td></tr>
<tr class="separator:abe2396d040b8fdb1946a63702f30e67c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5777d866bcf1d449e676c3d7eee1901" id="r_ab5777d866bcf1d449e676c3d7eee1901"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab5777d866bcf1d449e676c3d7eee1901">getCompletedChecklistItems</a> ($maintenanceId, $guidelineId)</td></tr>
<tr class="separator:ab5777d866bcf1d449e676c3d7eee1901"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8dcecd02b7451e4ce6f807170b758049" id="r_a8dcecd02b7451e4ce6f807170b758049"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8dcecd02b7451e4ce6f807170b758049">getAllCompletedChecklistItems</a> ($maintenanceId)</td></tr>
<tr class="separator:a8dcecd02b7451e4ce6f807170b758049"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95bb0e0eb1c41a1c8bec6ce98a0f7cb6" id="r_a95bb0e0eb1c41a1c8bec6ce98a0f7cb6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a95bb0e0eb1c41a1c8bec6ce98a0f7cb6">recordChecklistCompletion</a> ($maintenanceId, $checklistId, $completed=true, $completedBy=null, $notes='')</td></tr>
<tr class="separator:a95bb0e0eb1c41a1c8bec6ce98a0f7cb6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a61baaaf1adfacd6574436248f298281e" id="r_a61baaaf1adfacd6574436248f298281e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a61baaaf1adfacd6574436248f298281e">logGuidelineImplementation</a> ($maintenanceId, $guidelineId)</td></tr>
<tr class="separator:a61baaaf1adfacd6574436248f298281e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac2a53c38ccfa1c46b885ac7c2811243" id="r_aac2a53c38ccfa1c46b885ac7c2811243"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aac2a53c38ccfa1c46b885ac7c2811243">updateAllHealthMetrics</a> ()</td></tr>
<tr class="separator:aac2a53c38ccfa1c46b885ac7c2811243"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6be536865b41000e5ca2a1bb8994080" id="r_ac6be536865b41000e5ca2a1bb8994080"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac6be536865b41000e5ca2a1bb8994080">getImplementedGuidelines</a> ($maintenanceId)</td></tr>
<tr class="separator:ac6be536865b41000e5ca2a1bb8994080"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4705bd8f1afc9de6401a5c14082ceca9" id="r_a4705bd8f1afc9de6401a5c14082ceca9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4705bd8f1afc9de6401a5c14082ceca9">getDetailedImplementedGuidelines</a> ($maintenanceId)</td></tr>
<tr class="separator:a4705bd8f1afc9de6401a5c14082ceca9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3479744b4309d54812e49f4fdf605c28" id="r_a3479744b4309d54812e49f4fdf605c28"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3479744b4309d54812e49f4fdf605c28">getMaintenanceHistoryWithGuidelines</a> ($assetId)</td></tr>
<tr class="separator:a3479744b4309d54812e49f4fdf605c28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a712d5e3692faa0c9e6f6cb52079a36bf" id="r_a712d5e3692faa0c9e6f6cb52079a36bf"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a712d5e3692faa0c9e6f6cb52079a36bf">getGuidelineImplementations</a> ($guidelineId, $limit=null, $offset=0)</td></tr>
<tr class="separator:a712d5e3692faa0c9e6f6cb52079a36bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab101e35ab0da74f49df5f58253d43da3" id="r_ab101e35ab0da74f49df5f58253d43da3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab101e35ab0da74f49df5f58253d43da3">checkDataIntegrity</a> ()</td></tr>
<tr class="separator:ab101e35ab0da74f49df5f58253d43da3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afded54f2a79d75797c141f1c076d27ca" id="r_afded54f2a79d75797c141f1c076d27ca"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afded54f2a79d75797c141f1c076d27ca">fixOrphanedRecords</a> ()</td></tr>
<tr class="separator:afded54f2a79d75797c141f1c076d27ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe614689b30c18a752d2ce6be9e01e49" id="r_abe614689b30c18a752d2ce6be9e01e49"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abe614689b30c18a752d2ce6be9e01e49">findMissingMaintenanceRecords</a> ()</td></tr>
<tr class="separator:abe614689b30c18a752d2ce6be9e01e49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9cbd796bd274dad9c2a2ef7eac75dbff" id="r_a9cbd796bd274dad9c2a2ef7eac75dbff"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9cbd796bd274dad9c2a2ef7eac75dbff">recreateMissingRecords</a> ()</td></tr>
<tr class="separator:a9cbd796bd274dad9c2a2ef7eac75dbff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcedfb512247d32097f1d74a9e118ee9" id="r_adcedfb512247d32097f1d74a9e118ee9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adcedfb512247d32097f1d74a9e118ee9">getMostImplementedGuidelines</a> ($limit=null)</td></tr>
<tr class="separator:adcedfb512247d32097f1d74a9e118ee9"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a49112508b667299f63c3e43993c89ac1" name="a49112508b667299f63c3e43993c89ac1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a49112508b667299f63c3e43993c89ac1">&#9670;&#160;</a></span>addMaintenance()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">addMaintenance </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add maintenance record</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int|bool The new maintenance ID or false on failure </dd></dl>

</div>
</div>
<a id="afb3f191cdd7cdb9724de3d59626b162a" name="afb3f191cdd7cdb9724de3d59626b162a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afb3f191cdd7cdb9724de3d59626b162a">&#9670;&#160;</a></span>calculateAssetHealthScore()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">calculateAssetHealthScore </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Calculate health score for an asset</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>float </dd></dl>

</div>
</div>
<a id="ab101e35ab0da74f49df5f58253d43da3" name="ab101e35ab0da74f49df5f58253d43da3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab101e35ab0da74f49df5f58253d43da3">&#9670;&#160;</a></span>checkDataIntegrity()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">checkDataIntegrity </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Check data integrity of the maintenance_guideline_implementation table Identifies orphaned records (records with no corresponding maintenance_history or guideline)</p>
<dl class="section return"><dt>Returns</dt><dd>array Results of the integrity check </dd></dl>

</div>
</div>
<a id="aa9447fe3595934e7036eeba1eee31bae" name="aa9447fe3595934e7036eeba1eee31bae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa9447fe3595934e7036eeba1eee31bae">&#9670;&#160;</a></span>countAllMaintenanceHistory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">countAllMaintenanceHistory </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Count all maintenance history records</p>
<dl class="section return"><dt>Returns</dt><dd>int </dd></dl>

</div>
</div>
<a id="a38aac20b8e7f44f2cf503808243794ec" name="a38aac20b8e7f44f2cf503808243794ec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a38aac20b8e7f44f2cf503808243794ec">&#9670;&#160;</a></span>countAssetsWithHealthMetrics()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">countAssetsWithHealthMetrics </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$search</em></span><span class="paramdefsep"> = </span><span class="paramdefval">''</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Count total assets with health metrics (for pagination)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$search</td><td>Optional search term for filtering </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int </dd></dl>

</div>
</div>
<a id="a307b0ec1c1343e097ffa8d0a058cc353" name="a307b0ec1c1343e097ffa8d0a058cc353"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a307b0ec1c1343e097ffa8d0a058cc353">&#9670;&#160;</a></span>ensureGuidelineImplementationTableExists()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ensureGuidelineImplementationTableExists </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Ensure the maintenance_guideline_implementation table exists This is called once during initialization to prevent repeated table creation</p>
<dl class="section return"><dt>Returns</dt><dd>bool True if the table exists or was created successfully </dd></dl>

</div>
</div>
<a id="abe614689b30c18a752d2ce6be9e01e49" name="abe614689b30c18a752d2ce6be9e01e49"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe614689b30c18a752d2ce6be9e01e49">&#9670;&#160;</a></span>findMissingMaintenanceRecords()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">findMissingMaintenanceRecords </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Find missing maintenance history records Identifies implementation records where the maintenance_history record is missing</p>
<dl class="section return"><dt>Returns</dt><dd>array Results of the search </dd></dl>

</div>
</div>
<a id="afded54f2a79d75797c141f1c076d27ca" name="afded54f2a79d75797c141f1c076d27ca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afded54f2a79d75797c141f1c076d27ca">&#9670;&#160;</a></span>fixOrphanedRecords()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">fixOrphanedRecords </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Fix orphaned records in the maintenance_guideline_implementation table Removes records with no corresponding maintenance_history or guideline</p>
<dl class="section return"><dt>Returns</dt><dd>array Results of the fix operation </dd></dl>

</div>
</div>
<a id="a8dcecd02b7451e4ce6f807170b758049" name="a8dcecd02b7451e4ce6f807170b758049"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8dcecd02b7451e4ce6f807170b758049">&#9670;&#160;</a></span>getAllCompletedChecklistItems()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAllCompletedChecklistItems </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all completed checklist items for a maintenance record</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$maintenanceId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="ace2e334dc067d1e99ab1cf4bed40359f" name="ace2e334dc067d1e99ab1cf4bed40359f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ace2e334dc067d1e99ab1cf4bed40359f">&#9670;&#160;</a></span>getAllMaintenanceHistory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAllMaintenanceHistory </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$limit</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$offset</em></span><span class="paramdefsep"> = </span><span class="paramdefval">0</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all maintenance history records</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$limit</td><td>Optional limit for number of records to return </td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$offset</td><td>Optional offset for pagination </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a3712d99605d1092dd417d6813adfb838" name="a3712d99605d1092dd417d6813adfb838"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3712d99605d1092dd417d6813adfb838">&#9670;&#160;</a></span>getAllScheduledMaintenance()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAllScheduledMaintenance </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all scheduled maintenance records for an asset</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="af337d34247442623d3ef21c81c77b5c7" name="af337d34247442623d3ef21c81c77b5c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af337d34247442623d3ef21c81c77b5c7">&#9670;&#160;</a></span>getAssetsDueForMaintenance()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetsDueForMaintenance </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$daysAhead</em></span><span class="paramdefsep"> = </span><span class="paramdefval">30</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get assets due for maintenance</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$daysAhead</td><td>Look ahead days </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a234816f2513b32ba0fea57902e2ca897" name="a234816f2513b32ba0fea57902e2ca897"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a234816f2513b32ba0fea57902e2ca897">&#9670;&#160;</a></span>getAssetsWithHealthMetrics()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetsWithHealthMetrics </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$limit</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$offset</em></span><span class="paramdefsep"> = </span><span class="paramdefval">0</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$search</em></span><span class="paramdefsep"> = </span><span class="paramdefval">''</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$orderBy</em></span><span class="paramdefsep"> = </span><span class="paramdefval">'health_score'</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$orderDir</em></span><span class="paramdefsep"> = </span><span class="paramdefval">'ASC'</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get assets with health metrics</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$limit</td><td>Optional limit for pagination </td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$offset</td><td>Optional offset for pagination </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$search</td><td>Optional search term for filtering </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$orderBy</td><td>Optional column to order by </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$orderDir</td><td>Optional direction (ASC or DESC) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="abe2396d040b8fdb1946a63702f30e67c" name="abe2396d040b8fdb1946a63702f30e67c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe2396d040b8fdb1946a63702f30e67c">&#9670;&#160;</a></span>getChecklistItems()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getChecklistItems </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get checklist items for a guideline</p>
<p>Note: This method duplicates functionality in <a class="el" href="class_maintenance_guideline.html#abe2396d040b8fdb1946a63702f30e67c">MaintenanceGuideline\getChecklistItems</a> Consider using <a class="el" href="class_maintenance_guideline.html#abe2396d040b8fdb1946a63702f30e67c">MaintenanceGuideline\getChecklistItems</a> instead to avoid duplication</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="ab5777d866bcf1d449e676c3d7eee1901" name="ab5777d866bcf1d449e676c3d7eee1901"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab5777d866bcf1d449e676c3d7eee1901">&#9670;&#160;</a></span>getCompletedChecklistItems()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getCompletedChecklistItems </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get completed checklist items for a maintenance record and guideline</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$maintenanceId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a4705bd8f1afc9de6401a5c14082ceca9" name="a4705bd8f1afc9de6401a5c14082ceca9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4705bd8f1afc9de6401a5c14082ceca9">&#9670;&#160;</a></span>getDetailedImplementedGuidelines()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getDetailedImplementedGuidelines </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get detailed information about implemented guidelines for a maintenance record including checklist items</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$maintenanceId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a712d5e3692faa0c9e6f6cb52079a36bf" name="a712d5e3692faa0c9e6f6cb52079a36bf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a712d5e3692faa0c9e6f6cb52079a36bf">&#9670;&#160;</a></span>getGuidelineImplementations()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getGuidelineImplementations </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$limit</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$offset</em></span><span class="paramdefsep"> = </span><span class="paramdefval">0</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all implementations of a specific guideline across different maintenance records</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$limit</td><td>Optional limit for number of records to return </td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$offset</td><td>Optional offset for pagination </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="ac6be536865b41000e5ca2a1bb8994080" name="ac6be536865b41000e5ca2a1bb8994080"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac6be536865b41000e5ca2a1bb8994080">&#9670;&#160;</a></span>getImplementedGuidelines()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getImplementedGuidelines </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get implemented guidelines for a maintenance record</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$maintenanceId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a308b6d3e1f8e959f87bf5ae8f6c93ae6" name="a308b6d3e1f8e959f87bf5ae8f6c93ae6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a308b6d3e1f8e959f87bf5ae8f6c93ae6">&#9670;&#160;</a></span>getMaintenanceById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getMaintenanceById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get maintenance record by ID</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object|false </dd></dl>

</div>
</div>
<a id="a5028d7b9ec8e052e69d9f683e1ebfd07" name="a5028d7b9ec8e052e69d9f683e1ebfd07"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5028d7b9ec8e052e69d9f683e1ebfd07">&#9670;&#160;</a></span>getMaintenanceHistory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getMaintenanceHistory </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get maintenance history for an asset</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="a3479744b4309d54812e49f4fdf605c28" name="a3479744b4309d54812e49f4fdf605c28"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3479744b4309d54812e49f4fdf605c28">&#9670;&#160;</a></span>getMaintenanceHistoryWithGuidelines()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getMaintenanceHistoryWithGuidelines </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get maintenance records with implemented guidelines for an asset</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array </dd></dl>

</div>
</div>
<a id="adcedfb512247d32097f1d74a9e118ee9" name="adcedfb512247d32097f1d74a9e118ee9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adcedfb512247d32097f1d74a9e118ee9">&#9670;&#160;</a></span>getMostImplementedGuidelines()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getMostImplementedGuidelines </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$limit</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get most frequently implemented guidelines Returns a list of guidelines sorted by implementation count</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$limit</td><td>Optional limit for number of records to return (null for all records) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array List of guidelines with implementation counts </dd></dl>

</div>
</div>
<a id="adac8c5043e0bc976a86e615fce9b4a0d" name="adac8c5043e0bc976a86e615fce9b4a0d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adac8c5043e0bc976a86e615fce9b4a0d">&#9670;&#160;</a></span>getScheduledMaintenanceId()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getScheduledMaintenanceId </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceType</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get scheduled maintenance record ID</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$maintenanceType</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int|false </dd></dl>

</div>
</div>
<a id="a61baaaf1adfacd6574436248f298281e" name="a61baaaf1adfacd6574436248f298281e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a61baaaf1adfacd6574436248f298281e">&#9670;&#160;</a></span>logGuidelineImplementation()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">logGuidelineImplementation </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guidelineId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Log guideline implementation for a maintenance record</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$maintenanceId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$guidelineId</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="a95bb0e0eb1c41a1c8bec6ce98a0f7cb6" name="a95bb0e0eb1c41a1c8bec6ce98a0f7cb6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a95bb0e0eb1c41a1c8bec6ce98a0f7cb6">&#9670;&#160;</a></span>recordChecklistCompletion()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">recordChecklistCompletion </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$checklistId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$completed</em></span><span class="paramdefsep"> = </span><span class="paramdefval">true</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$completedBy</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$notes</em></span><span class="paramdefsep"> = </span><span class="paramdefval">''</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Record checklist completion</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$maintenanceId</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$checklistId</td><td></td></tr>
    <tr><td class="paramtype">bool</td><td class="paramname">$completed</td><td></td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$completedBy</td><td></td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$notes</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="a9cbd796bd274dad9c2a2ef7eac75dbff" name="a9cbd796bd274dad9c2a2ef7eac75dbff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9cbd796bd274dad9c2a2ef7eac75dbff">&#9670;&#160;</a></span>recreateMissingRecords()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">recreateMissingRecords </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Recreate missing maintenance history records Creates new maintenance records based on implementation records</p>
<dl class="section return"><dt>Returns</dt><dd>array Results of the recreation </dd></dl>

</div>
</div>
<a id="aac2a53c38ccfa1c46b885ac7c2811243" name="aac2a53c38ccfa1c46b885ac7c2811243"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aac2a53c38ccfa1c46b885ac7c2811243">&#9670;&#160;</a></span>updateAllHealthMetrics()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateAllHealthMetrics </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update health metrics for all assets</p>
<dl class="section return"><dt>Returns</dt><dd>int Number of assets updated </dd></dl>

</div>
</div>
<a id="a567f817301870b14b63b9607a8f1c182" name="a567f817301870b14b63b9607a8f1c182"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a567f817301870b14b63b9607a8f1c182">&#9670;&#160;</a></span>updateScheduledMaintenanceStatus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateScheduledMaintenanceStatus </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$maintenanceType</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update scheduled maintenance record to completed</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$assetId</td><td></td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$maintenanceType</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/models/<a class="el" href="_maintenance_model_8php.html">MaintenanceModel.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
