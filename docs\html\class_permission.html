<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Permission Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">Permission Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6bcadef8c7da37eb84ae95bb9b07e069" id="r_a6bcadef8c7da37eb84ae95bb9b07e069"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6bcadef8c7da37eb84ae95bb9b07e069">getAllPermissions</a> ()</td></tr>
<tr class="separator:a6bcadef8c7da37eb84ae95bb9b07e069"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9a3e9fc5bf30a63366590b19e489a58f" id="r_a9a3e9fc5bf30a63366590b19e489a58f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9a3e9fc5bf30a63366590b19e489a58f">getPermissionsByCategory</a> ($category)</td></tr>
<tr class="separator:a9a3e9fc5bf30a63366590b19e489a58f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a772b8c55b241668e6031bb263f5d832f" id="r_a772b8c55b241668e6031bb263f5d832f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a772b8c55b241668e6031bb263f5d832f">getAllCategories</a> ()</td></tr>
<tr class="separator:a772b8c55b241668e6031bb263f5d832f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3238707bb055c0edcccbd3aad5bd9a88" id="r_a3238707bb055c0edcccbd3aad5bd9a88"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3238707bb055c0edcccbd3aad5bd9a88">getPermissionById</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a3238707bb055c0edcccbd3aad5bd9a88"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef23bd8752f6255ed9ce7e15e41bba42" id="r_aef23bd8752f6255ed9ce7e15e41bba42"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aef23bd8752f6255ed9ce7e15e41bba42">getPermissionByName</a> ($name)</td></tr>
<tr class="separator:aef23bd8752f6255ed9ce7e15e41bba42"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf3185ee0140ecaef58f1d0c606db1f9" id="r_aaf3185ee0140ecaef58f1d0c606db1f9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaf3185ee0140ecaef58f1d0c606db1f9">createPermission</a> ($data)</td></tr>
<tr class="separator:aaf3185ee0140ecaef58f1d0c606db1f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9778cc0652a4dc666b7b05b3e514946a" id="r_a9778cc0652a4dc666b7b05b3e514946a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9778cc0652a4dc666b7b05b3e514946a">updatePermission</a> ($data)</td></tr>
<tr class="separator:a9778cc0652a4dc666b7b05b3e514946a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a294117b98d3b789cd31258bb83e961" id="r_a6a294117b98d3b789cd31258bb83e961"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6a294117b98d3b789cd31258bb83e961">deletePermission</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a6a294117b98d3b789cd31258bb83e961"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ee9b1302a08d6848721bd2f6f75f3b4" id="r_a5ee9b1302a08d6848721bd2f6f75f3b4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5ee9b1302a08d6848721bd2f6f75f3b4">getRolesWithPermission</a> ($permissionId)</td></tr>
<tr class="separator:a5ee9b1302a08d6848721bd2f6f75f3b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="class_permission.html">Permission</a> Model Handles database operations for permissions </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="aaf3185ee0140ecaef58f1d0c606db1f9" name="aaf3185ee0140ecaef58f1d0c606db1f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaf3185ee0140ecaef58f1d0c606db1f9">&#9670;&#160;</a></span>createPermission()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">createPermission </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Create a new permission</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td><a class="el" href="class_permission.html">Permission</a> data </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if successful, false otherwise </dd></dl>

</div>
</div>
<a id="a6a294117b98d3b789cd31258bb83e961" name="a6a294117b98d3b789cd31258bb83e961"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6a294117b98d3b789cd31258bb83e961">&#9670;&#160;</a></span>deletePermission()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">deletePermission </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete a permission</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td><a class="el" href="class_permission.html">Permission</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if successful, false otherwise </dd></dl>

</div>
</div>
<a id="a772b8c55b241668e6031bb263f5d832f" name="a772b8c55b241668e6031bb263f5d832f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a772b8c55b241668e6031bb263f5d832f">&#9670;&#160;</a></span>getAllCategories()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAllCategories </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all permission categories</p>
<dl class="section return"><dt>Returns</dt><dd>array Array of category names </dd></dl>

</div>
</div>
<a id="a6bcadef8c7da37eb84ae95bb9b07e069" name="a6bcadef8c7da37eb84ae95bb9b07e069"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6bcadef8c7da37eb84ae95bb9b07e069">&#9670;&#160;</a></span>getAllPermissions()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAllPermissions </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all permissions</p>
<dl class="section return"><dt>Returns</dt><dd>array Array of permission objects </dd></dl>

</div>
</div>
<a id="a3238707bb055c0edcccbd3aad5bd9a88" name="a3238707bb055c0edcccbd3aad5bd9a88"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3238707bb055c0edcccbd3aad5bd9a88">&#9670;&#160;</a></span>getPermissionById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getPermissionById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get permission by ID</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td><a class="el" href="class_permission.html">Permission</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object <a class="el" href="class_permission.html">Permission</a> object </dd></dl>

</div>
</div>
<a id="aef23bd8752f6255ed9ce7e15e41bba42" name="aef23bd8752f6255ed9ce7e15e41bba42"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aef23bd8752f6255ed9ce7e15e41bba42">&#9670;&#160;</a></span>getPermissionByName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getPermissionByName </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$name</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get permission by name</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$name</td><td><a class="el" href="class_permission.html">Permission</a> name </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object <a class="el" href="class_permission.html">Permission</a> object </dd></dl>

</div>
</div>
<a id="a9a3e9fc5bf30a63366590b19e489a58f" name="a9a3e9fc5bf30a63366590b19e489a58f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9a3e9fc5bf30a63366590b19e489a58f">&#9670;&#160;</a></span>getPermissionsByCategory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getPermissionsByCategory </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$category</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get permissions by category</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$category</td><td>Category name </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array Array of permission objects </dd></dl>

</div>
</div>
<a id="a5ee9b1302a08d6848721bd2f6f75f3b4" name="a5ee9b1302a08d6848721bd2f6f75f3b4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5ee9b1302a08d6848721bd2f6f75f3b4">&#9670;&#160;</a></span>getRolesWithPermission()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getRolesWithPermission </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$permissionId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all roles that have a specific permission</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$permissionId</td><td><a class="el" href="class_permission.html">Permission</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array Array of role objects </dd></dl>

</div>
</div>
<a id="a9778cc0652a4dc666b7b05b3e514946a" name="a9778cc0652a4dc666b7b05b3e514946a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9778cc0652a4dc666b7b05b3e514946a">&#9670;&#160;</a></span>updatePermission()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updatePermission </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update a permission</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td><a class="el" href="class_permission.html">Permission</a> data </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if successful, false otherwise </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/models/<a class="el" href="_permission_8php.html">Permission.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
