<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: SecurityEnhancements Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">SecurityEnhancements Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a013913ef660fea9e6f3af59afd767b96" id="r_a013913ef660fea9e6f3af59afd767b96"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a013913ef660fea9e6f3af59afd767b96">logSecurityEvent</a> ($eventType, $description, $userId=null)</td></tr>
<tr class="separator:a013913ef660fea9e6f3af59afd767b96"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4206d1d37283f2055dcf0f4295a7a693" id="r_a4206d1d37283f2055dcf0f4295a7a693"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4206d1d37283f2055dcf0f4295a7a693">recordLoginAttempt</a> ($email, $success, $userId=null)</td></tr>
<tr class="separator:a4206d1d37283f2055dcf0f4295a7a693"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cf9beec2eb67fb4f7662468376c03ef" id="r_a2cf9beec2eb67fb4f7662468376c03ef"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2cf9beec2eb67fb4f7662468376c03ef">isAccountLocked</a> ($userId)</td></tr>
<tr class="separator:a2cf9beec2eb67fb4f7662468376c03ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae969c75aed9842b49aa3095c0b8ec0d2" id="r_ae969c75aed9842b49aa3095c0b8ec0d2"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae969c75aed9842b49aa3095c0b8ec0d2">incrementFailedLoginAttempts</a> ($userId)</td></tr>
<tr class="separator:ae969c75aed9842b49aa3095c0b8ec0d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3e149b33afa4881d81ed72bc8598d05" id="r_ac3e149b33afa4881d81ed72bc8598d05"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac3e149b33afa4881d81ed72bc8598d05">resetFailedLoginAttempts</a> ($userId)</td></tr>
<tr class="separator:ac3e149b33afa4881d81ed72bc8598d05"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0695c274c2a2d9dc44179baf89f2f2d0" id="r_a0695c274c2a2d9dc44179baf89f2f2d0"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0695c274c2a2d9dc44179baf89f2f2d0">lockAccount</a> ($userId, $reason='Too many failed login attempts')</td></tr>
<tr class="separator:a0695c274c2a2d9dc44179baf89f2f2d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2958f6a8493757a62052e1b2e6d7e37c" id="r_a2958f6a8493757a62052e1b2e6d7e37c"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2958f6a8493757a62052e1b2e6d7e37c">unlockAccount</a> ($userId)</td></tr>
<tr class="separator:a2958f6a8493757a62052e1b2e6d7e37c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a1d971fda6ba0d428b6627bd57cd7cf" id="r_a6a1d971fda6ba0d428b6627bd57cd7cf"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6a1d971fda6ba0d428b6627bd57cd7cf">checkPasswordResetRateLimit</a> ($email)</td></tr>
<tr class="separator:a6a1d971fda6ba0d428b6627bd57cd7cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad153125ec23b30e3f7401166f6b250e3" id="r_ad153125ec23b30e3f7401166f6b250e3"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad153125ec23b30e3f7401166f6b250e3">createRememberMeToken</a> ($userId)</td></tr>
<tr class="separator:ad153125ec23b30e3f7401166f6b250e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d1905793279ffb8e49cd3a33a8adc7e" id="r_a2d1905793279ffb8e49cd3a33a8adc7e"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2d1905793279ffb8e49cd3a33a8adc7e">verifyRememberMeToken</a> ($selector, $validator)</td></tr>
<tr class="separator:a2d1905793279ffb8e49cd3a33a8adc7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ed7e1d4d635331234fc654f24757b20" id="r_a8ed7e1d4d635331234fc654f24757b20"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8ed7e1d4d635331234fc654f24757b20">deleteRememberMeToken</a> ($selector)</td></tr>
<tr class="separator:a8ed7e1d4d635331234fc654f24757b20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa4fd9e09fb2a0c403858e63983a319d5" id="r_aa4fd9e09fb2a0c403858e63983a319d5"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa4fd9e09fb2a0c403858e63983a319d5">deleteAllRememberMeTokens</a> ($userId)</td></tr>
<tr class="separator:aa4fd9e09fb2a0c403858e63983a319d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75213e4a19f7cf342423d6aa3742735d" id="r_a75213e4a19f7cf342423d6aa3742735d"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a75213e4a19f7cf342423d6aa3742735d">checkImportRateLimit</a> ($userId)</td></tr>
<tr class="separator:a75213e4a19f7cf342423d6aa3742735d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac583ea07fd84f5b1cd5d04f28d7dcd43" id="r_ac583ea07fd84f5b1cd5d04f28d7dcd43"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac583ea07fd84f5b1cd5d04f28d7dcd43">recordImportOperation</a> ($userId, $fileName, $fileSize, $success=true)</td></tr>
<tr class="separator:ac583ea07fd84f5b1cd5d04f28d7dcd43"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a791ef10ab64879493bfba71f2216445e" id="r_a791ef10ab64879493bfba71f2216445e"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a791ef10ab64879493bfba71f2216445e">validateFileSize</a> ($fileSize)</td></tr>
<tr class="separator:a791ef10ab64879493bfba71f2216445e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4244442b4f4abeeddaca69cf96f3d95b" id="r_a4244442b4f4abeeddaca69cf96f3d95b"><td class="memItemLeft" align="right" valign="top">static&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4244442b4f4abeeddaca69cf96f3d95b">validateCsvContent</a> ($filePath)</td></tr>
<tr class="separator:a4244442b4f4abeeddaca69cf96f3d95b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a0c9fe713d3d1b6f166d8768aaf619275" id="r_a0c9fe713d3d1b6f166d8768aaf619275"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0c9fe713d3d1b6f166d8768aaf619275">MAX_LOGIN_ATTEMPTS</a> = 5</td></tr>
<tr class="separator:a0c9fe713d3d1b6f166d8768aaf619275"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a297fc9db6f9eccebc5f8479a2ff3c585" id="r_a297fc9db6f9eccebc5f8479a2ff3c585"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a297fc9db6f9eccebc5f8479a2ff3c585">LOCKOUT_DURATION</a> = 30</td></tr>
<tr class="separator:a297fc9db6f9eccebc5f8479a2ff3c585"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1340b7d8521a3f20538fa27725ec834" id="r_ab1340b7d8521a3f20538fa27725ec834"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab1340b7d8521a3f20538fa27725ec834">PASSWORD_RESET_RATE_LIMIT</a> = 3</td></tr>
<tr class="separator:ab1340b7d8521a3f20538fa27725ec834"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb4b1920fd090c41e8a9d3b91ce9f124" id="r_abb4b1920fd090c41e8a9d3b91ce9f124"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abb4b1920fd090c41e8a9d3b91ce9f124">IMPORT_RATE_LIMIT</a> = 5</td></tr>
<tr class="separator:abb4b1920fd090c41e8a9d3b91ce9f124"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7a77c70c17891d528d8d405ba8111c30" id="r_a7a77c70c17891d528d8d405ba8111c30"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7a77c70c17891d528d8d405ba8111c30">MAX_IMPORT_FILE_SIZE</a> = 10485760</td></tr>
<tr class="separator:a7a77c70c17891d528d8d405ba8111c30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fe72921270c08d48d6b9b95648153e3" id="r_a3fe72921270c08d48d6b9b95648153e3"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3fe72921270c08d48d6b9b95648153e3">REMEMBER_ME_EXPIRY</a> = 30</td></tr>
<tr class="separator:a3fe72921270c08d48d6b9b95648153e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="class_security.html">Security</a> Enhancements Helper Contains methods for enhanced security features </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a75213e4a19f7cf342423d6aa3742735d" name="a75213e4a19f7cf342423d6aa3742735d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a75213e4a19f7cf342423d6aa3742735d">&#9670;&#160;</a></span>checkImportRateLimit()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static checkImportRateLimit </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Check if import operations are within rate limits</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if within rate limits, false otherwise </dd></dl>

</div>
</div>
<a id="a6a1d971fda6ba0d428b6627bd57cd7cf" name="a6a1d971fda6ba0d428b6627bd57cd7cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6a1d971fda6ba0d428b6627bd57cd7cf">&#9670;&#160;</a></span>checkPasswordResetRateLimit()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static checkPasswordResetRateLimit </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$email</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Check if password reset requests are within rate limits</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$email</td><td>Email address </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if within rate limits, false otherwise </dd></dl>

</div>
</div>
<a id="ad153125ec23b30e3f7401166f6b250e3" name="ad153125ec23b30e3f7401166f6b250e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad153125ec23b30e3f7401166f6b250e3">&#9670;&#160;</a></span>createRememberMeToken()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static createRememberMeToken </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Create a remember me token</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array|bool Token data if created successfully, false otherwise </dd></dl>

</div>
</div>
<a id="aa4fd9e09fb2a0c403858e63983a319d5" name="aa4fd9e09fb2a0c403858e63983a319d5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa4fd9e09fb2a0c403858e63983a319d5">&#9670;&#160;</a></span>deleteAllRememberMeTokens()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static deleteAllRememberMeTokens </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Delete all remember me tokens for a user</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if deleted successfully, false otherwise </dd></dl>

</div>
</div>
<a id="a8ed7e1d4d635331234fc654f24757b20" name="a8ed7e1d4d635331234fc654f24757b20"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8ed7e1d4d635331234fc654f24757b20">&#9670;&#160;</a></span>deleteRememberMeToken()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static deleteRememberMeToken </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$selector</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Delete a remember me token</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$selector</td><td>Token selector </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if deleted successfully, false otherwise </dd></dl>

</div>
</div>
<a id="ae969c75aed9842b49aa3095c0b8ec0d2" name="ae969c75aed9842b49aa3095c0b8ec0d2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae969c75aed9842b49aa3095c0b8ec0d2">&#9670;&#160;</a></span>incrementFailedLoginAttempts()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static incrementFailedLoginAttempts </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Increment failed login attempts and lock account if necessary</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if account was locked, false otherwise </dd></dl>

</div>
</div>
<a id="a2cf9beec2eb67fb4f7662468376c03ef" name="a2cf9beec2eb67fb4f7662468376c03ef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2cf9beec2eb67fb4f7662468376c03ef">&#9670;&#160;</a></span>isAccountLocked()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static isAccountLocked </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Check if an account is locked</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID to check </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if account is locked, false otherwise </dd></dl>

</div>
</div>
<a id="a0695c274c2a2d9dc44179baf89f2f2d0" name="a0695c274c2a2d9dc44179baf89f2f2d0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0695c274c2a2d9dc44179baf89f2f2d0">&#9670;&#160;</a></span>lockAccount()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static lockAccount </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$reason</em></span><span class="paramdefsep"> = </span><span class="paramdefval">'Too&#160;many&#160;failed&#160;login&#160;attempts'</span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Lock a user account</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID to lock </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$reason</td><td>Reason for lockout </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if locked successfully, false otherwise </dd></dl>

</div>
</div>
<a id="a013913ef660fea9e6f3af59afd767b96" name="a013913ef660fea9e6f3af59afd767b96"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a013913ef660fea9e6f3af59afd767b96">&#9670;&#160;</a></span>logSecurityEvent()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static logSecurityEvent </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$eventType</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$description</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Log a security event</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$eventType</td><td>The type of security event </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$description</td><td>Description of the event </td></tr>
    <tr><td class="paramtype">int&#160;|&#160;null</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID (if applicable) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if logged successfully, false otherwise </dd></dl>

</div>
</div>
<a id="ac583ea07fd84f5b1cd5d04f28d7dcd43" name="ac583ea07fd84f5b1cd5d04f28d7dcd43"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac583ea07fd84f5b1cd5d04f28d7dcd43">&#9670;&#160;</a></span>recordImportOperation()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static recordImportOperation </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$fileName</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$fileSize</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$success</em></span><span class="paramdefsep"> = </span><span class="paramdefval">true</span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Record an import operation</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$fileName</td><td>Name of the imported file </td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$fileSize</td><td>Size of the imported file in bytes </td></tr>
    <tr><td class="paramtype">bool</td><td class="paramname">$success</td><td>Whether the import was successful </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if recorded successfully, false otherwise </dd></dl>

</div>
</div>
<a id="a4206d1d37283f2055dcf0f4295a7a693" name="a4206d1d37283f2055dcf0f4295a7a693"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4206d1d37283f2055dcf0f4295a7a693">&#9670;&#160;</a></span>recordLoginAttempt()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static recordLoginAttempt </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$email</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$success</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span><span class="paramdefsep"> = </span><span class="paramdefval">null</span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Record a login attempt</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$email</td><td>Email address used in the attempt </td></tr>
    <tr><td class="paramtype">bool</td><td class="paramname">$success</td><td>Whether the login was successful </td></tr>
    <tr><td class="paramtype">int&#160;|&#160;null</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID (if successful) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if recorded successfully, false otherwise </dd></dl>

</div>
</div>
<a id="ac3e149b33afa4881d81ed72bc8598d05" name="ac3e149b33afa4881d81ed72bc8598d05"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac3e149b33afa4881d81ed72bc8598d05">&#9670;&#160;</a></span>resetFailedLoginAttempts()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static resetFailedLoginAttempts </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Reset failed login attempts counter</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if reset successfully, false otherwise </dd></dl>

</div>
</div>
<a id="a2958f6a8493757a62052e1b2e6d7e37c" name="a2958f6a8493757a62052e1b2e6d7e37c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2958f6a8493757a62052e1b2e6d7e37c">&#9670;&#160;</a></span>unlockAccount()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static unlockAccount </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Unlock a user account</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID to unlock </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if unlocked successfully, false otherwise </dd></dl>

</div>
</div>
<a id="a4244442b4f4abeeddaca69cf96f3d95b" name="a4244442b4f4abeeddaca69cf96f3d95b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4244442b4f4abeeddaca69cf96f3d95b">&#9670;&#160;</a></span>validateCsvContent()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static validateCsvContent </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$filePath</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Validate CSV file content</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$filePath</td><td>Path to the CSV file </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool|array True if valid, array of errors if invalid </dd></dl>

</div>
</div>
<a id="a791ef10ab64879493bfba71f2216445e" name="a791ef10ab64879493bfba71f2216445e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a791ef10ab64879493bfba71f2216445e">&#9670;&#160;</a></span>validateFileSize()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static validateFileSize </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$fileSize</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Validate file size</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$fileSize</td><td>Size of the file in bytes </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if file size is valid, false otherwise </dd></dl>

</div>
</div>
<a id="a2d1905793279ffb8e49cd3a33a8adc7e" name="a2d1905793279ffb8e49cd3a33a8adc7e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d1905793279ffb8e49cd3a33a8adc7e">&#9670;&#160;</a></span>verifyRememberMeToken()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static verifyRememberMeToken </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$selector</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$validator</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Verify a remember me token</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$selector</td><td>Token selector </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$validator</td><td>Token validator </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int|bool <a class="el" href="class_user.html">User</a> ID if token is valid, false otherwise </dd></dl>

</div>
</div>
<h2 class="groupheader">Field Documentation</h2>
<a id="abb4b1920fd090c41e8a9d3b91ce9f124" name="abb4b1920fd090c41e8a9d3b91ce9f124"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abb4b1920fd090c41e8a9d3b91ce9f124">&#9670;&#160;</a></span>IMPORT_RATE_LIMIT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const IMPORT_RATE_LIMIT = 5</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a297fc9db6f9eccebc5f8479a2ff3c585" name="a297fc9db6f9eccebc5f8479a2ff3c585"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a297fc9db6f9eccebc5f8479a2ff3c585">&#9670;&#160;</a></span>LOCKOUT_DURATION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const LOCKOUT_DURATION = 30</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7a77c70c17891d528d8d405ba8111c30" name="a7a77c70c17891d528d8d405ba8111c30"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7a77c70c17891d528d8d405ba8111c30">&#9670;&#160;</a></span>MAX_IMPORT_FILE_SIZE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const MAX_IMPORT_FILE_SIZE = 10485760</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0c9fe713d3d1b6f166d8768aaf619275" name="a0c9fe713d3d1b6f166d8768aaf619275"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0c9fe713d3d1b6f166d8768aaf619275">&#9670;&#160;</a></span>MAX_LOGIN_ATTEMPTS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const MAX_LOGIN_ATTEMPTS = 5</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab1340b7d8521a3f20538fa27725ec834" name="ab1340b7d8521a3f20538fa27725ec834"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab1340b7d8521a3f20538fa27725ec834">&#9670;&#160;</a></span>PASSWORD_RESET_RATE_LIMIT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const PASSWORD_RESET_RATE_LIMIT = 3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3fe72921270c08d48d6b9b95648153e3" name="a3fe72921270c08d48d6b9b95648153e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3fe72921270c08d48d6b9b95648153e3">&#9670;&#160;</a></span>REMEMBER_ME_EXPIRY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const REMEMBER_ME_EXPIRY = 30</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/helpers/<a class="el" href="_security_enhancements_8php.html">SecurityEnhancements.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
