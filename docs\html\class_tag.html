<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Tag Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">Tag Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae07173ab06a20e2f5bd928cc0518e01f" id="r_ae07173ab06a20e2f5bd928cc0518e01f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae07173ab06a20e2f5bd928cc0518e01f">getTags</a> ()</td></tr>
<tr class="separator:ae07173ab06a20e2f5bd928cc0518e01f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a401430eadd174788ff0568d8f0c9c6d8" id="r_a401430eadd174788ff0568d8f0c9c6d8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a401430eadd174788ff0568d8f0c9c6d8">getTagById</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a401430eadd174788ff0568d8f0c9c6d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e986e3f035b2b0fb56c40778ce1f040" id="r_a2e986e3f035b2b0fb56c40778ce1f040"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2e986e3f035b2b0fb56c40778ce1f040">getTagByName</a> ($name)</td></tr>
<tr class="separator:a2e986e3f035b2b0fb56c40778ce1f040"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ff2d7b570ab7813d09be22cb72fcd46" id="r_a4ff2d7b570ab7813d09be22cb72fcd46"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4ff2d7b570ab7813d09be22cb72fcd46">addTag</a> ($data)</td></tr>
<tr class="separator:a4ff2d7b570ab7813d09be22cb72fcd46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38825dc7a692a32bf2fecb1df70fed9d" id="r_a38825dc7a692a32bf2fecb1df70fed9d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a38825dc7a692a32bf2fecb1df70fed9d">updateTag</a> ($data)</td></tr>
<tr class="separator:a38825dc7a692a32bf2fecb1df70fed9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8136d79d1d3aa83567d0baaccdb97335" id="r_a8136d79d1d3aa83567d0baaccdb97335"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8136d79d1d3aa83567d0baaccdb97335">deleteTag</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a8136d79d1d3aa83567d0baaccdb97335"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a425a55b885d03edeafb572af23106a46" id="r_a425a55b885d03edeafb572af23106a46"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a425a55b885d03edeafb572af23106a46">getTagsForAsset</a> ($assetId)</td></tr>
<tr class="separator:a425a55b885d03edeafb572af23106a46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b19e0c9ea97d5b603d6a5a02940c523" id="r_a2b19e0c9ea97d5b603d6a5a02940c523"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2b19e0c9ea97d5b603d6a5a02940c523">addTagToAsset</a> ($assetId, $tagId)</td></tr>
<tr class="separator:a2b19e0c9ea97d5b603d6a5a02940c523"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a880f1cf91fb22405339d3e55f1e51afb" id="r_a880f1cf91fb22405339d3e55f1e51afb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a880f1cf91fb22405339d3e55f1e51afb">removeTagFromAsset</a> ($assetId, $tagId)</td></tr>
<tr class="separator:a880f1cf91fb22405339d3e55f1e51afb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf77d50b5c6361054aca053c5d2d6cf3" id="r_aaf77d50b5c6361054aca053c5d2d6cf3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaf77d50b5c6361054aca053c5d2d6cf3">removeAllTagsFromAsset</a> ($assetId)</td></tr>
<tr class="separator:aaf77d50b5c6361054aca053c5d2d6cf3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0e9b2e99baafec639b087d1a0ada8b2" id="r_ae0e9b2e99baafec639b087d1a0ada8b2"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae0e9b2e99baafec639b087d1a0ada8b2">getAssetsByTag</a> ($tagId)</td></tr>
<tr class="separator:ae0e9b2e99baafec639b087d1a0ada8b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1188485fb7b7b0abe22f005bed184c4c" id="r_a1188485fb7b7b0abe22f005bed184c4c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1188485fb7b7b0abe22f005bed184c4c">countAssetsWithTag</a> ($tagId)</td></tr>
<tr class="separator:a1188485fb7b7b0abe22f005bed184c4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a4ff2d7b570ab7813d09be22cb72fcd46" name="a4ff2d7b570ab7813d09be22cb72fcd46"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4ff2d7b570ab7813d09be22cb72fcd46">&#9670;&#160;</a></span>addTag()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">addTag </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2b19e0c9ea97d5b603d6a5a02940c523" name="a2b19e0c9ea97d5b603d6a5a02940c523"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2b19e0c9ea97d5b603d6a5a02940c523">&#9670;&#160;</a></span>addTagToAsset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">addTagToAsset </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$tagId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1188485fb7b7b0abe22f005bed184c4c" name="a1188485fb7b7b0abe22f005bed184c4c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1188485fb7b7b0abe22f005bed184c4c">&#9670;&#160;</a></span>countAssetsWithTag()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">countAssetsWithTag </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$tagId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8136d79d1d3aa83567d0baaccdb97335" name="a8136d79d1d3aa83567d0baaccdb97335"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8136d79d1d3aa83567d0baaccdb97335">&#9670;&#160;</a></span>deleteTag()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">deleteTag </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae0e9b2e99baafec639b087d1a0ada8b2" name="ae0e9b2e99baafec639b087d1a0ada8b2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae0e9b2e99baafec639b087d1a0ada8b2">&#9670;&#160;</a></span>getAssetsByTag()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAssetsByTag </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$tagId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a401430eadd174788ff0568d8f0c9c6d8" name="a401430eadd174788ff0568d8f0c9c6d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a401430eadd174788ff0568d8f0c9c6d8">&#9670;&#160;</a></span>getTagById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getTagById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2e986e3f035b2b0fb56c40778ce1f040" name="a2e986e3f035b2b0fb56c40778ce1f040"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2e986e3f035b2b0fb56c40778ce1f040">&#9670;&#160;</a></span>getTagByName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getTagByName </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$name</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae07173ab06a20e2f5bd928cc0518e01f" name="ae07173ab06a20e2f5bd928cc0518e01f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae07173ab06a20e2f5bd928cc0518e01f">&#9670;&#160;</a></span>getTags()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getTags </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a425a55b885d03edeafb572af23106a46" name="a425a55b885d03edeafb572af23106a46"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a425a55b885d03edeafb572af23106a46">&#9670;&#160;</a></span>getTagsForAsset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getTagsForAsset </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aaf77d50b5c6361054aca053c5d2d6cf3" name="aaf77d50b5c6361054aca053c5d2d6cf3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaf77d50b5c6361054aca053c5d2d6cf3">&#9670;&#160;</a></span>removeAllTagsFromAsset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">removeAllTagsFromAsset </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a880f1cf91fb22405339d3e55f1e51afb" name="a880f1cf91fb22405339d3e55f1e51afb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a880f1cf91fb22405339d3e55f1e51afb">&#9670;&#160;</a></span>removeTagFromAsset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">removeTagFromAsset </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$assetId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$tagId</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a38825dc7a692a32bf2fecb1df70fed9d" name="a38825dc7a692a32bf2fecb1df70fed9d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a38825dc7a692a32bf2fecb1df70fed9d">&#9670;&#160;</a></span>updateTag()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateTag </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/models/<a class="el" href="_tag_8php.html">Tag.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
