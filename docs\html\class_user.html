<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: User Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a>  </div>
  <div class="headertitle"><div class="title">User Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a095c5d389db211932136b53f25f39685" id="r_a095c5d389db211932136b53f25f39685"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a095c5d389db211932136b53f25f39685">__construct</a> ()</td></tr>
<tr class="separator:a095c5d389db211932136b53f25f39685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2bc607329e3fa62d3b95a76e61b650c" id="r_ad2bc607329e3fa62d3b95a76e61b650c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad2bc607329e3fa62d3b95a76e61b650c">register</a> ($data)</td></tr>
<tr class="separator:ad2bc607329e3fa62d3b95a76e61b650c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaaffadfaedf0decf75bab8923a8c8781" id="r_aaaffadfaedf0decf75bab8923a8c8781"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaaffadfaedf0decf75bab8923a8c8781">login</a> ($email, $password)</td></tr>
<tr class="separator:aaaffadfaedf0decf75bab8923a8c8781"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7d72fc4d79d853b8af320c0ce17ebd1" id="r_af7d72fc4d79d853b8af320c0ce17ebd1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af7d72fc4d79d853b8af320c0ce17ebd1">findUserByEmail</a> ($email)</td></tr>
<tr class="separator:af7d72fc4d79d853b8af320c0ce17ebd1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e7d616b80a7b0fb4b8d2736800e29cc" id="r_a5e7d616b80a7b0fb4b8d2736800e29cc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5e7d616b80a7b0fb4b8d2736800e29cc">getUserById</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>)</td></tr>
<tr class="separator:a5e7d616b80a7b0fb4b8d2736800e29cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a48a9c3492291c38e6113e40e04deb2ed" id="r_a48a9c3492291c38e6113e40e04deb2ed"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a48a9c3492291c38e6113e40e04deb2ed">getAllUsers</a> ()</td></tr>
<tr class="separator:a48a9c3492291c38e6113e40e04deb2ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6088f09cdf25effafc0261a893ddc316" id="r_a6088f09cdf25effafc0261a893ddc316"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6088f09cdf25effafc0261a893ddc316">updateStatus</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>, $status)</td></tr>
<tr class="separator:a6088f09cdf25effafc0261a893ddc316"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9bb4e869d9c4a6466fc110c69e2e6332" id="r_a9bb4e869d9c4a6466fc110c69e2e6332"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9bb4e869d9c4a6466fc110c69e2e6332">updateRole</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>, $role)</td></tr>
<tr class="separator:a9bb4e869d9c4a6466fc110c69e2e6332"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab4bfd178ad176ab7336faa21ae3dd807" id="r_ab4bfd178ad176ab7336faa21ae3dd807"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab4bfd178ad176ab7336faa21ae3dd807">updateLegacyRole</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>, $role)</td></tr>
<tr class="separator:ab4bfd178ad176ab7336faa21ae3dd807"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a971afa7848d88a57fe60e6f61fecf3cd" id="r_a971afa7848d88a57fe60e6f61fecf3cd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a971afa7848d88a57fe60e6f61fecf3cd">updatePassword</a> ($<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>, $password)</td></tr>
<tr class="separator:a971afa7848d88a57fe60e6f61fecf3cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0957b89a7a07da512227292fd49aa80" id="r_ae0957b89a7a07da512227292fd49aa80"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae0957b89a7a07da512227292fd49aa80">updateUser</a> ($data)</td></tr>
<tr class="separator:ae0957b89a7a07da512227292fd49aa80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e25c35f7741620a8309ea1c6a2117e6" id="r_a4e25c35f7741620a8309ea1c6a2117e6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4e25c35f7741620a8309ea1c6a2117e6">getUserByEmail</a> ($email)</td></tr>
<tr class="separator:a4e25c35f7741620a8309ea1c6a2117e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3df1c9b773342ccee92bf873c0677d45" id="r_a3df1c9b773342ccee92bf873c0677d45"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3df1c9b773342ccee92bf873c0677d45">createPasswordResetToken</a> ($email)</td></tr>
<tr class="separator:a3df1c9b773342ccee92bf873c0677d45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0cb5bf0a7e8f3638609c7df21b5e3234" id="r_a0cb5bf0a7e8f3638609c7df21b5e3234"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0cb5bf0a7e8f3638609c7df21b5e3234">verifyPasswordResetToken</a> ($email, $token)</td></tr>
<tr class="separator:a0cb5bf0a7e8f3638609c7df21b5e3234"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b89115d131a20f0970b7277e6c598b0" id="r_a6b89115d131a20f0970b7277e6c598b0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6b89115d131a20f0970b7277e6c598b0">markTokenAsUsed</a> ($email, $token)</td></tr>
<tr class="separator:a6b89115d131a20f0970b7277e6c598b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2fa1b4e2fbf3919435a676d6808e663f" id="r_a2fa1b4e2fbf3919435a676d6808e663f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2fa1b4e2fbf3919435a676d6808e663f">resetPasswordByEmail</a> ($email, $password)</td></tr>
<tr class="separator:a2fa1b4e2fbf3919435a676d6808e663f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add8aaf5e6881c74092b138a86de7764f" id="r_add8aaf5e6881c74092b138a86de7764f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#add8aaf5e6881c74092b138a86de7764f">createRememberMeToken</a> ($userId)</td></tr>
<tr class="separator:add8aaf5e6881c74092b138a86de7764f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06e108b031376baedd2ec162923e0af6" id="r_a06e108b031376baedd2ec162923e0af6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a06e108b031376baedd2ec162923e0af6">verifyRememberMeToken</a> ($selector, $validator)</td></tr>
<tr class="separator:a06e108b031376baedd2ec162923e0af6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a755028b25c004b15c7fe00f214297ec4" id="r_a755028b25c004b15c7fe00f214297ec4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a755028b25c004b15c7fe00f214297ec4">deleteRememberMeToken</a> ($selector)</td></tr>
<tr class="separator:a755028b25c004b15c7fe00f214297ec4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49f20a2213cf9dbab0288bbef9fa4de9" id="r_a49f20a2213cf9dbab0288bbef9fa4de9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a49f20a2213cf9dbab0288bbef9fa4de9">deleteAllRememberMeTokens</a> ($userId)</td></tr>
<tr class="separator:a49f20a2213cf9dbab0288bbef9fa4de9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5368738110a683cd7a567ae1327cc898" id="r_a5368738110a683cd7a567ae1327cc898"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5368738110a683cd7a567ae1327cc898">getUserRoles</a> ($userId)</td></tr>
<tr class="separator:a5368738110a683cd7a567ae1327cc898"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91222c71c8139bfa1bb5e8f6df776006" id="r_a91222c71c8139bfa1bb5e8f6df776006"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a91222c71c8139bfa1bb5e8f6df776006">getPrimaryRole</a> ($userId)</td></tr>
<tr class="separator:a91222c71c8139bfa1bb5e8f6df776006"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c9115ceb83c0aff453a7e11ac9e2a86" id="r_a4c9115ceb83c0aff453a7e11ac9e2a86"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4c9115ceb83c0aff453a7e11ac9e2a86">hasRole</a> ($userId, $role)</td></tr>
<tr class="separator:a4c9115ceb83c0aff453a7e11ac9e2a86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0055c1740759104b1c17445ea2bd581a" id="r_a0055c1740759104b1c17445ea2bd581a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0055c1740759104b1c17445ea2bd581a">getUserPermissions</a> ($userId)</td></tr>
<tr class="separator:a0055c1740759104b1c17445ea2bd581a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac30fcf0c0789d2e918af5e0926ee0d9f" id="r_ac30fcf0c0789d2e918af5e0926ee0d9f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac30fcf0c0789d2e918af5e0926ee0d9f">hasPermission</a> ($userId, $permission)</td></tr>
<tr class="separator:ac30fcf0c0789d2e918af5e0926ee0d9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4a5ea56c1886bd9841172b60638a6db1" id="r_a4a5ea56c1886bd9841172b60638a6db1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4a5ea56c1886bd9841172b60638a6db1">assignRoles</a> ($userId, $roleIds)</td></tr>
<tr class="separator:a4a5ea56c1886bd9841172b60638a6db1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a095c5d389db211932136b53f25f39685" name="a095c5d389db211932136b53f25f39685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a095c5d389db211932136b53f25f39685">&#9670;&#160;</a></span>__construct()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__construct </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Constructor</p>
<p>Initializes the <a class="el" href="class_user.html">User</a> model with database and role model instances.</p>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a4a5ea56c1886bd9841172b60638a6db1" name="a4a5ea56c1886bd9841172b60638a6db1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4a5ea56c1886bd9841172b60638a6db1">&#9670;&#160;</a></span>assignRoles()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">assignRoles </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$roleIds</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Assign roles to a user</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
    <tr><td class="paramtype">array</td><td class="paramname">$roleIds</td><td>Array of role IDs </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if successful, false otherwise </dd></dl>

</div>
</div>
<a id="a3df1c9b773342ccee92bf873c0677d45" name="a3df1c9b773342ccee92bf873c0677d45"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3df1c9b773342ccee92bf873c0677d45">&#9670;&#160;</a></span>createPasswordResetToken()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">createPasswordResetToken </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$email</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Create password reset token</p>
<p>Generates a secure token for password reset functionality. Token expires after 1 hour and replaces any existing tokens.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$email</td><td>The email address to create token for </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>string|false The generated token if successful, false otherwise </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="add8aaf5e6881c74092b138a86de7764f" name="add8aaf5e6881c74092b138a86de7764f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#add8aaf5e6881c74092b138a86de7764f">&#9670;&#160;</a></span>createRememberMeToken()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">createRememberMeToken </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Create a remember me token for a user</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array|bool Token data if created successfully, false otherwise </dd></dl>

</div>
</div>
<a id="a49f20a2213cf9dbab0288bbef9fa4de9" name="a49f20a2213cf9dbab0288bbef9fa4de9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a49f20a2213cf9dbab0288bbef9fa4de9">&#9670;&#160;</a></span>deleteAllRememberMeTokens()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">deleteAllRememberMeTokens </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete all remember me tokens for a user</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if deleted successfully, false otherwise </dd></dl>

</div>
</div>
<a id="a755028b25c004b15c7fe00f214297ec4" name="a755028b25c004b15c7fe00f214297ec4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a755028b25c004b15c7fe00f214297ec4">&#9670;&#160;</a></span>deleteRememberMeToken()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">deleteRememberMeToken </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$selector</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete a remember me token</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$selector</td><td>Token selector </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if deleted successfully, false otherwise </dd></dl>

</div>
</div>
<a id="af7d72fc4d79d853b8af320c0ce17ebd1" name="af7d72fc4d79d853b8af320c0ce17ebd1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af7d72fc4d79d853b8af320c0ce17ebd1">&#9670;&#160;</a></span>findUserByEmail()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">findUserByEmail </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$email</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Find user by email</p>
<p>Checks if a user exists with the given email address.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$email</td><td>The email address to search for </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if user exists, false otherwise </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a48a9c3492291c38e6113e40e04deb2ed" name="a48a9c3492291c38e6113e40e04deb2ed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a48a9c3492291c38e6113e40e04deb2ed">&#9670;&#160;</a></span>getAllUsers()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getAllUsers </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all users</p>
<p>Retrieves all users from the database ordered by name.</p>
<dl class="section return"><dt>Returns</dt><dd>array Array of user objects </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a91222c71c8139bfa1bb5e8f6df776006" name="a91222c71c8139bfa1bb5e8f6df776006"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a91222c71c8139bfa1bb5e8f6df776006">&#9670;&#160;</a></span>getPrimaryRole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getPrimaryRole </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get primary role for a user (for backward compatibility)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object|bool <a class="el" href="class_role.html">Role</a> object or false if no roles </dd></dl>

</div>
</div>
<a id="a4e25c35f7741620a8309ea1c6a2117e6" name="a4e25c35f7741620a8309ea1c6a2117e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4e25c35f7741620a8309ea1c6a2117e6">&#9670;&#160;</a></span>getUserByEmail()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getUserByEmail </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$email</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get user by email (returns user object)</p>
<p>Retrieves a complete user record by email address.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$email</td><td>The email address to search for </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object|false <a class="el" href="class_user.html">User</a> object if found, false otherwise </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a5e7d616b80a7b0fb4b8d2736800e29cc" name="a5e7d616b80a7b0fb4b8d2736800e29cc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5e7d616b80a7b0fb4b8d2736800e29cc">&#9670;&#160;</a></span>getUserById()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getUserById </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get user by ID</p>
<p>Retrieves a user record by their unique ID.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td>The user ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>object|false <a class="el" href="class_user.html">User</a> object if found, false otherwise </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a0055c1740759104b1c17445ea2bd581a" name="a0055c1740759104b1c17445ea2bd581a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0055c1740759104b1c17445ea2bd581a">&#9670;&#160;</a></span>getUserPermissions()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getUserPermissions </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all permissions for a user</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array Array of permission objects </dd></dl>

</div>
</div>
<a id="a5368738110a683cd7a567ae1327cc898" name="a5368738110a683cd7a567ae1327cc898"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5368738110a683cd7a567ae1327cc898">&#9670;&#160;</a></span>getUserRoles()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getUserRoles </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get all roles for a user</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>array Array of role objects </dd></dl>

</div>
</div>
<a id="ac30fcf0c0789d2e918af5e0926ee0d9f" name="ac30fcf0c0789d2e918af5e0926ee0d9f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac30fcf0c0789d2e918af5e0926ee0d9f">&#9670;&#160;</a></span>hasPermission()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">hasPermission </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$permission</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Check if a user has a specific permission</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
    <tr><td class="paramtype">int&#160;|&#160;string</td><td class="paramname">$permission</td><td><a class="el" href="class_permission.html">Permission</a> ID or name </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if the user has the permission, false otherwise </dd></dl>

</div>
</div>
<a id="a4c9115ceb83c0aff453a7e11ac9e2a86" name="a4c9115ceb83c0aff453a7e11ac9e2a86"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4c9115ceb83c0aff453a7e11ac9e2a86">&#9670;&#160;</a></span>hasRole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">hasRole </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$userId</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$role</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Check if a user has a specific role</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$userId</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
    <tr><td class="paramtype">int&#160;|&#160;string</td><td class="paramname">$role</td><td><a class="el" href="class_role.html">Role</a> ID or name </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if the user has the role, false otherwise </dd></dl>

</div>
</div>
<a id="aaaffadfaedf0decf75bab8923a8c8781" name="aaaffadfaedf0decf75bab8923a8c8781"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaaffadfaedf0decf75bab8923a8c8781">&#9670;&#160;</a></span>login()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">login </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$email</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$password</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Authenticate user login</p>
<p>Verifies user credentials and handles security features like account locking and failed login attempt tracking.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$email</td><td><a class="el" href="class_user.html">User</a>'s email address </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$password</td><td><a class="el" href="class_user.html">User</a>'s password </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>mixed <a class="el" href="class_user.html">User</a> object on success, 'inactive' if account inactive, 'locked' if locked, false on failure </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a6b89115d131a20f0970b7277e6c598b0" name="a6b89115d131a20f0970b7277e6c598b0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6b89115d131a20f0970b7277e6c598b0">&#9670;&#160;</a></span>markTokenAsUsed()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">markTokenAsUsed </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$email</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$token</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad2bc607329e3fa62d3b95a76e61b650c" name="ad2bc607329e3fa62d3b95a76e61b650c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad2bc607329e3fa62d3b95a76e61b650c">&#9670;&#160;</a></span>register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">register </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Register a new user</p>
<p>Creates a new user account with the provided data. Sets default role and status if not specified.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td><a class="el" href="class_user.html">User</a> registration data containing name, email, password, role, status </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if registration successful, false otherwise </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a2fa1b4e2fbf3919435a676d6808e663f" name="a2fa1b4e2fbf3919435a676d6808e663f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2fa1b4e2fbf3919435a676d6808e663f">&#9670;&#160;</a></span>resetPasswordByEmail()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">resetPasswordByEmail </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$email</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$password</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab4bfd178ad176ab7336faa21ae3dd807" name="ab4bfd178ad176ab7336faa21ae3dd807"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab4bfd178ad176ab7336faa21ae3dd807">&#9670;&#160;</a></span>updateLegacyRole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateLegacyRole </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$role</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update the legacy role field in the users table This is used for backward compatibility with the old role system</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td><a class="el" href="class_user.html">User</a> ID </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$role</td><td><a class="el" href="class_role.html">Role</a> value ('admin' or 'user') </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if successful, false otherwise </dd></dl>

</div>
</div>
<a id="a971afa7848d88a57fe60e6f61fecf3cd" name="a971afa7848d88a57fe60e6f61fecf3cd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a971afa7848d88a57fe60e6f61fecf3cd">&#9670;&#160;</a></span>updatePassword()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updatePassword </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$password</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update user password</p>
<p>Updates a user's password with the provided hashed password.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td>The user ID </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$password</td><td>The hashed password </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if update successful, false otherwise </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a9bb4e869d9c4a6466fc110c69e2e6332" name="a9bb4e869d9c4a6466fc110c69e2e6332"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9bb4e869d9c4a6466fc110c69e2e6332">&#9670;&#160;</a></span>updateRole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateRole </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$role</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update user role (legacy method)</p>
<p>Updates both the users table and user_roles table for backward compatibility. Uses database transactions to ensure data consistency.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td>The user ID </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$role</td><td>The role name ('admin' or 'user') </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if update successful, false otherwise </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname">Exception</td><td>If database transaction fails </td></tr>
  </table>
  </dd>
</dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a6088f09cdf25effafc0261a893ddc316" name="a6088f09cdf25effafc0261a893ddc316"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6088f09cdf25effafc0261a893ddc316">&#9670;&#160;</a></span>updateStatus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateStatus </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$id</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$status</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update user status</p>
<p>Updates the status of a user (active/inactive).</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$id</td><td>The user ID </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$status</td><td>The new status ('active' or 'inactive') </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if update successful, false otherwise </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="ae0957b89a7a07da512227292fd49aa80" name="ae0957b89a7a07da512227292fd49aa80"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae0957b89a7a07da512227292fd49aa80">&#9670;&#160;</a></span>updateUser()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">updateUser </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Update user information</p>
<p>Updates user name and optionally password. Dynamically builds query based on provided data.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">array</td><td class="paramname">$data</td><td><a class="el" href="class_user.html">User</a> data containing id, name, and optionally password </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if update successful, false otherwise </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a0cb5bf0a7e8f3638609c7df21b5e3234" name="a0cb5bf0a7e8f3638609c7df21b5e3234"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0cb5bf0a7e8f3638609c7df21b5e3234">&#9670;&#160;</a></span>verifyPasswordResetToken()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">verifyPasswordResetToken </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$email</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$token</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a06e108b031376baedd2ec162923e0af6" name="a06e108b031376baedd2ec162923e0af6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a06e108b031376baedd2ec162923e0af6">&#9670;&#160;</a></span>verifyRememberMeToken()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">verifyRememberMeToken </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$selector</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$validator</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Verify a remember me token</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$selector</td><td>Token selector </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$validator</td><td>Token validator </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int|bool <a class="el" href="class_user.html">User</a> ID if token is valid, false otherwise </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>app/models/<a class="el" href="_user_8php.html">User.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
