<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/config/config.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_aea9893897cc10308ef9329a16217ee4.html">config</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">config.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a293363d7988627f671958e2d908c202a" id="r_a293363d7988627f671958e2d908c202a"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a293363d7988627f671958e2d908c202a">DB_HOST</a> 'localhost'</td></tr>
<tr class="separator:a293363d7988627f671958e2d908c202a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d1d99f8e08f387d84fe9848f3357156" id="r_a1d1d99f8e08f387d84fe9848f3357156"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1d1d99f8e08f387d84fe9848f3357156">DB_USER</a> 'root'</td></tr>
<tr class="separator:a1d1d99f8e08f387d84fe9848f3357156"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8bb9c4546d91667cfa61879d83127a92" id="r_a8bb9c4546d91667cfa61879d83127a92"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8bb9c4546d91667cfa61879d83127a92">DB_PASS</a> ''</td></tr>
<tr class="separator:a8bb9c4546d91667cfa61879d83127a92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5db0d3504f917f268614c50b02c53e2" id="r_ab5db0d3504f917f268614c50b02c53e2"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab5db0d3504f917f268614c50b02c53e2">DB_NAME</a> 'asset_visibility'</td></tr>
<tr class="separator:ab5db0d3504f917f268614c50b02c53e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a10375bcd2448d71fb65abb104c207203" id="r_a10375bcd2448d71fb65abb104c207203"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a10375bcd2448d71fb65abb104c207203">APPROOT</a> dirname(dirname(__FILE__))</td></tr>
<tr class="separator:a10375bcd2448d71fb65abb104c207203"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a598166266a84ff3ecf84ef6f206ceefe" id="r_a598166266a84ff3ecf84ef6f206ceefe"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a598166266a84ff3ecf84ef6f206ceefe">URLROOT</a> 'http://localhost/asset_visibility'</td></tr>
<tr class="separator:a598166266a84ff3ecf84ef6f206ceefe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac91fe4eef242dfc35896ee6b7e378022" id="r_ac91fe4eef242dfc35896ee6b7e378022"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac91fe4eef242dfc35896ee6b7e378022">SITENAME</a> 'EVIS'</td></tr>
<tr class="separator:ac91fe4eef242dfc35896ee6b7e378022"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad1bee560341d87c3f76f4a85e7471113" id="r_ad1bee560341d87c3f76f4a85e7471113"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad1bee560341d87c3f76f4a85e7471113">RECAPTCHA_SITE_KEY</a> '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'</td></tr>
<tr class="separator:ad1bee560341d87c3f76f4a85e7471113"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a972a0dbe0d8dcf753aaafd21423a16ca" id="r_a972a0dbe0d8dcf753aaafd21423a16ca"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a972a0dbe0d8dcf753aaafd21423a16ca">RECAPTCHA_SECRET_KEY</a> '6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe'</td></tr>
<tr class="separator:a972a0dbe0d8dcf753aaafd21423a16ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62b9759f70d1e82d5ffda4279c18a193" id="r_a62b9759f70d1e82d5ffda4279c18a193"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a62b9759f70d1e82d5ffda4279c18a193">COOKIE_SECURE</a> false</td></tr>
<tr class="separator:a62b9759f70d1e82d5ffda4279c18a193"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0c7a6a56fe8ab3c3dd01dfc8244d8e9" id="r_ae0c7a6a56fe8ab3c3dd01dfc8244d8e9"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae0c7a6a56fe8ab3c3dd01dfc8244d8e9">COOKIE_HTTP_ONLY</a> true</td></tr>
<tr class="separator:ae0c7a6a56fe8ab3c3dd01dfc8244d8e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d4854f829361184da6cc6ac24b2a471" id="r_a8d4854f829361184da6cc6ac24b2a471"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8d4854f829361184da6cc6ac24b2a471">COOKIE_SAME_SITE</a> 'Lax'</td></tr>
<tr class="separator:a8d4854f829361184da6cc6ac24b2a471"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a21f5339599f84fa331879ca03ab55d56" id="r_a21f5339599f84fa331879ca03ab55d56"><td class="memItemLeft" align="right" valign="top">const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a21f5339599f84fa331879ca03ab55d56">ASSET_LIFETIME</a> 1825</td></tr>
<tr class="separator:a21f5339599f84fa331879ca03ab55d56"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a10375bcd2448d71fb65abb104c207203" name="a10375bcd2448d71fb65abb104c207203"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a10375bcd2448d71fb65abb104c207203">&#9670;&#160;</a></span>APPROOT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const APPROOT dirname(dirname(__FILE__))</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Application Configuration </p>

</div>
</div>
<a id="a21f5339599f84fa331879ca03ab55d56" name="a21f5339599f84fa331879ca03ab55d56"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a21f5339599f84fa331879ca03ab55d56">&#9670;&#160;</a></span>ASSET_LIFETIME</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const ASSET_LIFETIME 1825</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae0c7a6a56fe8ab3c3dd01dfc8244d8e9" name="ae0c7a6a56fe8ab3c3dd01dfc8244d8e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae0c7a6a56fe8ab3c3dd01dfc8244d8e9">&#9670;&#160;</a></span>COOKIE_HTTP_ONLY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const COOKIE_HTTP_ONLY true</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8d4854f829361184da6cc6ac24b2a471" name="a8d4854f829361184da6cc6ac24b2a471"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8d4854f829361184da6cc6ac24b2a471">&#9670;&#160;</a></span>COOKIE_SAME_SITE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const COOKIE_SAME_SITE 'Lax'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a62b9759f70d1e82d5ffda4279c18a193" name="a62b9759f70d1e82d5ffda4279c18a193"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a62b9759f70d1e82d5ffda4279c18a193">&#9670;&#160;</a></span>COOKIE_SECURE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const COOKIE_SECURE false</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a293363d7988627f671958e2d908c202a" name="a293363d7988627f671958e2d908c202a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a293363d7988627f671958e2d908c202a">&#9670;&#160;</a></span>DB_HOST</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const DB_HOST 'localhost'</td>
        </tr>
      </table>
</div><div class="memdoc">
<p><a class="el" href="class_database.html">Database</a> Configuration </p>

</div>
</div>
<a id="ab5db0d3504f917f268614c50b02c53e2" name="ab5db0d3504f917f268614c50b02c53e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab5db0d3504f917f268614c50b02c53e2">&#9670;&#160;</a></span>DB_NAME</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const DB_NAME 'asset_visibility'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8bb9c4546d91667cfa61879d83127a92" name="a8bb9c4546d91667cfa61879d83127a92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8bb9c4546d91667cfa61879d83127a92">&#9670;&#160;</a></span>DB_PASS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const DB_PASS ''</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1d1d99f8e08f387d84fe9848f3357156" name="a1d1d99f8e08f387d84fe9848f3357156"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1d1d99f8e08f387d84fe9848f3357156">&#9670;&#160;</a></span>DB_USER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const DB_USER 'root'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a972a0dbe0d8dcf753aaafd21423a16ca" name="a972a0dbe0d8dcf753aaafd21423a16ca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a972a0dbe0d8dcf753aaafd21423a16ca">&#9670;&#160;</a></span>RECAPTCHA_SECRET_KEY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const RECAPTCHA_SECRET_KEY '6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad1bee560341d87c3f76f4a85e7471113" name="ad1bee560341d87c3f76f4a85e7471113"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad1bee560341d87c3f76f4a85e7471113">&#9670;&#160;</a></span>RECAPTCHA_SITE_KEY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const RECAPTCHA_SITE_KEY '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'</td>
        </tr>
      </table>
</div><div class="memdoc">
<p><a class="el" href="class_security.html">Security</a> Configuration </p>

</div>
</div>
<a id="ac91fe4eef242dfc35896ee6b7e378022" name="ac91fe4eef242dfc35896ee6b7e378022"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac91fe4eef242dfc35896ee6b7e378022">&#9670;&#160;</a></span>SITENAME</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const SITENAME 'EVIS'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a598166266a84ff3ecf84ef6f206ceefe" name="a598166266a84ff3ecf84ef6f206ceefe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a598166266a84ff3ecf84ef6f206ceefe">&#9670;&#160;</a></span>URLROOT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const URLROOT 'http://localhost/asset_visibility'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
