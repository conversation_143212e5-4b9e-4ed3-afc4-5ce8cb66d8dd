<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/scripts/create_guideline_implementation_table.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_ba6a928bcbf4c3ed31eaaba20c0e04de.html">scripts</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">create_guideline_implementation_table.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a1fa3127fc82f96b1436d871ef02be319" id="r_a1fa3127fc82f96b1436d871ef02be319"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1fa3127fc82f96b1436d871ef02be319">$db</a> = new <a class="el" href="class_database.html">Database</a>()</td></tr>
<tr class="separator:a1fa3127fc82f96b1436d871ef02be319"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73004ce9cd673c1bfafd1dc351134797" id="r_a73004ce9cd673c1bfafd1dc351134797"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a73004ce9cd673c1bfafd1dc351134797">$output</a> = []</td></tr>
<tr class="separator:a73004ce9cd673c1bfafd1dc351134797"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a397d52f1e5cf2f76672fbfd1d7a65f7e" id="r_a397d52f1e5cf2f76672fbfd1d7a65f7e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a397d52f1e5cf2f76672fbfd1d7a65f7e">$output</a> [] = &quot;=== Maintenance Guideline Implementation Table Migration ===&quot;</td></tr>
<tr class="separator:a397d52f1e5cf2f76672fbfd1d7a65f7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe4cc9788f52e49485473dc699537388" id="r_abe4cc9788f52e49485473dc699537388"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abe4cc9788f52e49485473dc699537388">try</a></td></tr>
<tr class="separator:abe4cc9788f52e49485473dc699537388"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7978dab33fce909f84e229f7a7188f77" id="r_a7978dab33fce909f84e229f7a7188f77"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7978dab33fce909f84e229f7a7188f77">$tableExists</a> = $db-&gt;rowCount() &gt; 0</td></tr>
<tr class="separator:a7978dab33fce909f84e229f7a7188f77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3cd95c062d95e026a5559fcf9d8a3bf" id="r_ac3cd95c062d95e026a5559fcf9d8a3bf"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(! $tableExists)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a></td></tr>
<tr class="separator:ac3cd95c062d95e026a5559fcf9d8a3bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a739ac305594911a5539be43a2839ce59" id="r_a739ac305594911a5539be43a2839ce59"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a739ac305594911a5539be43a2839ce59">$tableDefinition</a> = $db-&gt;single()</td></tr>
<tr class="separator:a739ac305594911a5539be43a2839ce59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b57a487f9061adfdb05c16a1beff71c" id="r_a0b57a487f9061adfdb05c16a1beff71c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0b57a487f9061adfdb05c16a1beff71c">$createTableStatement</a> = $tableDefinition-&gt;{'Create Table'} ?? ''</td></tr>
<tr class="separator:a0b57a487f9061adfdb05c16a1beff71c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af66546a0dd955f2fffa08383403b1b44" id="r_af66546a0dd955f2fffa08383403b1b44"><td class="memItemLeft" align="right" valign="top"><a class="el" href="check__db_8php.html#a9ee42195f2b26ca51b7b816b4f28113e">catch</a>(Exception $<a class="el" href="output__helper_8php.html#a18d38faad6177eda235a3d9d28572984">e</a>) <a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>($output as $line)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af66546a0dd955f2fffa08383403b1b44">$logFile</a> = '../logs/migrations.log'</td></tr>
<tr class="separator:af66546a0dd955f2fffa08383403b1b44"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a0b57a487f9061adfdb05c16a1beff71c" name="a0b57a487f9061adfdb05c16a1beff71c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0b57a487f9061adfdb05c16a1beff71c">&#9670;&#160;</a></span>$createTableStatement</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$createTableStatement = $tableDefinition-&gt;{'Create Table'} ?? ''</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1fa3127fc82f96b1436d871ef02be319" name="a1fa3127fc82f96b1436d871ef02be319"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1fa3127fc82f96b1436d871ef02be319">&#9670;&#160;</a></span>$db</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$db = new <a class="el" href="class_database.html">Database</a>()</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Create Guideline Implementation Table Script</p>
<p>This script ensures that the maintenance_guideline_implementation table exists and has the correct structure. It also migrates data from maintenance_history_guidelines if needed. </p>

</div>
</div>
<a id="af66546a0dd955f2fffa08383403b1b44" name="af66546a0dd955f2fffa08383403b1b44"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af66546a0dd955f2fffa08383403b1b44">&#9670;&#160;</a></span>$logFile</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="check__db_8php.html#a9ee42195f2b26ca51b7b816b4f28113e">catch</a>(Exception $<a class="el" href="output__helper_8php.html#a18d38faad6177eda235a3d9d28572984">e</a>) <a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ( $output as $line) $logFile = '../logs/migrations.log'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a73004ce9cd673c1bfafd1dc351134797" name="a73004ce9cd673c1bfafd1dc351134797"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a73004ce9cd673c1bfafd1dc351134797">&#9670;&#160;</a></span>$output <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$output = []</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a397d52f1e5cf2f76672fbfd1d7a65f7e" name="a397d52f1e5cf2f76672fbfd1d7a65f7e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a397d52f1e5cf2f76672fbfd1d7a65f7e">&#9670;&#160;</a></span>$output <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$output[] = &quot;=== Maintenance Guideline Implementation Table Migration ===&quot;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a739ac305594911a5539be43a2839ce59" name="a739ac305594911a5539be43a2839ce59"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a739ac305594911a5539be43a2839ce59">&#9670;&#160;</a></span>$tableDefinition</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$tableDefinition = $db-&gt;single()</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7978dab33fce909f84e229f7a7188f77" name="a7978dab33fce909f84e229f7a7188f77"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7978dab33fce909f84e229f7a7188f77">&#9670;&#160;</a></span>$tableExists</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$tableExists = $db-&gt;rowCount() &gt; 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac3cd95c062d95e026a5559fcf9d8a3bf" name="ac3cd95c062d95e026a5559fcf9d8a3bf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac3cd95c062d95e026a5559fcf9d8a3bf">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $tableExists) else</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">        <a class="code hl_variable" href="#a73004ce9cd673c1bfafd1dc351134797">$output</a>[] = <span class="stringliteral">&quot;Table &#39;maintenance_guideline_implementation&#39; already exists.&quot;</span></div>
<div class="ttc" id="acreate__guideline__implementation__table_8php_html_a73004ce9cd673c1bfafd1dc351134797"><div class="ttname"><a href="#a73004ce9cd673c1bfafd1dc351134797">$output</a></div><div class="ttdeci">$output</div><div class="ttdef"><b>Definition</b> create_guideline_implementation_table.php:18</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="abe4cc9788f52e49485473dc699537388" name="abe4cc9788f52e49485473dc699537388"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe4cc9788f52e49485473dc699537388">&#9670;&#160;</a></span>try</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">try</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">    </div>
<div class="line">    <a class="code hl_variable" href="#a1fa3127fc82f96b1436d871ef02be319">$db</a>-&gt;query(<span class="stringliteral">&quot;SHOW TABLES LIKE &#39;maintenance_guideline_implementation&#39;&quot;</span>)</div>
<div class="ttc" id="acreate__guideline__implementation__table_8php_html_a1fa3127fc82f96b1436d871ef02be319"><div class="ttname"><a href="#a1fa3127fc82f96b1436d871ef02be319">$db</a></div><div class="ttdeci">$db</div><div class="ttdef"><b>Definition</b> create_guideline_implementation_table.php:15</div></div>
</div><!-- fragment -->
</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
