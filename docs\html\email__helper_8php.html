<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/helpers/email_helper.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_aa9f5e9ebaa2b53f41fac9466bd77901.html">helpers</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">email_helper.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ac7b4a4ee33f08e191dba634df62d43ce" id="r_ac7b4a4ee33f08e191dba634df62d43ce"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac7b4a4ee33f08e191dba634df62d43ce">sendEmail</a> ($to, $subject, $body, $altBody='')</td></tr>
<tr class="separator:ac7b4a4ee33f08e191dba634df62d43ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a478f8d1988c6b936dd9722c64f0f01e8" id="r_a478f8d1988c6b936dd9722c64f0f01e8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a478f8d1988c6b936dd9722c64f0f01e8">sendPasswordResetEmail</a> ($to, $name, $token)</td></tr>
<tr class="separator:a478f8d1988c6b936dd9722c64f0f01e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a id="ac7b4a4ee33f08e191dba634df62d43ce" name="ac7b4a4ee33f08e191dba634df62d43ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac7b4a4ee33f08e191dba634df62d43ce">&#9670;&#160;</a></span>sendEmail()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">sendEmail </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$to</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$subject</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$body</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$altBody</em></span><span class="paramdefsep"> = </span><span class="paramdefval">''</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Send an email using PHPMailer</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$to</td><td>Recipient email address </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$subject</td><td>Email subject </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$body</td><td>Email body (HTML) </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$altBody</td><td>Plain text alternative body </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if email sent successfully, false otherwise </dd></dl>

</div>
</div>
<a id="a478f8d1988c6b936dd9722c64f0f01e8" name="a478f8d1988c6b936dd9722c64f0f01e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a478f8d1988c6b936dd9722c64f0f01e8">&#9670;&#160;</a></span>sendPasswordResetEmail()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">sendPasswordResetEmail </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$to</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$name</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$token</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Send a password reset email</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$to</td><td>Recipient email address </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$name</td><td>Recipient name </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$token</td><td>Password reset token </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if email sent successfully, false otherwise </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
