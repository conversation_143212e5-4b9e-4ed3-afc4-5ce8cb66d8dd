<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/helpers/error_handler.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_aa9f5e9ebaa2b53f41fac9466bd77901.html">helpers</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">error_handler.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a03e29e7b07739ac892f00eaba52b2ac6" id="r_a03e29e7b07739ac892f00eaba52b2ac6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a03e29e7b07739ac892f00eaba52b2ac6">customErrorHandler</a> ($errno, $errstr, $errfile, $errline)</td></tr>
<tr class="separator:a03e29e7b07739ac892f00eaba52b2ac6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bd9e5161069e68fa292a678d6cb955d" id="r_a0bd9e5161069e68fa292a678d6cb955d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0bd9e5161069e68fa292a678d6cb955d">customExceptionHandler</a> ($exception)</td></tr>
<tr class="separator:a0bd9e5161069e68fa292a678d6cb955d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ec8538ecc12eddc0c5b7af771bc47d6" id="r_a1ec8538ecc12eddc0c5b7af771bc47d6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1ec8538ecc12eddc0c5b7af771bc47d6">get_error_type</a> ($errno)</td></tr>
<tr class="separator:a1ec8538ecc12eddc0c5b7af771bc47d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a id="a03e29e7b07739ac892f00eaba52b2ac6" name="a03e29e7b07739ac892f00eaba52b2ac6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a03e29e7b07739ac892f00eaba52b2ac6">&#9670;&#160;</a></span>customErrorHandler()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">customErrorHandler </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$errno</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$errstr</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$errfile</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$errline</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Custom error handler function</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$errno</td><td>Error number </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$errstr</td><td>Error message </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$errfile</td><td>File where the error occurred </td></tr>
    <tr><td class="paramtype">int</td><td class="paramname">$errline</td><td>Line number where the error occurred </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="a0bd9e5161069e68fa292a678d6cb955d" name="a0bd9e5161069e68fa292a678d6cb955d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0bd9e5161069e68fa292a678d6cb955d">&#9670;&#160;</a></span>customExceptionHandler()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">customExceptionHandler </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$exception</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Custom exception handler function</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">Exception</td><td class="paramname">$exception</td><td>The exception object </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>void </dd></dl>

</div>
</div>
<a id="a1ec8538ecc12eddc0c5b7af771bc47d6" name="a1ec8538ecc12eddc0c5b7af771bc47d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1ec8538ecc12eddc0c5b7af771bc47d6">&#9670;&#160;</a></span>get_error_type()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">get_error_type </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$errno</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get error type name from error number</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">int</td><td class="paramname">$errno</td><td>Error number </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>string Error type name </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
