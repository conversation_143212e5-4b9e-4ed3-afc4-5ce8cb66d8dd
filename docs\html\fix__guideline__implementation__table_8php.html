<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: fix_guideline_implementation_table.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">fix_guideline_implementation_table.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a73004ce9cd673c1bfafd1dc351134797" id="r_a73004ce9cd673c1bfafd1dc351134797"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a73004ce9cd673c1bfafd1dc351134797">$output</a> = []</td></tr>
<tr class="separator:a73004ce9cd673c1bfafd1dc351134797"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a397d52f1e5cf2f76672fbfd1d7a65f7e" id="r_a397d52f1e5cf2f76672fbfd1d7a65f7e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a397d52f1e5cf2f76672fbfd1d7a65f7e">$output</a> [] = &quot;&lt;h1&gt;<a class="el" href="class_maintenance.html">Maintenance</a> Guideline Implementation Table Fix&lt;/h1&gt;&quot;</td></tr>
<tr class="separator:a397d52f1e5cf2f76672fbfd1d7a65f7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe4cc9788f52e49485473dc699537388" id="r_abe4cc9788f52e49485473dc699537388"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abe4cc9788f52e49485473dc699537388">try</a></td></tr>
<tr class="separator:abe4cc9788f52e49485473dc699537388"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a011800c63ece4cbbfa77136a20607023" id="r_a011800c63ece4cbbfa77136a20607023"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a011800c63ece4cbbfa77136a20607023">$options</a></td></tr>
<tr class="separator:a011800c63ece4cbbfa77136a20607023"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5766efd703cef0e00bfc06b3f3acbe0e" id="r_a5766efd703cef0e00bfc06b3f3acbe0e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5766efd703cef0e00bfc06b3f3acbe0e">$pdo</a> = new PDO($dsn, <a class="el" href="config_8php.html#a1d1d99f8e08f387d84fe9848f3357156">DB_USER</a>, <a class="el" href="config_8php.html#a8bb9c4546d91667cfa61879d83127a92">DB_PASS</a>, $options)</td></tr>
<tr class="separator:a5766efd703cef0e00bfc06b3f3acbe0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af27a9140d5f2658693e7fd107f716449" id="r_af27a9140d5f2658693e7fd107f716449"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af27a9140d5f2658693e7fd107f716449">$stmt</a> = $pdo-&gt;query(&quot;SHOW TABLES LIKE 'maintenance_guideline_implementation'&quot;)</td></tr>
<tr class="separator:af27a9140d5f2658693e7fd107f716449"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7978dab33fce909f84e229f7a7188f77" id="r_a7978dab33fce909f84e229f7a7188f77"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7978dab33fce909f84e229f7a7188f77">$tableExists</a> = $stmt-&gt;rowCount() &gt; 0</td></tr>
<tr class="separator:a7978dab33fce909f84e229f7a7188f77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3cd95c062d95e026a5559fcf9d8a3bf" id="r_ac3cd95c062d95e026a5559fcf9d8a3bf"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(! $tableExists)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a></td></tr>
<tr class="separator:ac3cd95c062d95e026a5559fcf9d8a3bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a112ef069ddc0454086e3d1e6d8d55d07" id="r_a112ef069ddc0454086e3d1e6d8d55d07"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a112ef069ddc0454086e3d1e6d8d55d07">$result</a> = $stmt-&gt;fetch(PDO::FETCH_ASSOC)</td></tr>
<tr class="separator:a112ef069ddc0454086e3d1e6d8d55d07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a739ac305594911a5539be43a2839ce59" id="r_a739ac305594911a5539be43a2839ce59"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a739ac305594911a5539be43a2839ce59">$tableDefinition</a> = $stmt-&gt;fetch(PDO::FETCH_ASSOC)</td></tr>
<tr class="separator:a739ac305594911a5539be43a2839ce59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b57a487f9061adfdb05c16a1beff71c" id="r_a0b57a487f9061adfdb05c16a1beff71c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0b57a487f9061adfdb05c16a1beff71c">$createTableStatement</a> = $tableDefinition['Create Table'] ?? ''</td></tr>
<tr class="separator:a0b57a487f9061adfdb05c16a1beff71c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c0c259120a0307a68a6af6fc4920bbf" id="r_a7c0c259120a0307a68a6af6fc4920bbf"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7c0c259120a0307a68a6af6fc4920bbf">$orphanedRecords</a> = $stmt-&gt;fetchAll(PDO::FETCH_ASSOC)</td></tr>
<tr class="separator:a7c0c259120a0307a68a6af6fc4920bbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c02af40dc23ebcbfdf20ae145ef9824" id="r_a4c02af40dc23ebcbfdf20ae145ef9824"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4c02af40dc23ebcbfdf20ae145ef9824">catch</a> (PDOException $<a class="el" href="output__helper_8php.html#a18d38faad6177eda235a3d9d28572984">e</a>)</td></tr>
<tr class="separator:a4c02af40dc23ebcbfdf20ae145ef9824"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae47c9cdcb9158b51999009c1d97b68a6" id="r_ae47c9cdcb9158b51999009c1d97b68a6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae47c9cdcb9158b51999009c1d97b68a6">foreach</a> ( $output as $line)</td></tr>
<tr class="separator:ae47c9cdcb9158b51999009c1d97b68a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a0b57a487f9061adfdb05c16a1beff71c" name="a0b57a487f9061adfdb05c16a1beff71c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0b57a487f9061adfdb05c16a1beff71c">&#9670;&#160;</a></span>$createTableStatement</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$createTableStatement = $tableDefinition['Create Table'] ?? ''</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a011800c63ece4cbbfa77136a20607023" name="a011800c63ece4cbbfa77136a20607023"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a011800c63ece4cbbfa77136a20607023">&#9670;&#160;</a></span>$options</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$options</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= [</div>
<div class="line">        PDO::ATTR_ERRMODE =&gt; PDO::ERRMODE_EXCEPTION,</div>
<div class="line">        PDO::ATTR_DEFAULT_FETCH_MODE =&gt; PDO::FETCH_OBJ,</div>
<div class="line">        PDO::ATTR_EMULATE_PREPARES =&gt; <span class="keyword">false</span>,</div>
<div class="line">    ]</div>
</div><!-- fragment -->
</div>
</div>
<a id="a7c0c259120a0307a68a6af6fc4920bbf" name="a7c0c259120a0307a68a6af6fc4920bbf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7c0c259120a0307a68a6af6fc4920bbf">&#9670;&#160;</a></span>$orphanedRecords</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$orphanedRecords = $stmt-&gt;fetchAll(PDO::FETCH_ASSOC)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a73004ce9cd673c1bfafd1dc351134797" name="a73004ce9cd673c1bfafd1dc351134797"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a73004ce9cd673c1bfafd1dc351134797">&#9670;&#160;</a></span>$output <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$output = []</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Fix Guideline Implementation Table Script</p>
<p>This script ensures that the maintenance_guideline_implementation table exists and has the correct structure. It also checks for and fixes any data integrity issues. </p>

</div>
</div>
<a id="a397d52f1e5cf2f76672fbfd1d7a65f7e" name="a397d52f1e5cf2f76672fbfd1d7a65f7e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a397d52f1e5cf2f76672fbfd1d7a65f7e">&#9670;&#160;</a></span>$output <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$output[] = &quot;&lt;h1&gt;<a class="el" href="class_maintenance.html">Maintenance</a> Guideline Implementation Table Fix&lt;/h1&gt;&quot;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a5766efd703cef0e00bfc06b3f3acbe0e" name="a5766efd703cef0e00bfc06b3f3acbe0e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5766efd703cef0e00bfc06b3f3acbe0e">&#9670;&#160;</a></span>$pdo</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$pdo = new PDO($dsn, <a class="el" href="config_8php.html#a1d1d99f8e08f387d84fe9848f3357156">DB_USER</a>, <a class="el" href="config_8php.html#a8bb9c4546d91667cfa61879d83127a92">DB_PASS</a>, $options)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a112ef069ddc0454086e3d1e6d8d55d07" name="a112ef069ddc0454086e3d1e6d8d55d07"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a112ef069ddc0454086e3d1e6d8d55d07">&#9670;&#160;</a></span>$result</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$result = $stmt-&gt;fetch(PDO::FETCH_ASSOC)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af27a9140d5f2658693e7fd107f716449" name="af27a9140d5f2658693e7fd107f716449"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af27a9140d5f2658693e7fd107f716449">&#9670;&#160;</a></span>$stmt</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$stmt = $pdo-&gt;query(&quot;SHOW TABLES LIKE 'maintenance_guideline_implementation'&quot;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a739ac305594911a5539be43a2839ce59" name="a739ac305594911a5539be43a2839ce59"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a739ac305594911a5539be43a2839ce59">&#9670;&#160;</a></span>$tableDefinition</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$tableDefinition = $stmt-&gt;fetch(PDO::FETCH_ASSOC)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7978dab33fce909f84e229f7a7188f77" name="a7978dab33fce909f84e229f7a7188f77"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7978dab33fce909f84e229f7a7188f77">&#9670;&#160;</a></span>$tableExists</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$tableExists = $stmt-&gt;rowCount() &gt; 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4c02af40dc23ebcbfdf20ae145ef9824" name="a4c02af40dc23ebcbfdf20ae145ef9824"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4c02af40dc23ebcbfdf20ae145ef9824">&#9670;&#160;</a></span>catch</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">catch(PDOException $<a class="el" href="output__helper_8php.html#a18d38faad6177eda235a3d9d28572984">e</a>) </td>
          <td>(</td>
          <td class="paramtype">PDOException</td>          <td class="paramname"><span class="paramname"><em>$e</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac3cd95c062d95e026a5559fcf9d8a3bf" name="ac3cd95c062d95e026a5559fcf9d8a3bf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac3cd95c062d95e026a5559fcf9d8a3bf">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $tableExists) else</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">        <a class="code hl_variable" href="create__guideline__implementation__table_8php.html#a73004ce9cd673c1bfafd1dc351134797">$output</a>[] = <span class="stringliteral">&quot;&lt;p style=&#39;color:green&#39;&gt;maintenance_guideline_implementation table exists.&lt;/p&gt;&quot;</span></div>
<div class="ttc" id="acreate__guideline__implementation__table_8php_html_a73004ce9cd673c1bfafd1dc351134797"><div class="ttname"><a href="create__guideline__implementation__table_8php.html#a73004ce9cd673c1bfafd1dc351134797">$output</a></div><div class="ttdeci">$output</div><div class="ttdef"><b>Definition</b> create_guideline_implementation_table.php:18</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="ae47c9cdcb9158b51999009c1d97b68a6" name="ae47c9cdcb9158b51999009c1d97b68a6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae47c9cdcb9158b51999009c1d97b68a6">&#9670;&#160;</a></span>foreach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">foreach($output as $line) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$output as</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abe4cc9788f52e49485473dc699537388" name="abe4cc9788f52e49485473dc699537388"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe4cc9788f52e49485473dc699537388">&#9670;&#160;</a></span>try</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">try</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">    </div>
<div class="line">    $dsn = <span class="stringliteral">&#39;mysql:host=&#39;</span> . <a class="code hl_variable" href="config_8php.html#a293363d7988627f671958e2d908c202a">DB_HOST</a> . <span class="stringliteral">&#39;;dbname=&#39;</span> . <a class="code hl_variable" href="config_8php.html#ab5db0d3504f917f268614c50b02c53e2">DB_NAME</a></div>
<div class="ttc" id="aconfig_8php_html_a293363d7988627f671958e2d908c202a"><div class="ttname"><a href="config_8php.html#a293363d7988627f671958e2d908c202a">DB_HOST</a></div><div class="ttdeci">const DB_HOST</div><div class="ttdef"><b>Definition</b> config.php:5</div></div>
<div class="ttc" id="aconfig_8php_html_ab5db0d3504f917f268614c50b02c53e2"><div class="ttname"><a href="config_8php.html#ab5db0d3504f917f268614c50b02c53e2">DB_NAME</a></div><div class="ttdeci">const DB_NAME</div><div class="ttdef"><b>Definition</b> config.php:8</div></div>
</div><!-- fragment -->
</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
