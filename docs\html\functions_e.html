<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div class="contents">
<div class="textblock">Here is a list of all struct and union fields with links to the structures/unions they belong to:</div>

<h3><a id="index_e" name="index_e"></a>- e -</h3><ul>
<li>edit()&#160;:&#160;<a class="el" href="class_assets.html#a459ed16587e3a50b39b672c7e473abc5">Assets</a>, <a class="el" href="class_permissions.html#a459ed16587e3a50b39b672c7e473abc5">Permissions</a>, <a class="el" href="class_roles.html#a459ed16587e3a50b39b672c7e473abc5">Roles</a>, <a class="el" href="class_tags.html#a459ed16587e3a50b39b672c7e473abc5">Tags</a></li>
<li>editChecklistItem()&#160;:&#160;<a class="el" href="class_maintenance.html#a80c346d7c2eb6742214d7a2eafbbc6a6">Maintenance</a></li>
<li>editGuideline()&#160;:&#160;<a class="el" href="class_maintenance.html#accb1821e13b0574c994996c403d6e881">Maintenance</a></li>
<li>ensureErrorLogsTableExists()&#160;:&#160;<a class="el" href="class_error_log.html#ab5a561b39013b248d4be5a42f5fa8f48">ErrorLog</a></li>
<li>ensureGuidelineImplementationTableExists()&#160;:&#160;<a class="el" href="class_maintenance_model.html#a307b0ec1c1343e097ffa8d0a058cc353">MaintenanceModel</a></li>
<li>escapeOutput()&#160;:&#160;<a class="el" href="class_security.html#a5b86b53ed28d8dc7b1e9fe2a2d21cda8">Security</a></li>
<li>execute()&#160;:&#160;<a class="el" href="class_database.html#a1909f4b7f8129c7790cb75de2ffbe1e4">Database</a></li>
<li>export()&#160;:&#160;<a class="el" href="class_assets.html#a78e9aec2d288bbbc165f0ad1a447524e">Assets</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
