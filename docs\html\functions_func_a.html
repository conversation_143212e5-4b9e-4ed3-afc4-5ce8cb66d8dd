<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Data Fields - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div class="contents">
<div class="textblock">Here is a list of all functions with links to the structures/unions they belong to:</div>

<h3><a id="index_a" name="index_a"></a>- a -</h3><ul>
<li>about()&#160;:&#160;<a class="el" href="class_pages.html#a288fa575528fc7b49c23e125a5605039">Pages</a></li>
<li>add()&#160;:&#160;<a class="el" href="class_assets.html#a837ba24a1c3095ae67613238d866f79a">Assets</a>, <a class="el" href="class_maintenance.html#a5af77dc86e75e68b764cd874fa55c960">Maintenance</a>, <a class="el" href="class_permissions.html#a837ba24a1c3095ae67613238d866f79a">Permissions</a>, <a class="el" href="class_roles.html#a837ba24a1c3095ae67613238d866f79a">Roles</a>, <a class="el" href="class_tags.html#a837ba24a1c3095ae67613238d866f79a">Tags</a></li>
<li>addAsset()&#160;:&#160;<a class="el" href="class_asset.html#a91dfab2328e0161d3dca63dfcb8b097f">Asset</a></li>
<li>addAssetCost()&#160;:&#160;<a class="el" href="class_finance_model.html#a1c3b28394728db624c6b6602ff462e87">FinanceModel</a></li>
<li>addChecklistItem()&#160;:&#160;<a class="el" href="class_maintenance_guideline.html#aa78853d4b8a87ebcd6f89818ac37eaac">MaintenanceGuideline</a></li>
<li>addCost()&#160;:&#160;<a class="el" href="class_finance.html#a2518a250e7a0164d46b5257b2c2f3922">Finance</a></li>
<li>addGuideline()&#160;:&#160;<a class="el" href="class_maintenance_guideline.html#a383b611d9c3469a58ac395f64981c356">MaintenanceGuideline</a></li>
<li>addMaintenance()&#160;:&#160;<a class="el" href="class_maintenance_model.html#a49112508b667299f63c3e43993c89ac1">MaintenanceModel</a></li>
<li>addRecord()&#160;:&#160;<a class="el" href="class_asset_history.html#ae9a1c316aa86b83e83209d7c2435d841">AssetHistory</a></li>
<li>addTag()&#160;:&#160;<a class="el" href="class_tag.html#a4ff2d7b570ab7813d09be22cb72fcd46">Tag</a></li>
<li>addTagToAsset()&#160;:&#160;<a class="el" href="class_tag.html#a2b19e0c9ea97d5b603d6a5a02940c523">Tag</a></li>
<li>advancedSearchAssets()&#160;:&#160;<a class="el" href="class_asset.html#a488f0cf663d59902ee8008a71964f62c">Asset</a></li>
<li>allHistory()&#160;:&#160;<a class="el" href="class_maintenance.html#a1cefddff0a8d40a82cc589cdf00f5b63">Maintenance</a></li>
<li>asset()&#160;:&#160;<a class="el" href="class_compliance.html#aa9712d2ec27afddb5c060abd82a8fc39">Compliance</a>, <a class="el" href="class_finance.html#a70fafc7500687f64af7bf492d3a13c3c">Finance</a></li>
<li>assetCompliance()&#160;:&#160;<a class="el" href="class_maintenance.html#a322e3e8f6158aebfc7f0bc7c23f9f93d">Maintenance</a></li>
<li>assignPermissions()&#160;:&#160;<a class="el" href="class_role.html#af180b7a33eaf553419c164116e7bcdb2">Role</a></li>
<li>assignRoles()&#160;:&#160;<a class="el" href="class_user.html#a4a5ea56c1886bd9841172b60638a6db1">User</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
