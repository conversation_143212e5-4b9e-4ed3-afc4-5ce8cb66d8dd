<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Data Fields - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div class="contents">
<div class="textblock">Here is a list of all functions with links to the structures/unions they belong to:</div>

<h3><a id="index_c" name="index_c"></a>- c -</h3><ul>
<li>calculateAssetHealthScore()&#160;:&#160;<a class="el" href="class_maintenance_model.html#afb3f191cdd7cdb9724de3d59626b162a">MaintenanceModel</a></li>
<li>calculateDepreciation()&#160;:&#160;<a class="el" href="class_finance_model.html#ab6929cf3308d48a3c73d46cdf847a9b0">FinanceModel</a></li>
<li>checkDataIntegrity()&#160;:&#160;<a class="el" href="class_maintenance_model.html#ab101e35ab0da74f49df5f58253d43da3">MaintenanceModel</a></li>
<li>checkImportRateLimit()&#160;:&#160;<a class="el" href="class_security_enhancements.html#a75213e4a19f7cf342423d6aa3742735d">SecurityEnhancements</a></li>
<li>checkPasswordResetRateLimit()&#160;:&#160;<a class="el" href="class_security_enhancements.html#a6a1d971fda6ba0d428b6627bd57cd7cf">SecurityEnhancements</a></li>
<li>clear()&#160;:&#160;<a class="el" href="class_error_logs.html#aa821bec12eaa7e0f649397c9675ff505">ErrorLogs</a></li>
<li>clearErrorLogs()&#160;:&#160;<a class="el" href="class_error_log.html#a6979c1ed6251a51c8515d3bfacfb8d02">ErrorLog</a></li>
<li>complete()&#160;:&#160;<a class="el" href="class_maintenance.html#a93f0b59a2513f700476e3f8d01c30860">Maintenance</a></li>
<li>countAllMaintenanceHistory()&#160;:&#160;<a class="el" href="class_maintenance_model.html#aa9447fe3595934e7036eeba1eee31bae">MaintenanceModel</a></li>
<li>countAssetsWithHealthMetrics()&#160;:&#160;<a class="el" href="class_maintenance_model.html#a38aac20b8e7f44f2cf503808243794ec">MaintenanceModel</a></li>
<li>countAssetsWithTag()&#160;:&#160;<a class="el" href="class_tag.html#a1188485fb7b7b0abe22f005bed184c4c">Tag</a></li>
<li>countErrorLogs()&#160;:&#160;<a class="el" href="class_error_log.html#abba5797fcf8825cdb1a7638e090fbb94">ErrorLog</a></li>
<li>createPasswordResetToken()&#160;:&#160;<a class="el" href="class_user.html#a3df1c9b773342ccee92bf873c0677d45">User</a></li>
<li>createPermission()&#160;:&#160;<a class="el" href="class_permission.html#aaf3185ee0140ecaef58f1d0c606db1f9">Permission</a></li>
<li>createRememberMeToken()&#160;:&#160;<a class="el" href="class_security_enhancements.html#ad153125ec23b30e3f7401166f6b250e3">SecurityEnhancements</a>, <a class="el" href="class_user.html#add8aaf5e6881c74092b138a86de7764f">User</a></li>
<li>createRole()&#160;:&#160;<a class="el" href="class_role.html#a768411d287ea069df4e0331e6bcefe09">Role</a></li>
<li>createUserSession()&#160;:&#160;<a class="el" href="class_users.html#aefc10a3bb76cb1118a8c869e2e3ae03a">Users</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
