<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div class="contents">
<div class="textblock">Here is a list of all struct and union fields with links to the structures/unions they belong to:</div>

<h3><a id="index_g" name="index_g"></a>- g -</h3><ul>
<li>generate()&#160;:&#160;<a class="el" href="class_compliance.html#aa33e9836cc6af4b321a1586206e77193">Compliance</a></li>
<li>generateBudgetForecast()&#160;:&#160;<a class="el" href="class_finance_model.html#afc623d939da88738ae9729d532a45374">FinanceModel</a></li>
<li>generateCsrfToken()&#160;:&#160;<a class="el" href="class_security.html#a70b34e128c2e082e28e4b133bf532fbb">Security</a></li>
<li>generateReport()&#160;:&#160;<a class="el" href="class_compliance_model.html#acc7bd2f33a48df1f96004c6ac83bc452">ComplianceModel</a></li>
<li>getAllCategories()&#160;:&#160;<a class="el" href="class_permission.html#a772b8c55b241668e6031bb263f5d832f">Permission</a></li>
<li>getAllCompletedChecklistItems()&#160;:&#160;<a class="el" href="class_maintenance_model.html#a8dcecd02b7451e4ce6f807170b758049">MaintenanceModel</a></li>
<li>getAllComplianceStatus()&#160;:&#160;<a class="el" href="class_maintenance_compliance.html#ae004cfdc78ae9803530098e3b3fa8f34">MaintenanceCompliance</a></li>
<li>getAllGuidelines()&#160;:&#160;<a class="el" href="class_maintenance_guideline.html#acf5491f2d8fdc88257f5a86d9b858c51">MaintenanceGuideline</a></li>
<li>getAllMaintenanceHistory()&#160;:&#160;<a class="el" href="class_maintenance_model.html#ace2e334dc067d1e99ab1cf4bed40359f">MaintenanceModel</a></li>
<li>getAllPermissions()&#160;:&#160;<a class="el" href="class_permission.html#a6bcadef8c7da37eb84ae95bb9b07e069">Permission</a></li>
<li>getAllRoles()&#160;:&#160;<a class="el" href="class_role.html#ad9d41efb9af8f1d2f8d9575069976da0">Role</a></li>
<li>getAllScheduledMaintenance()&#160;:&#160;<a class="el" href="class_maintenance_model.html#a3712d99605d1092dd417d6813adfb838">MaintenanceModel</a></li>
<li>getAllUsers()&#160;:&#160;<a class="el" href="class_user.html#a48a9c3492291c38e6113e40e04deb2ed">User</a></li>
<li>getAssetById()&#160;:&#160;<a class="el" href="class_asset.html#a69a1960e84310a44bf8dbdeda4e34fdb">Asset</a></li>
<li>getAssetComplianceStatus()&#160;:&#160;<a class="el" href="class_compliance_model.html#a7feafecf43729d726a17800a2274562f">ComplianceModel</a>, <a class="el" href="class_maintenance_compliance.html#a38966b8a791ede8f4a33ada2c45900e4">MaintenanceCompliance</a></li>
<li>getAssetControlStatus()&#160;:&#160;<a class="el" href="class_compliance_model.html#a13e3ddd0e6d5a876f6d7b100a6f24acd">ComplianceModel</a></li>
<li>getAssetCosts()&#160;:&#160;<a class="el" href="class_finance_model.html#a722cf1192462e36e341e8e98d617a024">FinanceModel</a></li>
<li>getAssetCount()&#160;:&#160;<a class="el" href="class_asset.html#a4f1964df346c70fa2a3ef319bd9acb7a">Asset</a></li>
<li>getAssetCountByAcquisitionDate()&#160;:&#160;<a class="el" href="class_asset.html#ab6d82658428d5a3485a9c14c00c4cb91">Asset</a></li>
<li>getAssetCountByEmployee()&#160;:&#160;<a class="el" href="class_asset.html#acce8c1627f559a128e886639b0954511">Asset</a></li>
<li>getAssetCountByOS()&#160;:&#160;<a class="el" href="class_asset.html#adf321bfffefe63d041f00ad6adfca3fd">Asset</a></li>
<li>getAssetCountBySite()&#160;:&#160;<a class="el" href="class_asset.html#abdc448f418bdb34fd6cce81074ed02d8">Asset</a></li>
<li>getAssetCountByType()&#160;:&#160;<a class="el" href="class_asset.html#adcb3b1e930889634a52b36d2652b2eca">Asset</a></li>
<li>getAssetHistory()&#160;:&#160;<a class="el" href="class_asset.html#a7d272bd5a18c11c185325475ba86fe99">Asset</a>, <a class="el" href="class_asset_history.html#a7d272bd5a18c11c185325475ba86fe99">AssetHistory</a></li>
<li>getAssets()&#160;:&#160;<a class="el" href="class_asset.html#a60da5dbc75bf17137f158e70b09ef9ae">Asset</a>, <a class="el" href="class_tags.html#add8c3d3dd8b6b96ba72c4de1f8b5295f">Tags</a></li>
<li>getAssetsByIds()&#160;:&#160;<a class="el" href="class_asset.html#a77a2c945fd9edfec16d03c352b983b58">Asset</a></li>
<li>getAssetsByTag()&#160;:&#160;<a class="el" href="class_tag.html#ae0e9b2e99baafec639b087d1a0ada8b2">Tag</a></li>
<li>getAssetsDueForMaintenance()&#160;:&#160;<a class="el" href="class_maintenance_model.html#af337d34247442623d3ef21c81c77b5c7">MaintenanceModel</a></li>
<li>getAssetsForExport()&#160;:&#160;<a class="el" href="class_asset.html#a6ab50787cfda440dabe402645ff8ac6d">Asset</a></li>
<li>getAssetsWithHealthMetrics()&#160;:&#160;<a class="el" href="class_maintenance_model.html#a234816f2513b32ba0fea57902e2ca897">MaintenanceModel</a></li>
<li>getChecklistCompletion()&#160;:&#160;<a class="el" href="class_maintenance_compliance.html#a3b211ed4bb2c38a6eb9a7a0d6a42afab">MaintenanceCompliance</a></li>
<li>getChecklistItemById()&#160;:&#160;<a class="el" href="class_maintenance_guideline.html#a9237db02c249b6f068d682dd5df1b371">MaintenanceGuideline</a></li>
<li>getChecklistItems()&#160;:&#160;<a class="el" href="class_maintenance_guideline.html#abe2396d040b8fdb1946a63702f30e67c">MaintenanceGuideline</a>, <a class="el" href="class_maintenance_model.html#abe2396d040b8fdb1946a63702f30e67c">MaintenanceModel</a></li>
<li>getCompletedChecklistItems()&#160;:&#160;<a class="el" href="class_maintenance_model.html#ab5777d866bcf1d449e676c3d7eee1901">MaintenanceModel</a></li>
<li>getComplianceSummary()&#160;:&#160;<a class="el" href="class_compliance_model.html#a814c5d44551a1e675a00c71ca3742297">ComplianceModel</a></li>
<li>getComplianceSummaryByControl()&#160;:&#160;<a class="el" href="class_compliance_model.html#a06766d64303589b753c200226d796052">ComplianceModel</a></li>
<li>getCompliantEndpoints()&#160;:&#160;<a class="el" href="class_maintenance_compliance.html#ace44f8eca9de68cedee14e5fecd831a7">MaintenanceCompliance</a></li>
<li>getControlById()&#160;:&#160;<a class="el" href="class_compliance_model.html#a0418c1b96081854db5489ed186ad1337">ComplianceModel</a></li>
<li>getControlsByFramework()&#160;:&#160;<a class="el" href="class_compliance_model.html#aeac9b1b5819ae5e2970cfbd1a7597f76">ComplianceModel</a></li>
<li>getCostsByEquipmentType()&#160;:&#160;<a class="el" href="class_finance_model.html#ad9f0064083dfe65c08b304490432ecb8">FinanceModel</a></li>
<li>getCostsByFiscalYear()&#160;:&#160;<a class="el" href="class_finance_model.html#a8183489a77f4cdce3e1ec2665909d172">FinanceModel</a></li>
<li>getDetailedImplementedGuidelines()&#160;:&#160;<a class="el" href="class_maintenance_model.html#a4705bd8f1afc9de6401a5c14082ceca9">MaintenanceModel</a></li>
<li>getErrorLogById()&#160;:&#160;<a class="el" href="class_error_log.html#a3e0cd66d937aa3255f4c60b887f29f3f">ErrorLog</a></li>
<li>getErrorLogs()&#160;:&#160;<a class="el" href="class_error_log.html#a3f92688780dc6f24f4edc5aefdfd7dfe">ErrorLog</a></li>
<li>getFrameworkById()&#160;:&#160;<a class="el" href="class_compliance_model.html#a2a6f89d1e69db5f6f7ae1f97dde53d2e">ComplianceModel</a></li>
<li>getFrameworks()&#160;:&#160;<a class="el" href="class_compliance_model.html#a48611d2953815ea9e440be48270e5b41">ComplianceModel</a></li>
<li>getGuidelineById()&#160;:&#160;<a class="el" href="class_maintenance_guideline.html#a8ce317ffbfd16f85d200877dbb74d8ef">MaintenanceGuideline</a></li>
<li>getGuidelineComplianceStatus()&#160;:&#160;<a class="el" href="class_maintenance_compliance.html#ae41214459099d198fdbf254d1b6d3ea5">MaintenanceCompliance</a></li>
<li>getGuidelineImplementations()&#160;:&#160;<a class="el" href="class_maintenance_model.html#a712d5e3692faa0c9e6f6cb52079a36bf">MaintenanceModel</a></li>
<li>getGuidelinesByEquipmentType()&#160;:&#160;<a class="el" href="class_maintenance_guideline.html#ad2cc7d08e605aae76fe2d7fb575741b1">MaintenanceGuideline</a></li>
<li>getImplementedGuidelines()&#160;:&#160;<a class="el" href="class_maintenance_model.html#ac6be536865b41000e5ca2a1bb8994080">MaintenanceModel</a></li>
<li>getMaintenanceById()&#160;:&#160;<a class="el" href="class_maintenance_model.html#a308b6d3e1f8e959f87bf5ae8f6c93ae6">MaintenanceModel</a></li>
<li>getMaintenanceDueSoon()&#160;:&#160;<a class="el" href="class_maintenance_compliance.html#a78151f12d8c609fb337b7c75b901b09a">MaintenanceCompliance</a></li>
<li>getMaintenanceHistory()&#160;:&#160;<a class="el" href="class_maintenance_model.html#a5028d7b9ec8e052e69d9f683e1ebfd07">MaintenanceModel</a></li>
<li>getMaintenanceHistoryWithGuidelines()&#160;:&#160;<a class="el" href="class_maintenance_model.html#a3479744b4309d54812e49f4fdf605c28">MaintenanceModel</a></li>
<li>getMonthlyCosts()&#160;:&#160;<a class="el" href="class_finance_model.html#a35fd3a47017a454cba53c16cc6d7f6df">FinanceModel</a></li>
<li>getMostImplementedGuidelines()&#160;:&#160;<a class="el" href="class_maintenance_model.html#adcedfb512247d32097f1d74a9e118ee9">MaintenanceModel</a></li>
<li>getNextStepNumber()&#160;:&#160;<a class="el" href="class_maintenance_guideline.html#a8a36b79d222bae4ee9ebec107463a840">MaintenanceGuideline</a></li>
<li>getNonCompliantAssets()&#160;:&#160;<a class="el" href="class_compliance_model.html#ad933a2ed9c35310e79d219954f8fed3e">ComplianceModel</a></li>
<li>getOverdueMaintenance()&#160;:&#160;<a class="el" href="class_maintenance_compliance.html#a53eda7cd291800d6597fc9fd393d9cc9">MaintenanceCompliance</a></li>
<li>getPDO()&#160;:&#160;<a class="el" href="class_database.html#af708fa20ff0c04a5a9bd8badd63e632c">Database</a></li>
<li>getPermissionById()&#160;:&#160;<a class="el" href="class_permission.html#a3238707bb055c0edcccbd3aad5bd9a88">Permission</a></li>
<li>getPermissionByName()&#160;:&#160;<a class="el" href="class_permission.html#aef23bd8752f6255ed9ce7e15e41bba42">Permission</a></li>
<li>getPermissionsByCategory()&#160;:&#160;<a class="el" href="class_permission.html#a9a3e9fc5bf30a63366590b19e489a58f">Permission</a></li>
<li>getPrimaryRole()&#160;:&#160;<a class="el" href="class_user.html#a91222c71c8139bfa1bb5e8f6df776006">User</a></li>
<li>getRecentChanges()&#160;:&#160;<a class="el" href="class_asset_history.html#a526b9186f52bfa9f469457ee828e1968">AssetHistory</a></li>
<li>getRecentReports()&#160;:&#160;<a class="el" href="class_compliance_model.html#a3f36be93a3051b76fd781c4d27576bae">ComplianceModel</a></li>
<li>getReportById()&#160;:&#160;<a class="el" href="class_compliance_model.html#a7276523dbcf22869eb572eaf69c1e792">ComplianceModel</a></li>
<li>getRoleById()&#160;:&#160;<a class="el" href="class_role.html#adbabecd774e69faa53d68bb8ddedbd54">Role</a></li>
<li>getRoleByName()&#160;:&#160;<a class="el" href="class_role.html#a8314906463666f969d42371a864a164e">Role</a></li>
<li>getRolePermissions()&#160;:&#160;<a class="el" href="class_role.html#acbd22d5de18650bca342da1774129b78">Role</a></li>
<li>getRolesWithPermission()&#160;:&#160;<a class="el" href="class_permission.html#a5ee9b1302a08d6848721bd2f6f75f3b4">Permission</a></li>
<li>getScheduledMaintenanceId()&#160;:&#160;<a class="el" href="class_maintenance_model.html#adac8c5043e0bc976a86e615fce9b4a0d">MaintenanceModel</a></li>
<li>getTagById()&#160;:&#160;<a class="el" href="class_tag.html#a401430eadd174788ff0568d8f0c9c6d8">Tag</a></li>
<li>getTagByName()&#160;:&#160;<a class="el" href="class_tag.html#a2e986e3f035b2b0fb56c40778ce1f040">Tag</a></li>
<li>getTags()&#160;:&#160;<a class="el" href="class_tag.html#ae07173ab06a20e2f5bd928cc0518e01f">Tag</a></li>
<li>getTagsForAsset()&#160;:&#160;<a class="el" href="class_tag.html#a425a55b885d03edeafb572af23106a46">Tag</a></li>
<li>getTotalAssetsCount()&#160;:&#160;<a class="el" href="class_asset.html#a6425eeb4a115b8e037133d0bb3a8a82f">Asset</a></li>
<li>getTotalCostOfOwnership()&#160;:&#160;<a class="el" href="class_finance_model.html#a8e3a0e893bb4973a8ead487c7a2e84e4">FinanceModel</a></li>
<li>getTotalCount()&#160;:&#160;<a class="el" href="class_database.html#a4b44054e439ba58d94a43985eda4c41c">Database</a></li>
<li>getUniqueFieldValues()&#160;:&#160;<a class="el" href="class_asset.html#aaab70ee74a77249c0cb51a3ccd2eefd8">Asset</a></li>
<li>getUrl()&#160;:&#160;<a class="el" href="class_core.html#accd14bda49a1044b4d8dd93f020f11ee">Core</a></li>
<li>getUserByEmail()&#160;:&#160;<a class="el" href="class_user.html#a4e25c35f7741620a8309ea1c6a2117e6">User</a></li>
<li>getUserById()&#160;:&#160;<a class="el" href="class_user.html#a5e7d616b80a7b0fb4b8d2736800e29cc">User</a></li>
<li>getUserPermissions()&#160;:&#160;<a class="el" href="class_user.html#a0055c1740759104b1c17445ea2bd581a">User</a></li>
<li>getUserRoles()&#160;:&#160;<a class="el" href="class_user.html#a5368738110a683cd7a567ae1327cc898">User</a></li>
<li>getUsersWithRole()&#160;:&#160;<a class="el" href="class_role.html#ae5bd1e5322a775264f3a66451069f600">Role</a></li>
<li>guideline()&#160;:&#160;<a class="el" href="class_maintenance.html#abf9c215de21af2165cda212eaa586067">Maintenance</a></li>
<li>guidelineImplementations()&#160;:&#160;<a class="el" href="class_maintenance.html#adb25853af641d105c381e29e0d2e6409">Maintenance</a></li>
<li>guidelines()&#160;:&#160;<a class="el" href="class_maintenance.html#ad1c0026170594acf2d25446f4e359832">Maintenance</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
