<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div class="contents">
<div class="textblock">Here is a list of all struct and union fields with links to the structures/unions they belong to:</div>

<h3><a id="index_r" name="index_r"></a>- r -</h3><ul>
<li>recalculateHealthMetrics()&#160;:&#160;<a class="el" href="class_maintenance.html#aafa7994a16f9c00e068c54343174ca82">Maintenance</a></li>
<li>recordChecklistCompletion()&#160;:&#160;<a class="el" href="class_maintenance_compliance.html#ac4690ec3327999f4578be947e8809291">MaintenanceCompliance</a>, <a class="el" href="class_maintenance_model.html#a95bb0e0eb1c41a1c8bec6ce98a0f7cb6">MaintenanceModel</a></li>
<li>recordImportOperation()&#160;:&#160;<a class="el" href="class_security_enhancements.html#ac583ea07fd84f5b1cd5d04f28d7dcd43">SecurityEnhancements</a></li>
<li>recordLoginAttempt()&#160;:&#160;<a class="el" href="class_security_enhancements.html#a4206d1d37283f2055dcf0f4295a7a693">SecurityEnhancements</a></li>
<li>recreateMissingRecords()&#160;:&#160;<a class="el" href="class_maintenance.html#a9cbd796bd274dad9c2a2ef7eac75dbff">Maintenance</a>, <a class="el" href="class_maintenance_model.html#a9cbd796bd274dad9c2a2ef7eac75dbff">MaintenanceModel</a></li>
<li>register()&#160;:&#160;<a class="el" href="class_user.html#ad2bc607329e3fa62d3b95a76e61b650c">User</a>, <a class="el" href="class_users.html#acc294a6cc8e69743746820e3d15e3f78">Users</a></li>
<li>REMEMBER_ME_EXPIRY&#160;:&#160;<a class="el" href="class_security_enhancements.html#a3fe72921270c08d48d6b9b95648153e3">SecurityEnhancements</a></li>
<li>removeAllTagsFromAsset()&#160;:&#160;<a class="el" href="class_tag.html#aaf77d50b5c6361054aca053c5d2d6cf3">Tag</a></li>
<li>removeTagFromAsset()&#160;:&#160;<a class="el" href="class_tag.html#a880f1cf91fb22405339d3e55f1e51afb">Tag</a></li>
<li>report()&#160;:&#160;<a class="el" href="class_compliance.html#ac7912ba6d06a4a6cf2a4b9b59a42348a">Compliance</a></li>
<li>resetFailedLoginAttempts()&#160;:&#160;<a class="el" href="class_security_enhancements.html#ac3e149b33afa4881d81ed72bc8598d05">SecurityEnhancements</a></li>
<li>resetPassword()&#160;:&#160;<a class="el" href="class_users.html#aeca207a42dbe064c5646029f139aa787">Users</a></li>
<li>resetPasswordByEmail()&#160;:&#160;<a class="el" href="class_user.html#a2fa1b4e2fbf3919435a676d6808e663f">User</a></li>
<li>resetPasswordWithToken()&#160;:&#160;<a class="el" href="class_users.html#abd105defab1eca8a3fb91bca9ba910e4">Users</a></li>
<li>resultSet()&#160;:&#160;<a class="el" href="class_database.html#a8d76b101d743baa11d67bed3d92e9ced">Database</a></li>
<li>roles()&#160;:&#160;<a class="el" href="class_users.html#a73ca737a6f9f1af62cb8c7a4a53b589d">Users</a></li>
<li>rowCount()&#160;:&#160;<a class="el" href="class_database.html#a82b073888555fc72e57142fe913db377">Database</a></li>
<li>runMigration()&#160;:&#160;<a class="el" href="class_maintenance.html#a63340b3b2a8d07f4a03d1dc922530f38">Maintenance</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
