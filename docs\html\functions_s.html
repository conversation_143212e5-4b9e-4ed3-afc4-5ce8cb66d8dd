<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div class="contents">
<div class="textblock">Here is a list of all struct and union fields with links to the structures/unions they belong to:</div>

<h3><a id="index_s" name="index_s"></a>- s -</h3><ul>
<li>sanitizeInput()&#160;:&#160;<a class="el" href="class_security.html#aa7a83d3a48c6f050ffa947f770c291aa">Security</a></li>
<li>sanitizePostData()&#160;:&#160;<a class="el" href="class_controller.html#a0d92de8136cebc006a407442aab9db0a">Controller</a>, <a class="el" href="class_security.html#ab7474581f8ab54f56d7c285f873d4512">Security</a></li>
<li>search()&#160;:&#160;<a class="el" href="class_assets.html#a796bf438724e047aeef18579732a3780">Assets</a></li>
<li>searchAssets()&#160;:&#160;<a class="el" href="class_asset.html#a9ed8debba2e7a08cce8fc517d61ef203">Asset</a></li>
<li>serialNumberExists()&#160;:&#160;<a class="el" href="class_asset.html#ab743e64ad3125a35028e7e8eb8c862fd">Asset</a></li>
<li>setSecurityHeaders()&#160;:&#160;<a class="el" href="class_security.html#a643f5130705b7cbb5981d0edf91b4505">Security</a></li>
<li>show()&#160;:&#160;<a class="el" href="class_assets.html#ae4914d07a9bbe4aede7a5dea759f6287">Assets</a>, <a class="el" href="class_permissions.html#ae4914d07a9bbe4aede7a5dea759f6287">Permissions</a>, <a class="el" href="class_roles.html#ae4914d07a9bbe4aede7a5dea759f6287">Roles</a></li>
<li>single()&#160;:&#160;<a class="el" href="class_database.html#a9024b1dea78a802643ae2c66aa1c4a24">Database</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
