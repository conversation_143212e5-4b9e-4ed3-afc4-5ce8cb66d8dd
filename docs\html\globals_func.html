<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div class="contents">
<div class="textblock">Here is a list of all functions with links to the files they belong to:</div><ul>
<li>clearGlobalPermissionCache()&#160;:&#160;<a class="el" href="session__helper_8php.html#a346f134a5d2ca39e76810b757cf6255d">session_helper.php</a></li>
<li>clearPermissionCache()&#160;:&#160;<a class="el" href="session__helper_8php.html#abde6b371dbe38c869ffdd4684d046a4a">session_helper.php</a></li>
<li>customErrorHandler()&#160;:&#160;<a class="el" href="error__handler_8php.html#a03e29e7b07739ac892f00eaba52b2ac6">error_handler.php</a></li>
<li>customExceptionHandler()&#160;:&#160;<a class="el" href="error__handler_8php.html#a0bd9e5161069e68fa292a678d6cb955d">error_handler.php</a></li>
<li>e()&#160;:&#160;<a class="el" href="output__helper_8php.html#a18d38faad6177eda235a3d9d28572984">output_helper.php</a></li>
<li>flash()&#160;:&#160;<a class="el" href="session__helper_8php.html#a944d856551c6f577255c4c853fc9ee49">session_helper.php</a></li>
<li>get_error_type()&#160;:&#160;<a class="el" href="error__handler_8php.html#a1ec8538ecc12eddc0c5b7af771bc47d6">error_handler.php</a></li>
<li>getSortIndicator()&#160;:&#160;<a class="el" href="app_2views_2assets_2index_8php.html#ab29771c47555edcc9d91bfbe99f84b63">index.php</a>, <a class="el" href="search_8php.html#ad02b5389c04582e80c7de1ab48955b77">search.php</a></li>
<li>getUploadErrorMessage()&#160;:&#160;<a class="el" href="test__file__upload_8php.html#a714ce50db18b1104062399cdec8486c6">test_file_upload.php</a></li>
<li>hasPermission()&#160;:&#160;<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">session_helper.php</a></li>
<li>hasRole()&#160;:&#160;<a class="el" href="session__helper_8php.html#a53a5bca218d3879ec04ebc1cb2c2bb56">session_helper.php</a></li>
<li>isAdmin()&#160;:&#160;<a class="el" href="session__helper_8php.html#aabf23b66cd362adaa508de5bfb22706a">session_helper.php</a></li>
<li>isLoggedIn()&#160;:&#160;<a class="el" href="session__helper_8php.html#a33bdd79e5da367ebddd4cfbdbbfc7cff">session_helper.php</a></li>
<li>paginationUrl()&#160;:&#160;<a class="el" href="pagination_8php.html#ac7739cd67fc2b6f9404e19dffbd2432b">pagination.php</a></li>
<li>redirect()&#160;:&#160;<a class="el" href="session__helper_8php.html#aa7e78bf5572fe3a109a59d4a63e0fa34">session_helper.php</a></li>
<li>safe_echo()&#160;:&#160;<a class="el" href="output__helper_8php.html#a8e7a9215f6f4f47e8dda9cba0ca31664">output_helper.php</a></li>
<li>safe_echo_nl2br()&#160;:&#160;<a class="el" href="output__helper_8php.html#a1ea7ef3a26ba61cde214d4a03d9ecc37">output_helper.php</a></li>
<li>safe_echo_raw()&#160;:&#160;<a class="el" href="output__helper_8php.html#a6c900ca36f5aa693d1463711751111b7">output_helper.php</a></li>
<li>sendEmail()&#160;:&#160;<a class="el" href="email__helper_8php.html#ac7b4a4ee33f08e191dba634df62d43ce">email_helper.php</a></li>
<li>sendPasswordResetEmail()&#160;:&#160;<a class="el" href="email__helper_8php.html#a478f8d1988c6b936dd9722c64f0f01e8">email_helper.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
