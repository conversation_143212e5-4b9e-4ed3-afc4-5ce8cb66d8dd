<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a id="index_s" name="index_s"></a>- s -</h3><ul>
<li>safe_echo()&#160;:&#160;<a class="el" href="output__helper_8php.html#a8e7a9215f6f4f47e8dda9cba0ca31664">output_helper.php</a></li>
<li>safe_echo_nl2br()&#160;:&#160;<a class="el" href="output__helper_8php.html#a1ea7ef3a26ba61cde214d4a03d9ecc37">output_helper.php</a></li>
<li>safe_echo_raw()&#160;:&#160;<a class="el" href="output__helper_8php.html#a6c900ca36f5aa693d1463711751111b7">output_helper.php</a></li>
<li>sendEmail()&#160;:&#160;<a class="el" href="email__helper_8php.html#ac7b4a4ee33f08e191dba634df62d43ce">email_helper.php</a></li>
<li>sendPasswordResetEmail()&#160;:&#160;<a class="el" href="email__helper_8php.html#a478f8d1988c6b936dd9722c64f0f01e8">email_helper.php</a></li>
<li>SITENAME&#160;:&#160;<a class="el" href="config_8php.html#ac91fe4eef242dfc35896ee6b7e378022">config.php</a></li>
<li>switch&#160;:&#160;<a class="el" href="app_2views_2error__logs_2index_8php.html#a4bde7d5ea853e6dda100e3831dc3946b">index.php</a>, <a class="el" href="view_8php.html#ab79dec0f9fdcc05f90072eb2dbc59e56">view.php</a>, <a class="el" href="all__history_8php.html#a0762c7751ac35e246e17b4993490dff6">all_history.php</a>, <a class="el" href="guideline_8php.html#acce0ffd4737c5884a796b27d1efa48b5">guideline.php</a>, <a class="el" href="guideline__implementations_8php.html#a2d00dcd83fafcc8bda8c9f54822ecfdb">guideline_implementations.php</a>, <a class="el" href="history_8php.html#a0762c7751ac35e246e17b4993490dff6">history.php</a>, <a class="el" href="view__record_8php.html#ac3b4e985ec4050d6034cf1af22dfda1f">view_record.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
