<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div class="contents">
<div class="textblock">Here is a list of all variables with links to the files they belong to:</div>

<h3><a id="index__28" name="index__28"></a>- ( -</h3><ul>
<li>( $data[ 'allRoles'] as $role)&#160;:&#160;<a class="el" href="views_2users_2_roles_8php.html#a490f231e34fb2bad81c67a2270d9fbd0">roles.php</a></li>
<li>( $data[ 'categories'] as $category)&#160;:&#160;<a class="el" href="permissions_2add_8php.html#aaca3f24b593427103fa5a3989b6c30d8">add.php</a>, <a class="el" href="permissions_2edit_8php.html#aaca3f24b593427103fa5a3989b6c30d8">edit.php</a></li>
<li>( $data[ 'equipment_types'] as $type)&#160;:&#160;<a class="el" href="edit__guideline_8php.html#af7429309c703156f262362327ed1cb9f">edit_guideline.php</a></li>
<li>( $data[ 'filter_options'][ 'administration_types'] as $type)&#160;:&#160;<a class="el" href="search_8php.html#ad85aac63a81d7c2ac50aca0943a7d320">search.php</a></li>
<li>( $data[ 'filter_options'][ 'equipment_types'] as $type)&#160;:&#160;<a class="el" href="search_8php.html#aefc614adca80226b3fa8c61efe9e812b">search.php</a></li>
<li>( $data[ 'filter_options'][ 'operating_systems'] as $os)&#160;:&#160;<a class="el" href="search_8php.html#a769cb414d72ae3111d1c1d0a7ca3639a">search.php</a></li>
<li>( $data[ 'filter_options'][ 'program_sections'] as $section)&#160;:&#160;<a class="el" href="search_8php.html#a171d039a7479dd10161072b0a682c87f">search.php</a></li>
<li>( $data[ 'filter_options'][ 'site_names'] as $site)&#160;:&#160;<a class="el" href="search_8php.html#afbb5860222327979c6ac93aef21c0591">search.php</a></li>
<li>( $data[ 'filter_options'][ 'xdr_options'] as $option)&#160;:&#160;<a class="el" href="search_8php.html#affe50d4c06fe2b1388f190dbbc30c6ee">search.php</a></li>
<li>( $data[ 'tags'] as $tag)&#160;:&#160;<a class="el" href="search_8php.html#a9d32cc8a805b1b28a351dfe876bfb668">search.php</a></li>
<li>( $endPage&lt; $lastPage)&#160;:&#160;<a class="el" href="pagination_8php.html#a83bf805250f2ee633dd2ce7c36af3412">pagination.php</a></li>
<li>( $permission-&gt;category==$category)&#160;:&#160;<a class="el" href="views_2roles_2_permissions_8php.html#aeb9d65197630299c1b081387f72a433d">permissions.php</a></li>
<li>(!empty( $data[ 'asset_tags']))&#160;:&#160;<a class="el" href="assets_2edit_8php.html#a9987c3716013426bdc2ca1cf8e6031e1">edit.php</a></li>
<li>(!empty( $guidelineDetails))&#160;:&#160;<a class="el" href="view__record_8php.html#a844929e9dc05dd77d3b6b756032a6d16">view_record.php</a></li>
<li>(count( $data[ 'assets_due']) &gt; 0)&#160;:&#160;<a class="el" href="app_2views_2maintenance_2index_8php.html#a87e6ad6ae335c64338f0832e043b573c">index.php</a></li>
<li>(count( $data[ 'assets_health']) &gt; 0)&#160;:&#160;<a class="el" href="app_2views_2maintenance_2index_8php.html#af90db8bde5b9826e549922ed58ea68f6">index.php</a></li>
<li>(count( $data[ 'compliant_endpoints']) &gt; 0)&#160;:&#160;<a class="el" href="monitoring_8php.html#a0d7d9cb40393c201098a44cb3d4afe32">monitoring.php</a></li>
<li>(count( $data[ 'control_summary']) &gt; 0)&#160;:&#160;<a class="el" href="app_2views_2compliance_2index_8php.html#a88b90db63c2b36b85d371bab0ef3b644">index.php</a></li>
<li>(count( $data[ 'costs_by_equipment_type']) &gt; 0)&#160;:&#160;<a class="el" href="app_2views_2finance_2index_8php.html#a5553b91b143c1b248fca77c94960999a">index.php</a></li>
<li>(count( $data[ 'guidelines']) &gt; 0)&#160;:&#160;<a class="el" href="guidelines_8php.html#a568b6957bf610739c8755ee9539e8f60">guidelines.php</a></li>
<li>(count( $data[ 'history']) &gt; 0)&#160;:&#160;<a class="el" href="assets_2show_8php.html#a51e72331a03f6099b0e4ec461afd8598">show.php</a></li>
<li>(count( $data[ 'maintenance_due_soon']) &gt; 0)&#160;:&#160;<a class="el" href="monitoring_8php.html#a3279e125a4b737c6dc2c979becc04b30">monitoring.php</a></li>
<li>(count( $data[ 'non_compliant_assets']) &gt; 0)&#160;:&#160;<a class="el" href="app_2views_2compliance_2index_8php.html#a80e005f3bdab5f2680d6494ec89ff8b3">index.php</a></li>
<li>(count( $data[ 'overdue_maintenance']) &gt; 0)&#160;:&#160;<a class="el" href="monitoring_8php.html#ad65be5df1eedfe40f4fcfe99e3200095">monitoring.php</a></li>
<li>(count( $data[ 'recent_reports']) &gt; 0)&#160;:&#160;<a class="el" href="app_2views_2compliance_2index_8php.html#a015fdb3803c848a73f2d486cd919da6e">index.php</a></li>
<li>(count( $data[ 'tags']) &gt; 0)&#160;:&#160;<a class="el" href="app_2views_2tags_2index_8php.html#a9ecd3cc9b74fe3a89ef6f4bc22000e84">index.php</a></li>
<li>(empty( $data[ 'frameworks']))&#160;:&#160;<a class="el" href="app_2views_2compliance_2index_8php.html#a243e196c10a5fb8d371ea4c420892780">index.php</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
