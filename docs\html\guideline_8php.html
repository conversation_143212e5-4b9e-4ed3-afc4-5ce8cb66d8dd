<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/maintenance/guideline.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_287ed6d8d174ec1b6d586a434511d951.html">maintenance</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">guideline.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a9b4b8d3eb38c434be02d3e95ff1fb83b" id="r_a9b4b8d3eb38c434be02d3e95ff1fb83b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9b4b8d3eb38c434be02d3e95ff1fb83b">$importanceClass</a> = 'bg-blue-100 text-blue-800'</td></tr>
<tr class="separator:a9b4b8d3eb38c434be02d3e95ff1fb83b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acce0ffd4737c5884a796b27d1efa48b5" id="r_acce0ffd4737c5884a796b27d1efa48b5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acce0ffd4737c5884a796b27d1efa48b5">switch</a> ( $data[ 'guideline']-&gt;importance)</td></tr>
<tr class="separator:acce0ffd4737c5884a796b27d1efa48b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace2bc155e2c2e60f4ca6e084433104a5" id="r_ace2bc155e2c2e60f4ca6e084433104a5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ace2bc155e2c2e60f4ca6e084433104a5">if</a> (isset( $data[ 'compliance_status']) &amp;&amp;!empty( $data[ 'compliance_status']))</td></tr>
<tr class="separator:ace2bc155e2c2e60f4ca6e084433104a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b9d42daa53a6dc9fb6156d46d3f63e6" id="r_a3b9d42daa53a6dc9fb6156d46d3f63e6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3b9d42daa53a6dc9fb6156d46d3f63e6">$totalAssets</a> = ($data['compliance_status']-&gt;compliant_count ?? 0) + ($data['compliance_status']-&gt;non_compliant_count ?? 0)</td></tr>
<tr class="separator:a3b9d42daa53a6dc9fb6156d46d3f63e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d4ce20a31e47694d0105327b8502fe6" id="r_a2d4ce20a31e47694d0105327b8502fe6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2d4ce20a31e47694d0105327b8502fe6">$compliancePercentage</a> = $totalAssets &gt; 0 ? round(($data['compliance_status']-&gt;compliant_count ?? 0) / $totalAssets * 100) : 0</td></tr>
<tr class="separator:a2d4ce20a31e47694d0105327b8502fe6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a326d890de202dd1ba5a2d20b910f6e70" id="r_a326d890de202dd1ba5a2d20b910f6e70"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a326d890de202dd1ba5a2d20b910f6e70">$complianceClass</a> = 'text-red-600'</td></tr>
<tr class="separator:a326d890de202dd1ba5a2d20b910f6e70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82cd33ca97ff99f2fcc5e9c81d65251b" id="r_a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82cd33ca97ff99f2fcc5e9c81d65251b">endif</a></td></tr>
<tr class="separator:a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9084991b222cd72d17cb4af9bb4a81bf" id="r_a9084991b222cd72d17cb4af9bb4a81bf"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(isset($data['checklist_items']) &amp;&amp;!empty($data['checklist_items']))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9084991b222cd72d17cb4af9bb4a81bf">$displayIndex</a> = 0</td></tr>
<tr class="separator:a9084991b222cd72d17cb4af9bb4a81bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ca62e5bb65d68baeeba9a691736e665" id="r_a3ca62e5bb65d68baeeba9a691736e665"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>($data['checklist_items'] as $item)(in_array($item-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>, $displayedItemIds))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3ca62e5bb65d68baeeba9a691736e665">$displayedItemIds</a> [] = $item-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a></td></tr>
<tr class="separator:a3ca62e5bb65d68baeeba9a691736e665"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a326d890de202dd1ba5a2d20b910f6e70" name="a326d890de202dd1ba5a2d20b910f6e70"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a326d890de202dd1ba5a2d20b910f6e70">&#9670;&#160;</a></span>$complianceClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$complianceClass = 'text-red-600'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2d4ce20a31e47694d0105327b8502fe6" name="a2d4ce20a31e47694d0105327b8502fe6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d4ce20a31e47694d0105327b8502fe6">&#9670;&#160;</a></span>$compliancePercentage</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$compliancePercentage = $totalAssets &gt; 0 ? round(($data['compliance_status']-&gt;compliant_count ?? 0) / $totalAssets * 100) : 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3ca62e5bb65d68baeeba9a691736e665" name="a3ca62e5bb65d68baeeba9a691736e665"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ca62e5bb65d68baeeba9a691736e665">&#9670;&#160;</a></span>$displayedItemIds</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ( $data[ 'checklist_items'] as $item) (in_array( $item-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>, $displayedItemIds)) $displayedItemIds[] = $item-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9084991b222cd72d17cb4af9bb4a81bf" name="a9084991b222cd72d17cb4af9bb4a81bf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9084991b222cd72d17cb4af9bb4a81bf">&#9670;&#160;</a></span>$displayIndex</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$displayIndex = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9b4b8d3eb38c434be02d3e95ff1fb83b" name="a9b4b8d3eb38c434be02d3e95ff1fb83b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b4b8d3eb38c434be02d3e95ff1fb83b">&#9670;&#160;</a></span>$importanceClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$importanceClass = 'bg-blue-100 text-blue-800'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3b9d42daa53a6dc9fb6156d46d3f63e6" name="a3b9d42daa53a6dc9fb6156d46d3f63e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3b9d42daa53a6dc9fb6156d46d3f63e6">&#9670;&#160;</a></span>$totalAssets</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$totalAssets = ($data['compliance_status']-&gt;compliant_count ?? 0) + ($data['compliance_status']-&gt;non_compliant_count ?? 0)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a82cd33ca97ff99f2fcc5e9c81d65251b" name="a82cd33ca97ff99f2fcc5e9c81d65251b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82cd33ca97ff99f2fcc5e9c81d65251b">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ace2bc155e2c2e60f4ca6e084433104a5" name="ace2bc155e2c2e60f4ca6e084433104a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ace2bc155e2c2e60f4ca6e084433104a5">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if( $compliancePercentage &gt;=90) <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> if($compliancePercentage &gt;=70) <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> if($compliancePercentage &gt;=50) </td>
          <td>(</td>
          <td class="paramtype">isset( $data[ 'compliance_status']) &amp;&amp;!empty( $data[ 'compliance_status'])</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="acce0ffd4737c5884a796b27d1efa48b5" name="acce0ffd4737c5884a796b27d1efa48b5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acce0ffd4737c5884a796b27d1efa48b5">&#9670;&#160;</a></span>switch</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">switch($data['guideline']-&gt;importance) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data-&gt;</em></span>[ 'guideline']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
