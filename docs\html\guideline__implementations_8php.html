<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/maintenance/guideline_implementations.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_287ed6d8d174ec1b6d586a434511d951.html">maintenance</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">guideline_implementations.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a9b4b8d3eb38c434be02d3e95ff1fb83b" id="r_a9b4b8d3eb38c434be02d3e95ff1fb83b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9b4b8d3eb38c434be02d3e95ff1fb83b">$importanceClass</a> = 'bg-blue-100 text-blue-800'</td></tr>
<tr class="separator:a9b4b8d3eb38c434be02d3e95ff1fb83b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d00dcd83fafcc8bda8c9f54822ecfdb" id="r_a2d00dcd83fafcc8bda8c9f54822ecfdb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2d00dcd83fafcc8bda8c9f54822ecfdb">switch</a> ( $data[ 'guideline']-&gt;importance)</td></tr>
<tr class="separator:a2d00dcd83fafcc8bda8c9f54822ecfdb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c60048091118c9f9090c304d09b0e5c" id="r_a2c60048091118c9f9090c304d09b0e5c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($data['implementations']))($data['implementations'] as $implementation)($implementation-&gt;implemented_date) ? date('M <a class="el" href="maintenance_2add_8php.html#aa6e74d4a6956f60f154d17e5f1a247eb">j</a> <a class="el" href="maintenance_2add_8php.html#af684b39ee5e37a5a84094eb9c37e94e2">Y</a> N A&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2c60048091118c9f9090c304d09b0e5c">h</a></td></tr>
<tr class="separator:a2c60048091118c9f9090c304d09b0e5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afdce259f9a4417669a271883cd011b0f" id="r_afdce259f9a4417669a271883cd011b0f"><td class="memItemLeft" align="right" valign="top">if(!empty($data['implementations']))($data['implementations'] as $implementation)($implementation-&gt;implemented_date) ? date('M <a class="el" href="maintenance_2add_8php.html#aa6e74d4a6956f60f154d17e5f1a247eb">j</a> <a class="el" href="maintenance_2add_8php.html#af684b39ee5e37a5a84094eb9c37e94e2">Y</a> N A strtotime( $implementation-&gt;implemented_date))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afdce259f9a4417669a271883cd011b0f">if</a> (isset( $implementation-&gt;computer_host_name) &amp;&amp;isset( $implementation-&gt;equipment_type))</td></tr>
<tr class="separator:afdce259f9a4417669a271883cd011b0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8b4bb1441c6ab4dcb28a37bc46c8ead" id="r_ae8b4bb1441c6ab4dcb28a37bc46c8ead"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae8b4bb1441c6ab4dcb28a37bc46c8ead">__pad1__</a></td></tr>
<tr class="separator:ae8b4bb1441c6ab4dcb28a37bc46c8ead"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed2d37b4e8da3f52103ae96ce9d26d82" id="r_aed2d37b4e8da3f52103ae96ce9d26d82"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aed2d37b4e8da3f52103ae96ce9d26d82">__pad2__</a></td></tr>
<tr class="separator:aed2d37b4e8da3f52103ae96ce9d26d82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7a2f290836f6cfc3034c179440448395" id="r_a7a2f290836f6cfc3034c179440448395"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(isset($implementation-&gt;maintenance_id)) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(isset( $implementation-&gt;asset_id)) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!isset($implementation-&gt;maintenance_id) &amp;&amp;!isset($implementation-&gt;asset_id))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7a2f290836f6cfc3034c179440448395">endforeach</a></td></tr>
<tr class="separator:a7a2f290836f6cfc3034c179440448395"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82cd33ca97ff99f2fcc5e9c81d65251b" id="r_a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $data[ 'current_page'] &gt; 1) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data['current_page']&lt; $data['total_pages'])&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82cd33ca97ff99f2fcc5e9c81d65251b">endif</a></td></tr>
<tr class="separator:a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad3aa1069376b85bd4e503b216d54b18d" id="r_ad3aa1069376b85bd4e503b216d54b18d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad3aa1069376b85bd4e503b216d54b18d">__pad3__</a></td></tr>
<tr class="separator:ad3aa1069376b85bd4e503b216d54b18d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a9b4b8d3eb38c434be02d3e95ff1fb83b" name="a9b4b8d3eb38c434be02d3e95ff1fb83b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b4b8d3eb38c434be02d3e95ff1fb83b">&#9670;&#160;</a></span>$importanceClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$importanceClass = 'bg-blue-100 text-blue-800'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae8b4bb1441c6ab4dcb28a37bc46c8ead" name="ae8b4bb1441c6ab4dcb28a37bc46c8ead"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae8b4bb1441c6ab4dcb28a37bc46c8ead">&#9670;&#160;</a></span>__pad1__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad1__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aed2d37b4e8da3f52103ae96ce9d26d82" name="aed2d37b4e8da3f52103ae96ce9d26d82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aed2d37b4e8da3f52103ae96ce9d26d82">&#9670;&#160;</a></span>__pad2__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad2__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad3aa1069376b85bd4e503b216d54b18d" name="ad3aa1069376b85bd4e503b216d54b18d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad3aa1069376b85bd4e503b216d54b18d">&#9670;&#160;</a></span>__pad3__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad3__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7a2f290836f6cfc3034c179440448395" name="a7a2f290836f6cfc3034c179440448395"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7a2f290836f6cfc3034c179440448395">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(isset( $implementation-&gt;maintenance_id)) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(isset($implementation-&gt;asset_id)) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (!isset( $implementation-&gt;maintenance_id) &amp;&amp;!isset( $implementation-&gt;asset_id)) endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a82cd33ca97ff99f2fcc5e9c81d65251b" name="a82cd33ca97ff99f2fcc5e9c81d65251b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82cd33ca97ff99f2fcc5e9c81d65251b">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2c60048091118c9f9090c304d09b0e5c" name="a2c60048091118c9f9090c304d09b0e5c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2c60048091118c9f9090c304d09b0e5c">&#9670;&#160;</a></span>h</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty( $data[ 'implementations'])) ( $data[ 'implementations'] as $implementation) ( $implementation-&gt;implemented_date) ? date( 'M <a class="el" href="maintenance_2add_8php.html#aa6e74d4a6956f60f154d17e5f1a247eb">j</a> <a class="el" href="maintenance_2add_8php.html#af684b39ee5e37a5a84094eb9c37e94e2">Y</a> N A h</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="afdce259f9a4417669a271883cd011b0f" name="afdce259f9a4417669a271883cd011b0f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afdce259f9a4417669a271883cd011b0f">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if(!empty( $data[ 'implementations']))( $data[ 'implementations'] as $implementation)( $implementation-&gt;implemented_date) ? date( 'M <a class="el" href="maintenance_2add_8php.html#aa6e74d4a6956f60f154d17e5f1a247eb">j</a> <a class="el" href="maintenance_2add_8php.html#af684b39ee5e37a5a84094eb9c37e94e2">Y</a> N A strtotime($implementation-&gt;implemented_date)) if(isset($implementation-&gt;computer_host_name) &amp;&amp;isset($implementation-&gt;equipment_type)) </td>
          <td>(</td>
          <td class="paramtype">isset( $implementation-&gt;computer_host_name) &amp;&amp;isset( $implementation-&gt;equipment_type)</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2d00dcd83fafcc8bda8c9f54822ecfdb" name="a2d00dcd83fafcc8bda8c9f54822ecfdb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d00dcd83fafcc8bda8c9f54822ecfdb">&#9670;&#160;</a></span>switch</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(isset($implementation-&gt;status)) switch($implementation-&gt;status) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data-&gt;</em></span>[ 'guideline']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
