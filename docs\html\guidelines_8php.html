<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/maintenance/guidelines.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_287ed6d8d174ec1b6d586a434511d951.html">maintenance</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">guidelines.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:afbbbbd3342729ba99f577b836cf3f479" id="r_afbbbbd3342729ba99f577b836cf3f479"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afbbbbd3342729ba99f577b836cf3f479">$equipmentTypes</a> = []</td></tr>
<tr class="separator:afbbbbd3342729ba99f577b836cf3f479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7dd844225544a6bbbc3d05fd86bd40d" id="r_ad7dd844225544a6bbbc3d05fd86bd40d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad7dd844225544a6bbbc3d05fd86bd40d">foreach</a> ( $data[ 'guidelines'] as $guideline)</td></tr>
<tr class="separator:ad7dd844225544a6bbbc3d05fd86bd40d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c1bcb77c0686b142a36054fc8c0ff82" id="r_a2c1bcb77c0686b142a36054fc8c0ff82"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2c1bcb77c0686b142a36054fc8c0ff82">$criticalCount</a> = 0</td></tr>
<tr class="separator:a2c1bcb77c0686b142a36054fc8c0ff82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7dd844225544a6bbbc3d05fd86bd40d" id="r_ad7dd844225544a6bbbc3d05fd86bd40d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad7dd844225544a6bbbc3d05fd86bd40d">foreach</a> ( $data[ 'guidelines'] as $guideline)</td></tr>
<tr class="separator:ad7dd844225544a6bbbc3d05fd86bd40d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a568b6957bf610739c8755ee9539e8f60" id="r_a568b6957bf610739c8755ee9539e8f60"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a568b6957bf610739c8755ee9539e8f60">(count( $data[ 'guidelines']) &gt; 0)</a> ( $data[ 'guidelines'] as $guideline)</td></tr>
<tr class="separator:a568b6957bf610739c8755ee9539e8f60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acce42e56c42b7e5c124a396db855895c" id="r_acce42e56c42b7e5c124a396db855895c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $guideline-&gt;frequency_days&lt; 30) else <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($guideline-&gt;frequency_days&lt; 365)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acce42e56c42b7e5c124a396db855895c">else</a></td></tr>
<tr class="separator:acce42e56c42b7e5c124a396db855895c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b4b8d3eb38c434be02d3e95ff1fb83b" id="r_a9b4b8d3eb38c434be02d3e95ff1fb83b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9b4b8d3eb38c434be02d3e95ff1fb83b">$importanceClass</a> = 'bg-blue-100 text-blue-800'</td></tr>
<tr class="separator:a9b4b8d3eb38c434be02d3e95ff1fb83b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba5673371203cfb0eb42925f370ee6de" id="r_aba5673371203cfb0eb42925f370ee6de"><td class="memItemLeft" align="right" valign="top">if($guideline-&gt;importance=='critical') <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> if( $guideline-&gt;importance=='high') <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aba5673371203cfb0eb42925f370ee6de">if</a> ( $guideline-&gt;importance=='low')</td></tr>
<tr class="separator:aba5673371203cfb0eb42925f370ee6de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82cd33ca97ff99f2fcc5e9c81d65251b" id="r_a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82cd33ca97ff99f2fcc5e9c81d65251b">endif</a></td></tr>
<tr class="separator:a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a2c1bcb77c0686b142a36054fc8c0ff82" name="a2c1bcb77c0686b142a36054fc8c0ff82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2c1bcb77c0686b142a36054fc8c0ff82">&#9670;&#160;</a></span>$criticalCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$criticalCount = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="afbbbbd3342729ba99f577b836cf3f479" name="afbbbbd3342729ba99f577b836cf3f479"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afbbbbd3342729ba99f577b836cf3f479">&#9670;&#160;</a></span>$equipmentTypes</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$equipmentTypes = []</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9b4b8d3eb38c434be02d3e95ff1fb83b" name="a9b4b8d3eb38c434be02d3e95ff1fb83b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b4b8d3eb38c434be02d3e95ff1fb83b">&#9670;&#160;</a></span>$importanceClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$importanceClass = 'bg-blue-100 text-blue-800'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a568b6957bf610739c8755ee9539e8f60" name="a568b6957bf610739c8755ee9539e8f60"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a568b6957bf610739c8755ee9539e8f60">&#9670;&#160;</a></span>(count( $data[ 'guidelines']) &gt; 0)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (count($data['guidelines']) &gt; 0)($data['guidelines'] as $guideline) </td>
          <td>(</td>
          <td class="paramtype">count( $data[ 'guidelines'])</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="acce42e56c42b7e5c124a396db855895c" name="acce42e56c42b7e5c124a396db855895c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acce42e56c42b7e5c124a396db855895c">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($guideline-&gt;frequency_days&lt; 30) else <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $guideline-&gt;frequency_days&lt; 365) else </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">                                                echo floor($guideline-&gt;frequency_days / 365) . <span class="stringliteral">&#39; years&#39;</span></div>
</div><!-- fragment -->
</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a82cd33ca97ff99f2fcc5e9c81d65251b" name="a82cd33ca97ff99f2fcc5e9c81d65251b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82cd33ca97ff99f2fcc5e9c81d65251b">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad7dd844225544a6bbbc3d05fd86bd40d" name="ad7dd844225544a6bbbc3d05fd86bd40d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad7dd844225544a6bbbc3d05fd86bd40d">&#9670;&#160;</a></span>foreach <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">foreach($data['guidelines'] as $guideline) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'guidelines']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad7dd844225544a6bbbc3d05fd86bd40d" name="ad7dd844225544a6bbbc3d05fd86bd40d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad7dd844225544a6bbbc3d05fd86bd40d">&#9670;&#160;</a></span>foreach <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">foreach($data['guidelines'] as $guideline) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'guidelines']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aba5673371203cfb0eb42925f370ee6de" name="aba5673371203cfb0eb42925f370ee6de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aba5673371203cfb0eb42925f370ee6de">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if( $guideline-&gt;importance=='critical') <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> if($guideline-&gt;importance=='high') <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> if($guideline-&gt;importance=='low') </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$guideline-&gt;</em></span><span class="paramdefsep"> = </span><span class="paramdefval">=&#160;'low'</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
