<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/maintenance/history.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_287ed6d8d174ec1b6d586a434511d951.html">maintenance</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">history.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a1ccb94cadb093164e431630a7020d49f" id="r_a1ccb94cadb093164e431630a7020d49f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1ccb94cadb093164e431630a7020d49f">$healthScore</a> = $data['health_metrics']['health_score']</td></tr>
<tr class="separator:a1ccb94cadb093164e431630a7020d49f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab82a04539e3f3415ec35adedf87b141f" id="r_ab82a04539e3f3415ec35adedf87b141f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab82a04539e3f3415ec35adedf87b141f">$bgColor</a> = 'bg-red-100'</td></tr>
<tr class="separator:ab82a04539e3f3415ec35adedf87b141f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab99343d4912650d1363355564aa6c179" id="r_ab99343d4912650d1363355564aa6c179"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab99343d4912650d1363355564aa6c179">$textColor</a> = 'text-red-500'</td></tr>
<tr class="separator:ab99343d4912650d1363355564aa6c179"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d2ef56084da76900b54dc6e24111a1f" id="r_a5d2ef56084da76900b54dc6e24111a1f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($healthScore &gt;=90) elseif( $healthScore &gt;=70)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5d2ef56084da76900b54dc6e24111a1f">elseif</a> ( $healthScore &gt;=50)</td></tr>
<tr class="separator:a5d2ef56084da76900b54dc6e24111a1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7a9279b820a48336935e6399ca4449d" id="r_ab7a9279b820a48336935e6399ca4449d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab7a9279b820a48336935e6399ca4449d">$remainingDays</a> = $data['health_metrics']['estimated_remaining_life']</td></tr>
<tr class="separator:ab7a9279b820a48336935e6399ca4449d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3499cf00dcf227701f7c64cd355f97ab" id="r_a3499cf00dcf227701f7c64cd355f97ab"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3499cf00dcf227701f7c64cd355f97ab">$remainingText</a> = ''</td></tr>
<tr class="separator:a3499cf00dcf227701f7c64cd355f97ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab69592dceb9ac068ae47b1fbab27393e" id="r_ab69592dceb9ac068ae47b1fbab27393e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $remainingDays &gt; 365) <a class="el" href="app_2views_2compliance_2index_8php.html#a2fe92cbc651f6f645279ed84f54274dd">elseif</a>($remainingDays &gt; 30)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab69592dceb9ac068ae47b1fbab27393e">else</a></td></tr>
<tr class="separator:ab69592dceb9ac068ae47b1fbab27393e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0ae4ebc0aa0cdb2a8c4137e7284f6ed" id="r_ad0ae4ebc0aa0cdb2a8c4137e7284f6ed"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad0ae4ebc0aa0cdb2a8c4137e7284f6ed">$failureProb</a> = $data['health_metrics']['failure_probability']</td></tr>
<tr class="separator:ad0ae4ebc0aa0cdb2a8c4137e7284f6ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d5245acca2b1882e8d22272d0f95462" id="r_a5d5245acca2b1882e8d22272d0f95462"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5d5245acca2b1882e8d22272d0f95462">$fpBgColor</a> = 'bg-green-100'</td></tr>
<tr class="separator:a5d5245acca2b1882e8d22272d0f95462"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a583d8afa12d4cd1cfb8d6ae5adc43a4b" id="r_a583d8afa12d4cd1cfb8d6ae5adc43a4b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a583d8afa12d4cd1cfb8d6ae5adc43a4b">$fpTextColor</a> = 'text-green-500'</td></tr>
<tr class="separator:a583d8afa12d4cd1cfb8d6ae5adc43a4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad91b289b04487d30b11cd3dc57b4dee5" id="r_ad91b289b04487d30b11cd3dc57b4dee5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad91b289b04487d30b11cd3dc57b4dee5">$typeClass</a> = 'bg-blue-100 text-blue-800'</td></tr>
<tr class="separator:ad91b289b04487d30b11cd3dc57b4dee5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0762c7751ac35e246e17b4993490dff6" id="r_a0762c7751ac35e246e17b4993490dff6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0762c7751ac35e246e17b4993490dff6">switch</a> ( $record-&gt;maintenance_type)</td></tr>
<tr class="separator:a0762c7751ac35e246e17b4993490dff6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($data['implemented_guidelines'][$record-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>]))($data['implemented_guidelines'][$record-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>] as $guideline)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82cd33ca97ff99f2fcc5e9c81d65251b" id="r_a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82cd33ca97ff99f2fcc5e9c81d65251b">endif</a></td></tr>
<tr class="separator:a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab9b724306b055e8e4ed6d1e1f1653f1" id="r_aab9b724306b055e8e4ed6d1e1f1653f1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aab9b724306b055e8e4ed6d1e1f1653f1">$statusClass</a> = 'bg-gray-100 text-gray-800'</td></tr>
<tr class="separator:aab9b724306b055e8e4ed6d1e1f1653f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a151ebc11c7bfaffb7cbcfc761c61eec2" id="r_a151ebc11c7bfaffb7cbcfc761c61eec2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data['health_metrics']['health_score']&lt; 70) <a class="el" href="maintenance_2add_8php.html#af684b39ee5e37a5a84094eb9c37e94e2">Y</a> M&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a151ebc11c7bfaffb7cbcfc761c61eec2">j</a></td></tr>
<tr class="separator:a151ebc11c7bfaffb7cbcfc761c61eec2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="ab82a04539e3f3415ec35adedf87b141f" name="ab82a04539e3f3415ec35adedf87b141f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab82a04539e3f3415ec35adedf87b141f">&#9670;&#160;</a></span>$bgColor</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$bgColor = 'bg-red-100'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad0ae4ebc0aa0cdb2a8c4137e7284f6ed" name="ad0ae4ebc0aa0cdb2a8c4137e7284f6ed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad0ae4ebc0aa0cdb2a8c4137e7284f6ed">&#9670;&#160;</a></span>$failureProb</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$failureProb = $data['health_metrics']['failure_probability']</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a5d5245acca2b1882e8d22272d0f95462" name="a5d5245acca2b1882e8d22272d0f95462"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5d5245acca2b1882e8d22272d0f95462">&#9670;&#160;</a></span>$fpBgColor</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$fpBgColor = 'bg-green-100'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a583d8afa12d4cd1cfb8d6ae5adc43a4b" name="a583d8afa12d4cd1cfb8d6ae5adc43a4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a583d8afa12d4cd1cfb8d6ae5adc43a4b">&#9670;&#160;</a></span>$fpTextColor</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$fpTextColor = 'text-green-500'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1ccb94cadb093164e431630a7020d49f" name="a1ccb94cadb093164e431630a7020d49f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1ccb94cadb093164e431630a7020d49f">&#9670;&#160;</a></span>$healthScore</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$healthScore = $data['health_metrics']['health_score']</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab7a9279b820a48336935e6399ca4449d" name="ab7a9279b820a48336935e6399ca4449d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7a9279b820a48336935e6399ca4449d">&#9670;&#160;</a></span>$remainingDays</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$remainingDays = $data['health_metrics']['estimated_remaining_life']</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3499cf00dcf227701f7c64cd355f97ab" name="a3499cf00dcf227701f7c64cd355f97ab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3499cf00dcf227701f7c64cd355f97ab">&#9670;&#160;</a></span>$remainingText</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$remainingText = ''</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aab9b724306b055e8e4ed6d1e1f1653f1" name="aab9b724306b055e8e4ed6d1e1f1653f1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aab9b724306b055e8e4ed6d1e1f1653f1">&#9670;&#160;</a></span>$statusClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$statusClass = 'bg-gray-100 text-gray-800'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab99343d4912650d1363355564aa6c179" name="ab99343d4912650d1363355564aa6c179"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab99343d4912650d1363355564aa6c179">&#9670;&#160;</a></span>$textColor</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$textColor = 'text-red-500'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad91b289b04487d30b11cd3dc57b4dee5" name="ad91b289b04487d30b11cd3dc57b4dee5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad91b289b04487d30b11cd3dc57b4dee5">&#9670;&#160;</a></span>$typeClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$typeClass = 'bg-blue-100 text-blue-800'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab69592dceb9ac068ae47b1fbab27393e" name="ab69592dceb9ac068ae47b1fbab27393e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab69592dceb9ac068ae47b1fbab27393e">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($remainingDays &gt; 365) <a class="el" href="app_2views_2compliance_2index_8php.html#a2fe92cbc651f6f645279ed84f54274dd">elseif</a> ( $remainingDays &gt; 30) else</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">                    <a class="code hl_variable" href="#a3499cf00dcf227701f7c64cd355f97ab">$remainingText</a> = <a class="code hl_variable" href="#ab7a9279b820a48336935e6399ca4449d">$remainingDays</a> . <span class="stringliteral">&#39; day&#39;</span> . (<a class="code hl_variable" href="#ab7a9279b820a48336935e6399ca4449d">$remainingDays</a> &gt; 1 ? <span class="charliteral">&#39;s&#39;</span> : <span class="stringliteral">&#39;&#39;</span>)</div>
<div class="ttc" id="ahistory_8php_html_a3499cf00dcf227701f7c64cd355f97ab"><div class="ttname"><a href="#a3499cf00dcf227701f7c64cd355f97ab">$remainingText</a></div><div class="ttdeci">$remainingText</div><div class="ttdef"><b>Definition</b> history.php:68</div></div>
<div class="ttc" id="ahistory_8php_html_ab7a9279b820a48336935e6399ca4449d"><div class="ttname"><a href="#ab7a9279b820a48336935e6399ca4449d">$remainingDays</a></div><div class="ttdeci">$remainingDays</div><div class="ttdef"><b>Definition</b> history.php:67</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a5d2ef56084da76900b54dc6e24111a1f" name="a5d2ef56084da76900b54dc6e24111a1f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5d2ef56084da76900b54dc6e24111a1f">&#9670;&#160;</a></span>elseif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($failureProb &gt;=60) elseif($failureProb &gt;=30) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$healthScore &gt;=</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a82cd33ca97ff99f2fcc5e9c81d65251b" name="a82cd33ca97ff99f2fcc5e9c81d65251b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82cd33ca97ff99f2fcc5e9c81d65251b">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a151ebc11c7bfaffb7cbcfc761c61eec2" name="a151ebc11c7bfaffb7cbcfc761c61eec2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a151ebc11c7bfaffb7cbcfc761c61eec2">&#9670;&#160;</a></span>j</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $data[ 'health_metrics'][ 'health_score']&lt; 70) <a class="el" href="maintenance_2add_8php.html#af684b39ee5e37a5a84094eb9c37e94e2">Y</a> M j </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0762c7751ac35e246e17b4993490dff6" name="a0762c7751ac35e246e17b4993490dff6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0762c7751ac35e246e17b4993490dff6">&#9670;&#160;</a></span>switch</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">switch($record-&gt;status) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$record-&gt;</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
