<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: import_csv.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">import_csv.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:abe4cc9788f52e49485473dc699537388" id="r_abe4cc9788f52e49485473dc699537388"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abe4cc9788f52e49485473dc699537388">try</a></td></tr>
<tr class="separator:abe4cc9788f52e49485473dc699537388"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3e7241ecae2d76e9bca9c774781d1880" id="r_a3e7241ecae2d76e9bca9c774781d1880"><td class="memItemLeft" align="right" valign="top"><a class="el" href="check__db_8php.html#a9ee42195f2b26ca51b7b816b4f28113e">catch</a>(PDOException $<a class="el" href="output__helper_8php.html#a18d38faad6177eda235a3d9d28572984">e</a>)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3e7241ecae2d76e9bca9c774781d1880">$csvFile</a> = 'FO II _ICT ASSET INVENTORY.csv'</td></tr>
<tr class="separator:a3e7241ecae2d76e9bca9c774781d1880"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa72ed5abe27f57130dd8ab9ee3889f22" id="r_aa72ed5abe27f57130dd8ab9ee3889f22"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!file_exists($csvFile))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa72ed5abe27f57130dd8ab9ee3889f22">$file</a> = fopen($csvFile, 'r')</td></tr>
<tr class="separator:aa72ed5abe27f57130dd8ab9ee3889f22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a21f9f43b345ece3b727d24158bda1bfd" id="r_a21f9f43b345ece3b727d24158bda1bfd"><td class="memItemLeft" align="right" valign="top">for($i=0; $i&lt; 7; $i++)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a21f9f43b345ece3b727d24158bda1bfd">$sql</a></td></tr>
<tr class="separator:a21f9f43b345ece3b727d24158bda1bfd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af27a9140d5f2658693e7fd107f716449" id="r_af27a9140d5f2658693e7fd107f716449"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af27a9140d5f2658693e7fd107f716449">$stmt</a> = $pdo-&gt;prepare($sql)</td></tr>
<tr class="separator:af27a9140d5f2658693e7fd107f716449"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09df382259cc3cee673a691e821d0186" id="r_a09df382259cc3cee673a691e821d0186"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a09df382259cc3cee673a691e821d0186">$importCount</a> = 0</td></tr>
<tr class="separator:a09df382259cc3cee673a691e821d0186"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a3e7241ecae2d76e9bca9c774781d1880" name="a3e7241ecae2d76e9bca9c774781d1880"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3e7241ecae2d76e9bca9c774781d1880">&#9670;&#160;</a></span>$csvFile</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="check__db_8php.html#a9ee42195f2b26ca51b7b816b4f28113e">catch</a> (PDOException $<a class="el" href="output__helper_8php.html#a18d38faad6177eda235a3d9d28572984">e</a>) $csvFile = 'FO II _ICT ASSET INVENTORY.csv'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa72ed5abe27f57130dd8ab9ee3889f22" name="aa72ed5abe27f57130dd8ab9ee3889f22"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa72ed5abe27f57130dd8ab9ee3889f22">&#9670;&#160;</a></span>$file</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (!file_exists( $csvFile)) $file = fopen($csvFile, 'r')</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a09df382259cc3cee673a691e821d0186" name="a09df382259cc3cee673a691e821d0186"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a09df382259cc3cee673a691e821d0186">&#9670;&#160;</a></span>$importCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$importCount = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a21f9f43b345ece3b727d24158bda1bfd" name="a21f9f43b345ece3b727d24158bda1bfd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a21f9f43b345ece3b727d24158bda1bfd">&#9670;&#160;</a></span>$sql</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">for ( $i=0;$i&lt; 7;$i++) $sql </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= <span class="stringliteral">&quot;INSERT INTO assets (inventory_date, site_name, employee_name, active_directory_name, position, program_section, computer_host_name, equipment_type, acquisition_type, operating_system, administration_type, xdr_installed, device_custodian, remarks, par_number, serial_number, acquisition_date, estimated_useful_life) </span></div>
<div class="line"><span class="stringliteral">        VALUES (:inventory_date, :site_name, :employee_name, :active_directory_name, :position, :program_section, :computer_host_name, :equipment_type, :acquisition_type, :operating_system, :administration_type, :xdr_installed, :device_custodian, :remarks, :par_number, :serial_number, :acquisition_date, :estimated_useful_life)&quot;</span></div>
</div><!-- fragment -->
</div>
</div>
<a id="af27a9140d5f2658693e7fd107f716449" name="af27a9140d5f2658693e7fd107f716449"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af27a9140d5f2658693e7fd107f716449">&#9670;&#160;</a></span>$stmt</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$stmt = $pdo-&gt;prepare($sql)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abe4cc9788f52e49485473dc699537388" name="abe4cc9788f52e49485473dc699537388"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe4cc9788f52e49485473dc699537388">&#9670;&#160;</a></span>try</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">try</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">    <a class="code hl_variable" href="fix__guideline__implementation__table_8php.html#a5766efd703cef0e00bfc06b3f3acbe0e">$pdo</a> = <span class="keyword">new</span> PDO(<span class="stringliteral">&#39;mysql:host=&#39;</span> . <a class="code hl_variable" href="config_8php.html#a293363d7988627f671958e2d908c202a">DB_HOST</a> . <span class="stringliteral">&#39;;dbname=&#39;</span> . <a class="code hl_variable" href="config_8php.html#ab5db0d3504f917f268614c50b02c53e2">DB_NAME</a>, <a class="code hl_variable" href="config_8php.html#a1d1d99f8e08f387d84fe9848f3357156">DB_USER</a>, <a class="code hl_variable" href="config_8php.html#a8bb9c4546d91667cfa61879d83127a92">DB_PASS</a>)</div>
<div class="ttc" id="aconfig_8php_html_a1d1d99f8e08f387d84fe9848f3357156"><div class="ttname"><a href="config_8php.html#a1d1d99f8e08f387d84fe9848f3357156">DB_USER</a></div><div class="ttdeci">const DB_USER</div><div class="ttdef"><b>Definition</b> config.php:6</div></div>
<div class="ttc" id="aconfig_8php_html_a293363d7988627f671958e2d908c202a"><div class="ttname"><a href="config_8php.html#a293363d7988627f671958e2d908c202a">DB_HOST</a></div><div class="ttdeci">const DB_HOST</div><div class="ttdef"><b>Definition</b> config.php:5</div></div>
<div class="ttc" id="aconfig_8php_html_a8bb9c4546d91667cfa61879d83127a92"><div class="ttname"><a href="config_8php.html#a8bb9c4546d91667cfa61879d83127a92">DB_PASS</a></div><div class="ttdeci">const DB_PASS</div><div class="ttdef"><b>Definition</b> config.php:7</div></div>
<div class="ttc" id="aconfig_8php_html_ab5db0d3504f917f268614c50b02c53e2"><div class="ttname"><a href="config_8php.html#ab5db0d3504f917f268614c50b02c53e2">DB_NAME</a></div><div class="ttdeci">const DB_NAME</div><div class="ttdef"><b>Definition</b> config.php:8</div></div>
<div class="ttc" id="afix__guideline__implementation__table_8php_html_a5766efd703cef0e00bfc06b3f3acbe0e"><div class="ttname"><a href="fix__guideline__implementation__table_8php.html#a5766efd703cef0e00bfc06b3f3acbe0e">$pdo</a></div><div class="ttdeci">$pdo</div><div class="ttdef"><b>Definition</b> fix_guideline_implementation_table.php:26</div></div>
</div><!-- fragment -->
</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
