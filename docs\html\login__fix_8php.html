<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: login_fix.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">login_fix.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a13feb61b92e1e15e598806dc6e51f328" id="r_a13feb61b92e1e15e598806dc6e51f328"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(<a class="el" href="session__helper_8php.html#a33bdd79e5da367ebddd4cfbdbbfc7cff">isLoggedIn</a>())&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a13feb61b92e1e15e598806dc6e51f328">else</a></td></tr>
<tr class="separator:a13feb61b92e1e15e598806dc6e51f328"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe4cc9788f52e49485473dc699537388" id="r_abe4cc9788f52e49485473dc699537388"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abe4cc9788f52e49485473dc699537388">try</a></td></tr>
<tr class="separator:abe4cc9788f52e49485473dc699537388"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af27a9140d5f2658693e7fd107f716449" id="r_af27a9140d5f2658693e7fd107f716449"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af27a9140d5f2658693e7fd107f716449">$stmt</a> = $pdo-&gt;query(&quot;SELECT * FROM users WHERE email = '<EMAIL>'&quot;)</td></tr>
<tr class="separator:af27a9140d5f2658693e7fd107f716449"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad3b3c64b25fbbb6aec24407e4333aa71" id="r_ad3b3c64b25fbbb6aec24407e4333aa71"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad3b3c64b25fbbb6aec24407e4333aa71">$admin</a> = $stmt-&gt;fetch(PDO::FETCH_ASSOC)</td></tr>
<tr class="separator:ad3b3c64b25fbbb6aec24407e4333aa71"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a607686ef9f99ea7c42f4f3dd3dbb2b0d" id="r_a607686ef9f99ea7c42f4f3dd3dbb2b0d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a607686ef9f99ea7c42f4f3dd3dbb2b0d">$password</a> = password_hash('password123', PASSWORD_DEFAULT)</td></tr>
<tr class="separator:a607686ef9f99ea7c42f4f3dd3dbb2b0d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="ad3b3c64b25fbbb6aec24407e4333aa71" name="ad3b3c64b25fbbb6aec24407e4333aa71"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad3b3c64b25fbbb6aec24407e4333aa71">&#9670;&#160;</a></span>$admin</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$admin = $stmt-&gt;fetch(PDO::FETCH_ASSOC)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a607686ef9f99ea7c42f4f3dd3dbb2b0d" name="a607686ef9f99ea7c42f4f3dd3dbb2b0d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a607686ef9f99ea7c42f4f3dd3dbb2b0d">&#9670;&#160;</a></span>$password</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$password = password_hash('password123', PASSWORD_DEFAULT)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af27a9140d5f2658693e7fd107f716449" name="af27a9140d5f2658693e7fd107f716449"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af27a9140d5f2658693e7fd107f716449">&#9670;&#160;</a></span>$stmt</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$stmt = $pdo-&gt;query(&quot;SELECT * FROM users WHERE email = '<EMAIL>'&quot;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a13feb61b92e1e15e598806dc6e51f328" name="a13feb61b92e1e15e598806dc6e51f328"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a13feb61b92e1e15e598806dc6e51f328">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $stmt-&gt;execute()) else</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">    echo <span class="stringliteral">&quot;&lt;p style=&#39;color:red&#39;&gt;✗ User is not logged in&lt;/p&gt;&quot;</span></div>
</div><!-- fragment -->
</div>
</div>
<a id="abe4cc9788f52e49485473dc699537388" name="abe4cc9788f52e49485473dc699537388"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe4cc9788f52e49485473dc699537388">&#9670;&#160;</a></span>try</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">try</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">        <a class="code hl_variable" href="fix__guideline__implementation__table_8php.html#a5766efd703cef0e00bfc06b3f3acbe0e">$pdo</a> = <span class="keyword">new</span> PDO(<span class="stringliteral">&#39;mysql:host=&#39;</span> . <a class="code hl_variable" href="config_8php.html#a293363d7988627f671958e2d908c202a">DB_HOST</a> . <span class="stringliteral">&#39;;dbname=&#39;</span> . <a class="code hl_variable" href="config_8php.html#ab5db0d3504f917f268614c50b02c53e2">DB_NAME</a>, <a class="code hl_variable" href="config_8php.html#a1d1d99f8e08f387d84fe9848f3357156">DB_USER</a>, <a class="code hl_variable" href="config_8php.html#a8bb9c4546d91667cfa61879d83127a92">DB_PASS</a>)</div>
<div class="ttc" id="aconfig_8php_html_a1d1d99f8e08f387d84fe9848f3357156"><div class="ttname"><a href="config_8php.html#a1d1d99f8e08f387d84fe9848f3357156">DB_USER</a></div><div class="ttdeci">const DB_USER</div><div class="ttdef"><b>Definition</b> config.php:6</div></div>
<div class="ttc" id="aconfig_8php_html_a293363d7988627f671958e2d908c202a"><div class="ttname"><a href="config_8php.html#a293363d7988627f671958e2d908c202a">DB_HOST</a></div><div class="ttdeci">const DB_HOST</div><div class="ttdef"><b>Definition</b> config.php:5</div></div>
<div class="ttc" id="aconfig_8php_html_a8bb9c4546d91667cfa61879d83127a92"><div class="ttname"><a href="config_8php.html#a8bb9c4546d91667cfa61879d83127a92">DB_PASS</a></div><div class="ttdeci">const DB_PASS</div><div class="ttdef"><b>Definition</b> config.php:7</div></div>
<div class="ttc" id="aconfig_8php_html_ab5db0d3504f917f268614c50b02c53e2"><div class="ttname"><a href="config_8php.html#ab5db0d3504f917f268614c50b02c53e2">DB_NAME</a></div><div class="ttdeci">const DB_NAME</div><div class="ttdef"><b>Definition</b> config.php:8</div></div>
<div class="ttc" id="afix__guideline__implementation__table_8php_html_a5766efd703cef0e00bfc06b3f3acbe0e"><div class="ttname"><a href="fix__guideline__implementation__table_8php.html#a5766efd703cef0e00bfc06b3f3acbe0e">$pdo</a></div><div class="ttdeci">$pdo</div><div class="ttdef"><b>Definition</b> fix_guideline_implementation_table.php:26</div></div>
</div><!-- fragment -->
</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
