<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/maintenance/add.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_287ed6d8d174ec1b6d586a434511d951.html">maintenance</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">add.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a68143d415e263441ec9b170332943f27" id="r_a68143d415e263441ec9b170332943f27"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a68143d415e263441ec9b170332943f27">if</a> ( $data[ 'asset'])</td></tr>
<tr class="separator:a68143d415e263441ec9b170332943f27"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82cd33ca97ff99f2fcc5e9c81d65251b" id="r_a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82cd33ca97ff99f2fcc5e9c81d65251b">endif</a></td></tr>
<tr class="separator:a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6e74d4a6956f60f154d17e5f1a247eb" id="r_aa6e74d4a6956f60f154d17e5f1a247eb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $data[ 'asset_id']) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa6e74d4a6956f60f154d17e5f1a247eb">j</a> ['asset_id'] &amp;&amp;!empty($data['applicable_guidelines'])) <a class="el" href="#af684b39ee5e37a5a84094eb9c37e94e2">Y</a> M</td></tr>
<tr class="separator:aa6e74d4a6956f60f154d17e5f1a247eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af684b39ee5e37a5a84094eb9c37e94e2" id="r_af684b39ee5e37a5a84094eb9c37e94e2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $data[ 'asset_id']) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af684b39ee5e37a5a84094eb9c37e94e2">Y</a> ['asset_id'] &amp;&amp;!empty($data['applicable_guidelines'])) Y M</td></tr>
<tr class="separator:af684b39ee5e37a5a84094eb9c37e94e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bee1c6028cca051cae04a7f46b36ab4" id="r_a0bee1c6028cca051cae04a7f46b36ab4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $data[ 'asset_id']) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0bee1c6028cca051cae04a7f46b36ab4">id</a> ['asset_id'] &amp;&amp;!empty($data['applicable_guidelines'])) <a class="el" href="#af684b39ee5e37a5a84094eb9c37e94e2">Y</a> M $guideline</td></tr>
<tr class="separator:a0bee1c6028cca051cae04a7f46b36ab4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa0a4053ebd2900597f1cb834f093a04" id="r_aaa0a4053ebd2900597f1cb834f093a04"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaa0a4053ebd2900597f1cb834f093a04">$checklistItems</a> = $db-&gt;resultSet()</td></tr>
<tr class="separator:aaa0a4053ebd2900597f1cb834f093a04"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa194accd5ee3788f9386f5c80afc99d7" id="r_aa194accd5ee3788f9386f5c80afc99d7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty( $checklistItems))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa194accd5ee3788f9386f5c80afc99d7">foreach</a> ( $checklistItems as $item)</td></tr>
<tr class="separator:aa194accd5ee3788f9386f5c80afc99d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="aaa0a4053ebd2900597f1cb834f093a04" name="aaa0a4053ebd2900597f1cb834f093a04"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaa0a4053ebd2900597f1cb834f093a04">&#9670;&#160;</a></span>$checklistItems</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$checklistItems = $db-&gt;resultSet()</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a82cd33ca97ff99f2fcc5e9c81d65251b" name="a82cd33ca97ff99f2fcc5e9c81d65251b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82cd33ca97ff99f2fcc5e9c81d65251b">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa194accd5ee3788f9386f5c80afc99d7" name="aa194accd5ee3788f9386f5c80afc99d7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa194accd5ee3788f9386f5c80afc99d7">&#9670;&#160;</a></span>foreach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($checklistItems)) foreach($checklistItems as $item) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$checklistItems as</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0bee1c6028cca051cae04a7f46b36ab4" name="a0bee1c6028cca051cae04a7f46b36ab4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0bee1c6028cca051cae04a7f46b36ab4">&#9670;&#160;</a></span>id</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data['asset_id']) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $data id[ 'asset_id'] &amp;&amp;!empty( $data[ 'applicable_guidelines'])) <a class="el" href="#af684b39ee5e37a5a84094eb9c37e94e2">Y</a> M $guideline</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a68143d415e263441ec9b170332943f27" name="a68143d415e263441ec9b170332943f27"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a68143d415e263441ec9b170332943f27">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if($data['asset']) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data</em></span>[ 'asset']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa6e74d4a6956f60f154d17e5f1a247eb" name="aa6e74d4a6956f60f154d17e5f1a247eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa6e74d4a6956f60f154d17e5f1a247eb">&#9670;&#160;</a></span>j</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data['asset_id']) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $data j[ 'asset_id'] &amp;&amp;!empty( $data[ 'applicable_guidelines'])) <a class="el" href="#af684b39ee5e37a5a84094eb9c37e94e2">Y</a> M</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af684b39ee5e37a5a84094eb9c37e94e2" name="af684b39ee5e37a5a84094eb9c37e94e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af684b39ee5e37a5a84094eb9c37e94e2">&#9670;&#160;</a></span>Y</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data['asset_id']) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $data Y[ 'asset_id'] &amp;&amp;!empty( $data[ 'applicable_guidelines'])) Y M</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
