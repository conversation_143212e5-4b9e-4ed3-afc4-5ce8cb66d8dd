<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/users/manage.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_f74f3790f48dd7404afdfc13a8a7407b.html">users</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">manage.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:aaba99e56d85eb963ae741afc28ba2424" id="r_aaba99e56d85eb963ae741afc28ba2424"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>( $data[ 'users'] as $user)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaba99e56d85eb963ae741afc28ba2424">if</a> ( $user-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>==$_SESSION[ 'user_id'])</td></tr>
<tr class="separator:aaba99e56d85eb963ae741afc28ba2424"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77e4ffd6c4f34ba51bd783e131d75684" id="r_a77e4ffd6c4f34ba51bd783e131d75684"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a77e4ffd6c4f34ba51bd783e131d75684">endforeach</a></td></tr>
<tr class="separator:a77e4ffd6c4f34ba51bd783e131d75684"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b54768bcf2217f756f6b02ed5b35c68" id="r_a9b54768bcf2217f756f6b02ed5b35c68"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $user-&gt;status=='active') <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($user-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a> !=$_SESSION&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9b54768bcf2217f756f6b02ed5b35c68">endif</a> ['user_id'] &amp;&amp;<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>('manage_users'))</td></tr>
<tr class="separator:a9b54768bcf2217f756f6b02ed5b35c68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d4d37cf09fd3898b6b03000ae59b1f0" id="r_a4d4d37cf09fd3898b6b03000ae59b1f0"><td class="memItemLeft" align="right" valign="top">toggle role btn text indigo&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4d4d37cf09fd3898b6b03000ae59b1f0">hover</a></td></tr>
<tr class="separator:a4d4d37cf09fd3898b6b03000ae59b1f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a77e4ffd6c4f34ba51bd783e131d75684" name="a77e4ffd6c4f34ba51bd783e131d75684"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77e4ffd6c4f34ba51bd783e131d75684">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>( 'manage_users')) endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9b54768bcf2217f756f6b02ed5b35c68" name="a9b54768bcf2217f756f6b02ed5b35c68"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b54768bcf2217f756f6b02ed5b35c68">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4d4d37cf09fd3898b6b03000ae59b1f0" name="a4d4d37cf09fd3898b6b03000ae59b1f0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4d4d37cf09fd3898b6b03000ae59b1f0">&#9670;&#160;</a></span>hover</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">toggle role btn text indigo hover</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aaba99e56d85eb963ae741afc28ba2424" name="aaba99e56d85eb963ae741afc28ba2424"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaba99e56d85eb963ae741afc28ba2424">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>($data['users'] as $user) if($user-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>==$_SESSION['user_id']) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$user-&gt;</em></span><span class="paramdefsep"> = </span><span class="paramdefval">=&#160;$_SESSION['user_id']</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
