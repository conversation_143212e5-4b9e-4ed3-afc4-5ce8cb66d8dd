<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Asset Visibility System</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div><div class="header">
  <div class="headertitle"><div class="title">Asset Visibility System</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p><a class="anchor" id="autotoc_md15"></a></p>
<p>A PHP OOP MVC application for managing ICT assets inventory.</p>
<h1><a class="anchor" id="autotoc_md16"></a>
Features</h1>
<ul>
<li><a class="el" href="class_user.html">User</a> authentication (login/register)</li>
<li><a class="el" href="class_asset.html">Asset</a> management (CRUD operations)</li>
<li>CSV import functionality for bulk asset uploads</li>
<li>Search functionality</li>
<li>Responsive design with Bootstrap and ShadCN UI</li>
<li>jQuery for enhanced interactivity</li>
</ul>
<h1><a class="anchor" id="autotoc_md17"></a>
Requirements</h1>
<ul>
<li>PHP 7.4 or higher</li>
<li>MySQL 5.7 or higher</li>
<li>Apache web server with mod_rewrite enabled</li>
</ul>
<h1><a class="anchor" id="autotoc_md18"></a>
Installation</h1>
<ol type="1">
<li>Clone the repository to your web server's document root: <div class="fragment"><div class="line">git clone https://github.com/jmmaguigad/asset_visibility.git</div>
</div><!-- fragment --></li>
<li>Create a MySQL database: <div class="fragment"><div class="line">mysql -u root -p</div>
<div class="line">CREATE DATABASE asset_visibility;</div>
<div class="line">exit;</div>
</div><!-- fragment --></li>
<li>Import the database schema: <div class="fragment"><div class="line">mysql -u root -p asset_visibility &lt; database.sql</div>
</div><!-- fragment --></li>
<li>Configure the database connection in <code><a class="el" href="config_8php.html">app/config/config.php</a></code>: <div class="fragment"><div class="line">define(<span class="stringliteral">&#39;DB_HOST&#39;</span>, <span class="stringliteral">&#39;localhost&#39;</span>);</div>
<div class="line">define(<span class="stringliteral">&#39;DB_USER&#39;</span>, <span class="stringliteral">&#39;your_username&#39;</span>);</div>
<div class="line">define(<span class="stringliteral">&#39;DB_PASS&#39;</span>, <span class="stringliteral">&#39;your_password&#39;</span>);</div>
<div class="line">define(<span class="stringliteral">&#39;DB_NAME&#39;</span>, <span class="stringliteral">&#39;asset_visibility&#39;</span>);</div>
</div><!-- fragment --></li>
<li>Configure the URL root in <code><a class="el" href="config_8php.html">app/config/config.php</a></code>: <div class="fragment"><div class="line">define(<span class="stringliteral">&#39;URLROOT&#39;</span>, <span class="stringliteral">&#39;http://localhost/asset_visibility&#39;</span>);</div>
</div><!-- fragment --></li>
<li>Import data from the CSV file: <div class="fragment"><div class="line">php import_csv.php</div>
</div><!-- fragment --></li>
<li>Set up Apache virtual host (optional): <div class="fragment"><div class="line">&lt;VirtualHost *:80&gt;</div>
<div class="line">    ServerName asset_visibility.local</div>
<div class="line">    DocumentRoot /path/to/asset_visibility</div>
<div class="line">    &lt;Directory /path/to/asset_visibility&gt;</div>
<div class="line">        Options Indexes FollowSymLinks MultiViews</div>
<div class="line">        AllowOverride All</div>
<div class="line">        Require all granted</div>
<div class="line">    &lt;/Directory&gt;</div>
<div class="line">&lt;/VirtualHost&gt;</div>
</div><!-- fragment --></li>
<li>Add the domain to your hosts file (optional): <div class="fragment"><div class="line">127.0.0.1 asset_visibility.local</div>
</div><!-- fragment --></li>
</ol>
<h1><a class="anchor" id="autotoc_md19"></a>
Usage</h1>
<ol type="1">
<li>Navigate to the application URL (e.g., <a href="http://localhost/asset_visibility">http://localhost/asset_visibility</a>)</li>
<li>Login with the default admin account:<ul>
<li>Email: <a href="#" onclick="location.href='mai'+'lto:'+'adm'+'in'+'@ex'+'am'+'ple'+'.c'+'om'; return false;">admin<span class="obfuscator">.nosp@m.</span>@exa<span class="obfuscator">.nosp@m.</span>mple.<span class="obfuscator">.nosp@m.</span>com</a></li>
<li>Password: password123</li>
</ul>
</li>
<li>Start managing your assets!</li>
</ol>
<h2><a class="anchor" id="autotoc_md20"></a>
Importing Assets from CSV</h2>
<ol type="1">
<li>Login as an administrator</li>
<li>Navigate to the <a class="el" href="class_assets.html">Assets</a> page</li>
<li>Click on the "Import CSV" button</li>
<li>Upload your CSV file following the required format</li>
<li>Adjust the number of header rows to skip if needed</li>
<li>Click "Import" to process the file</li>
</ol>
<p>The CSV file should have the following columns in order:</p><ul>
<li>Date</li>
<li>Site Name</li>
<li>Employee Name</li>
<li>Active Directory Name</li>
<li>Position</li>
<li>Program/Section</li>
<li>Computer/Host Name (required)</li>
<li>Type of Equipment (required)</li>
<li>Acquisition Type</li>
<li>Operating System</li>
<li>Administration Type</li>
<li>XDR Installed (Yes/No)</li>
<li>Device Custodian</li>
<li>Remarks</li>
<li>PAR Number</li>
<li>Serial Number (required)</li>
<li>Acquisition Date</li>
<li>Estimated Useful Life</li>
</ul>
<h1><a class="anchor" id="autotoc_md21"></a>
Directory Structure</h1>
<div class="fragment"><div class="line">asset_visibility/</div>
<div class="line">├── app/</div>
<div class="line">│   ├── bootstrap.php</div>
<div class="line">│   ├── config/</div>
<div class="line">│   ├── controllers/</div>
<div class="line">│   ├── core/</div>
<div class="line">│   ├── helpers/</div>
<div class="line">│   ├── models/</div>
<div class="line">│   └── views/</div>
<div class="line">├── public/</div>
<div class="line">│   ├── css/</div>
<div class="line">│   ├── js/</div>
<div class="line">│   ├── img/</div>
<div class="line">│   ├── .htaccess</div>
<div class="line">│   └── index.php</div>
<div class="line">├── .htaccess</div>
<div class="line">├── database.sql</div>
<div class="line">└── import_csv.php</div>
</div><!-- fragment --><h1><a class="anchor" id="autotoc_md22"></a>
License</h1>
<p>This project is licensed under the MIT License. </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
