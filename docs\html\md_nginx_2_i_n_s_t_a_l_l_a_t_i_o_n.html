<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: Nginx Installation Guide for Asset Visibility System</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div><div class="header">
  <div class="headertitle"><div class="title">Nginx Installation Guide for Asset Visibility System</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p><a class="anchor" id="autotoc_md0"></a></p>
<p>This guide will help you set up the <a class="el" href="class_asset.html">Asset</a> Visibility System with Nginx as the web server.</p>
<h1><a class="anchor" id="autotoc_md1"></a>
Prerequisites</h1>
<ul>
<li>Nginx installed on your server</li>
<li>PHP 8.0 or higher with PHP-FPM</li>
<li>MySQL or MariaDB</li>
<li>The <a class="el" href="class_asset.html">Asset</a> Visibility System codebase</li>
</ul>
<h1><a class="anchor" id="autotoc_md2"></a>
Installation Steps</h1>
<h2><a class="anchor" id="autotoc_md3"></a>
1. Install Nginx and PHP-FPM</h2>
<div class="fragment"><div class="line"># For Ubuntu/Debian</div>
<div class="line">sudo apt update</div>
<div class="line">sudo apt install nginx php-fpm php-mysql php-mbstring php-xml php-curl</div>
<div class="line"> </div>
<div class="line"># For CentOS/RHEL</div>
<div class="line">sudo yum install epel-release</div>
<div class="line">sudo yum install nginx php-fpm php-mysql php-mbstring php-xml php-curl</div>
</div><!-- fragment --><h2><a class="anchor" id="autotoc_md4"></a>
2. Configure PHP-FPM</h2>
<p>Check your PHP-FPM version and configuration:</p>
<div class="fragment"><div class="line">php -v</div>
</div><!-- fragment --><p>Edit the PHP-FPM configuration if needed:</p>
<div class="fragment"><div class="line"># For Ubuntu/Debian (adjust the PHP version as needed)</div>
<div class="line">sudo nano /etc/php/8.0/fpm/php.ini</div>
<div class="line"> </div>
<div class="line"># For CentOS/RHEL</div>
<div class="line">sudo nano /etc/php.ini</div>
</div><!-- fragment --><p>Recommended PHP settings:</p>
<div class="fragment"><div class="line">upload_max_filesize = 20M</div>
<div class="line">post_max_size = 20M</div>
<div class="line">memory_limit = 256M</div>
<div class="line">max_execution_time = 120</div>
<div class="line">date.timezone = &quot;Your/Timezone&quot;  # e.g., &quot;America/New_York&quot;</div>
</div><!-- fragment --><p>Restart PHP-FPM:</p>
<div class="fragment"><div class="line"># For Ubuntu/Debian</div>
<div class="line">sudo systemctl restart php8.0-fpm</div>
<div class="line"> </div>
<div class="line"># For CentOS/RHEL</div>
<div class="line">sudo systemctl restart php-fpm</div>
</div><!-- fragment --><h2><a class="anchor" id="autotoc_md5"></a>
3. Configure Nginx</h2>
<p>Copy the provided Nginx configuration file to the Nginx sites directory:</p>
<div class="fragment"><div class="line"># For Ubuntu/Debian</div>
<div class="line">sudo cp nginx/asset_visibility.conf /etc/nginx/sites-available/</div>
<div class="line">sudo ln -s /etc/nginx/sites-available/asset_visibility.conf /etc/nginx/sites-enabled/</div>
<div class="line"> </div>
<div class="line"># For CentOS/RHEL</div>
<div class="line">sudo cp nginx/asset_visibility.conf /etc/nginx/conf.d/</div>
</div><!-- fragment --><p>Edit the configuration file to match your server setup:</p>
<div class="fragment"><div class="line"># For Ubuntu/Debian</div>
<div class="line">sudo nano /etc/nginx/sites-available/asset_visibility.conf</div>
<div class="line"> </div>
<div class="line"># For CentOS/RHEL</div>
<div class="line">sudo nano /etc/nginx/conf.d/asset_visibility.conf</div>
</div><!-- fragment --><p>Make sure to update:</p><ul>
<li><code>server_name</code> - Set to your domain or IP address</li>
<li><code>root</code> - Set to the actual path of your <a class="el" href="class_asset.html">Asset</a> Visibility System</li>
<li><code>fastcgi_pass</code> - Set to the correct PHP-FPM socket or TCP address</li>
</ul>
<h2><a class="anchor" id="autotoc_md6"></a>
4. Set Up Local Host (Development Environment)</h2>
<p>If you're setting up on a local development machine, add the domain to your hosts file:</p>
<div class="fragment"><div class="line"># For Windows</div>
<div class="line"># Edit C:\Windows\System32\drivers\etc\hosts</div>
<div class="line"># Add: 127.0.0.1 asset_visibility.local</div>
<div class="line"> </div>
<div class="line"># For Linux/Mac</div>
<div class="line">sudo nano /etc/hosts</div>
<div class="line"># Add: 127.0.0.1 asset_visibility.local</div>
</div><!-- fragment --><h2><a class="anchor" id="autotoc_md7"></a>
5. Test and Restart Nginx</h2>
<p>Test the Nginx configuration:</p>
<div class="fragment"><div class="line">sudo nginx -t</div>
</div><!-- fragment --><p>If the test is successful, restart Nginx:</p>
<div class="fragment"><div class="line">sudo systemctl restart nginx</div>
</div><!-- fragment --><h2><a class="anchor" id="autotoc_md8"></a>
6. Set Proper Permissions</h2>
<p>Make sure Nginx can read the files and PHP-FPM can write to necessary directories:</p>
<div class="fragment"><div class="line"># Adjust the user and group as needed (www-data for Ubuntu, nginx for CentOS)</div>
<div class="line">sudo chown -R www-data:www-data /path/to/asset_visibility</div>
<div class="line">sudo find /path/to/asset_visibility -type d -exec chmod 755 {} \;</div>
<div class="line">sudo find /path/to/asset_visibility -type f -exec chmod 644 {} \;</div>
</div><!-- fragment --><h2><a class="anchor" id="autotoc_md9"></a>
7. Update Application Configuration</h2>
<p>Update the <code><a class="el" href="config_8php.html">app/config/config.php</a></code> file to match your new setup:</p>
<div class="fragment"><div class="line">define(<span class="stringliteral">&#39;URLROOT&#39;</span>, <span class="stringliteral">&#39;http://asset_visibility.local&#39;</span>); <span class="comment">// Change to your domain</span></div>
</div><!-- fragment --><h2><a class="anchor" id="autotoc_md10"></a>
8. Setting Up SSL (Optional but Recommended)</h2>
<p>Generate SSL certificates (or use Let's Encrypt):</p>
<div class="fragment"><div class="line"># Using Let&#39;s Encrypt</div>
<div class="line">sudo apt install certbot python3-certbot-nginx</div>
<div class="line">sudo certbot --nginx -d asset_visibility.local</div>
</div><!-- fragment --><p>Or use self-signed certificates for development:</p>
<div class="fragment"><div class="line">sudo mkdir -p /etc/nginx/ssl</div>
<div class="line">sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout /etc/nginx/ssl/asset_visibility.key -out /etc/nginx/ssl/asset_visibility.crt</div>
</div><!-- fragment --><p>Then uncomment the HTTPS server block in the Nginx configuration file and restart Nginx.</p>
<h1><a class="anchor" id="autotoc_md11"></a>
Troubleshooting</h1>
<h2><a class="anchor" id="autotoc_md12"></a>
Common Issues</h2>
<ol type="1">
<li><b>403 Forbidden Error</b><ul>
<li>Check file permissions</li>
<li>Verify Nginx user has access to the files</li>
</ul>
</li>
<li><b>404 Not Found Error</b><ul>
<li>Check the <code>root</code> directive in the Nginx configuration</li>
<li>Verify the rewrite rules</li>
</ul>
</li>
<li><b>502 Bad Gateway Error</b><ul>
<li>Check if PHP-FPM is running</li>
<li>Verify the <code>fastcgi_pass</code> directive</li>
</ul>
</li>
<li><b>Blank Page or PHP Errors</b><ul>
<li>Check PHP error logs</li>
<li>Enable error reporting in PHP</li>
</ul>
</li>
</ol>
<h2><a class="anchor" id="autotoc_md13"></a>
Checking Logs</h2>
<div class="fragment"><div class="line"># Nginx error logs</div>
<div class="line">sudo tail -f /var/log/nginx/error.log</div>
<div class="line">sudo tail -f /var/log/nginx/asset_visibility_error.log</div>
<div class="line"> </div>
<div class="line"># PHP-FPM logs</div>
<div class="line">sudo tail -f /var/log/php8.0-fpm.log  # Adjust version as needed</div>
</div><!-- fragment --><h1><a class="anchor" id="autotoc_md14"></a>
Security Considerations</h1>
<ol type="1">
<li><b>Firewall Configuration</b><ul>
<li>Allow only ports 80 and 443</li>
<li>Use UFW or firewalld to manage firewall rules</li>
</ul>
</li>
<li><b>Regular Updates</b><ul>
<li>Keep Nginx, PHP, and all dependencies updated</li>
</ul>
</li>
<li><b>Use HTTPS</b><ul>
<li>Always use SSL/TLS in production</li>
<li>Redirect HTTP to HTTPS</li>
</ul>
</li>
<li><b>Secure Headers</b><ul>
<li>The provided configuration includes security headers</li>
<li>Consider using additional headers as needed </li>
</ul>
</li>
</ol>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
