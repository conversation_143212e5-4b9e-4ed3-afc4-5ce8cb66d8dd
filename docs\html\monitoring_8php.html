<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/maintenance/monitoring.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_287ed6d8d174ec1b6d586a434511d951.html">maintenance</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">monitoring.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:aaf8006d608d2a4f1d18029ca0974e3b3" id="r_aaf8006d608d2a4f1d18029ca0974e3b3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaf8006d608d2a4f1d18029ca0974e3b3">$totalCompliance</a> = $data['compliant_count'] + $data['due_soon_count'] + $data['overdue_count'] + $data['not_applicable_count']</td></tr>
<tr class="separator:aaf8006d608d2a4f1d18029ca0974e3b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42c52794fac97a1493ac79713f561aea" id="r_a42c52794fac97a1493ac79713f561aea"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($totalCompliance &gt; 0)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a42c52794fac97a1493ac79713f561aea">else</a></td></tr>
<tr class="separator:a42c52794fac97a1493ac79713f561aea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abddda9a408bfb52fd323be438cd3fe94" id="r_abddda9a408bfb52fd323be438cd3fe94"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abddda9a408bfb52fd323be438cd3fe94">$dueSoonPercent</a> = 0</td></tr>
<tr class="separator:abddda9a408bfb52fd323be438cd3fe94"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac563375536f300b2749efe560e300a49" id="r_ac563375536f300b2749efe560e300a49"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac563375536f300b2749efe560e300a49">$overduePercent</a> = 0</td></tr>
<tr class="separator:ac563375536f300b2749efe560e300a49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad60a350defbdae74bc48faa36a85c480" id="r_ad60a350defbdae74bc48faa36a85c480"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad60a350defbdae74bc48faa36a85c480">$notApplicablePercent</a> = 0</td></tr>
<tr class="separator:ad60a350defbdae74bc48faa36a85c480"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad65be5df1eedfe40f4fcfe99e3200095" id="r_ad65be5df1eedfe40f4fcfe99e3200095"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad65be5df1eedfe40f4fcfe99e3200095">(count( $data[ 'overdue_maintenance']) &gt; 0)</a> ( $data[ 'overdue_maintenance'] as $item)</td></tr>
<tr class="separator:ad65be5df1eedfe40f4fcfe99e3200095"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b4b8d3eb38c434be02d3e95ff1fb83b" id="r_a9b4b8d3eb38c434be02d3e95ff1fb83b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9b4b8d3eb38c434be02d3e95ff1fb83b">$importanceClass</a> = 'bg-blue-100 text-blue-800'</td></tr>
<tr class="separator:a9b4b8d3eb38c434be02d3e95ff1fb83b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a424696754ad463bb909e68f85c19b462" id="r_a424696754ad463bb909e68f85c19b462"><td class="memItemLeft" align="right" valign="top">if($item-&gt;importance=='critical') <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> if( $item-&gt;importance=='high') <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a424696754ad463bb909e68f85c19b462">if</a> ( $item-&gt;importance=='low')</td></tr>
<tr class="separator:a424696754ad463bb909e68f85c19b462"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3279e125a4b737c6dc2c979becc04b30" id="r_a3279e125a4b737c6dc2c979becc04b30"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3279e125a4b737c6dc2c979becc04b30">(count( $data[ 'maintenance_due_soon']) &gt; 0)</a> ( $data[ 'maintenance_due_soon'] as $item)</td></tr>
<tr class="separator:a3279e125a4b737c6dc2c979becc04b30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8b4bb1441c6ab4dcb28a37bc46c8ead" id="r_ae8b4bb1441c6ab4dcb28a37bc46c8ead"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae8b4bb1441c6ab4dcb28a37bc46c8ead">__pad1__</a></td></tr>
<tr class="separator:ae8b4bb1441c6ab4dcb28a37bc46c8ead"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d7d9cb40393c201098a44cb3d4afe32" id="r_a0d7d9cb40393c201098a44cb3d4afe32"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0d7d9cb40393c201098a44cb3d4afe32">(count( $data[ 'compliant_endpoints']) &gt; 0)</a> ( $data[ 'compliant_endpoints'] as $item)</td></tr>
<tr class="separator:a0d7d9cb40393c201098a44cb3d4afe32"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed2d37b4e8da3f52103ae96ce9d26d82" id="r_aed2d37b4e8da3f52103ae96ce9d26d82"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aed2d37b4e8da3f52103ae96ce9d26d82">__pad2__</a></td></tr>
<tr class="separator:aed2d37b4e8da3f52103ae96ce9d26d82"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="abddda9a408bfb52fd323be438cd3fe94" name="abddda9a408bfb52fd323be438cd3fe94"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abddda9a408bfb52fd323be438cd3fe94">&#9670;&#160;</a></span>$dueSoonPercent</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$dueSoonPercent = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9b4b8d3eb38c434be02d3e95ff1fb83b" name="a9b4b8d3eb38c434be02d3e95ff1fb83b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b4b8d3eb38c434be02d3e95ff1fb83b">&#9670;&#160;</a></span>$importanceClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$importanceClass = 'bg-blue-100 text-blue-800'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad60a350defbdae74bc48faa36a85c480" name="ad60a350defbdae74bc48faa36a85c480"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad60a350defbdae74bc48faa36a85c480">&#9670;&#160;</a></span>$notApplicablePercent</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$notApplicablePercent = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac563375536f300b2749efe560e300a49" name="ac563375536f300b2749efe560e300a49"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac563375536f300b2749efe560e300a49">&#9670;&#160;</a></span>$overduePercent</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$overduePercent = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aaf8006d608d2a4f1d18029ca0974e3b3" name="aaf8006d608d2a4f1d18029ca0974e3b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaf8006d608d2a4f1d18029ca0974e3b3">&#9670;&#160;</a></span>$totalCompliance</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$totalCompliance = $data['compliant_count'] + $data['due_soon_count'] + $data['overdue_count'] + $data['not_applicable_count']</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0d7d9cb40393c201098a44cb3d4afe32" name="a0d7d9cb40393c201098a44cb3d4afe32"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0d7d9cb40393c201098a44cb3d4afe32">&#9670;&#160;</a></span>(count( $data[ 'compliant_endpoints']) &gt; 0)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (count($data['compliant_endpoints']) &gt; 0)($data['compliant_endpoints'] as $item) </td>
          <td>(</td>
          <td class="paramtype">count( $data[ 'compliant_endpoints'])</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3279e125a4b737c6dc2c979becc04b30" name="a3279e125a4b737c6dc2c979becc04b30"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3279e125a4b737c6dc2c979becc04b30">&#9670;&#160;</a></span>(count( $data[ 'maintenance_due_soon']) &gt; 0)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (count($data['maintenance_due_soon']) &gt; 0)($data['maintenance_due_soon'] as $item) </td>
          <td>(</td>
          <td class="paramtype">count( $data[ 'maintenance_due_soon'])</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad65be5df1eedfe40f4fcfe99e3200095" name="ad65be5df1eedfe40f4fcfe99e3200095"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad65be5df1eedfe40f4fcfe99e3200095">&#9670;&#160;</a></span>(count( $data[ 'overdue_maintenance']) &gt; 0)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (count($data['overdue_maintenance']) &gt; 0)($data['overdue_maintenance'] as $item) </td>
          <td>(</td>
          <td class="paramtype">count( $data[ 'overdue_maintenance'])</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae8b4bb1441c6ab4dcb28a37bc46c8ead" name="ae8b4bb1441c6ab4dcb28a37bc46c8ead"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae8b4bb1441c6ab4dcb28a37bc46c8ead">&#9670;&#160;</a></span>__pad1__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad1__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aed2d37b4e8da3f52103ae96ce9d26d82" name="aed2d37b4e8da3f52103ae96ce9d26d82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aed2d37b4e8da3f52103ae96ce9d26d82">&#9670;&#160;</a></span>__pad2__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad2__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a42c52794fac97a1493ac79713f561aea" name="a42c52794fac97a1493ac79713f561aea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a42c52794fac97a1493ac79713f561aea">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $totalCompliance &gt; 0) else</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">                </div>
<div class="line">                $compliantPercent = 0</div>
</div><!-- fragment -->
</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a424696754ad463bb909e68f85c19b462" name="a424696754ad463bb909e68f85c19b462"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a424696754ad463bb909e68f85c19b462">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if( $item-&gt;importance=='critical') <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> if($item-&gt;importance=='high') <a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> if($item-&gt;importance=='low') </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$item-&gt;</em></span><span class="paramdefsep"> = </span><span class="paramdefval">=&#160;'low'</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
