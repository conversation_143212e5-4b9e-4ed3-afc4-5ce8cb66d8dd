<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: AssetVisibility Namespace Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="headertitle"><div class="title">AssetVisibility Namespace Reference</div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="class_assets.html">Assets</a> <a class="el" href="class_controller.html">Controller</a></p>
<p>Handles all asset-related operations including CRUD operations, search functionality, import/export, and bulk operations.</p>
<dl class="section author"><dt>Author</dt><dd><a class="el" href="class_asset.html">Asset</a> Visibility Development Team </dd></dl>
<dl class="section version"><dt>Version</dt><dd>1.0.0 </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0</dd></dl>
<p><a class="el" href="class_pages.html">Pages</a> <a class="el" href="class_controller.html">Controller</a></p>
<p>Handles static pages and general content pages for the application. This includes the home page, about page, and other informational pages.</p>
<dl class="section author"><dt>Author</dt><dd><a class="el" href="class_asset.html">Asset</a> Visibility Development Team </dd></dl>
<dl class="section version"><dt>Version</dt><dd>1.0.0 </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0</dd></dl>
<p><a class="el" href="class_users.html">Users</a> <a class="el" href="class_controller.html">Controller</a></p>
<p>Handles all user-related operations including authentication, registration, profile management, and user administration.</p>
<dl class="section author"><dt>Author</dt><dd><a class="el" href="class_asset.html">Asset</a> Visibility Development Team </dd></dl>
<dl class="section version"><dt>Version</dt><dd>1.0.0 </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0</dd></dl>
<p>Base <a class="el" href="class_controller.html">Controller</a> Class</p>
<p>This is the base controller class that all other controllers extend. It provides common functionality for loading models and views.</p>
<dl class="section author"><dt>Author</dt><dd><a class="el" href="class_asset.html">Asset</a> Visibility Development Team </dd></dl>
<dl class="section version"><dt>Version</dt><dd>1.0.0 </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0</dd></dl>
<p>PDO <a class="el" href="class_database.html">Database</a> Class</p>
<p>Provides a secure database abstraction layer using PDO with prepared statements. Includes features for pagination, parameter binding, and SQL injection protection.</p>
<dl class="section author"><dt>Author</dt><dd><a class="el" href="class_asset.html">Asset</a> Visibility Development Team </dd></dl>
<dl class="section version"><dt>Version</dt><dd>1.0.0 </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0</dd></dl>
<p><a class="el" href="class_security.html">Security</a> Helper Class</p>
<p>Provides comprehensive security functionality including CSRF protection, XSS prevention, input sanitization, and security headers management.</p>
<dl class="section author"><dt>Author</dt><dd><a class="el" href="class_asset.html">Asset</a> Visibility Development Team </dd></dl>
<dl class="section version"><dt>Version</dt><dd>1.0.0 </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0</dd></dl>
<p>Session Helper Functions</p>
<p>Provides session management, authentication, and permission checking functionality for the <a class="el" href="class_asset.html">Asset</a> Visibility application.</p>
<dl class="section author"><dt>Author</dt><dd><a class="el" href="class_asset.html">Asset</a> Visibility Development Team </dd></dl>
<dl class="section version"><dt>Version</dt><dd>1.0.0 </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0</dd></dl>
<p><a class="el" href="class_asset.html">Asset</a> Model</p>
<p>Handles all asset-related database operations including CRUD operations, search functionality, import/export, and asset history tracking.</p>
<dl class="section author"><dt>Author</dt><dd><a class="el" href="class_asset.html">Asset</a> Visibility Development Team </dd></dl>
<dl class="section version"><dt>Version</dt><dd>1.0.0 </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0</dd></dl>
<p><a class="el" href="class_role.html">Role</a> Model</p>
<p>Handles all role-related database operations including CRUD operations, permission management, and user-role associations.</p>
<dl class="section author"><dt>Author</dt><dd><a class="el" href="class_asset.html">Asset</a> Visibility Development Team </dd></dl>
<dl class="section version"><dt>Version</dt><dd>1.0.0 </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0</dd></dl>
<p><a class="el" href="class_user.html">User</a> Model</p>
<p>Handles all user-related database operations including authentication, registration, role management, and permission checking.</p>
<dl class="section author"><dt>Author</dt><dd><a class="el" href="class_asset.html">Asset</a> Visibility Development Team </dd></dl>
<dl class="section version"><dt>Version</dt><dd>1.0.0 </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
