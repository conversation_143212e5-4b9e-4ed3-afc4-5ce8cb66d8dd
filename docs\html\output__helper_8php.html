<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/helpers/output_helper.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_aa9f5e9ebaa2b53f41fac9466bd77901.html">helpers</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">output_helper.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a18d38faad6177eda235a3d9d28572984" id="r_a18d38faad6177eda235a3d9d28572984"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a18d38faad6177eda235a3d9d28572984">e</a> ($output)</td></tr>
<tr class="separator:a18d38faad6177eda235a3d9d28572984"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e7a9215f6f4f47e8dda9cba0ca31664" id="r_a8e7a9215f6f4f47e8dda9cba0ca31664"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e7a9215f6f4f47e8dda9cba0ca31664">safe_echo</a> ($output)</td></tr>
<tr class="separator:a8e7a9215f6f4f47e8dda9cba0ca31664"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ea7ef3a26ba61cde214d4a03d9ecc37" id="r_a1ea7ef3a26ba61cde214d4a03d9ecc37"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1ea7ef3a26ba61cde214d4a03d9ecc37">safe_echo_nl2br</a> ($output)</td></tr>
<tr class="separator:a1ea7ef3a26ba61cde214d4a03d9ecc37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c900ca36f5aa693d1463711751111b7" id="r_a6c900ca36f5aa693d1463711751111b7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6c900ca36f5aa693d1463711751111b7">safe_echo_raw</a> ($output)</td></tr>
<tr class="separator:a6c900ca36f5aa693d1463711751111b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a id="a18d38faad6177eda235a3d9d28572984" name="a18d38faad6177eda235a3d9d28572984"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a18d38faad6177eda235a3d9d28572984">&#9670;&#160;</a></span>e()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">e </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$output</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Output Helper Contains functions for escaping output to prevent XSS attacks Escape HTML output to prevent XSS attacks</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$output</td><td>The string to escape </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>string The escaped string </dd></dl>

</div>
</div>
<a id="a8e7a9215f6f4f47e8dda9cba0ca31664" name="a8e7a9215f6f4f47e8dda9cba0ca31664"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e7a9215f6f4f47e8dda9cba0ca31664">&#9670;&#160;</a></span>safe_echo()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">safe_echo </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$output</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Safely output HTML content</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$output</td><td>The string to output </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>void </dd></dl>

</div>
</div>
<a id="a1ea7ef3a26ba61cde214d4a03d9ecc37" name="a1ea7ef3a26ba61cde214d4a03d9ecc37"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1ea7ef3a26ba61cde214d4a03d9ecc37">&#9670;&#160;</a></span>safe_echo_nl2br()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">safe_echo_nl2br </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$output</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Safely output HTML content with line breaks</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$output</td><td>The string to output </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>void </dd></dl>

</div>
</div>
<a id="a6c900ca36f5aa693d1463711751111b7" name="a6c900ca36f5aa693d1463711751111b7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6c900ca36f5aa693d1463711751111b7">&#9670;&#160;</a></span>safe_echo_raw()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">safe_echo_raw </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$output</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Safely output HTML content with no escaping (for trusted HTML) Use with extreme caution - only for content that is guaranteed to be safe</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$output</td><td>The string to output </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>void </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
