<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/inc/pagination.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_1cec51c71b357fc4627c725c5725fe5b.html">inc</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">pagination.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ac7739cd67fc2b6f9404e19dffbd2432b" id="r_ac7739cd67fc2b6f9404e19dffbd2432b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac7739cd67fc2b6f9404e19dffbd2432b">paginationUrl</a> ($page, $queryParams)</td></tr>
<tr class="separator:ac7739cd67fc2b6f9404e19dffbd2432b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a6b131a0bac3dbe16421d586922963846" id="r_a6b131a0bac3dbe16421d586922963846"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(isset($data['pagination']) &amp;&amp; $data['pagination']['lastPage'] &gt; 1)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6b131a0bac3dbe16421d586922963846">$currentPage</a> = $pagination['page']</td></tr>
<tr class="separator:a6b131a0bac3dbe16421d586922963846"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8dea370eb48bd0145447252ffd04e4f" id="r_af8dea370eb48bd0145447252ffd04e4f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af8dea370eb48bd0145447252ffd04e4f">$lastPage</a> = $pagination['lastPage']</td></tr>
<tr class="separator:af8dea370eb48bd0145447252ffd04e4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f947559eb28e03070173d49dafc44a1" id="r_a9f947559eb28e03070173d49dafc44a1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9f947559eb28e03070173d49dafc44a1">$queryParams</a> = $_GET</td></tr>
<tr class="separator:a9f947559eb28e03070173d49dafc44a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7141618604c4edd13033c36a8c97bb60" id="r_a7141618604c4edd13033c36a8c97bb60"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7141618604c4edd13033c36a8c97bb60">$startPage</a> = max(1, $currentPage - 2)</td></tr>
<tr class="separator:a7141618604c4edd13033c36a8c97bb60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae12f8ad07b55a483cd345c1261d56a92" id="r_ae12f8ad07b55a483cd345c1261d56a92"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae12f8ad07b55a483cd345c1261d56a92">$endPage</a> = min($lastPage, $startPage + 4)</td></tr>
<tr class="separator:ae12f8ad07b55a483cd345c1261d56a92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aea4423f2a1453df3beda2db93281783e" id="r_aea4423f2a1453df3beda2db93281783e"><td class="memItemLeft" align="right" valign="top">if($endPage - $startPage&lt; 4 &amp;&amp; $startPage &gt; 1) if( $currentPage &gt; 1&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aea4423f2a1453df3beda2db93281783e">if</a> )( $currentPage&lt; $lastPage)</td></tr>
<tr class="separator:aea4423f2a1453df3beda2db93281783e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76322bf3984bf5542a977dfd27d2a2f9" id="r_a76322bf3984bf5542a977dfd27d2a2f9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $currentPage &gt; 1) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($startPage &gt; 1)($startPage &gt; 2)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a76322bf3984bf5542a977dfd27d2a2f9">endif</a></td></tr>
<tr class="separator:a76322bf3984bf5542a977dfd27d2a2f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab654bd74b463b61f008b1077ce27b3c" id="r_aab654bd74b463b61f008b1077ce27b3c"><td class="memItemLeft" align="right" valign="top">for($i=$startPage; $i&lt;=$endPage; $i++)($i==$currentPage)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aab654bd74b463b61f008b1077ce27b3c">else</a></td></tr>
<tr class="separator:aab654bd74b463b61f008b1077ce27b3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8fdc27183f296411bac00ed522ee1ac" id="r_ae8fdc27183f296411bac00ed522ee1ac"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae8fdc27183f296411bac00ed522ee1ac">endfor</a></td></tr>
<tr class="separator:ae8fdc27183f296411bac00ed522ee1ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83bf805250f2ee633dd2ce7c36af3412" id="r_a83bf805250f2ee633dd2ce7c36af3412"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a83bf805250f2ee633dd2ce7c36af3412">( $endPage&lt; $lastPage)</a> ( $endPage&lt; $lastPage - 1)</td></tr>
<tr class="separator:a83bf805250f2ee633dd2ce7c36af3412"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a id="ac7739cd67fc2b6f9404e19dffbd2432b" name="ac7739cd67fc2b6f9404e19dffbd2432b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac7739cd67fc2b6f9404e19dffbd2432b">&#9670;&#160;</a></span>paginationUrl()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">paginationUrl </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$page</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$queryParams</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a6b131a0bac3dbe16421d586922963846" name="a6b131a0bac3dbe16421d586922963846"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6b131a0bac3dbe16421d586922963846">&#9670;&#160;</a></span>$currentPage</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (isset( $data[ 'pagination']) &amp;&amp;$data[ 'pagination'][ 'lastPage'] &gt; 1) $currentPage = $pagination['page']</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Pagination Component</p>
<p>Displays pagination controls for tables</p>
<p>Required parameters in $data:</p><ul>
<li>pagination: array containing:<ul>
<li>total: total number of items</li>
<li>page: current page number</li>
<li>perPage: number of items per page</li>
<li>lastPage: last page number </li>
</ul>
</li>
</ul>

</div>
</div>
<a id="ae12f8ad07b55a483cd345c1261d56a92" name="ae12f8ad07b55a483cd345c1261d56a92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae12f8ad07b55a483cd345c1261d56a92">&#9670;&#160;</a></span>$endPage</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$endPage = min($lastPage, $startPage + 4)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af8dea370eb48bd0145447252ffd04e4f" name="af8dea370eb48bd0145447252ffd04e4f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af8dea370eb48bd0145447252ffd04e4f">&#9670;&#160;</a></span>$lastPage</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$lastPage = $pagination['lastPage']</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9f947559eb28e03070173d49dafc44a1" name="a9f947559eb28e03070173d49dafc44a1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f947559eb28e03070173d49dafc44a1">&#9670;&#160;</a></span>$queryParams</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">for ( $i=$startPage;$i&lt;=$endPage;$i++) ( $i==$currentPage $queryParams = $_GET</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7141618604c4edd13033c36a8c97bb60" name="a7141618604c4edd13033c36a8c97bb60"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7141618604c4edd13033c36a8c97bb60">&#9670;&#160;</a></span>$startPage</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$startPage = max(1, $currentPage - 2)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a83bf805250f2ee633dd2ce7c36af3412" name="a83bf805250f2ee633dd2ce7c36af3412"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a83bf805250f2ee633dd2ce7c36af3412">&#9670;&#160;</a></span>( $endPage&lt; $lastPage)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ($endPage&lt; $lastPage)($endPage&lt; $lastPage - 1) </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aab654bd74b463b61f008b1077ce27b3c" name="aab654bd74b463b61f008b1077ce27b3c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aab654bd74b463b61f008b1077ce27b3c">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">for ( $i=$startPage;$i&lt;=$endPage;$i++) ( $i==$currentPage) else</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae8fdc27183f296411bac00ed522ee1ac" name="ae8fdc27183f296411bac00ed522ee1ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae8fdc27183f296411bac00ed522ee1ac">&#9670;&#160;</a></span>endfor</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endfor</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a76322bf3984bf5542a977dfd27d2a2f9" name="a76322bf3984bf5542a977dfd27d2a2f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a76322bf3984bf5542a977dfd27d2a2f9">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $currentPage&lt; $lastPage) endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aea4423f2a1453df3beda2db93281783e" name="aea4423f2a1453df3beda2db93281783e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aea4423f2a1453df3beda2db93281783e">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if( $endPage - $startPage&lt; 4 &amp;&amp;$startPage &gt; 1) if($currentPage &gt; 1 if) ($currentPage&lt; $lastPage) </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
