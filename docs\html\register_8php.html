<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/users/register.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_f74f3790f48dd7404afdfc13a8a7407b.html">users</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">register.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a64410dead16b2816b2f31c3df96935c3" id="r_a64410dead16b2816b2f31c3df96935c3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty( $data[ 'name_err'])) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($data['name_err']))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a64410dead16b2816b2f31c3df96935c3">endif</a></td></tr>
<tr class="separator:a64410dead16b2816b2f31c3df96935c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e77afec1fc0f87305414d77bdc8cbad" id="r_a7e77afec1fc0f87305414d77bdc8cbad"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty( $data[ 'password_err'])) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($data['password_err']))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7e77afec1fc0f87305414d77bdc8cbad">else</a></td></tr>
<tr class="separator:a7e77afec1fc0f87305414d77bdc8cbad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5227b73106de5719f8e6830383ace299" id="r_a5227b73106de5719f8e6830383ace299"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty( $data[ 'password_err'])) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($data['password_err'])&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5227b73106de5719f8e6830383ace299">lowercase</a></td></tr>
<tr class="separator:a5227b73106de5719f8e6830383ace299"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa0ecf0eda943d7316f4a313104e2d008" id="r_aa0ecf0eda943d7316f4a313104e2d008"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty( $data[ 'password_err'])) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($data['password_err']&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa0ecf0eda943d7316f4a313104e2d008">number</a></td></tr>
<tr class="separator:aa0ecf0eda943d7316f4a313104e2d008"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a7e77afec1fc0f87305414d77bdc8cbad" name="a7e77afec1fc0f87305414d77bdc8cbad"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7e77afec1fc0f87305414d77bdc8cbad">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($data['password_err'])) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (!empty( $data[ 'password_err'])) else</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a64410dead16b2816b2f31c3df96935c3" name="a64410dead16b2816b2f31c3df96935c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a64410dead16b2816b2f31c3df96935c3">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($data['confirm_password_err'])) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (!empty( $data[ 'confirm_password_err'])) endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a5227b73106de5719f8e6830383ace299" name="a5227b73106de5719f8e6830383ace299"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5227b73106de5719f8e6830383ace299">&#9670;&#160;</a></span>lowercase</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($data['password_err'])) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (!empty( $data[ 'password_err']) lowercase</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa0ecf0eda943d7316f4a313104e2d008" name="aa0ecf0eda943d7316f4a313104e2d008"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa0ecf0eda943d7316f4a313104e2d008">&#9670;&#160;</a></span>number</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($data['password_err'])) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (!empty( $data[ 'password_err'] number</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
