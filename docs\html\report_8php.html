<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/compliance/report.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_f53799ec3239dd83ce111fd4501b9606.html">compliance</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">report.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:ad25bb0d600debde6675a0e92c0333146" id="r_ad25bb0d600debde6675a0e92c0333146"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad25bb0d600debde6675a0e92c0333146">$overallCompliance</a> = $data['report']-&gt;report_data-&gt;overall_compliance</td></tr>
<tr class="separator:ad25bb0d600debde6675a0e92c0333146"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a284a2c36de7e8c8d980bc3f704ee0523" id="r_a284a2c36de7e8c8d980bc3f704ee0523"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $overallCompliance &gt;=90) <a class="el" href="app_2views_2compliance_2index_8php.html#a2fe92cbc651f6f645279ed84f54274dd">elseif</a>($overallCompliance &gt;=70)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a284a2c36de7e8c8d980bc3f704ee0523">else</a></td></tr>
<tr class="separator:a284a2c36de7e8c8d980bc3f704ee0523"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a52b109dcfbeb9d1d9daaacdd457d3021" id="r_a52b109dcfbeb9d1d9daaacdd457d3021"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ( $data[ 'report']-&gt;report_data-&gt;asset_summary as $asset) = $forecast['projected_amount']</td></tr>
<tr class="separator:a52b109dcfbeb9d1d9daaacdd457d3021"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73dbf60765f3b9036cf082caad668726" id="r_a73dbf60765f3b9036cf082caad668726"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a73dbf60765f3b9036cf082caad668726">$totalControls</a> = $asset-&gt;total_controls</td></tr>
<tr class="separator:a73dbf60765f3b9036cf082caad668726"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada4b4b6268421c9e025f977cae774b92" id="r_ada4b4b6268421c9e025f977cae774b92"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ada4b4b6268421c9e025f977cae774b92">$applicableControls</a> = $totalControls - $asset-&gt;not_applicable_count</td></tr>
<tr class="separator:ada4b4b6268421c9e025f977cae774b92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d4ce20a31e47694d0105327b8502fe6" id="r_a2d4ce20a31e47694d0105327b8502fe6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2d4ce20a31e47694d0105327b8502fe6">$compliancePercentage</a> = $applicableControls &gt; 0 ? ($asset-&gt;compliant_count / $applicableControls) * 100 : 0</td></tr>
<tr class="separator:a2d4ce20a31e47694d0105327b8502fe6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab99343d4912650d1363355564aa6c179" id="r_ab99343d4912650d1363355564aa6c179"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab99343d4912650d1363355564aa6c179">$textColor</a> = 'text-red-500'</td></tr>
<tr class="separator:ab99343d4912650d1363355564aa6c179"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef76637aa1522ed86c0454bd6c1a50fd" id="r_aef76637aa1522ed86c0454bd6c1a50fd"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>( $compliancePercentage &gt;=90)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aef76637aa1522ed86c0454bd6c1a50fd">elseif</a> ( $compliancePercentage &gt;=70)</td></tr>
<tr class="separator:aef76637aa1522ed86c0454bd6c1a50fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a426e5ef5de550913c16700467762c1e9" id="r_a426e5ef5de550913c16700467762c1e9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a426e5ef5de550913c16700467762c1e9">foreach</a> ( $data[ 'report']-&gt;report_data-&gt;control_summary as $control)</td></tr>
<tr class="separator:a426e5ef5de550913c16700467762c1e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b9d42daa53a6dc9fb6156d46d3f63e6" id="r_a3b9d42daa53a6dc9fb6156d46d3f63e6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3b9d42daa53a6dc9fb6156d46d3f63e6">$totalAssets</a> = $control-&gt;total_assets</td></tr>
<tr class="separator:a3b9d42daa53a6dc9fb6156d46d3f63e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3707071a7a6abab5047d40ecf2edab61" id="r_a3707071a7a6abab5047d40ecf2edab61"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3707071a7a6abab5047d40ecf2edab61">$applicableAssets</a> = $totalAssets - $control-&gt;not_applicable_count</td></tr>
<tr class="separator:a3707071a7a6abab5047d40ecf2edab61"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a3707071a7a6abab5047d40ecf2edab61" name="a3707071a7a6abab5047d40ecf2edab61"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3707071a7a6abab5047d40ecf2edab61">&#9670;&#160;</a></span>$applicableAssets</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$applicableAssets = $totalAssets - $control-&gt;not_applicable_count</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ada4b4b6268421c9e025f977cae774b92" name="ada4b4b6268421c9e025f977cae774b92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada4b4b6268421c9e025f977cae774b92">&#9670;&#160;</a></span>$applicableControls</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$applicableControls = $totalControls - $asset-&gt;not_applicable_count</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2d4ce20a31e47694d0105327b8502fe6" name="a2d4ce20a31e47694d0105327b8502fe6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d4ce20a31e47694d0105327b8502fe6">&#9670;&#160;</a></span>$compliancePercentage</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$compliancePercentage = $applicableControls &gt; 0 ? ($asset-&gt;compliant_count / $applicableControls) * 100 : 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad25bb0d600debde6675a0e92c0333146" name="ad25bb0d600debde6675a0e92c0333146"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad25bb0d600debde6675a0e92c0333146">&#9670;&#160;</a></span>$overallCompliance</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$overallCompliance = $data['report']-&gt;report_data-&gt;overall_compliance</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab99343d4912650d1363355564aa6c179" name="ab99343d4912650d1363355564aa6c179"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab99343d4912650d1363355564aa6c179">&#9670;&#160;</a></span>$textColor</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$textColor = 'text-red-500'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3b9d42daa53a6dc9fb6156d46d3f63e6" name="a3b9d42daa53a6dc9fb6156d46d3f63e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3b9d42daa53a6dc9fb6156d46d3f63e6">&#9670;&#160;</a></span>$totalAssets</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$totalAssets = $control-&gt;total_assets</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a73dbf60765f3b9036cf082caad668726" name="a73dbf60765f3b9036cf082caad668726"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a73dbf60765f3b9036cf082caad668726">&#9670;&#160;</a></span>$totalControls</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$totalControls = $asset-&gt;total_controls</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a284a2c36de7e8c8d980bc3f704ee0523" name="a284a2c36de7e8c8d980bc3f704ee0523"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a284a2c36de7e8c8d980bc3f704ee0523">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($overallCompliance &gt;=90) <a class="el" href="app_2views_2compliance_2index_8php.html#a2fe92cbc651f6f645279ed84f54274dd">elseif</a> ( $overallCompliance &gt;=70) else</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">                                echo <span class="stringliteral">&#39;text-red-600&#39;</span></div>
</div><!-- fragment -->
</div>
</div>
<a id="aef76637aa1522ed86c0454bd6c1a50fd" name="aef76637aa1522ed86c0454bd6c1a50fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aef76637aa1522ed86c0454bd6c1a50fd">&#9670;&#160;</a></span>elseif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($compliancePercentage &gt;=90) elseif($compliancePercentage &gt;=70) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$compliancePercentage &gt;=</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a52b109dcfbeb9d1d9daaacdd457d3021" name="a52b109dcfbeb9d1d9daaacdd457d3021"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a52b109dcfbeb9d1d9daaacdd457d3021">&#9670;&#160;</a></span>foreach <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">foreach </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data-&gt;report_data-&gt;asset_summary as</em></span>['report']</td><td>)</td>
          <td> = $forecast['projected_amount']</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a426e5ef5de550913c16700467762c1e9" name="a426e5ef5de550913c16700467762c1e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a426e5ef5de550913c16700467762c1e9">&#9670;&#160;</a></span>foreach <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">foreach($data['report']-&gt;report_data-&gt;control_summary as $control) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data-&gt;report_data-&gt;control_summary as</em></span>[ 'report']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
