<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/assets/search.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_4bf277b741c35ce534efde8f7dcf6e83.html">assets</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">search.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ad02b5389c04582e80c7de1ab48955b77" id="r_ad02b5389c04582e80c7de1ab48955b77"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(count( $data[ 'assets']) &gt; 0)( $field, $currentSort, $currentOrder)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad02b5389c04582e80c7de1ab48955b77">getSortIndicator</a> ($field, $currentSort, $currentOrder)</td></tr>
<tr class="separator:ad02b5389c04582e80c7de1ab48955b77"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:af8c725106f89f888eb62471868f675f6" id="r_af8c725106f89f888eb62471868f675f6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!isset($data['is_advanced'])||! $data['is_advanced'])(empty($data['term']))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af8c725106f89f888eb62471868f675f6">else</a></td></tr>
<tr class="separator:af8c725106f89f888eb62471868f675f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefc614adca80226b3fa8c61efe9e812b" id="r_aefc614adca80226b3fa8c61efe9e812b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aefc614adca80226b3fa8c61efe9e812b">( $data[ 'filter_options'][ 'equipment_types'] as $type)</a> (isset( $data[ 'params'][ 'equipment_type']) &amp;&amp;$data[ 'params'][ 'equipment_type']==$type-&gt;equipment_type) ? 'selected'</td></tr>
<tr class="separator:aefc614adca80226b3fa8c61efe9e812b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afbb5860222327979c6ac93aef21c0591" id="r_afbb5860222327979c6ac93aef21c0591"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afbb5860222327979c6ac93aef21c0591">( $data[ 'filter_options'][ 'site_names'] as $site)</a> (isset( $data[ 'params'][ 'site_name']) &amp;&amp;$data[ 'params'][ 'site_name']==$site-&gt;site_name) ? 'selected'</td></tr>
<tr class="separator:afbb5860222327979c6ac93aef21c0591"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a769cb414d72ae3111d1c1d0a7ca3639a" id="r_a769cb414d72ae3111d1c1d0a7ca3639a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a769cb414d72ae3111d1c1d0a7ca3639a">( $data[ 'filter_options'][ 'operating_systems'] as $os)</a> (isset( $data[ 'params'][ 'operating_system']) &amp;&amp;$data[ 'params'][ 'operating_system']==$os-&gt;operating_system) ? 'selected'</td></tr>
<tr class="separator:a769cb414d72ae3111d1c1d0a7ca3639a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a171d039a7479dd10161072b0a682c87f" id="r_a171d039a7479dd10161072b0a682c87f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a171d039a7479dd10161072b0a682c87f">( $data[ 'filter_options'][ 'program_sections'] as $section)</a> (isset( $data[ 'params'][ 'program_section']) &amp;&amp;$data[ 'params'][ 'program_section']==$section-&gt;program_section) ? 'selected'</td></tr>
<tr class="separator:a171d039a7479dd10161072b0a682c87f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad85aac63a81d7c2ac50aca0943a7d320" id="r_ad85aac63a81d7c2ac50aca0943a7d320"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad85aac63a81d7c2ac50aca0943a7d320">( $data[ 'filter_options'][ 'administration_types'] as $type)</a> (isset( $data[ 'params'][ 'administration_type']) &amp;&amp;$data[ 'params'][ 'administration_type']==$type-&gt;administration_type) ? 'selected'</td></tr>
<tr class="separator:ad85aac63a81d7c2ac50aca0943a7d320"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affe50d4c06fe2b1388f190dbbc30c6ee" id="r_affe50d4c06fe2b1388f190dbbc30c6ee"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#affe50d4c06fe2b1388f190dbbc30c6ee">( $data[ 'filter_options'][ 'xdr_options'] as $option)</a> (isset( $data[ 'params'][ 'xdr_installed']) &amp;&amp;$data[ 'params'][ 'xdr_installed']==$option-&gt;xdr_installed) ? 'selected'</td></tr>
<tr class="separator:affe50d4c06fe2b1388f190dbbc30c6ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9d32cc8a805b1b28a351dfe876bfb668" id="r_a9d32cc8a805b1b28a351dfe876bfb668"><td class="memItemLeft" align="right" valign="top"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9d32cc8a805b1b28a351dfe876bfb668">( $data[ 'tags'] as $tag)</a> (isset( $data[ 'params'][ 'tag_id']) &amp;&amp;$data[ 'params'][ 'tag_id']==$tag-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>) ? 'selected'</td></tr>
<tr class="separator:a9d32cc8a805b1b28a351dfe876bfb668"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a704178ddfa9f900a42449584b09b856f" id="r_a704178ddfa9f900a42449584b09b856f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a704178ddfa9f900a42449584b09b856f">if</a> (isset( $data[ 'is_advanced']) &amp;&amp;$data[ 'is_advanced'] &amp;&amp;!empty( $data[ 'params']))</td></tr>
<tr class="separator:a704178ddfa9f900a42449584b09b856f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82cd33ca97ff99f2fcc5e9c81d65251b" id="r_a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82cd33ca97ff99f2fcc5e9c81d65251b">endif</a></td></tr>
<tr class="separator:a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a663ff282593675a5d351416419b26cbb" id="r_a663ff282593675a5d351416419b26cbb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>('export_assets'))(isset($data['is_advanced']) &amp;&amp; $data['is_advanced'] &amp;&amp;!empty($data['params'])) we need to build the export URL with all parameters&lt; a href=&quot;&lt;?php echo <a class="el" href="config_8php.html#a598166266a84ff3ecf84ef6f206ceefe">URLROOT</a>; ?&gt; assets export&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a663ff282593675a5d351416419b26cbb">advanced</a></td></tr>
<tr class="separator:a663ff282593675a5d351416419b26cbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20c4a031eb504739e03f6014cdfb0055" id="r_a20c4a031eb504739e03f6014cdfb0055"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a20c4a031eb504739e03f6014cdfb0055">$tagModel</a> = new <a class="el" href="class_tag.html">Tag</a>()</td></tr>
<tr class="separator:a20c4a031eb504739e03f6014cdfb0055"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74cea71d3e2cbaff24dfb33210faa0f7" id="r_a74cea71d3e2cbaff24dfb33210faa0f7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a74cea71d3e2cbaff24dfb33210faa0f7">$assetId</a> = isset($asset-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>) ? $asset-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a> : $asset['<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>']</td></tr>
<tr class="separator:a74cea71d3e2cbaff24dfb33210faa0f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf61640e87147db95185d5d01f3b66d1" id="r_abf61640e87147db95185d5d01f3b66d1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abf61640e87147db95185d5d01f3b66d1">$assetTags</a> = $tagModel-&gt;getTagsForAsset($assetId)</td></tr>
<tr class="separator:abf61640e87147db95185d5d01f3b66d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8b4bb1441c6ab4dcb28a37bc46c8ead" id="r_ae8b4bb1441c6ab4dcb28a37bc46c8ead"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae8b4bb1441c6ab4dcb28a37bc46c8ead">__pad1__</a></td></tr>
<tr class="separator:ae8b4bb1441c6ab4dcb28a37bc46c8ead"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a id="ad02b5389c04582e80c7de1ab48955b77" name="ad02b5389c04582e80c7de1ab48955b77"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad02b5389c04582e80c7de1ab48955b77">&#9670;&#160;</a></span>getSortIndicator()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(count($data['assets']) &gt; 0)($field, $currentSort, $currentOrder) getSortIndicator </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$field</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$currentSort</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$currentOrder</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a74cea71d3e2cbaff24dfb33210faa0f7" name="a74cea71d3e2cbaff24dfb33210faa0f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a74cea71d3e2cbaff24dfb33210faa0f7">&#9670;&#160;</a></span>$assetId</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$assetId = isset($asset-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>) ? $asset-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a> : $asset['<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>']</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abf61640e87147db95185d5d01f3b66d1" name="abf61640e87147db95185d5d01f3b66d1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf61640e87147db95185d5d01f3b66d1">&#9670;&#160;</a></span>$assetTags</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$assetTags = $tagModel-&gt;getTagsForAsset($assetId)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a20c4a031eb504739e03f6014cdfb0055" name="a20c4a031eb504739e03f6014cdfb0055"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a20c4a031eb504739e03f6014cdfb0055">&#9670;&#160;</a></span>$tagModel</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$tagModel = new <a class="el" href="class_tag.html">Tag</a>()</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad85aac63a81d7c2ac50aca0943a7d320" name="ad85aac63a81d7c2ac50aca0943a7d320"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad85aac63a81d7c2ac50aca0943a7d320">&#9670;&#160;</a></span>( $data[ 'filter_options'][ 'administration_types'] as $type)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ($data['filter_options']['administration_types'] as $type)(isset($data['params']['administration_type']) &amp;&amp; $data['params']['administration_type']==$type-&gt;administration_type) ? 'selected' </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'filter_options'][ 'administration_types']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aefc614adca80226b3fa8c61efe9e812b" name="aefc614adca80226b3fa8c61efe9e812b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefc614adca80226b3fa8c61efe9e812b">&#9670;&#160;</a></span>( $data[ 'filter_options'][ 'equipment_types'] as $type)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ($data['filter_options']['equipment_types'] as $type)(isset($data['params']['equipment_type']) &amp;&amp; $data['params']['equipment_type']==$type-&gt;equipment_type) ? 'selected' </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'filter_options'][ 'equipment_types']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a769cb414d72ae3111d1c1d0a7ca3639a" name="a769cb414d72ae3111d1c1d0a7ca3639a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a769cb414d72ae3111d1c1d0a7ca3639a">&#9670;&#160;</a></span>( $data[ 'filter_options'][ 'operating_systems'] as $os)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ($data['filter_options']['operating_systems'] as $os)(isset($data['params']['operating_system']) &amp;&amp; $data['params']['operating_system']==$os-&gt;operating_system) ? 'selected' </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'filter_options'][ 'operating_systems']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a171d039a7479dd10161072b0a682c87f" name="a171d039a7479dd10161072b0a682c87f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a171d039a7479dd10161072b0a682c87f">&#9670;&#160;</a></span>( $data[ 'filter_options'][ 'program_sections'] as $section)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ($data['filter_options']['program_sections'] as $section)(isset($data['params']['program_section']) &amp;&amp; $data['params']['program_section']==$section-&gt;program_section) ? 'selected' </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'filter_options'][ 'program_sections']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="afbb5860222327979c6ac93aef21c0591" name="afbb5860222327979c6ac93aef21c0591"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afbb5860222327979c6ac93aef21c0591">&#9670;&#160;</a></span>( $data[ 'filter_options'][ 'site_names'] as $site)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ($data['filter_options']['site_names'] as $site)(isset($data['params']['site_name']) &amp;&amp; $data['params']['site_name']==$site-&gt;site_name) ? 'selected' </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'filter_options'][ 'site_names']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="affe50d4c06fe2b1388f190dbbc30c6ee" name="affe50d4c06fe2b1388f190dbbc30c6ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#affe50d4c06fe2b1388f190dbbc30c6ee">&#9670;&#160;</a></span>( $data[ 'filter_options'][ 'xdr_options'] as $option)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ($data['filter_options']['xdr_options'] as $option)(isset($data['params']['xdr_installed']) &amp;&amp; $data['params']['xdr_installed']==$option-&gt;xdr_installed) ? 'selected' </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'filter_options'][ 'xdr_options']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9d32cc8a805b1b28a351dfe876bfb668" name="a9d32cc8a805b1b28a351dfe876bfb668"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9d32cc8a805b1b28a351dfe876bfb668">&#9670;&#160;</a></span>( $data[ 'tags'] as $tag)</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="report_8php.html#a52b109dcfbeb9d1d9daaacdd457d3021">foreach</a> ($data['tags'] as $tag)(isset($data['params']['tag_id']) &amp;&amp; $data['params']['tag_id']==$tag-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a>) ? 'selected' </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>[ 'tags']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae8b4bb1441c6ab4dcb28a37bc46c8ead" name="ae8b4bb1441c6ab4dcb28a37bc46c8ead"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae8b4bb1441c6ab4dcb28a37bc46c8ead">&#9670;&#160;</a></span>__pad1__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad1__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a663ff282593675a5d351416419b26cbb" name="a663ff282593675a5d351416419b26cbb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a663ff282593675a5d351416419b26cbb">&#9670;&#160;</a></span>advanced</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (<a class="el" href="session__helper_8php.html#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a>( 'export_assets')) (isset( $data[ 'is_advanced']) &amp;&amp;$data[ 'is_advanced'] &amp;&amp;!empty( $data[ 'params'])) we need to build the export URL with all parameters&lt;a href=&quot;&lt;?php echo <a class="el" href="config_8php.html#a598166266a84ff3ecf84ef6f206ceefe">URLROOT</a>; ?&gt; assets export advanced</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af8c725106f89f888eb62471868f675f6" name="af8c725106f89f888eb62471868f675f6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af8c725106f89f888eb62471868f675f6">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (!isset( $data[ 'is_advanced'])||! $data[ 'is_advanced']) (empty( $data[ 'term'])) else</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a82cd33ca97ff99f2fcc5e9c81d65251b" name="a82cd33ca97ff99f2fcc5e9c81d65251b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82cd33ca97ff99f2fcc5e9c81d65251b">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a704178ddfa9f900a42449584b09b856f" name="a704178ddfa9f900a42449584b09b856f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a704178ddfa9f900a42449584b09b856f">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if($key=='tag_id') </td>
          <td>(</td>
          <td class="paramtype">isset( $data[ 'is_advanced']) &amp;&amp;$data &amp;&amp;!empty( $data[ 'params'])</td>          <td class="paramname"><span class="paramname"><em></em></span>[ 'is_advanced']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=&gt; <span class="stringliteral">&#39;Host Name&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;employee_name&#39;</span> =&gt; <span class="stringliteral">&#39;Employee Name&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;equipment_type&#39;</span> =&gt; <span class="stringliteral">&#39;Equipment Type&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;site_name&#39;</span> =&gt; <span class="stringliteral">&#39;Site Name&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;operating_system&#39;</span> =&gt; <span class="stringliteral">&#39;Operating System&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;program_section&#39;</span> =&gt; <span class="stringliteral">&#39;Program Section&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;administration_type&#39;</span> =&gt; <span class="stringliteral">&#39;Administration Type&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;xdr_installed&#39;</span> =&gt; <span class="stringliteral">&#39;XDR Installed&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;acquisition_date_from&#39;</span> =&gt; <span class="stringliteral">&#39;Acquisition Date From&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;acquisition_date_to&#39;</span> =&gt; <span class="stringliteral">&#39;Acquisition Date To&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;inventory_date_from&#39;</span> =&gt; <span class="stringliteral">&#39;Inventory Date From&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;inventory_date_to&#39;</span> =&gt; <span class="stringliteral">&#39;Inventory Date To&#39;</span>,</div>
<div class="line">                <span class="stringliteral">&#39;tag_id&#39;</span> =&gt; <span class="stringliteral">&#39;Tag&#39;</span></div>
<div class="line">            ]</div>
</div><!-- fragment -->
</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
