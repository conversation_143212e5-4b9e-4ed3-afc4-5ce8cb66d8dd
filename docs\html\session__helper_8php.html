<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/helpers/session_helper.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_aa9f5e9ebaa2b53f41fac9466bd77901.html">helpers</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#namespaces">Namespaces</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">session_helper.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="namespaces" name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">namespace &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespace_asset_visibility.html">AssetVisibility</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a944d856551c6f577255c4c853fc9ee49" id="r_a944d856551c6f577255c4c853fc9ee49"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a944d856551c6f577255c4c853fc9ee49">flash</a> ($name='', $message='', $class='bg-green-100 text-green-800')</td></tr>
<tr class="separator:a944d856551c6f577255c4c853fc9ee49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a33bdd79e5da367ebddd4cfbdbbfc7cff" id="r_a33bdd79e5da367ebddd4cfbdbbfc7cff"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a33bdd79e5da367ebddd4cfbdbbfc7cff">isLoggedIn</a> ()</td></tr>
<tr class="separator:a33bdd79e5da367ebddd4cfbdbbfc7cff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aabf23b66cd362adaa508de5bfb22706a" id="r_aabf23b66cd362adaa508de5bfb22706a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aabf23b66cd362adaa508de5bfb22706a">isAdmin</a> ()</td></tr>
<tr class="separator:aabf23b66cd362adaa508de5bfb22706a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4da2a6a1e77331cc90a7d38bba8c442f" id="r_a4da2a6a1e77331cc90a7d38bba8c442f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4da2a6a1e77331cc90a7d38bba8c442f">hasPermission</a> ($permission)</td></tr>
<tr class="separator:a4da2a6a1e77331cc90a7d38bba8c442f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a53a5bca218d3879ec04ebc1cb2c2bb56" id="r_a53a5bca218d3879ec04ebc1cb2c2bb56"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a53a5bca218d3879ec04ebc1cb2c2bb56">hasRole</a> ($role)</td></tr>
<tr class="separator:a53a5bca218d3879ec04ebc1cb2c2bb56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abde6b371dbe38c869ffdd4684d046a4a" id="r_abde6b371dbe38c869ffdd4684d046a4a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abde6b371dbe38c869ffdd4684d046a4a">clearPermissionCache</a> ()</td></tr>
<tr class="separator:abde6b371dbe38c869ffdd4684d046a4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a346f134a5d2ca39e76810b757cf6255d" id="r_a346f134a5d2ca39e76810b757cf6255d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a346f134a5d2ca39e76810b757cf6255d">clearGlobalPermissionCache</a> ()</td></tr>
<tr class="separator:a346f134a5d2ca39e76810b757cf6255d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7e78bf5572fe3a109a59d4a63e0fa34" id="r_aa7e78bf5572fe3a109a59d4a63e0fa34"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa7e78bf5572fe3a109a59d4a63e0fa34">redirect</a> ($page)</td></tr>
<tr class="separator:aa7e78bf5572fe3a109a59d4a63e0fa34"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a id="a346f134a5d2ca39e76810b757cf6255d" name="a346f134a5d2ca39e76810b757cf6255d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a346f134a5d2ca39e76810b757cf6255d">&#9670;&#160;</a></span>clearGlobalPermissionCache()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">clearGlobalPermissionCache </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Clear permission cache for all users by updating a global timestamp</p>
<p>Forces all users to refresh their permissions on next check by updating a global cache timestamp file.</p>
<dl class="section return"><dt>Returns</dt><dd>void </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="abde6b371dbe38c869ffdd4684d046a4a" name="abde6b371dbe38c869ffdd4684d046a4a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abde6b371dbe38c869ffdd4684d046a4a">&#9670;&#160;</a></span>clearPermissionCache()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">clearPermissionCache </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Clear the permission and role cache</p>
<p>Clears the current user's cached permissions and roles. Call this when user permissions or roles are updated.</p>
<dl class="section return"><dt>Returns</dt><dd>void </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a944d856551c6f577255c4c853fc9ee49" name="a944d856551c6f577255c4c853fc9ee49"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a944d856551c6f577255c4c853fc9ee49">&#9670;&#160;</a></span>flash()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">flash </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$name</em></span><span class="paramdefsep"> = </span><span class="paramdefval">''</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$message</em></span><span class="paramdefsep"> = </span><span class="paramdefval">''</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$class</em></span><span class="paramdefsep"> = </span><span class="paramdefval">'bg-green-100&#160;text-green-800'</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Flash message helper</p>
<p>Manages flash messages using SweetAlert2 for user notifications. Supports setting and displaying messages with different alert types.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$name</td><td>The name/key for the flash message </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$message</td><td>The message content (empty to display existing message) </td></tr>
    <tr><td class="paramtype">string</td><td class="paramname">$class</td><td>CSS class for styling (determines alert type) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>void </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a4da2a6a1e77331cc90a7d38bba8c442f" name="a4da2a6a1e77331cc90a7d38bba8c442f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4da2a6a1e77331cc90a7d38bba8c442f">&#9670;&#160;</a></span>hasPermission()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">hasPermission </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$permission</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Check if the current user has a specific permission</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$permission</td><td><a class="el" href="class_permission.html">Permission</a> name to check </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if the user has the permission, false otherwise </dd></dl>

</div>
</div>
<a id="a53a5bca218d3879ec04ebc1cb2c2bb56" name="a53a5bca218d3879ec04ebc1cb2c2bb56"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a53a5bca218d3879ec04ebc1cb2c2bb56">&#9670;&#160;</a></span>hasRole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">hasRole </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$role</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Check if the current user has a specific role</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$role</td><td><a class="el" href="class_role.html">Role</a> name to check </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool True if the user has the role, false otherwise </dd></dl>

</div>
</div>
<a id="aabf23b66cd362adaa508de5bfb22706a" name="aabf23b66cd362adaa508de5bfb22706a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aabf23b66cd362adaa508de5bfb22706a">&#9670;&#160;</a></span>isAdmin()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">isAdmin </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Check if user is an administrator</p>
<p>Checks both the new role-based system and legacy session role for backward compatibility.</p>
<dl class="section return"><dt>Returns</dt><dd>bool True if user is an administrator, false otherwise </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="a33bdd79e5da367ebddd4cfbdbbfc7cff" name="a33bdd79e5da367ebddd4cfbdbbfc7cff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a33bdd79e5da367ebddd4cfbdbbfc7cff">&#9670;&#160;</a></span>isLoggedIn()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">isLoggedIn </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Check if user is logged in</p>
<p>Checks both session data and remember me cookies to determine if a user is authenticated.</p>
<dl class="section return"><dt>Returns</dt><dd>bool True if user is logged in, false otherwise </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
<a id="aa7e78bf5572fe3a109a59d4a63e0fa34" name="aa7e78bf5572fe3a109a59d4a63e0fa34"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa7e78bf5572fe3a109a59d4a63e0fa34">&#9670;&#160;</a></span>redirect()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">redirect </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$page</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Redirect to a specific page</p>
<p>Performs HTTP redirect to the specified page with proper URL construction. Handles both relative and absolute URLs correctly.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramtype">string</td><td class="paramname">$page</td><td>The page/URL to redirect to </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>void This function exits the script after redirect </dd></dl>
<dl class="section since"><dt>Since</dt><dd>1.0.0 </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
