<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: test_file_upload.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">test_file_upload.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a714ce50db18b1104062399cdec8486c6" id="r_a714ce50db18b1104062399cdec8486c6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a714ce50db18b1104062399cdec8486c6">getUploadErrorMessage</a> ($errorCode)</td></tr>
<tr class="separator:a714ce50db18b1104062399cdec8486c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a76101de3df453e22e2df687ad2e74a13" id="r_a76101de3df453e22e2df687ad2e74a13"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a76101de3df453e22e2df687ad2e74a13">$uploadDir</a> = ini_get('upload_tmp_dir') ?: sys_get_temp_dir()</td></tr>
<tr class="separator:a76101de3df453e22e2df687ad2e74a13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69dafa0546695386cd61c36aa16cca44" id="r_a69dafa0546695386cd61c36aa16cca44"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a69dafa0546695386cd61c36aa16cca44">if</a> ( $_SERVER[ 'REQUEST_METHOD']==='POST' &amp;&amp;isset( $_FILES[ 'test_file']))</td></tr>
<tr class="separator:a69dafa0546695386cd61c36aa16cca44"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a id="a714ce50db18b1104062399cdec8486c6" name="a714ce50db18b1104062399cdec8486c6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a714ce50db18b1104062399cdec8486c6">&#9670;&#160;</a></span>getUploadErrorMessage()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">getUploadErrorMessage </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$errorCode</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a76101de3df453e22e2df687ad2e74a13" name="a76101de3df453e22e2df687ad2e74a13"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a76101de3df453e22e2df687ad2e74a13">&#9670;&#160;</a></span>$uploadDir</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$uploadDir = ini_get('upload_tmp_dir') ?: sys_get_temp_dir()</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a69dafa0546695386cd61c36aa16cca44" name="a69dafa0546695386cd61c36aa16cca44"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a69dafa0546695386cd61c36aa16cca44">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if($_SERVER['REQUEST_METHOD']==='POST' &amp;&amp;isset($_FILES['test_file'])) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$_SERVER</em></span>[ 'REQUEST_METHOD']<span class="paramdefsep"> = </span><span class="paramdefval">==&#160;'POST'&#160;&amp;&amp;&#160;isset($_FILES['test_file'])</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
