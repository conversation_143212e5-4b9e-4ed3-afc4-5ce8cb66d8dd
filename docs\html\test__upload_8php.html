<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/assets/test_upload.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_4bf277b741c35ce534efde8f7dcf6e83.html">assets</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">test_upload.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a82cd33ca97ff99f2fcc5e9c81d65251b" id="r_a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(isset($data['error']))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82cd33ca97ff99f2fcc5e9c81d65251b">endif</a></td></tr>
<tr class="separator:a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade7bf03e85a66a48fcc445fd7884d157" id="r_ade7bf03e85a66a48fcc445fd7884d157"><td class="memItemLeft" align="right" valign="top">if(isset( $data[ 'success']))&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ade7bf03e85a66a48fcc445fd7884d157">if</a> (isset( $data[ 'file_info']))</td></tr>
<tr class="separator:ade7bf03e85a66a48fcc445fd7884d157"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a82cd33ca97ff99f2fcc5e9c81d65251b" name="a82cd33ca97ff99f2fcc5e9c81d65251b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82cd33ca97ff99f2fcc5e9c81d65251b">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ade7bf03e85a66a48fcc445fd7884d157" name="ade7bf03e85a66a48fcc445fd7884d157"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ade7bf03e85a66a48fcc445fd7884d157">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if(isset($data['success'])) if(isset($data['file_info'])) </td>
          <td>(</td>
          <td class="paramtype">isset( $data[ 'file_info'])</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
