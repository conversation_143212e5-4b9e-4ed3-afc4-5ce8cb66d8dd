<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/error_logs/view.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_c09662c790968526f76075857adb0866.html">error_logs</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">view.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:ae6507b0dfbff66ac3a6b4dfd6c4d1a34" id="r_ae6507b0dfbff66ac3a6b4dfd6c4d1a34"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae6507b0dfbff66ac3a6b4dfd6c4d1a34">$levelClass</a> = 'bg-blue-100 text-blue-800'</td></tr>
<tr class="separator:ae6507b0dfbff66ac3a6b4dfd6c4d1a34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab79dec0f9fdcc05f90072eb2dbc59e56" id="r_ab79dec0f9fdcc05f90072eb2dbc59e56"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab79dec0f9fdcc05f90072eb2dbc59e56">switch</a> ( $data[ 'error_log']-&gt;level)</td></tr>
<tr class="separator:ab79dec0f9fdcc05f90072eb2dbc59e56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5075cf3cbf9ce1cfa0d52924033abf51" id="r_a5075cf3cbf9ce1cfa0d52924033abf51"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>($data['error_log']-&gt;trace)&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5075cf3cbf9ce1cfa0d52924033abf51">else</a></td></tr>
<tr class="separator:a5075cf3cbf9ce1cfa0d52924033abf51"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="ae6507b0dfbff66ac3a6b4dfd6c4d1a34" name="ae6507b0dfbff66ac3a6b4dfd6c4d1a34"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae6507b0dfbff66ac3a6b4dfd6c4d1a34">&#9670;&#160;</a></span>$levelClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$levelClass = 'bg-blue-100 text-blue-800'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a5075cf3cbf9ce1cfa0d52924033abf51" name="a5075cf3cbf9ce1cfa0d52924033abf51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5075cf3cbf9ce1cfa0d52924033abf51">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> ( $data[ 'error_log']-&gt;user_agent) else</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab79dec0f9fdcc05f90072eb2dbc59e56" name="ab79dec0f9fdcc05f90072eb2dbc59e56"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab79dec0f9fdcc05f90072eb2dbc59e56">&#9670;&#160;</a></span>switch</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">switch($data['error_log']-&gt;level) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data-&gt;</em></span>[ 'error_log']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
