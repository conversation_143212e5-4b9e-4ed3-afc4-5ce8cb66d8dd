<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>EVIS: app/views/maintenance/view_record.php File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">EVIS<span id="projectnumber">&#160;1.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){ initResizable(false); });
/* @license-end */
</script>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d422163b96683743ed3963d4aac17747.html">app</a></li><li class="navelem"><a class="el" href="dir_beed7f924c9b0f17d4f4a2501a7114aa.html">views</a></li><li class="navelem"><a class="el" href="dir_287ed6d8d174ec1b6d586a434511d951.html">maintenance</a></li>  </ul>
</div>
</div><!-- top -->
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle"><div class="title">view_record.php File Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a65afead519af968d2d7dc5397dab937e" id="r_a65afead519af968d2d7dc5397dab937e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a65afead519af968d2d7dc5397dab937e">if</a> (isset( $_GET[ 'debug']) &amp;&amp;$_GET[ 'debug']==1)</td></tr>
<tr class="separator:a65afead519af968d2d7dc5397dab937e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a65f6f567bea1128b76e0c5c69ab57907" id="r_a65f6f567bea1128b76e0c5c69ab57907"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a65f6f567bea1128b76e0c5c69ab57907">foreach</a> ( $data[ 'completed_checklist_items'] as $guidelineId=&gt; $items)</td></tr>
<tr class="separator:a65f6f567bea1128b76e0c5c69ab57907"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672d9707ef91db026c210f98cc601123" id="r_a672d9707ef91db026c210f98cc601123"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a672d9707ef91db026c210f98cc601123">endforeach</a></td></tr>
<tr class="separator:a672d9707ef91db026c210f98cc601123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82cd33ca97ff99f2fcc5e9c81d65251b" id="r_a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a82cd33ca97ff99f2fcc5e9c81d65251b">endif</a></td></tr>
<tr class="separator:a82cd33ca97ff99f2fcc5e9c81d65251b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad91b289b04487d30b11cd3dc57b4dee5" id="r_ad91b289b04487d30b11cd3dc57b4dee5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad91b289b04487d30b11cd3dc57b4dee5">$typeClass</a> = 'bg-blue-100 text-blue-800'</td></tr>
<tr class="separator:ad91b289b04487d30b11cd3dc57b4dee5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3b4e985ec4050d6034cf1af22dfda1f" id="r_ac3b4e985ec4050d6034cf1af22dfda1f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac3b4e985ec4050d6034cf1af22dfda1f">switch</a> ( $data[ 'maintenance_record']-&gt;maintenance_type)</td></tr>
<tr class="separator:ac3b4e985ec4050d6034cf1af22dfda1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab9b724306b055e8e4ed6d1e1f1653f1" id="r_aab9b724306b055e8e4ed6d1e1f1653f1"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aab9b724306b055e8e4ed6d1e1f1653f1">$statusClass</a> = 'bg-gray-100 text-gray-800'</td></tr>
<tr class="separator:aab9b724306b055e8e4ed6d1e1f1653f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59fb42f3b0dc7ba1d0e2a57b6dd4cf4d" id="r_a59fb42f3b0dc7ba1d0e2a57b6dd4cf4d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a59fb42f3b0dc7ba1d0e2a57b6dd4cf4d">switch</a> ( $data[ 'maintenance_record']-&gt;status)</td></tr>
<tr class="separator:a59fb42f3b0dc7ba1d0e2a57b6dd4cf4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1fa3127fc82f96b1436d871ef02be319" id="r_a1fa3127fc82f96b1436d871ef02be319"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1fa3127fc82f96b1436d871ef02be319">$db</a> = new <a class="el" href="class_database.html">Database</a>()</td></tr>
<tr class="separator:a1fa3127fc82f96b1436d871ef02be319"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27ae3a2b96c15043fb33a4686ae3f15f" id="r_a27ae3a2b96c15043fb33a4686ae3f15f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a27ae3a2b96c15043fb33a4686ae3f15f">$countResult</a> = $db-&gt;single()</td></tr>
<tr class="separator:a27ae3a2b96c15043fb33a4686ae3f15f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab690e0531aea866478f7d205048d16c3" id="r_ab690e0531aea866478f7d205048d16c3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab690e0531aea866478f7d205048d16c3">$guidelineCount</a> = $countResult-&gt;count</td></tr>
<tr class="separator:ab690e0531aea866478f7d205048d16c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2adffdd84d6ab1ab2ce17da57327f4fc" id="r_a2adffdd84d6ab1ab2ce17da57327f4fc"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2adffdd84d6ab1ab2ce17da57327f4fc">$implementedGuidelines</a> = $db-&gt;resultSet()</td></tr>
<tr class="separator:a2adffdd84d6ab1ab2ce17da57327f4fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a164a6a39728ebceba09a9a68a15839" id="r_a5a164a6a39728ebceba09a9a68a15839"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5a164a6a39728ebceba09a9a68a15839">$guidelineDetails</a> = []</td></tr>
<tr class="separator:a5a164a6a39728ebceba09a9a68a15839"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a844929e9dc05dd77d3b6b756032a6d16" id="r_a844929e9dc05dd77d3b6b756032a6d16"><td class="memItemLeft" align="right" valign="top"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty( $implementedGuidelines)) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a844929e9dc05dd77d3b6b756032a6d16">(!empty( $guidelineDetails))</a> ( $guidelineDetails as $index=&gt; $guideline)</td></tr>
<tr class="separator:a844929e9dc05dd77d3b6b756032a6d16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b4b8d3eb38c434be02d3e95ff1fb83b" id="r_a9b4b8d3eb38c434be02d3e95ff1fb83b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9b4b8d3eb38c434be02d3e95ff1fb83b">$importanceClass</a> = 'bg-blue-100 text-blue-800'</td></tr>
<tr class="separator:a9b4b8d3eb38c434be02d3e95ff1fb83b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4fc34755212ee7f3d75abc75c55a9ad4" id="r_a4fc34755212ee7f3d75abc75c55a9ad4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4fc34755212ee7f3d75abc75c55a9ad4">$completedItems</a> = []</td></tr>
<tr class="separator:a4fc34755212ee7f3d75abc75c55a9ad4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ba3d54e0f8c662f4482d113c94577e6" id="r_a8ba3d54e0f8c662f4482d113c94577e6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8ba3d54e0f8c662f4482d113c94577e6">$guidelineId</a> = $guideline-&gt;guideline_id ?? $guideline-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a></td></tr>
<tr class="separator:a8ba3d54e0f8c662f4482d113c94577e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e01dcc96c43199448ee66f7c2ae8ea6" id="r_a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e01dcc96c43199448ee66f7c2ae8ea6">__pad0__</a></td></tr>
<tr class="separator:a8e01dcc96c43199448ee66f7c2ae8ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a4fc34755212ee7f3d75abc75c55a9ad4" name="a4fc34755212ee7f3d75abc75c55a9ad4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4fc34755212ee7f3d75abc75c55a9ad4">&#9670;&#160;</a></span>$completedItems</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$completedItems = []</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a27ae3a2b96c15043fb33a4686ae3f15f" name="a27ae3a2b96c15043fb33a4686ae3f15f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27ae3a2b96c15043fb33a4686ae3f15f">&#9670;&#160;</a></span>$countResult</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$countResult = $db-&gt;single()</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1fa3127fc82f96b1436d871ef02be319" name="a1fa3127fc82f96b1436d871ef02be319"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1fa3127fc82f96b1436d871ef02be319">&#9670;&#160;</a></span>$db</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$db = new <a class="el" href="class_database.html">Database</a>()</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab690e0531aea866478f7d205048d16c3" name="ab690e0531aea866478f7d205048d16c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab690e0531aea866478f7d205048d16c3">&#9670;&#160;</a></span>$guidelineCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$guidelineCount = $countResult-&gt;count</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a5a164a6a39728ebceba09a9a68a15839" name="a5a164a6a39728ebceba09a9a68a15839"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5a164a6a39728ebceba09a9a68a15839">&#9670;&#160;</a></span>$guidelineDetails</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$guidelineDetails = []</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8ba3d54e0f8c662f4482d113c94577e6" name="a8ba3d54e0f8c662f4482d113c94577e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8ba3d54e0f8c662f4482d113c94577e6">&#9670;&#160;</a></span>$guidelineId</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$guidelineId = $guideline-&gt;guideline_id ?? $guideline-&gt;<a class="el" href="maintenance_2add_8php.html#a0bee1c6028cca051cae04a7f46b36ab4">id</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2adffdd84d6ab1ab2ce17da57327f4fc" name="a2adffdd84d6ab1ab2ce17da57327f4fc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2adffdd84d6ab1ab2ce17da57327f4fc">&#9670;&#160;</a></span>$implementedGuidelines</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$implementedGuidelines = $db-&gt;resultSet()</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9b4b8d3eb38c434be02d3e95ff1fb83b" name="a9b4b8d3eb38c434be02d3e95ff1fb83b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b4b8d3eb38c434be02d3e95ff1fb83b">&#9670;&#160;</a></span>$importanceClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$importanceClass = 'bg-blue-100 text-blue-800'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aab9b724306b055e8e4ed6d1e1f1653f1" name="aab9b724306b055e8e4ed6d1e1f1653f1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aab9b724306b055e8e4ed6d1e1f1653f1">&#9670;&#160;</a></span>$statusClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$statusClass = 'bg-gray-100 text-gray-800'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad91b289b04487d30b11cd3dc57b4dee5" name="ad91b289b04487d30b11cd3dc57b4dee5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad91b289b04487d30b11cd3dc57b4dee5">&#9670;&#160;</a></span>$typeClass</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">$typeClass = 'bg-blue-100 text-blue-800'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a844929e9dc05dd77d3b6b756032a6d16" name="a844929e9dc05dd77d3b6b756032a6d16"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a844929e9dc05dd77d3b6b756032a6d16">&#9670;&#160;</a></span>(!empty( $guidelineDetails))</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a>(!empty($implementedGuidelines)) <a class="el" href="bootstrap_8php.html#af75becf76e03572890c9841a9295e925">if</a> (!empty($guidelineDetails))($guidelineDetails as $index=&gt; $guideline) </td>
          <td>(</td>
          <td class="paramtype">!</td>          <td class="paramname"><span class="paramname"><em>empty</em></span> $guidelineDetails</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8e01dcc96c43199448ee66f7c2ae8ea6" name="a8e01dcc96c43199448ee66f7c2ae8ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e01dcc96c43199448ee66f7c2ae8ea6">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="create__guideline__implementation__table_8php.html#ac3cd95c062d95e026a5559fcf9d8a3bf">else</a> __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a672d9707ef91db026c210f98cc601123" name="a672d9707ef91db026c210f98cc601123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672d9707ef91db026c210f98cc601123">&#9670;&#160;</a></span>endforeach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endforeach</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a82cd33ca97ff99f2fcc5e9c81d65251b" name="a82cd33ca97ff99f2fcc5e9c81d65251b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82cd33ca97ff99f2fcc5e9c81d65251b">&#9670;&#160;</a></span>endif</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">endif</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a65f6f567bea1128b76e0c5c69ab57907" name="a65f6f567bea1128b76e0c5c69ab57907"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a65f6f567bea1128b76e0c5c69ab57907">&#9670;&#160;</a></span>foreach</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">foreach($completedItems as $item) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data as</em></span>['completed_checklist_items'], </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$items</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a65afead519af968d2d7dc5397dab937e" name="a65afead519af968d2d7dc5397dab937e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a65afead519af968d2d7dc5397dab937e">&#9670;&#160;</a></span>if</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if(isset($_GET['debug']) &amp;&amp; $_GET['debug']==1) </td>
          <td>(</td>
          <td class="paramtype">isset($_GET['debug']) &amp;&amp;</td>          <td class="paramname"><span class="paramname"><em>$_GET</em></span>[ 'debug']<span class="paramdefsep"> = </span><span class="paramdefval">=&#160;1</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac3b4e985ec4050d6034cf1af22dfda1f" name="ac3b4e985ec4050d6034cf1af22dfda1f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac3b4e985ec4050d6034cf1af22dfda1f">&#9670;&#160;</a></span>switch <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">switch($guideline-&gt;importance) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data-&gt;</em></span>[ 'maintenance_record']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a59fb42f3b0dc7ba1d0e2a57b6dd4cf4d" name="a59fb42f3b0dc7ba1d0e2a57b6dd4cf4d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a59fb42f3b0dc7ba1d0e2a57b6dd4cf4d">&#9670;&#160;</a></span>switch <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">switch($data['maintenance_record']-&gt;status) </td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>$data-&gt;</em></span>[ 'maintenance_record']</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2
</small></address>
</div><!-- doc-content -->
</body>
</html>
