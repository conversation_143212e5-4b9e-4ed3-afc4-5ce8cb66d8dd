\doxysection{app/views/maintenance/all\+\_\+history.php File Reference}
\hypertarget{all__history_8php}{}\label{all__history_8php}\index{app/views/maintenance/all\_history.php@{app/views/maintenance/all\_history.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{all__history_8php_ad91b289b04487d30b11cd3dc57b4dee5}{\$type\+Class}} = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{all__history_8php_a0762c7751ac35e246e17b4993490dff6}{switch}} ( \$record-\/$>$maintenance\+\_\+type)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}implemented\+\_\+guidelines\textquotesingle{}\mbox{]}\mbox{[}\$record-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\mbox{]}))(\$data\mbox{[}\textquotesingle{}implemented\+\_\+guidelines\textquotesingle{}\mbox{]}\mbox{[}\$record-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\mbox{]} as \$guideline) \mbox{\hyperlink{all__history_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{all__history_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\item 
\mbox{\hyperlink{all__history_8php_aab9b724306b055e8e4ed6d1e1f1653f1}{\$status\+Class}} = \textquotesingle{}bg-\/gray-\/100 text-\/gray-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{all__history_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{all__history_8php_aab9b724306b055e8e4ed6d1e1f1653f1}\index{all\_history.php@{all\_history.php}!\$statusClass@{\$statusClass}}
\index{\$statusClass@{\$statusClass}!all\_history.php@{all\_history.php}}
\doxysubsubsection{\texorpdfstring{\$statusClass}{\$statusClass}}
{\footnotesize\ttfamily \label{all__history_8php_aab9b724306b055e8e4ed6d1e1f1653f1} 
\$status\+Class = \textquotesingle{}bg-\/gray-\/100 text-\/gray-\/800\textquotesingle{}}

\Hypertarget{all__history_8php_ad91b289b04487d30b11cd3dc57b4dee5}\index{all\_history.php@{all\_history.php}!\$typeClass@{\$typeClass}}
\index{\$typeClass@{\$typeClass}!all\_history.php@{all\_history.php}}
\doxysubsubsection{\texorpdfstring{\$typeClass}{\$typeClass}}
{\footnotesize\ttfamily \label{all__history_8php_ad91b289b04487d30b11cd3dc57b4dee5} 
\$type\+Class = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}}

\Hypertarget{all__history_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{all\_history.php@{all\_history.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!all\_history.php@{all\_history.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{all__history_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{all__history_8php_a672d9707ef91db026c210f98cc601123}\index{all\_history.php@{all\_history.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!all\_history.php@{all\_history.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{all__history_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{all__history_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{all\_history.php@{all\_history.php}!endif@{endif}}
\index{endif@{endif}!all\_history.php@{all\_history.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{all__history_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

\Hypertarget{all__history_8php_a0762c7751ac35e246e17b4993490dff6}\index{all\_history.php@{all\_history.php}!switch@{switch}}
\index{switch@{switch}!all\_history.php@{all\_history.php}}
\doxysubsubsection{\texorpdfstring{switch}{switch}}
{\footnotesize\ttfamily \label{all__history_8php_a0762c7751ac35e246e17b4993490dff6} 
switch(\$record-\/$>$status) (\begin{DoxyParamCaption}\item[{}]{\$record-\/$>$}{}\end{DoxyParamCaption})}

