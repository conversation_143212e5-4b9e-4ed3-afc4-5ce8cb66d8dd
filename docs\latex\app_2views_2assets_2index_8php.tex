\doxysection{app/views/assets/index.php File Reference}
\hypertarget{app_2views_2assets_2index_8php}{}\label{app_2views_2assets_2index_8php}\index{app/views/assets/index.php@{app/views/assets/index.php}}
\doxysubsubsection*{Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\mbox{\hyperlink{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}{has\+Permission}}(\textquotesingle{}create\+\_\+assets\textquotesingle{})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\mbox{\hyperlink{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}{has\+Permission}}( \textquotesingle{}import\+\_\+assets\textquotesingle{})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\mbox{\hyperlink{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}{has\+Permission}}(\textquotesingle{}export\+\_\+assets\textquotesingle{})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(count( \$data\mbox{[} \textquotesingle{}assets\textquotesingle{}\mbox{]}) $>$ 0)( \$field, \$current\+Sort, \$current\+Order) \mbox{\hyperlink{app_2views_2assets_2index_8php_ab29771c47555edcc9d91bfbe99f84b63}{get\+Sort\+Indicator}} (\$field, \$current\+Sort, \$current\+Order)
\end{DoxyCompactItemize}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}(\$data\mbox{[}\textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}equipment\+\_\+types\textquotesingle{}\mbox{]} as \$type) \mbox{\hyperlink{app_2views_2assets_2index_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{app_2views_2assets_2index_8php_a20c4a031eb504739e03f6014cdfb0055}{\$tag\+Model}} = new \mbox{\hyperlink{class_tag}{Tag}}()
\item 
\mbox{\hyperlink{app_2views_2assets_2index_8php_a74cea71d3e2cbaff24dfb33210faa0f7}{\$asset\+Id}} = isset(\$asset-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}) ? \$asset-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}} \+: \$asset\mbox{[}\textquotesingle{}\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\textquotesingle{}\mbox{]}
\item 
\mbox{\hyperlink{app_2views_2assets_2index_8php_abf61640e87147db95185d5d01f3b66d1}{\$asset\+Tags}} = \$tag\+Model-\/$>$get\+Tags\+For\+Asset(\$asset\+Id)
\item 
\mbox{\hyperlink{app_2views_2assets_2index_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{app_2views_2assets_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\end{DoxyCompactItemize}


\doxysubsection{Function Documentation}
\Hypertarget{app_2views_2assets_2index_8php_ab29771c47555edcc9d91bfbe99f84b63}\index{index.php@{index.php}!getSortIndicator@{getSortIndicator}}
\index{getSortIndicator@{getSortIndicator}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{getSortIndicator()}{getSortIndicator()}}
{\footnotesize\ttfamily \label{app_2views_2assets_2index_8php_ab29771c47555edcc9d91bfbe99f84b63} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\mbox{\hyperlink{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}{has\+Permission}}( \textquotesingle{}create\+\_\+assets\textquotesingle{})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\mbox{\hyperlink{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}{has\+Permission}}(\textquotesingle{}import\+\_\+assets\textquotesingle{})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\mbox{\hyperlink{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}{has\+Permission}}( \textquotesingle{}export\+\_\+assets\textquotesingle{})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(count(\$data\mbox{[}\textquotesingle{}assets\textquotesingle{}\mbox{]}) $>$ 0)(\$field, \$current\+Sort, \$current\+Order) get\+Sort\+Indicator (\begin{DoxyParamCaption}\item[{}]{\$field}{, }\item[{}]{\$current\+Sort}{, }\item[{}]{\$current\+Order}{}\end{DoxyParamCaption})}



\doxysubsection{Variable Documentation}
\Hypertarget{app_2views_2assets_2index_8php_a74cea71d3e2cbaff24dfb33210faa0f7}\index{index.php@{index.php}!\$assetId@{\$assetId}}
\index{\$assetId@{\$assetId}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$assetId}{\$assetId}}
{\footnotesize\ttfamily \label{app_2views_2assets_2index_8php_a74cea71d3e2cbaff24dfb33210faa0f7} 
\$asset\+Id = isset(\$asset-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}) ? \$asset-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}} \+: \$asset\mbox{[}\textquotesingle{}\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\textquotesingle{}\mbox{]}}

\Hypertarget{app_2views_2assets_2index_8php_abf61640e87147db95185d5d01f3b66d1}\index{index.php@{index.php}!\$assetTags@{\$assetTags}}
\index{\$assetTags@{\$assetTags}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$assetTags}{\$assetTags}}
{\footnotesize\ttfamily \label{app_2views_2assets_2index_8php_abf61640e87147db95185d5d01f3b66d1} 
\$asset\+Tags = \$tag\+Model-\/$>$get\+Tags\+For\+Asset(\$asset\+Id)}

\Hypertarget{app_2views_2assets_2index_8php_a20c4a031eb504739e03f6014cdfb0055}\index{index.php@{index.php}!\$tagModel@{\$tagModel}}
\index{\$tagModel@{\$tagModel}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$tagModel}{\$tagModel}}
{\footnotesize\ttfamily \label{app_2views_2assets_2index_8php_a20c4a031eb504739e03f6014cdfb0055} 
\$tag\+Model = new \mbox{\hyperlink{class_tag}{Tag}}()}

\Hypertarget{app_2views_2assets_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{index.php@{index.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{app_2views_2assets_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{app_2views_2assets_2index_8php_a672d9707ef91db026c210f98cc601123}\index{index.php@{index.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{app_2views_2assets_2index_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{app_2views_2assets_2index_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{index.php@{index.php}!endif@{endif}}
\index{endif@{endif}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{app_2views_2assets_2index_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

