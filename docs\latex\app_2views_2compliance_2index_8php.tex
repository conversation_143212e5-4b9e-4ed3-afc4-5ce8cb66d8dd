\doxysection{app/views/compliance/index.php File Reference}
\hypertarget{app_2views_2compliance_2index_8php}{}\label{app_2views_2compliance_2index_8php}\index{app/views/compliance/index.php@{app/views/compliance/index.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{app_2views_2compliance_2index_8php_a243e196c10a5fb8d371ea4c420892780}{(empty( \$data\mbox{[} \textquotesingle{}frameworks\textquotesingle{}\mbox{]}))}} ( \$data\mbox{[} \textquotesingle{}frameworks\textquotesingle{}\mbox{]} as \$framework)
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_a53332b4185c5d728f0f11e2fc73fc6c6}{if}} ( \$data\mbox{[} \textquotesingle{}selected\+\_\+framework\textquotesingle{}\mbox{]})
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6}{\$total\+Assets}} = count(\$data\mbox{[}\textquotesingle{}compliance\+\_\+summary\textquotesingle{}\mbox{]})
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_a73dbf60765f3b9036cf082caad668726}{\$total\+Controls}} = count(\$data\mbox{[}\textquotesingle{}control\+\_\+summary\textquotesingle{}\mbox{]})
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_a29a31da736043f70eb4c00d38901e709}{\$compliant\+Assets}} = 0
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_a2b10ee15f2a50ebb857910457f7909eb}{\$non\+Compliant\+Assets}} = 0
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_af237d81a9ea4bf557609c9a1ee33febf}{\$in\+Progress\+Assets}} = 0
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}(\$data\mbox{[}\textquotesingle{}compliance\+\_\+summary\textquotesingle{}\mbox{]} as \$summary) \mbox{\hyperlink{app_2views_2compliance_2index_8php_a2999f88c51c3f083b4d50815639f5080}{\$total\+Compliance}} = 0
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_a0bbc899e361b1dc3af1d3e5be47fcbe5}{\$total\+Assessments}} = 0
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}(\$data\mbox{[}\textquotesingle{}control\+\_\+summary\textquotesingle{}\mbox{]} as \$control) \mbox{\hyperlink{app_2views_2compliance_2index_8php_a123663bab6dc36d847b4fdd422ec78b9}{\$overall\+Compliance}} = \$total\+Assessments $>$ 0 ? (\$total\+Compliance / \$total\+Assessments) \texorpdfstring{$\ast$}{*} 100 \+: 0
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_a481f92faa68a232509ff785cf4f70e44}{\$bar\+Color}} = \textquotesingle{}bg-\/red-\/500\textquotesingle{}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$overall\+Compliance $>$=90) elseif( \$overall\+Compliance $>$=70) \mbox{\hyperlink{app_2views_2compliance_2index_8php_a2fe92cbc651f6f645279ed84f54274dd}{elseif}} ( \$overall\+Compliance $>$=50)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{app_2views_2compliance_2index_8php_a80e005f3bdab5f2680d6494ec89ff8b3}{(count( \$data\mbox{[} \textquotesingle{}non\+\_\+compliant\+\_\+assets\textquotesingle{}\mbox{]}) $>$ 0)}} ( \$data\mbox{[} \textquotesingle{}non\+\_\+compliant\+\_\+assets\textquotesingle{}\mbox{]} as \$asset)
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_a2d4ce20a31e47694d0105327b8502fe6}{\$compliance\+Percentage}} = 100 -\/ \$asset-\/$>$non\+\_\+compliant\+\_\+percentage
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{app_2views_2compliance_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{app_2views_2compliance_2index_8php_a88b90db63c2b36b85d371bab0ef3b644}{(count( \$data\mbox{[} \textquotesingle{}control\+\_\+summary\textquotesingle{}\mbox{]}) $>$ 0)}} ( \$data\mbox{[} \textquotesingle{}control\+\_\+summary\textquotesingle{}\mbox{]} as \$control)
\item 
\mbox{\hyperlink{app_2views_2compliance_2index_8php_a3f027137bc02172b9aa60c1c9f1af7ac}{\$assessed\+Count}} = \$control-\/$>$compliant\+\_\+count + \$control-\/$>$non\+\_\+compliant\+\_\+count + \$control-\/$>$not\+\_\+applicable\+\_\+count
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{app_2views_2compliance_2index_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}{\+\_\+\+\_\+pad1\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{app_2views_2compliance_2index_8php_a015fdb3803c848a73f2d486cd919da6e}{(count( \$data\mbox{[} \textquotesingle{}recent\+\_\+reports\textquotesingle{}\mbox{]}) $>$ 0)}} ( \$data\mbox{[} \textquotesingle{}recent\+\_\+reports\textquotesingle{}\mbox{]} as \$report)
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{app_2views_2compliance_2index_8php_aed2d37b4e8da3f52103ae96ce9d26d82}{\+\_\+\+\_\+pad2\+\_\+\+\_\+}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{app_2views_2compliance_2index_8php_a3f027137bc02172b9aa60c1c9f1af7ac}\index{index.php@{index.php}!\$assessedCount@{\$assessedCount}}
\index{\$assessedCount@{\$assessedCount}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$assessedCount}{\$assessedCount}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a3f027137bc02172b9aa60c1c9f1af7ac} 
\$assessed\+Count = \$control-\/$>$compliant\+\_\+count + \$control-\/$>$non\+\_\+compliant\+\_\+count + \$control-\/$>$not\+\_\+applicable\+\_\+count}

\Hypertarget{app_2views_2compliance_2index_8php_a481f92faa68a232509ff785cf4f70e44}\index{index.php@{index.php}!\$barColor@{\$barColor}}
\index{\$barColor@{\$barColor}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$barColor}{\$barColor}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a481f92faa68a232509ff785cf4f70e44} 
\$bar\+Color = \textquotesingle{}bg-\/red-\/500\textquotesingle{}}

\Hypertarget{app_2views_2compliance_2index_8php_a2d4ce20a31e47694d0105327b8502fe6}\index{index.php@{index.php}!\$compliancePercentage@{\$compliancePercentage}}
\index{\$compliancePercentage@{\$compliancePercentage}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$compliancePercentage}{\$compliancePercentage}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a2d4ce20a31e47694d0105327b8502fe6} 
\$compliance\+Percentage = 100 -\/ \$asset-\/$>$non\+\_\+compliant\+\_\+percentage}

\Hypertarget{app_2views_2compliance_2index_8php_a29a31da736043f70eb4c00d38901e709}\index{index.php@{index.php}!\$compliantAssets@{\$compliantAssets}}
\index{\$compliantAssets@{\$compliantAssets}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$compliantAssets}{\$compliantAssets}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a29a31da736043f70eb4c00d38901e709} 
\$compliant\+Assets = 0}

\Hypertarget{app_2views_2compliance_2index_8php_af237d81a9ea4bf557609c9a1ee33febf}\index{index.php@{index.php}!\$inProgressAssets@{\$inProgressAssets}}
\index{\$inProgressAssets@{\$inProgressAssets}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$inProgressAssets}{\$inProgressAssets}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_af237d81a9ea4bf557609c9a1ee33febf} 
\$in\+Progress\+Assets = 0}

\Hypertarget{app_2views_2compliance_2index_8php_a2b10ee15f2a50ebb857910457f7909eb}\index{index.php@{index.php}!\$nonCompliantAssets@{\$nonCompliantAssets}}
\index{\$nonCompliantAssets@{\$nonCompliantAssets}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$nonCompliantAssets}{\$nonCompliantAssets}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a2b10ee15f2a50ebb857910457f7909eb} 
\$non\+Compliant\+Assets = 0}

\Hypertarget{app_2views_2compliance_2index_8php_a123663bab6dc36d847b4fdd422ec78b9}\index{index.php@{index.php}!\$overallCompliance@{\$overallCompliance}}
\index{\$overallCompliance@{\$overallCompliance}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$overallCompliance}{\$overallCompliance}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a123663bab6dc36d847b4fdd422ec78b9} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} ( \$data\mbox{[} \textquotesingle{}control\+\_\+summary\textquotesingle{}\mbox{]} as \$control) \$overall\+Compliance = \$total\+Assessments $>$ 0 ? (\$total\+Compliance / \$total\+Assessments) \texorpdfstring{$\ast$}{*} 100 \+: 0}

\Hypertarget{app_2views_2compliance_2index_8php_a0bbc899e361b1dc3af1d3e5be47fcbe5}\index{index.php@{index.php}!\$totalAssessments@{\$totalAssessments}}
\index{\$totalAssessments@{\$totalAssessments}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$totalAssessments}{\$totalAssessments}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a0bbc899e361b1dc3af1d3e5be47fcbe5} 
\$total\+Assessments = 0}

\Hypertarget{app_2views_2compliance_2index_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6}\index{index.php@{index.php}!\$totalAssets@{\$totalAssets}}
\index{\$totalAssets@{\$totalAssets}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$totalAssets}{\$totalAssets}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6} 
\$total\+Assets = count(\$data\mbox{[}\textquotesingle{}compliance\+\_\+summary\textquotesingle{}\mbox{]})}

\Hypertarget{app_2views_2compliance_2index_8php_a2999f88c51c3f083b4d50815639f5080}\index{index.php@{index.php}!\$totalCompliance@{\$totalCompliance}}
\index{\$totalCompliance@{\$totalCompliance}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$totalCompliance}{\$totalCompliance}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a2999f88c51c3f083b4d50815639f5080} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} ( \$data\mbox{[} \textquotesingle{}compliance\+\_\+summary\textquotesingle{}\mbox{]} as \$summary) \$total\+Compliance = 0}

\Hypertarget{app_2views_2compliance_2index_8php_a73dbf60765f3b9036cf082caad668726}\index{index.php@{index.php}!\$totalControls@{\$totalControls}}
\index{\$totalControls@{\$totalControls}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$totalControls}{\$totalControls}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a73dbf60765f3b9036cf082caad668726} 
\$total\+Controls = count(\$data\mbox{[}\textquotesingle{}control\+\_\+summary\textquotesingle{}\mbox{]})}

\Hypertarget{app_2views_2compliance_2index_8php_a88b90db63c2b36b85d371bab0ef3b644}\index{index.php@{index.php}!(count( \$data\mbox{[} \textquotesingle{}control\_summary\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}control\_summary\textquotesingle{}]) $>$ 0)}}
\index{(count( \$data\mbox{[} \textquotesingle{}control\_summary\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}control\_summary\textquotesingle{}]) $>$ 0)}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{(count( \$data[ \textquotesingle{}control\_summary\textquotesingle{}]) $>$ 0)}{(count( \$data[ 'control\_summary']) > 0)}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a88b90db63c2b36b85d371bab0ef3b644} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (count(\$data\mbox{[}\textquotesingle{}control\+\_\+summary\textquotesingle{}\mbox{]}) $>$ 0)(\$data\mbox{[}\textquotesingle{}control\+\_\+summary\textquotesingle{}\mbox{]} as \$control) (\begin{DoxyParamCaption}\item[{count( \$data\mbox{[} \textquotesingle{}control\+\_\+summary\textquotesingle{}\mbox{]})}]{}{, }\item[{0}]{}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2compliance_2index_8php_a80e005f3bdab5f2680d6494ec89ff8b3}\index{index.php@{index.php}!(count( \$data\mbox{[} \textquotesingle{}non\_compliant\_assets\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}non\_compliant\_assets\textquotesingle{}]) $>$ 0)}}
\index{(count( \$data\mbox{[} \textquotesingle{}non\_compliant\_assets\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}non\_compliant\_assets\textquotesingle{}]) $>$ 0)}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{(count( \$data[ \textquotesingle{}non\_compliant\_assets\textquotesingle{}]) $>$ 0)}{(count( \$data[ 'non\_compliant\_assets']) > 0)}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a80e005f3bdab5f2680d6494ec89ff8b3} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (count(\$data\mbox{[}\textquotesingle{}non\+\_\+compliant\+\_\+assets\textquotesingle{}\mbox{]}) $>$ 0)(\$data\mbox{[}\textquotesingle{}non\+\_\+compliant\+\_\+assets\textquotesingle{}\mbox{]} as \$asset) (\begin{DoxyParamCaption}\item[{count( \$data\mbox{[} \textquotesingle{}non\+\_\+compliant\+\_\+assets\textquotesingle{}\mbox{]})}]{}{, }\item[{0}]{}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2compliance_2index_8php_a015fdb3803c848a73f2d486cd919da6e}\index{index.php@{index.php}!(count( \$data\mbox{[} \textquotesingle{}recent\_reports\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}recent\_reports\textquotesingle{}]) $>$ 0)}}
\index{(count( \$data\mbox{[} \textquotesingle{}recent\_reports\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}recent\_reports\textquotesingle{}]) $>$ 0)}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{(count( \$data[ \textquotesingle{}recent\_reports\textquotesingle{}]) $>$ 0)}{(count( \$data[ 'recent\_reports']) > 0)}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a015fdb3803c848a73f2d486cd919da6e} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (count(\$data\mbox{[}\textquotesingle{}recent\+\_\+reports\textquotesingle{}\mbox{]}) $>$ 0)(\$data\mbox{[}\textquotesingle{}recent\+\_\+reports\textquotesingle{}\mbox{]} as \$report) (\begin{DoxyParamCaption}\item[{count( \$data\mbox{[} \textquotesingle{}recent\+\_\+reports\textquotesingle{}\mbox{]})}]{}{, }\item[{0}]{}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2compliance_2index_8php_a243e196c10a5fb8d371ea4c420892780}\index{index.php@{index.php}!(empty( \$data\mbox{[} \textquotesingle{}frameworks\textquotesingle{}\mbox{]}))@{(empty( \$data[ \textquotesingle{}frameworks\textquotesingle{}]))}}
\index{(empty( \$data\mbox{[} \textquotesingle{}frameworks\textquotesingle{}\mbox{]}))@{(empty( \$data[ \textquotesingle{}frameworks\textquotesingle{}]))}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{(empty( \$data[ \textquotesingle{}frameworks\textquotesingle{}]))}{(empty( \$data[ 'frameworks']))}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a243e196c10a5fb8d371ea4c420892780} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (empty(\$data\mbox{[}\textquotesingle{}frameworks\textquotesingle{}\mbox{]}))(\$data\mbox{[}\textquotesingle{}frameworks\textquotesingle{}\mbox{]} as \$framework) (\begin{DoxyParamCaption}\item[{empty( \$data\mbox{[} \textquotesingle{}frameworks\textquotesingle{}\mbox{]})}]{}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2compliance_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{index.php@{index.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{app_2views_2compliance_2index_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}\index{index.php@{index.php}!\_\_pad1\_\_@{\_\_pad1\_\_}}
\index{\_\_pad1\_\_@{\_\_pad1\_\_}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad1\_\_}{\_\_pad1\_\_}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad1\+\_\+\+\_\+}

\Hypertarget{app_2views_2compliance_2index_8php_aed2d37b4e8da3f52103ae96ce9d26d82}\index{index.php@{index.php}!\_\_pad2\_\_@{\_\_pad2\_\_}}
\index{\_\_pad2\_\_@{\_\_pad2\_\_}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad2\_\_}{\_\_pad2\_\_}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_aed2d37b4e8da3f52103ae96ce9d26d82} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad2\+\_\+\+\_\+}

\Hypertarget{app_2views_2compliance_2index_8php_a2fe92cbc651f6f645279ed84f54274dd}\index{index.php@{index.php}!elseif@{elseif}}
\index{elseif@{elseif}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{elseif}{elseif}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a2fe92cbc651f6f645279ed84f54274dd} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$compliance\+Percentage $>$=90) elseif(\$compliance\+Percentage $>$=70) elseif(\$compliance\+Percentage $>$=50) (\begin{DoxyParamCaption}\item[{}]{\$overall\+Compliance $>$=}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2compliance_2index_8php_a672d9707ef91db026c210f98cc601123}\index{index.php@{index.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{app_2views_2compliance_2index_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{index.php@{index.php}!endif@{endif}}
\index{endif@{endif}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

\Hypertarget{app_2views_2compliance_2index_8php_a53332b4185c5d728f0f11e2fc73fc6c6}\index{index.php@{index.php}!if@{if}}
\index{if@{if}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{app_2views_2compliance_2index_8php_a53332b4185c5d728f0f11e2fc73fc6c6} 
if(\$data\mbox{[}\textquotesingle{}selected\+\_\+framework\textquotesingle{}\mbox{]}) (\begin{DoxyParamCaption}\item[{}]{\$data}{\mbox{[} \textquotesingle{}selected\+\_\+framework\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

