\doxysection{app/views/error\+\_\+logs/index.php File Reference}
\hypertarget{app_2views_2error__logs_2index_8php}{}\label{app_2views_2error__logs_2index_8php}\index{app/views/error\_logs/index.php@{app/views/error\_logs/index.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}error\+\_\+logs\textquotesingle{}\mbox{]}))(\$data\mbox{[}\textquotesingle{}error\+\_\+logs\textquotesingle{}\mbox{]} as \$log) \mbox{\hyperlink{app_2views_2error__logs_2index_8php_a9f27ec358be1812cedbb2e90857a47ee}{\$level\+Class}} = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{app_2views_2error__logs_2index_8php_a4bde7d5ea853e6dda100e3831dc3946b}{switch}} ( \$log-\/$>$level)
\item 
\mbox{\hyperlink{app_2views_2error__logs_2index_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$data\mbox{[} \textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]} $>$ 1) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$data\mbox{[}\textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]}$<$ \$data\mbox{[}\textquotesingle{}total\+\_\+pages\textquotesingle{}\mbox{]}) \mbox{\hyperlink{app_2views_2error__logs_2index_8php_a10bb4f97f8b642fa51417e7118eca7c9}{endif}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{app_2views_2error__logs_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{app_2views_2error__logs_2index_8php_a9f27ec358be1812cedbb2e90857a47ee}\index{index.php@{index.php}!\$levelClass@{\$levelClass}}
\index{\$levelClass@{\$levelClass}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$levelClass}{\$levelClass}}
{\footnotesize\ttfamily \label{app_2views_2error__logs_2index_8php_a9f27ec358be1812cedbb2e90857a47ee} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}error\+\_\+logs\textquotesingle{}\mbox{]})) ( \$data\mbox{[} \textquotesingle{}error\+\_\+logs\textquotesingle{}\mbox{]} as \$log) \$level\+Class = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}}

\Hypertarget{app_2views_2error__logs_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{index.php@{index.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{app_2views_2error__logs_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{app_2views_2error__logs_2index_8php_a672d9707ef91db026c210f98cc601123}\index{index.php@{index.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{app_2views_2error__logs_2index_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{app_2views_2error__logs_2index_8php_a10bb4f97f8b642fa51417e7118eca7c9}\index{index.php@{index.php}!endif@{endif}}
\index{endif@{endif}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{app_2views_2error__logs_2index_8php_a10bb4f97f8b642fa51417e7118eca7c9} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$data\mbox{[}\textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]} $>$ 1) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$data\mbox{[} \textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]}$<$ \$data\mbox{[} \textquotesingle{}total\+\_\+pages\textquotesingle{}\mbox{]}) endif (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2error__logs_2index_8php_a4bde7d5ea853e6dda100e3831dc3946b}\index{index.php@{index.php}!switch@{switch}}
\index{switch@{switch}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{switch}{switch}}
{\footnotesize\ttfamily \label{app_2views_2error__logs_2index_8php_a4bde7d5ea853e6dda100e3831dc3946b} 
switch(\$log-\/$>$level) (\begin{DoxyParamCaption}\item[{}]{\$log-\/$>$}{}\end{DoxyParamCaption})}

