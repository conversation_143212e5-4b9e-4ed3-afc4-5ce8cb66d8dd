\doxysection{app/views/finance/index.php File Reference}
\hypertarget{app_2views_2finance_2index_8php}{}\label{app_2views_2finance_2index_8php}\index{app/views/finance/index.php@{app/views/finance/index.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{app_2views_2finance_2index_8php_a284811b011d52a7f346faf819e079383}{\$total\+Cost}} = 0
\item 
\mbox{\hyperlink{app_2views_2finance_2index_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6}{\$total\+Assets}} = 0
\item 
\mbox{\hyperlink{app_2views_2finance_2index_8php_a88bcfdf9ca7cdcc0c0f4ca3cf47f55f8}{foreach}} ( \$data\mbox{[} \textquotesingle{}costs\+\_\+by\+\_\+type\textquotesingle{}\mbox{]} as \$cost)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{app_2views_2finance_2index_8php_a5553b91b143c1b248fca77c94960999a}{(count( \$data\mbox{[} \textquotesingle{}costs\+\_\+by\+\_\+equipment\+\_\+type\textquotesingle{}\mbox{]}) $>$ 0)}} ( \$data\mbox{[} \textquotesingle{}costs\+\_\+by\+\_\+equipment\+\_\+type\textquotesingle{}\mbox{]} as \$cost)
\item 
\mbox{\hyperlink{app_2views_2finance_2index_8php_adf1f638188316a0955150f271ea199f9}{endforeach}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{app_2views_2finance_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{app_2views_2finance_2index_8php_aa7cb2fb2d2964b1cf70f9c35f1e3c0af}{\$total\+Projected}} = 0
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset(\$forecast\mbox{[}\textquotesingle{}eol\+\_\+assets\+\_\+count\textquotesingle{}\mbox{]})) \mbox{\hyperlink{app_2views_2finance_2index_8php_a4f5e7dd6e52a56a405eb9dbfd2677708}{endif}}
\item 
\mbox{\hyperlink{app_2views_2finance_2index_8php_a26bf6c514c5b5c6daf1475c9e0cad453}{\$growth\+Class}} = \textquotesingle{}text-\/gray-\/500\textquotesingle{}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$forecast\mbox{[} \textquotesingle{}growth\+\_\+percentage\textquotesingle{}\mbox{]} $>$ 0) \mbox{\hyperlink{app_2views_2finance_2index_8php_af4f64bc5f928cddab2505b3d2fc180bc}{elseif}} ( \$forecast\mbox{[} \textquotesingle{}growth\+\_\+percentage\textquotesingle{}\mbox{]}$<$ 0)
\item 
\mbox{\hyperlink{app_2views_2finance_2index_8php_a570b1ffaf84608acaa9b662240f9296f}{\$total\+Growth}} = \$total\+Historical $>$ 0 ? ((\$total\+Projected / \$total\+Historical) -\/ 1) \texorpdfstring{$\ast$}{*} 100 \+: 0
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$total\+Growth $>$ 0)(\$total\+Growth$<$ 0) \mbox{\hyperlink{app_2views_2finance_2index_8php_a6df4abe9c77f961baffc617d019af60f}{else}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{app_2views_2finance_2index_8php_a26bf6c514c5b5c6daf1475c9e0cad453}\index{index.php@{index.php}!\$growthClass@{\$growthClass}}
\index{\$growthClass@{\$growthClass}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$growthClass}{\$growthClass}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_a26bf6c514c5b5c6daf1475c9e0cad453} 
\$growth\+Class = \textquotesingle{}text-\/gray-\/500\textquotesingle{}}

\Hypertarget{app_2views_2finance_2index_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6}\index{index.php@{index.php}!\$totalAssets@{\$totalAssets}}
\index{\$totalAssets@{\$totalAssets}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$totalAssets}{\$totalAssets}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6} 
\$total\+Assets = 0}

\Hypertarget{app_2views_2finance_2index_8php_a284811b011d52a7f346faf819e079383}\index{index.php@{index.php}!\$totalCost@{\$totalCost}}
\index{\$totalCost@{\$totalCost}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$totalCost}{\$totalCost}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_a284811b011d52a7f346faf819e079383} 
\$total\+Cost = 0}

\Hypertarget{app_2views_2finance_2index_8php_a570b1ffaf84608acaa9b662240f9296f}\index{index.php@{index.php}!\$totalGrowth@{\$totalGrowth}}
\index{\$totalGrowth@{\$totalGrowth}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$totalGrowth}{\$totalGrowth}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_a570b1ffaf84608acaa9b662240f9296f} 
\$total\+Growth = \$total\+Historical $>$ 0 ? ((\$total\+Projected / \$total\+Historical) -\/ 1) \texorpdfstring{$\ast$}{*} 100 \+: 0}

\Hypertarget{app_2views_2finance_2index_8php_aa7cb2fb2d2964b1cf70f9c35f1e3c0af}\index{index.php@{index.php}!\$totalProjected@{\$totalProjected}}
\index{\$totalProjected@{\$totalProjected}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$totalProjected}{\$totalProjected}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_aa7cb2fb2d2964b1cf70f9c35f1e3c0af} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} ( \$data\mbox{[} \textquotesingle{}budget\+\_\+forecast\textquotesingle{}\mbox{]} as \$forecast) \$total\+Projected = 0}

\Hypertarget{app_2views_2finance_2index_8php_a5553b91b143c1b248fca77c94960999a}\index{index.php@{index.php}!(count( \$data\mbox{[} \textquotesingle{}costs\_by\_equipment\_type\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}costs\_by\_equipment\_type\textquotesingle{}]) $>$ 0)}}
\index{(count( \$data\mbox{[} \textquotesingle{}costs\_by\_equipment\_type\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}costs\_by\_equipment\_type\textquotesingle{}]) $>$ 0)}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{(count( \$data[ \textquotesingle{}costs\_by\_equipment\_type\textquotesingle{}]) $>$ 0)}{(count( \$data[ 'costs\_by\_equipment\_type']) > 0)}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_a5553b91b143c1b248fca77c94960999a} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (count(\$data\mbox{[}\textquotesingle{}costs\+\_\+by\+\_\+equipment\+\_\+type\textquotesingle{}\mbox{]}) $>$ 0)(\$data\mbox{[}\textquotesingle{}costs\+\_\+by\+\_\+equipment\+\_\+type\textquotesingle{}\mbox{]} as \$cost) (\begin{DoxyParamCaption}\item[{count( \$data\mbox{[} \textquotesingle{}costs\+\_\+by\+\_\+equipment\+\_\+type\textquotesingle{}\mbox{]})}]{}{, }\item[{0}]{}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2finance_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{index.php@{index.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{app_2views_2finance_2index_8php_a6df4abe9c77f961baffc617d019af60f}\index{index.php@{index.php}!else@{else}}
\index{else@{else}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_a6df4abe9c77f961baffc617d019af60f} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$total\+Growth $>$ 0) ( \$total\+Growth$<$ 0) else}

\Hypertarget{app_2views_2finance_2index_8php_af4f64bc5f928cddab2505b3d2fc180bc}\index{index.php@{index.php}!elseif@{elseif}}
\index{elseif@{elseif}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{elseif}{elseif}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_af4f64bc5f928cddab2505b3d2fc180bc} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$total\+Growth $>$ 0) elseif(\$total\+Growth$<$ 0) (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2finance_2index_8php_adf1f638188316a0955150f271ea199f9}\index{index.php@{index.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_adf1f638188316a0955150f271ea199f9} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$forecast\mbox{[} \textquotesingle{}growth\+\_\+percentage\textquotesingle{}\mbox{]} $>$ 0) ( \$forecast\mbox{[} \textquotesingle{}growth\+\_\+percentage\textquotesingle{}\mbox{]}$<$ 0) endforeach}

\Hypertarget{app_2views_2finance_2index_8php_a4f5e7dd6e52a56a405eb9dbfd2677708}\index{index.php@{index.php}!endif@{endif}}
\index{endif@{endif}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_a4f5e7dd6e52a56a405eb9dbfd2677708} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (isset( \$forecast\mbox{[} \textquotesingle{}eol\+\_\+assets\+\_\+count\textquotesingle{}\mbox{]})) endif}

\Hypertarget{app_2views_2finance_2index_8php_a88bcfdf9ca7cdcc0c0f4ca3cf47f55f8}\index{index.php@{index.php}!foreach@{foreach}}
\index{foreach@{foreach}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}}
{\footnotesize\ttfamily \label{app_2views_2finance_2index_8php_a88bcfdf9ca7cdcc0c0f4ca3cf47f55f8} 
foreach(\$data\mbox{[}\textquotesingle{}costs\+\_\+by\+\_\+type\textquotesingle{}\mbox{]} as \$cost) (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}costs\+\_\+by\+\_\+type\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

