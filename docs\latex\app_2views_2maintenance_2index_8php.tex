\doxysection{app/views/maintenance/index.php File Reference}
\hypertarget{app_2views_2maintenance_2index_8php}{}\label{app_2views_2maintenance_2index_8php}\index{app/views/maintenance/index.php@{app/views/maintenance/index.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_a2c1bcb77c0686b142a36054fc8c0ff82}{\$critical\+Count}} = 0
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_a1340fb8e5a9e6a079b46d34d100713fb}{foreach}} ( \$data\mbox{[} \textquotesingle{}assets\+\_\+health\textquotesingle{}\mbox{]} as \$asset)
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_a57234fb07677f1698ffd77858ed94f76}{\$total\+Score}} = 0
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_a0564d98bf5dd495a3827a3a9b4cf74b8}{\$valid\+Count}} = 0
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}( \$data\mbox{[} \textquotesingle{}assets\+\_\+health\textquotesingle{}\mbox{]} as \$asset) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$valid\+Count $>$ 0) \mbox{\hyperlink{app_2views_2maintenance_2index_8php_a9507c0d7c11912b019f9c1de81865e56}{else}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{app_2views_2maintenance_2index_8php_a87e6ad6ae335c64338f0832e043b573c}{(count( \$data\mbox{[} \textquotesingle{}assets\+\_\+due\textquotesingle{}\mbox{]}) $>$ 0)}} ( \$data\mbox{[} \textquotesingle{}assets\+\_\+due\textquotesingle{}\mbox{]} as \$asset)
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_a6f269df22fd4acadaf5438a2f6994341}{\$days\+Remaining}} = \$asset-\/$>$days\+\_\+remaining
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_ab99343d4912650d1363355564aa6c179}{\$text\+Color}} = \textquotesingle{}text-\/green-\/500\textquotesingle{}
\item 
if( \$days\+Remaining$<$=7) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{app_2views_2maintenance_2index_8php_a810457ca020359cc6ac82360b6524b67}{if}} ( \$days\+Remaining$<$=14)
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{app_2views_2maintenance_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{app_2views_2maintenance_2index_8php_af90db8bde5b9826e549922ed58ea68f6}{(count( \$data\mbox{[} \textquotesingle{}assets\+\_\+health\textquotesingle{}\mbox{]}) $>$ 0)}} ( \$data\mbox{[} \textquotesingle{}order\+\_\+by\textquotesingle{}\mbox{]}==\textquotesingle{}computer\+\_\+host\+\_\+name\textquotesingle{})
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_a1a6589cf136d38601621ed34cf1c63b9}{if}} ( \$data\mbox{[} \textquotesingle{}order\+\_\+by\textquotesingle{}\mbox{]}==\textquotesingle{}equipment\+\_\+type\textquotesingle{})
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$data\mbox{[} \textquotesingle{}order\+\_\+by\textquotesingle{}\mbox{]}==\textquotesingle{}health\+\_\+score\textquotesingle{}) \mbox{\hyperlink{app_2views_2maintenance_2index_8php_a976c0aadcca5628cd02713ca4890b341}{foreach}} ( \$data\mbox{[} \textquotesingle{}assets\+\_\+health\textquotesingle{}\mbox{]} as \$asset)
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_a1ccb94cadb093164e431630a7020d49f}{\$health\+Score}} = \$asset-\/$>$health\+\_\+score ?? 0
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_ab82a04539e3f3415ec35adedf87b141f}{\$bg\+Color}} = \textquotesingle{}bg-\/green-\/500\textquotesingle{}
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_ac51d4079cb386b1268110b732f7f9405}{\$status\+Text}} = \textquotesingle{}Good\textquotesingle{}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$data\mbox{[}\textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]} $>$ 1) \mbox{\hyperlink{app_2views_2maintenance_2index_8php_acc352a22ca4439892ce16877974a71b7}{\$start\+Page}} = max(1, \$data\mbox{[}\textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]} -\/ 2)
\item 
\mbox{\hyperlink{app_2views_2maintenance_2index_8php_ae12f8ad07b55a483cd345c1261d56a92}{\$end\+Page}} = min(\$data\mbox{[}\textquotesingle{}total\+\_\+pages\textquotesingle{}\mbox{]}, \$data\mbox{[}\textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]} + 2)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$start\+Page $>$ 1) for(\$i=\$start\+Page; \$i$<$=\$end\+Page; \$i++) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$end\+Page$<$ \$data\mbox{[} \textquotesingle{}total\+\_\+pages\textquotesingle{}\mbox{]}) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$data\mbox{[}\textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]}$<$ \$data\mbox{[}\textquotesingle{}total\+\_\+pages\textquotesingle{}\mbox{]}) \mbox{\hyperlink{app_2views_2maintenance_2index_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{app_2views_2maintenance_2index_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}{\+\_\+\+\_\+pad1\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{app_2views_2maintenance_2index_8php_aed2d37b4e8da3f52103ae96ce9d26d82}{\+\_\+\+\_\+pad2\+\_\+\+\_\+}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{app_2views_2maintenance_2index_8php_ab82a04539e3f3415ec35adedf87b141f}\index{index.php@{index.php}!\$bgColor@{\$bgColor}}
\index{\$bgColor@{\$bgColor}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$bgColor}{\$bgColor}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_ab82a04539e3f3415ec35adedf87b141f} 
\$bg\+Color = \textquotesingle{}bg-\/green-\/500\textquotesingle{}}

\Hypertarget{app_2views_2maintenance_2index_8php_a2c1bcb77c0686b142a36054fc8c0ff82}\index{index.php@{index.php}!\$criticalCount@{\$criticalCount}}
\index{\$criticalCount@{\$criticalCount}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$criticalCount}{\$criticalCount}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a2c1bcb77c0686b142a36054fc8c0ff82} 
\$critical\+Count = 0}

\Hypertarget{app_2views_2maintenance_2index_8php_a6f269df22fd4acadaf5438a2f6994341}\index{index.php@{index.php}!\$daysRemaining@{\$daysRemaining}}
\index{\$daysRemaining@{\$daysRemaining}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$daysRemaining}{\$daysRemaining}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a6f269df22fd4acadaf5438a2f6994341} 
\$days\+Remaining = \$asset-\/$>$days\+\_\+remaining}

\Hypertarget{app_2views_2maintenance_2index_8php_ae12f8ad07b55a483cd345c1261d56a92}\index{index.php@{index.php}!\$endPage@{\$endPage}}
\index{\$endPage@{\$endPage}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$endPage}{\$endPage}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_ae12f8ad07b55a483cd345c1261d56a92} 
\$end\+Page = min(\$data\mbox{[}\textquotesingle{}total\+\_\+pages\textquotesingle{}\mbox{]}, \$data\mbox{[}\textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]} + 2)}

\Hypertarget{app_2views_2maintenance_2index_8php_a1ccb94cadb093164e431630a7020d49f}\index{index.php@{index.php}!\$healthScore@{\$healthScore}}
\index{\$healthScore@{\$healthScore}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$healthScore}{\$healthScore}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a1ccb94cadb093164e431630a7020d49f} 
\$health\+Score = \$asset-\/$>$health\+\_\+score ?? 0}

\Hypertarget{app_2views_2maintenance_2index_8php_acc352a22ca4439892ce16877974a71b7}\index{index.php@{index.php}!\$startPage@{\$startPage}}
\index{\$startPage@{\$startPage}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$startPage}{\$startPage}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_acc352a22ca4439892ce16877974a71b7} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$data\mbox{[} \textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]} $>$ 1) \$start\+Page = max(1, \$data\mbox{[}\textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]} -\/ 2)}

\Hypertarget{app_2views_2maintenance_2index_8php_ac51d4079cb386b1268110b732f7f9405}\index{index.php@{index.php}!\$statusText@{\$statusText}}
\index{\$statusText@{\$statusText}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$statusText}{\$statusText}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_ac51d4079cb386b1268110b732f7f9405} 
\$status\+Text = \textquotesingle{}Good\textquotesingle{}}

\Hypertarget{app_2views_2maintenance_2index_8php_ab99343d4912650d1363355564aa6c179}\index{index.php@{index.php}!\$textColor@{\$textColor}}
\index{\$textColor@{\$textColor}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$textColor}{\$textColor}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_ab99343d4912650d1363355564aa6c179} 
\$text\+Color = \textquotesingle{}text-\/green-\/500\textquotesingle{}}

\Hypertarget{app_2views_2maintenance_2index_8php_a57234fb07677f1698ffd77858ed94f76}\index{index.php@{index.php}!\$totalScore@{\$totalScore}}
\index{\$totalScore@{\$totalScore}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$totalScore}{\$totalScore}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a57234fb07677f1698ffd77858ed94f76} 
\$total\+Score = 0}

\Hypertarget{app_2views_2maintenance_2index_8php_a0564d98bf5dd495a3827a3a9b4cf74b8}\index{index.php@{index.php}!\$validCount@{\$validCount}}
\index{\$validCount@{\$validCount}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\$validCount}{\$validCount}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a0564d98bf5dd495a3827a3a9b4cf74b8} 
\$valid\+Count = 0}

\Hypertarget{app_2views_2maintenance_2index_8php_a87e6ad6ae335c64338f0832e043b573c}\index{index.php@{index.php}!(count( \$data\mbox{[} \textquotesingle{}assets\_due\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}assets\_due\textquotesingle{}]) $>$ 0)}}
\index{(count( \$data\mbox{[} \textquotesingle{}assets\_due\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}assets\_due\textquotesingle{}]) $>$ 0)}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{(count( \$data[ \textquotesingle{}assets\_due\textquotesingle{}]) $>$ 0)}{(count( \$data[ 'assets\_due']) > 0)}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a87e6ad6ae335c64338f0832e043b573c} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (count(\$data\mbox{[}\textquotesingle{}assets\+\_\+due\textquotesingle{}\mbox{]}) $>$ 0)(\$data\mbox{[}\textquotesingle{}assets\+\_\+due\textquotesingle{}\mbox{]} as \$asset) (\begin{DoxyParamCaption}\item[{count( \$data\mbox{[} \textquotesingle{}assets\+\_\+due\textquotesingle{}\mbox{]})}]{}{, }\item[{0}]{}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2maintenance_2index_8php_af90db8bde5b9826e549922ed58ea68f6}\index{index.php@{index.php}!(count( \$data\mbox{[} \textquotesingle{}assets\_health\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}assets\_health\textquotesingle{}]) $>$ 0)}}
\index{(count( \$data\mbox{[} \textquotesingle{}assets\_health\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}assets\_health\textquotesingle{}]) $>$ 0)}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{(count( \$data[ \textquotesingle{}assets\_health\textquotesingle{}]) $>$ 0)}{(count( \$data[ 'assets\_health']) > 0)}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_af90db8bde5b9826e549922ed58ea68f6} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (count(\$data\mbox{[}\textquotesingle{}assets\+\_\+health\textquotesingle{}\mbox{]}) $>$ 0)(\$data\mbox{[}\textquotesingle{}order\+\_\+by\textquotesingle{}\mbox{]}==\textquotesingle{}computer\+\_\+host\+\_\+name\textquotesingle{}) (\begin{DoxyParamCaption}\item[{count( \$data\mbox{[} \textquotesingle{}assets\+\_\+health\textquotesingle{}\mbox{]})}]{}{, }\item[{0}]{}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2maintenance_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{index.php@{index.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{app_2views_2maintenance_2index_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}\index{index.php@{index.php}!\_\_pad1\_\_@{\_\_pad1\_\_}}
\index{\_\_pad1\_\_@{\_\_pad1\_\_}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad1\_\_}{\_\_pad1\_\_}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad1\+\_\+\+\_\+}

\Hypertarget{app_2views_2maintenance_2index_8php_aed2d37b4e8da3f52103ae96ce9d26d82}\index{index.php@{index.php}!\_\_pad2\_\_@{\_\_pad2\_\_}}
\index{\_\_pad2\_\_@{\_\_pad2\_\_}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad2\_\_}{\_\_pad2\_\_}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_aed2d37b4e8da3f52103ae96ce9d26d82} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad2\+\_\+\+\_\+}

\Hypertarget{app_2views_2maintenance_2index_8php_a9507c0d7c11912b019f9c1de81865e56}\index{index.php@{index.php}!else@{else}}
\index{else@{else}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a9507c0d7c11912b019f9c1de81865e56} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}(\$data\mbox{[}\textquotesingle{}assets\+\_\+health\textquotesingle{}\mbox{]} as \$asset) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$valid\+Count $>$ 0) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ echo\ \textcolor{stringliteral}{'N/A'}}

\end{DoxyCode}
\Hypertarget{app_2views_2maintenance_2index_8php_a672d9707ef91db026c210f98cc601123}\index{index.php@{index.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{app_2views_2maintenance_2index_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{index.php@{index.php}!endif@{endif}}
\index{endif@{endif}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2maintenance_2index_8php_a1340fb8e5a9e6a079b46d34d100713fb}\index{index.php@{index.php}!foreach@{foreach}}
\index{foreach@{foreach}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a1340fb8e5a9e6a079b46d34d100713fb} 
foreach(\$data\mbox{[}\textquotesingle{}assets\+\_\+health\textquotesingle{}\mbox{]} as \$asset) (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}assets\+\_\+health\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2maintenance_2index_8php_a976c0aadcca5628cd02713ca4890b341}\index{index.php@{index.php}!foreach@{foreach}}
\index{foreach@{foreach}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a976c0aadcca5628cd02713ca4890b341} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$data\mbox{[}\textquotesingle{}order\+\_\+by\textquotesingle{}\mbox{]}==\textquotesingle{}health\+\_\+score\textquotesingle{}) foreach(\$data\mbox{[}\textquotesingle{}assets\+\_\+health\textquotesingle{}\mbox{]} as \$asset) (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}assets\+\_\+health\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2maintenance_2index_8php_a1a6589cf136d38601621ed34cf1c63b9}\index{index.php@{index.php}!if@{if}}
\index{if@{if}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{if}{if}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a1a6589cf136d38601621ed34cf1c63b9} 
if(\$data\mbox{[}\textquotesingle{}order\+\_\+by\textquotesingle{}\mbox{]}==\textquotesingle{}equipment\+\_\+type\textquotesingle{}) (\begin{DoxyParamCaption}\item[{}]{\$data}{\mbox{[} \textquotesingle{}order\+\_\+by\textquotesingle{}\mbox{]} = {\ttfamily =~\textquotesingle{}equipment\+\_\+type\textquotesingle{}}}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2maintenance_2index_8php_a810457ca020359cc6ac82360b6524b67}\index{index.php@{index.php}!if@{if}}
\index{if@{if}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{if}{if}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily \label{app_2views_2maintenance_2index_8php_a810457ca020359cc6ac82360b6524b67} 
if(\$health\+Score$<$ 40) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if(\$health\+Score$<$ 70) (\begin{DoxyParamCaption}\item[{}]{\$days\+Remaining$<$=}{}\end{DoxyParamCaption})}

