\doxysection{app/views/roles/index.php File Reference}
\hypertarget{app_2views_2roles_2index_8php}{}\label{app_2views_2roles_2index_8php}\index{app/views/roles/index.php@{app/views/roles/index.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{app_2views_2roles_2index_8php_ab78450e16a061bbc11f72bf1f8e27d3c}{if}} ( \$role-\/$>$is\+\_\+system\+\_\+role)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(! \$role-\/$>$is\+\_\+system\+\_\+role) \mbox{\hyperlink{app_2views_2roles_2index_8php_a1503edf98562298d8024253085f1f76d}{endforeach}}
\item 
\mbox{\hyperlink{app_2views_2roles_2index_8php_a585ca23b0e2286d604231a36cf1e8d96}{if}} (empty( \$data\mbox{[} \textquotesingle{}roles\textquotesingle{}\mbox{]}))
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{app_2views_2roles_2index_8php_a1503edf98562298d8024253085f1f76d}\index{index.php@{index.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{app_2views_2roles_2index_8php_a1503edf98562298d8024253085f1f76d} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (! \$role-\/$>$is\+\_\+system\+\_\+role) endforeach}

\Hypertarget{app_2views_2roles_2index_8php_ab78450e16a061bbc11f72bf1f8e27d3c}\index{index.php@{index.php}!if@{if}}
\index{if@{if}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{if}{if}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily \label{app_2views_2roles_2index_8php_ab78450e16a061bbc11f72bf1f8e27d3c} 
if(\$role-\/$>$is\+\_\+system\+\_\+role) (\begin{DoxyParamCaption}\item[{}]{\$role-\/$>$}{}\end{DoxyParamCaption})}

\Hypertarget{app_2views_2roles_2index_8php_a585ca23b0e2286d604231a36cf1e8d96}\index{index.php@{index.php}!if@{if}}
\index{if@{if}!index.php@{index.php}}
\doxysubsubsection{\texorpdfstring{if}{if}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily \label{app_2views_2roles_2index_8php_a585ca23b0e2286d604231a36cf1e8d96} 
if(empty(\$data\mbox{[}\textquotesingle{}roles\textquotesingle{}\mbox{]})) (\begin{DoxyParamCaption}\item[{empty( \$data\mbox{[} \textquotesingle{}roles\textquotesingle{}\mbox{]})}]{}{}\end{DoxyParamCaption})}

