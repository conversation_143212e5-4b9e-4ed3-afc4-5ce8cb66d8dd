\doxysection{app/views/maintenance/asset\+\_\+compliance.php File Reference}
\hypertarget{asset__compliance_8php}{}\label{asset__compliance_8php}\index{app/views/maintenance/asset\_compliance.php@{app/views/maintenance/asset\_compliance.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(count(\$data\mbox{[}\textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]}) $>$ 0)(\$data\mbox{[}\textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]} as \$item) \mbox{\hyperlink{asset__compliance_8php_a9b5772ede97d94185285d949b96ea584}{\$importance\+Class}} = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}
\item 
if(\$item-\/$>$importance==\textquotesingle{}critical\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if( \$item-\/$>$importance==\textquotesingle{}high\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{asset__compliance_8php_a70554adcf742439d591dd0641b1f5b66}{if}} ( \$item-\/$>$importance==\textquotesingle{}low\textquotesingle{})
\item 
\mbox{\hyperlink{asset__compliance_8php_aab9b724306b055e8e4ed6d1e1f1653f1}{\$status\+Class}} = \textquotesingle{}bg-\/gray-\/100 text-\/gray-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{asset__compliance_8php_ac51d4079cb386b1268110b732f7f9405}{\$status\+Text}} = \textquotesingle{}Not Applicable\textquotesingle{}
\item 
\mbox{\hyperlink{asset__compliance_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{asset__compliance_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{asset__compliance_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}{\+\_\+\+\_\+pad1\+\_\+\+\_\+}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{asset__compliance_8php_a9b5772ede97d94185285d949b96ea584}\index{asset\_compliance.php@{asset\_compliance.php}!\$importanceClass@{\$importanceClass}}
\index{\$importanceClass@{\$importanceClass}!asset\_compliance.php@{asset\_compliance.php}}
\doxysubsubsection{\texorpdfstring{\$importanceClass}{\$importanceClass}}
{\footnotesize\ttfamily \label{asset__compliance_8php_a9b5772ede97d94185285d949b96ea584} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (count( \$data\mbox{[} \textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]}) $>$ 0) ( \$data\mbox{[} \textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]} as \$item) \$importance\+Class = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}}

\Hypertarget{asset__compliance_8php_aab9b724306b055e8e4ed6d1e1f1653f1}\index{asset\_compliance.php@{asset\_compliance.php}!\$statusClass@{\$statusClass}}
\index{\$statusClass@{\$statusClass}!asset\_compliance.php@{asset\_compliance.php}}
\doxysubsubsection{\texorpdfstring{\$statusClass}{\$statusClass}}
{\footnotesize\ttfamily \label{asset__compliance_8php_aab9b724306b055e8e4ed6d1e1f1653f1} 
\$status\+Class = \textquotesingle{}bg-\/gray-\/100 text-\/gray-\/800\textquotesingle{}}

\Hypertarget{asset__compliance_8php_ac51d4079cb386b1268110b732f7f9405}\index{asset\_compliance.php@{asset\_compliance.php}!\$statusText@{\$statusText}}
\index{\$statusText@{\$statusText}!asset\_compliance.php@{asset\_compliance.php}}
\doxysubsubsection{\texorpdfstring{\$statusText}{\$statusText}}
{\footnotesize\ttfamily \label{asset__compliance_8php_ac51d4079cb386b1268110b732f7f9405} 
\$status\+Text = \textquotesingle{}Not Applicable\textquotesingle{}}

\Hypertarget{asset__compliance_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{asset\_compliance.php@{asset\_compliance.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!asset\_compliance.php@{asset\_compliance.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{asset__compliance_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{asset__compliance_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}\index{asset\_compliance.php@{asset\_compliance.php}!\_\_pad1\_\_@{\_\_pad1\_\_}}
\index{\_\_pad1\_\_@{\_\_pad1\_\_}!asset\_compliance.php@{asset\_compliance.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad1\_\_}{\_\_pad1\_\_}}
{\footnotesize\ttfamily \label{asset__compliance_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad1\+\_\+\+\_\+}

\Hypertarget{asset__compliance_8php_a672d9707ef91db026c210f98cc601123}\index{asset\_compliance.php@{asset\_compliance.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!asset\_compliance.php@{asset\_compliance.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{asset__compliance_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{asset__compliance_8php_a70554adcf742439d591dd0641b1f5b66}\index{asset\_compliance.php@{asset\_compliance.php}!if@{if}}
\index{if@{if}!asset\_compliance.php@{asset\_compliance.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{asset__compliance_8php_a70554adcf742439d591dd0641b1f5b66} 
if(\$record-\/$>$status==\textquotesingle{}completed\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if( \$record-\/$>$status==\textquotesingle{}scheduled\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if(\$record-\/$>$status==\textquotesingle{}overdue\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if(\$record-\/$>$status==\textquotesingle{}cancelled\textquotesingle{}) (\begin{DoxyParamCaption}\item[{}]{\$item-\/$>$}{ = {\ttfamily =~\textquotesingle{}low\textquotesingle{}}}\end{DoxyParamCaption})}

