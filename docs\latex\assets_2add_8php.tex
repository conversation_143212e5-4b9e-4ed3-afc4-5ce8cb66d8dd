\doxysection{app/views/assets/add.php File Reference}
\hypertarget{assets_2add_8php}{}\label{assets_2add_8php}\index{app/views/assets/add.php@{app/views/assets/add.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}(\$data\mbox{[}\textquotesingle{}available\+\_\+tags\textquotesingle{}\mbox{]} as \$tag) \mbox{\hyperlink{assets_2add_8php_add767f1a68dbc8f9ef7dc81a34ba7f3f}{endforeach}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{assets_2add_8php_add767f1a68dbc8f9ef7dc81a34ba7f3f}\index{add.php@{add.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!add.php@{add.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{assets_2add_8php_add767f1a68dbc8f9ef7dc81a34ba7f3f} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} ( \$data\mbox{[} \textquotesingle{}available\+\_\+tags\textquotesingle{}\mbox{]} as \$tag) endforeach}

