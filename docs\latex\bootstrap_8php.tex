\doxysection{app/bootstrap.php File Reference}
\hypertarget{bootstrap_8php}{}\label{bootstrap_8php}\index{app/bootstrap.php@{app/bootstrap.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
const \mbox{\hyperlink{bootstrap_8php_a8757a57d09df5349f8b93b2083e29a1e}{ENVIRONMENT}} \textquotesingle{}development\textquotesingle{}
\item 
\mbox{\hyperlink{bootstrap_8php_a01d711562d54e54d71a9c7244d0acc77}{\$logs\+Dir}} = \mbox{\hyperlink{config_8php_a10375bcd2448d71fb65abb104c207203}{APPROOT}} . \textquotesingle{}/logs\textquotesingle{}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!file\+\_\+exists( \$logs\+Dir))
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{bootstrap_8php_a01d711562d54e54d71a9c7244d0acc77}\index{bootstrap.php@{bootstrap.php}!\$logsDir@{\$logsDir}}
\index{\$logsDir@{\$logsDir}!bootstrap.php@{bootstrap.php}}
\doxysubsubsection{\texorpdfstring{\$logsDir}{\$logsDir}}
{\footnotesize\ttfamily \label{bootstrap_8php_a01d711562d54e54d71a9c7244d0acc77} 
\$logs\+Dir = \mbox{\hyperlink{config_8php_a10375bcd2448d71fb65abb104c207203}{APPROOT}} . \textquotesingle{}/logs\textquotesingle{}}

\Hypertarget{bootstrap_8php_a8757a57d09df5349f8b93b2083e29a1e}\index{bootstrap.php@{bootstrap.php}!ENVIRONMENT@{ENVIRONMENT}}
\index{ENVIRONMENT@{ENVIRONMENT}!bootstrap.php@{bootstrap.php}}
\doxysubsubsection{\texorpdfstring{ENVIRONMENT}{ENVIRONMENT}}
{\footnotesize\ttfamily \label{bootstrap_8php_a8757a57d09df5349f8b93b2083e29a1e} 
const ENVIRONMENT \textquotesingle{}development\textquotesingle{}}

\Hypertarget{bootstrap_8php_af75becf76e03572890c9841a9295e925}\index{bootstrap.php@{bootstrap.php}!if@{if}}
\index{if@{if}!bootstrap.php@{bootstrap.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{bootstrap_8php_af75becf76e03572890c9841a9295e925} 
if (\begin{DoxyParamCaption}\item[{!}]{file\+\_\+exists}{ \$logs\+Dir = {\ttfamily =~\textquotesingle{}overdue\textquotesingle{}}}\end{DoxyParamCaption})}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \mbox{\hyperlink{create__guideline__implementation__table_8php_a73004ce9cd673c1bfafd1dc351134797}{\$output}}[]\ =\ \textcolor{stringliteral}{"{}Table\ structure\ looks\ good\ (no\ UNIQUE\ constraint)."{}}}

\end{DoxyCode}
