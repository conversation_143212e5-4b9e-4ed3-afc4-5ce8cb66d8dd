\doxysection{public/check\+\_\+assets.php File Reference}
\hypertarget{check__assets_8php}{}\label{check__assets_8php}\index{public/check\_assets.php@{public/check\_assets.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{check__assets_8php_abe4cc9788f52e49485473dc699537388}{try}}
\item 
\mbox{\hyperlink{check__assets_8php_af27a9140d5f2658693e7fd107f716449}{\$stmt}} = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}assets\textquotesingle{}"{})
\item 
\mbox{\hyperlink{check__assets_8php_a6f8cf61e47812c53a24ccf689c122172}{if}} ( \$stmt-\/$>$row\+Count()==0)
\item 
\mbox{\hyperlink{check__assets_8php_a19d2a3d21fe02053311fde465e6ae2e9}{\$columns}} = \$stmt-\/$>$fetch\+All(PDO\+::\+FETCH\+\_\+\+ASSOC)
\item 
\mbox{\hyperlink{check__assets_8php_a8010c67e0e83cf2b8f3ebdde1dccbb0a}{foreach}} ( \$columns as \$column)
\item 
\mbox{\hyperlink{check__assets_8php_a112ef069ddc0454086e3d1e6d8d55d07}{\$result}} = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$result\mbox{[}\textquotesingle{}count\textquotesingle{}\mbox{]}==0) \mbox{\hyperlink{check__assets_8php_ae05cebee2dbf4f8a8285423197140090}{else}}
\item 
\mbox{\hyperlink{check__assets_8php_a2a1d24d79a2cd6e3ae6e2964fc4e8383}{\$assets}} = \$stmt-\/$>$fetch\+All(PDO\+::\+FETCH\+\_\+\+ASSOC)
\item 
\mbox{\hyperlink{check__assets_8php_a061d93912bbecbfcb1d588879a1c96d0}{foreach}} (array\+\_\+keys( \$assets\mbox{[}0\mbox{]}) as \$header)
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{check__assets_8php_a2a1d24d79a2cd6e3ae6e2964fc4e8383}\index{check\_assets.php@{check\_assets.php}!\$assets@{\$assets}}
\index{\$assets@{\$assets}!check\_assets.php@{check\_assets.php}}
\doxysubsubsection{\texorpdfstring{\$assets}{\$assets}}
{\footnotesize\ttfamily \label{check__assets_8php_a2a1d24d79a2cd6e3ae6e2964fc4e8383} 
\$assets = \$stmt-\/$>$fetch\+All(PDO\+::\+FETCH\+\_\+\+ASSOC)}

\Hypertarget{check__assets_8php_a19d2a3d21fe02053311fde465e6ae2e9}\index{check\_assets.php@{check\_assets.php}!\$columns@{\$columns}}
\index{\$columns@{\$columns}!check\_assets.php@{check\_assets.php}}
\doxysubsubsection{\texorpdfstring{\$columns}{\$columns}}
{\footnotesize\ttfamily \label{check__assets_8php_a19d2a3d21fe02053311fde465e6ae2e9} 
\$columns = \$stmt-\/$>$fetch\+All(PDO\+::\+FETCH\+\_\+\+ASSOC)}

\Hypertarget{check__assets_8php_a112ef069ddc0454086e3d1e6d8d55d07}\index{check\_assets.php@{check\_assets.php}!\$result@{\$result}}
\index{\$result@{\$result}!check\_assets.php@{check\_assets.php}}
\doxysubsubsection{\texorpdfstring{\$result}{\$result}}
{\footnotesize\ttfamily \label{check__assets_8php_a112ef069ddc0454086e3d1e6d8d55d07} 
\$result = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)}

\Hypertarget{check__assets_8php_af27a9140d5f2658693e7fd107f716449}\index{check\_assets.php@{check\_assets.php}!\$stmt@{\$stmt}}
\index{\$stmt@{\$stmt}!check\_assets.php@{check\_assets.php}}
\doxysubsubsection{\texorpdfstring{\$stmt}{\$stmt}}
{\footnotesize\ttfamily \label{check__assets_8php_af27a9140d5f2658693e7fd107f716449} 
\$stmt = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}assets\textquotesingle{}"{})}

\Hypertarget{check__assets_8php_ae05cebee2dbf4f8a8285423197140090}\index{check\_assets.php@{check\_assets.php}!else@{else}}
\index{else@{else}!check\_assets.php@{check\_assets.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{check__assets_8php_ae05cebee2dbf4f8a8285423197140090} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$result\mbox{[} \textquotesingle{}count\textquotesingle{}\mbox{]}==0) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ }
\DoxyCodeLine{\ \ \ \ \ \ \ \ \mbox{\hyperlink{check__db_8php_af27a9140d5f2658693e7fd107f716449}{\$stmt}}\ =\ \mbox{\hyperlink{fix__guideline__implementation__table_8php_a5766efd703cef0e00bfc06b3f3acbe0e}{\$pdo}}-\/>query(\textcolor{stringliteral}{"{}SELECT\ *\ FROM\ assets\ LIMIT\ 3"{}})}

\end{DoxyCode}
\Hypertarget{check__assets_8php_a8010c67e0e83cf2b8f3ebdde1dccbb0a}\index{check\_assets.php@{check\_assets.php}!foreach@{foreach}}
\index{foreach@{foreach}!check\_assets.php@{check\_assets.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily \label{check__assets_8php_a8010c67e0e83cf2b8f3ebdde1dccbb0a} 
foreach(\$assets as \$asset) (\begin{DoxyParamCaption}\item[{}]{\$columns as}{}\end{DoxyParamCaption})}

\Hypertarget{check__assets_8php_a061d93912bbecbfcb1d588879a1c96d0}\index{check\_assets.php@{check\_assets.php}!foreach@{foreach}}
\index{foreach@{foreach}!check\_assets.php@{check\_assets.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily \label{check__assets_8php_a061d93912bbecbfcb1d588879a1c96d0} 
foreach(array\+\_\+keys(\$assets\mbox{[}0\mbox{]}) as \$header) (\begin{DoxyParamCaption}\item[{array\+\_\+keys(\$assets\mbox{[}0\mbox{]}) as}]{\$header}{}\end{DoxyParamCaption})}

\Hypertarget{check__assets_8php_a6f8cf61e47812c53a24ccf689c122172}\index{check\_assets.php@{check\_assets.php}!if@{if}}
\index{if@{if}!check\_assets.php@{check\_assets.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{check__assets_8php_a6f8cf61e47812c53a24ccf689c122172} 
if(\$stmt-\/$>$row\+Count()==0) (\begin{DoxyParamCaption}\item[{}]{\$stmt-\/$>$}{() = {\ttfamily =~0}}\end{DoxyParamCaption})}

\Hypertarget{check__assets_8php_abe4cc9788f52e49485473dc699537388}\index{check\_assets.php@{check\_assets.php}!try@{try}}
\index{try@{try}!check\_assets.php@{check\_assets.php}}
\doxysubsubsection{\texorpdfstring{try}{try}}
{\footnotesize\ttfamily \label{check__assets_8php_abe4cc9788f52e49485473dc699537388} 
try}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ }
\DoxyCodeLine{\ \ \ \ \mbox{\hyperlink{fix__guideline__implementation__table_8php_a5766efd703cef0e00bfc06b3f3acbe0e}{\$pdo}}\ =\ \textcolor{keyword}{new}\ PDO(\textcolor{stringliteral}{'mysql:host='}\ .\ \mbox{\hyperlink{config_8php_a293363d7988627f671958e2d908c202a}{DB\_HOST}}\ .\ \textcolor{stringliteral}{';dbname='}\ .\ \mbox{\hyperlink{config_8php_ab5db0d3504f917f268614c50b02c53e2}{DB\_NAME}},\ \mbox{\hyperlink{config_8php_a1d1d99f8e08f387d84fe9848f3357156}{DB\_USER}},\ \mbox{\hyperlink{config_8php_a8bb9c4546d91667cfa61879d83127a92}{DB\_PASS}})}

\end{DoxyCode}
