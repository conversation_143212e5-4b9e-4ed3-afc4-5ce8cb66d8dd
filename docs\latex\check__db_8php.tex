\doxysection{check\+\_\+db.\+php File Reference}
\hypertarget{check__db_8php}{}\label{check__db_8php}\index{check\_db.php@{check\_db.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{check__db_8php_abe4cc9788f52e49485473dc699537388}{try}}
\item 
\mbox{\hyperlink{check__db_8php_a9ee42195f2b26ca51b7b816b4f28113e}{catch}} (Exception \$\mbox{\hyperlink{output__helper_8php_a18d38faad6177eda235a3d9d28572984}{e}})
\item 
\mbox{\hyperlink{check__db_8php_af27a9140d5f2658693e7fd107f716449}{\$stmt}} = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}assets\textquotesingle{}"{})
\item 
\mbox{\hyperlink{check__db_8php_a6f8cf61e47812c53a24ccf689c122172}{if}} ( \$stmt-\/$>$row\+Count()==0)
\item 
\mbox{\hyperlink{check__db_8php_a112ef069ddc0454086e3d1e6d8d55d07}{\$result}} = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$result\mbox{[}\textquotesingle{}count\textquotesingle{}\mbox{]} $>$ 0) \mbox{\hyperlink{check__db_8php_a3750c7492008b48abc325797bf4cd5a6}{else}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{check__db_8php_a112ef069ddc0454086e3d1e6d8d55d07}\index{check\_db.php@{check\_db.php}!\$result@{\$result}}
\index{\$result@{\$result}!check\_db.php@{check\_db.php}}
\doxysubsubsection{\texorpdfstring{\$result}{\$result}}
{\footnotesize\ttfamily \label{check__db_8php_a112ef069ddc0454086e3d1e6d8d55d07} 
\$result = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)}

\Hypertarget{check__db_8php_af27a9140d5f2658693e7fd107f716449}\index{check\_db.php@{check\_db.php}!\$stmt@{\$stmt}}
\index{\$stmt@{\$stmt}!check\_db.php@{check\_db.php}}
\doxysubsubsection{\texorpdfstring{\$stmt}{\$stmt}}
{\footnotesize\ttfamily \label{check__db_8php_af27a9140d5f2658693e7fd107f716449} 
\$stmt = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}assets\textquotesingle{}"{})}

\Hypertarget{check__db_8php_a9ee42195f2b26ca51b7b816b4f28113e}\index{check\_db.php@{check\_db.php}!catch@{catch}}
\index{catch@{catch}!check\_db.php@{check\_db.php}}
\doxysubsubsection{\texorpdfstring{catch}{catch}}
{\footnotesize\ttfamily \label{check__db_8php_a9ee42195f2b26ca51b7b816b4f28113e} 
catch(Exception \$\mbox{\hyperlink{output__helper_8php_a18d38faad6177eda235a3d9d28572984}{e}}) (\begin{DoxyParamCaption}\item[{Exception}]{\$e}{}\end{DoxyParamCaption})}

\Hypertarget{check__db_8php_a3750c7492008b48abc325797bf4cd5a6}\index{check\_db.php@{check\_db.php}!else@{else}}
\index{else@{else}!check\_db.php@{check\_db.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{check__db_8php_a3750c7492008b48abc325797bf4cd5a6} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$result\mbox{[} \textquotesingle{}count\textquotesingle{}\mbox{]} $>$ 0) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ echo\ \textcolor{stringliteral}{"{}<strong\ style='color:red'>No\ assets\ found\ in\ the\ database.</strong><br>"{}}}

\end{DoxyCode}
\Hypertarget{check__db_8php_a6f8cf61e47812c53a24ccf689c122172}\index{check\_db.php@{check\_db.php}!if@{if}}
\index{if@{if}!check\_db.php@{check\_db.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{check__db_8php_a6f8cf61e47812c53a24ccf689c122172} 
if(\$stmt-\/$>$row\+Count()==0) (\begin{DoxyParamCaption}\item[{}]{\$stmt-\/$>$}{() = {\ttfamily =~0}}\end{DoxyParamCaption})}

\Hypertarget{check__db_8php_abe4cc9788f52e49485473dc699537388}\index{check\_db.php@{check\_db.php}!try@{try}}
\index{try@{try}!check\_db.php@{check\_db.php}}
\doxysubsubsection{\texorpdfstring{try}{try}}
{\footnotesize\ttfamily \label{check__db_8php_abe4cc9788f52e49485473dc699537388} 
try}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ require\_once\ \textcolor{stringliteral}{'app/config/config.php'}}

\end{DoxyCode}
