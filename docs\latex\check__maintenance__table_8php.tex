\doxysection{check\+\_\+maintenance\+\_\+table.\+php File Reference}
\hypertarget{check__maintenance__table_8php}{}\label{check__maintenance__table_8php}\index{check\_maintenance\_table.php@{check\_maintenance\_table.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{check__maintenance__table_8php_abe4cc9788f52e49485473dc699537388}{try}}
\item 
\mbox{\hyperlink{check__maintenance__table_8php_a73d05e91025770cf57df81f52e8d58d8}{\$stmt}} = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}maintenance\+\_\+guideline\+\_\+implementation\textquotesingle{}"{})
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$stmt-\/$>$row\+Count()==0) \mbox{\hyperlink{check__maintenance__table_8php_a3750c7492008b48abc325797bf4cd5a6}{else}}
\item 
\mbox{\hyperlink{check__maintenance__table_8php_a112ef069ddc0454086e3d1e6d8d55d07}{\$result}} = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{check__maintenance__table_8php_a112ef069ddc0454086e3d1e6d8d55d07}\index{check\_maintenance\_table.php@{check\_maintenance\_table.php}!\$result@{\$result}}
\index{\$result@{\$result}!check\_maintenance\_table.php@{check\_maintenance\_table.php}}
\doxysubsubsection{\texorpdfstring{\$result}{\$result}}
{\footnotesize\ttfamily \label{check__maintenance__table_8php_a112ef069ddc0454086e3d1e6d8d55d07} 
\$result = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)}

\Hypertarget{check__maintenance__table_8php_a73d05e91025770cf57df81f52e8d58d8}\index{check\_maintenance\_table.php@{check\_maintenance\_table.php}!\$stmt@{\$stmt}}
\index{\$stmt@{\$stmt}!check\_maintenance\_table.php@{check\_maintenance\_table.php}}
\doxysubsubsection{\texorpdfstring{\$stmt}{\$stmt}}
{\footnotesize\ttfamily \label{check__maintenance__table_8php_a73d05e91025770cf57df81f52e8d58d8} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$stmt-\/$>$row\+Count() $>$ 0) \$stmt = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}maintenance\+\_\+guideline\+\_\+implementation\textquotesingle{}"{})}

\Hypertarget{check__maintenance__table_8php_a3750c7492008b48abc325797bf4cd5a6}\index{check\_maintenance\_table.php@{check\_maintenance\_table.php}!else@{else}}
\index{else@{else}!check\_maintenance\_table.php@{check\_maintenance\_table.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{check__maintenance__table_8php_a3750c7492008b48abc325797bf4cd5a6} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$result\mbox{[} \textquotesingle{}count\textquotesingle{}\mbox{]} $>$ 0) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ echo\ \textcolor{stringliteral}{"{}<p\ style='color:green'>maintenance\_guideline\_implementation\ table\ exists.</p>"{}}}

\end{DoxyCode}
\Hypertarget{check__maintenance__table_8php_abe4cc9788f52e49485473dc699537388}\index{check\_maintenance\_table.php@{check\_maintenance\_table.php}!try@{try}}
\index{try@{try}!check\_maintenance\_table.php@{check\_maintenance\_table.php}}
\doxysubsubsection{\texorpdfstring{try}{try}}
{\footnotesize\ttfamily \label{check__maintenance__table_8php_abe4cc9788f52e49485473dc699537388} 
try}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ }
\DoxyCodeLine{\ \ \ \ \mbox{\hyperlink{fix__guideline__implementation__table_8php_a5766efd703cef0e00bfc06b3f3acbe0e}{\$pdo}}\ =\ \textcolor{keyword}{new}\ PDO(\textcolor{stringliteral}{'mysql:host='}\ .\ \mbox{\hyperlink{config_8php_a293363d7988627f671958e2d908c202a}{DB\_HOST}}\ .\ \textcolor{stringliteral}{';dbname='}\ .\ \mbox{\hyperlink{config_8php_ab5db0d3504f917f268614c50b02c53e2}{DB\_NAME}},\ \mbox{\hyperlink{config_8php_a1d1d99f8e08f387d84fe9848f3357156}{DB\_USER}},\ \mbox{\hyperlink{config_8php_a8bb9c4546d91667cfa61879d83127a92}{DB\_PASS}})}

\end{DoxyCode}
