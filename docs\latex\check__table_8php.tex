\doxysection{check\+\_\+table.\+php File Reference}
\hypertarget{check__table_8php}{}\label{check__table_8php}\index{check\_table.php@{check\_table.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{check__table_8php_abe4cc9788f52e49485473dc699537388}{try}}
\item 
\mbox{\hyperlink{check__table_8php_af27a9140d5f2658693e7fd107f716449}{\$stmt}} = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}maintenance\+\_\+guideline\+\_\+implementation\textquotesingle{}"{})
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{check__table_8php_af27a9140d5f2658693e7fd107f716449}\index{check\_table.php@{check\_table.php}!\$stmt@{\$stmt}}
\index{\$stmt@{\$stmt}!check\_table.php@{check\_table.php}}
\doxysubsubsection{\texorpdfstring{\$stmt}{\$stmt}}
{\footnotesize\ttfamily \label{check__table_8php_af27a9140d5f2658693e7fd107f716449} 
\$stmt = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}maintenance\+\_\+guideline\+\_\+implementation\textquotesingle{}"{})}

\Hypertarget{check__table_8php_abe4cc9788f52e49485473dc699537388}\index{check\_table.php@{check\_table.php}!try@{try}}
\index{try@{try}!check\_table.php@{check\_table.php}}
\doxysubsubsection{\texorpdfstring{try}{try}}
{\footnotesize\ttfamily \label{check__table_8php_abe4cc9788f52e49485473dc699537388} 
try}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \mbox{\hyperlink{fix__guideline__implementation__table_8php_a5766efd703cef0e00bfc06b3f3acbe0e}{\$pdo}}\ =\ \textcolor{keyword}{new}\ PDO(\textcolor{stringliteral}{'mysql:host=localhost;dbname=asset\_visibility'},\ \textcolor{stringliteral}{'root'},\ \textcolor{stringliteral}{''})}

\end{DoxyCode}
