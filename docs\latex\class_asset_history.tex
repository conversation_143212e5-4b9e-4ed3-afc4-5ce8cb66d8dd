\doxysection{Asset\+History Class Reference}
\hypertarget{class_asset_history}{}\label{class_asset_history}\index{AssetHistory@{AssetHistory}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_asset_history_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_asset_history_ae9a1c316aa86b83e83209d7c2435d841}{add\+Record}} (\$asset\+Id, \$field\+Name, \$old\+Value, \$new\+Value, \$changed\+By)
\item 
\mbox{\hyperlink{class_asset_history_a7d272bd5a18c11c185325475ba86fe99}{get\+Asset\+History}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_asset_history_a526b9186f52bfa9f469457ee828e1968}{get\+Recent\+Changes}} (\$limit=10)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_asset_history_a095c5d389db211932136b53f25f39685}\index{AssetHistory@{AssetHistory}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!AssetHistory@{AssetHistory}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_asset_history_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_asset_history_ae9a1c316aa86b83e83209d7c2435d841}\index{AssetHistory@{AssetHistory}!addRecord@{addRecord}}
\index{addRecord@{addRecord}!AssetHistory@{AssetHistory}}
\doxysubsubsection{\texorpdfstring{addRecord()}{addRecord()}}
{\footnotesize\ttfamily \label{class_asset_history_ae9a1c316aa86b83e83209d7c2435d841} 
add\+Record (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{, }\item[{}]{\$field\+Name}{, }\item[{}]{\$old\+Value}{, }\item[{}]{\$new\+Value}{, }\item[{}]{\$changed\+By}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_history_a7d272bd5a18c11c185325475ba86fe99}\index{AssetHistory@{AssetHistory}!getAssetHistory@{getAssetHistory}}
\index{getAssetHistory@{getAssetHistory}!AssetHistory@{AssetHistory}}
\doxysubsubsection{\texorpdfstring{getAssetHistory()}{getAssetHistory()}}
{\footnotesize\ttfamily \label{class_asset_history_a7d272bd5a18c11c185325475ba86fe99} 
get\+Asset\+History (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

\Hypertarget{class_asset_history_a526b9186f52bfa9f469457ee828e1968}\index{AssetHistory@{AssetHistory}!getRecentChanges@{getRecentChanges}}
\index{getRecentChanges@{getRecentChanges}!AssetHistory@{AssetHistory}}
\doxysubsubsection{\texorpdfstring{getRecentChanges()}{getRecentChanges()}}
{\footnotesize\ttfamily \label{class_asset_history_a526b9186f52bfa9f469457ee828e1968} 
get\+Recent\+Changes (\begin{DoxyParamCaption}\item[{}]{\$limit}{ = {\ttfamily 10}}\end{DoxyParamCaption})}



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{_asset_history_8php}{Asset\+History.\+php}}\end{DoxyCompactItemize}
