\doxysection{Compliance Class Reference}
\hypertarget{class_compliance}{}\label{class_compliance}\index{Compliance@{Compliance}}
Inheritance diagram for Compliance\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{class_compliance}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_compliance_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_compliance_a149eb92716c1084a935e04a8d95f7347}{index}} ()
\item 
\mbox{\hyperlink{class_compliance_aaa462bba054828b894e52f915ead6345}{framework}} (\$framework\+Id)
\item 
\mbox{\hyperlink{class_compliance_aa9712d2ec27afddb5c060abd82a8fc39}{asset}} (\$asset\+Id, \$framework\+Id)
\item 
\mbox{\hyperlink{class_compliance_ad98cc30b3386d3b1e3ceda687803ede2}{update}} (\$asset\+Id, \$control\+Id)
\item 
\mbox{\hyperlink{class_compliance_aa33e9836cc6af4b321a1586206e77193}{generate}} (\$framework\+Id)
\item 
\mbox{\hyperlink{class_compliance_ac7912ba6d06a4a6cf2a4b9b59a42348a}{report}} (\$report\+Id)
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_ac531eb761b130b1925a8bae5c33af2fc}{model}} (\$model)
\item 
\mbox{\hyperlink{class_controller_a11f0e20b30b899d00b009a9bb1afe43d}{view}} (\$view, \$data=\mbox{[}$\,$\mbox{]})
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Protected Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_a0d92de8136cebc006a407442aab9db0a}{sanitize\+Post\+Data}} (\$data)
\item 
\mbox{\hyperlink{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52}{validate\+Csrf\+Token}} (\$token)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_compliance_a095c5d389db211932136b53f25f39685}\index{Compliance@{Compliance}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Compliance@{Compliance}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_compliance_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_compliance_aa9712d2ec27afddb5c060abd82a8fc39}\index{Compliance@{Compliance}!asset@{asset}}
\index{asset@{asset}!Compliance@{Compliance}}
\doxysubsubsection{\texorpdfstring{asset()}{asset()}}
{\footnotesize\ttfamily \label{class_compliance_aa9712d2ec27afddb5c060abd82a8fc39} 
asset (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{, }\item[{}]{\$framework\+Id}{}\end{DoxyParamCaption})}

View asset compliance details


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
int & {\em \$framework\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_compliance_aaa462bba054828b894e52f915ead6345}\index{Compliance@{Compliance}!framework@{framework}}
\index{framework@{framework}!Compliance@{Compliance}}
\doxysubsubsection{\texorpdfstring{framework()}{framework()}}
{\footnotesize\ttfamily \label{class_compliance_aaa462bba054828b894e52f915ead6345} 
framework (\begin{DoxyParamCaption}\item[{}]{\$framework\+Id}{}\end{DoxyParamCaption})}

View compliance dashboard for a specific framework


\begin{DoxyParams}[1]{Parameters}
int & {\em \$framework\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_compliance_aa33e9836cc6af4b321a1586206e77193}\index{Compliance@{Compliance}!generate@{generate}}
\index{generate@{generate}!Compliance@{Compliance}}
\doxysubsubsection{\texorpdfstring{generate()}{generate()}}
{\footnotesize\ttfamily \label{class_compliance_aa33e9836cc6af4b321a1586206e77193} 
generate (\begin{DoxyParamCaption}\item[{}]{\$framework\+Id}{}\end{DoxyParamCaption})}

Generate compliance report


\begin{DoxyParams}[1]{Parameters}
int & {\em \$framework\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_compliance_a149eb92716c1084a935e04a8d95f7347}\index{Compliance@{Compliance}!index@{index}}
\index{index@{index}!Compliance@{Compliance}}
\doxysubsubsection{\texorpdfstring{index()}{index()}}
{\footnotesize\ttfamily \label{class_compliance_a149eb92716c1084a935e04a8d95f7347} 
index (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\doxylink{class_compliance}{Compliance} dashboard \Hypertarget{class_compliance_ac7912ba6d06a4a6cf2a4b9b59a42348a}\index{Compliance@{Compliance}!report@{report}}
\index{report@{report}!Compliance@{Compliance}}
\doxysubsubsection{\texorpdfstring{report()}{report()}}
{\footnotesize\ttfamily \label{class_compliance_ac7912ba6d06a4a6cf2a4b9b59a42348a} 
report (\begin{DoxyParamCaption}\item[{}]{\$report\+Id}{}\end{DoxyParamCaption})}

View compliance report


\begin{DoxyParams}[1]{Parameters}
int & {\em \$report\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_compliance_ad98cc30b3386d3b1e3ceda687803ede2}\index{Compliance@{Compliance}!update@{update}}
\index{update@{update}!Compliance@{Compliance}}
\doxysubsubsection{\texorpdfstring{update()}{update()}}
{\footnotesize\ttfamily \label{class_compliance_ad98cc30b3386d3b1e3ceda687803ede2} 
update (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{, }\item[{}]{\$control\+Id}{}\end{DoxyParamCaption})}

Update asset compliance status


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
int & {\em \$control\+Id} & \\
\hline
\end{DoxyParams}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/controllers/\mbox{\hyperlink{_compliance_8php}{Compliance.\+php}}\end{DoxyCompactItemize}
