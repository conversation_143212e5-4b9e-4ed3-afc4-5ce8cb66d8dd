\doxysection{Compliance\+Model Class Reference}
\hypertarget{class_compliance_model}{}\label{class_compliance_model}\index{ComplianceModel@{ComplianceModel}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_compliance_model_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_compliance_model_a48611d2953815ea9e440be48270e5b41}{get\+Frameworks}} ()
\item 
\mbox{\hyperlink{class_compliance_model_a2a6f89d1e69db5f6f7ae1f97dde53d2e}{get\+Framework\+By\+Id}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_compliance_model_aeac9b1b5819ae5e2970cfbd1a7597f76}{get\+Controls\+By\+Framework}} (\$framework\+Id)
\item 
\mbox{\hyperlink{class_compliance_model_a7feafecf43729d726a17800a2274562f}{get\+Asset\+Compliance\+Status}} (\$asset\+Id, \$framework\+Id)
\item 
\mbox{\hyperlink{class_compliance_model_a77ab529e36423aae6bc0a5b8f5cc6b7a}{update\+Asset\+Compliance}} (\$data)
\item 
\mbox{\hyperlink{class_compliance_model_a814c5d44551a1e675a00c71ca3742297}{get\+Compliance\+Summary}} (\$framework\+Id)
\item 
\mbox{\hyperlink{class_compliance_model_a06766d64303589b753c200226d796052}{get\+Compliance\+Summary\+By\+Control}} (\$framework\+Id)
\item 
\mbox{\hyperlink{class_compliance_model_acc7bd2f33a48df1f96004c6ac83bc452}{generate\+Report}} (\$framework\+Id, \$user\+Id)
\item 
\mbox{\hyperlink{class_compliance_model_a7276523dbcf22869eb572eaf69c1e792}{get\+Report\+By\+Id}} (\$report\+Id)
\item 
\mbox{\hyperlink{class_compliance_model_a3f36be93a3051b76fd781c4d27576bae}{get\+Recent\+Reports}} (\$limit=5)
\item 
\mbox{\hyperlink{class_compliance_model_ad933a2ed9c35310e79d219954f8fed3e}{get\+Non\+Compliant\+Assets}} (\$framework\+Id, \$limit=10)
\item 
\mbox{\hyperlink{class_compliance_model_a0418c1b96081854db5489ed186ad1337}{get\+Control\+By\+Id}} (\$control\+Id)
\item 
\mbox{\hyperlink{class_compliance_model_a13e3ddd0e6d5a876f6d7b100a6f24acd}{get\+Asset\+Control\+Status}} (\$asset\+Id, \$control\+Id)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_compliance_model_a095c5d389db211932136b53f25f39685}\index{ComplianceModel@{ComplianceModel}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_compliance_model_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_compliance_model_acc7bd2f33a48df1f96004c6ac83bc452}\index{ComplianceModel@{ComplianceModel}!generateReport@{generateReport}}
\index{generateReport@{generateReport}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{generateReport()}{generateReport()}}
{\footnotesize\ttfamily \label{class_compliance_model_acc7bd2f33a48df1f96004c6ac83bc452} 
generate\+Report (\begin{DoxyParamCaption}\item[{}]{\$framework\+Id}{, }\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})}

Generate compliance report


\begin{DoxyParams}[1]{Parameters}
int & {\em \$framework\+Id} & \\
\hline
int & {\em \$user\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
int Report ID 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_a7feafecf43729d726a17800a2274562f}\index{ComplianceModel@{ComplianceModel}!getAssetComplianceStatus@{getAssetComplianceStatus}}
\index{getAssetComplianceStatus@{getAssetComplianceStatus}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{getAssetComplianceStatus()}{getAssetComplianceStatus()}}
{\footnotesize\ttfamily \label{class_compliance_model_a7feafecf43729d726a17800a2274562f} 
get\+Asset\+Compliance\+Status (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{, }\item[{}]{\$framework\+Id}{}\end{DoxyParamCaption})}

Get compliance status for an asset


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
int & {\em \$framework\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_a13e3ddd0e6d5a876f6d7b100a6f24acd}\index{ComplianceModel@{ComplianceModel}!getAssetControlStatus@{getAssetControlStatus}}
\index{getAssetControlStatus@{getAssetControlStatus}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{getAssetControlStatus()}{getAssetControlStatus()}}
{\footnotesize\ttfamily \label{class_compliance_model_a13e3ddd0e6d5a876f6d7b100a6f24acd} 
get\+Asset\+Control\+Status (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{, }\item[{}]{\$control\+Id}{}\end{DoxyParamCaption})}

Get compliance status for an asset and control


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
int & {\em \$control\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_a814c5d44551a1e675a00c71ca3742297}\index{ComplianceModel@{ComplianceModel}!getComplianceSummary@{getComplianceSummary}}
\index{getComplianceSummary@{getComplianceSummary}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{getComplianceSummary()}{getComplianceSummary()}}
{\footnotesize\ttfamily \label{class_compliance_model_a814c5d44551a1e675a00c71ca3742297} 
get\+Compliance\+Summary (\begin{DoxyParamCaption}\item[{}]{\$framework\+Id}{}\end{DoxyParamCaption})}

Get compliance summary for all assets


\begin{DoxyParams}[1]{Parameters}
int & {\em \$framework\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_a06766d64303589b753c200226d796052}\index{ComplianceModel@{ComplianceModel}!getComplianceSummaryByControl@{getComplianceSummaryByControl}}
\index{getComplianceSummaryByControl@{getComplianceSummaryByControl}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{getComplianceSummaryByControl()}{getComplianceSummaryByControl()}}
{\footnotesize\ttfamily \label{class_compliance_model_a06766d64303589b753c200226d796052} 
get\+Compliance\+Summary\+By\+Control (\begin{DoxyParamCaption}\item[{}]{\$framework\+Id}{}\end{DoxyParamCaption})}

Get compliance summary by control


\begin{DoxyParams}[1]{Parameters}
int & {\em \$framework\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_a0418c1b96081854db5489ed186ad1337}\index{ComplianceModel@{ComplianceModel}!getControlById@{getControlById}}
\index{getControlById@{getControlById}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{getControlById()}{getControlById()}}
{\footnotesize\ttfamily \label{class_compliance_model_a0418c1b96081854db5489ed186ad1337} 
get\+Control\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$control\+Id}{}\end{DoxyParamCaption})}

Get control by ID


\begin{DoxyParams}[1]{Parameters}
int & {\em \$control\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_aeac9b1b5819ae5e2970cfbd1a7597f76}\index{ComplianceModel@{ComplianceModel}!getControlsByFramework@{getControlsByFramework}}
\index{getControlsByFramework@{getControlsByFramework}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{getControlsByFramework()}{getControlsByFramework()}}
{\footnotesize\ttfamily \label{class_compliance_model_aeac9b1b5819ae5e2970cfbd1a7597f76} 
get\+Controls\+By\+Framework (\begin{DoxyParamCaption}\item[{}]{\$framework\+Id}{}\end{DoxyParamCaption})}

Get controls for a framework


\begin{DoxyParams}[1]{Parameters}
int & {\em \$framework\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_a2a6f89d1e69db5f6f7ae1f97dde53d2e}\index{ComplianceModel@{ComplianceModel}!getFrameworkById@{getFrameworkById}}
\index{getFrameworkById@{getFrameworkById}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{getFrameworkById()}{getFrameworkById()}}
{\footnotesize\ttfamily \label{class_compliance_model_a2a6f89d1e69db5f6f7ae1f97dde53d2e} 
get\+Framework\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Get framework by ID


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_a48611d2953815ea9e440be48270e5b41}\index{ComplianceModel@{ComplianceModel}!getFrameworks@{getFrameworks}}
\index{getFrameworks@{getFrameworks}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{getFrameworks()}{getFrameworks()}}
{\footnotesize\ttfamily \label{class_compliance_model_a48611d2953815ea9e440be48270e5b41} 
get\+Frameworks (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Get all compliance frameworks

\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_ad933a2ed9c35310e79d219954f8fed3e}\index{ComplianceModel@{ComplianceModel}!getNonCompliantAssets@{getNonCompliantAssets}}
\index{getNonCompliantAssets@{getNonCompliantAssets}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{getNonCompliantAssets()}{getNonCompliantAssets()}}
{\footnotesize\ttfamily \label{class_compliance_model_ad933a2ed9c35310e79d219954f8fed3e} 
get\+Non\+Compliant\+Assets (\begin{DoxyParamCaption}\item[{}]{\$framework\+Id}{, }\item[{}]{\$limit}{ = {\ttfamily 10}}\end{DoxyParamCaption})}

Get non-\/compliant assets


\begin{DoxyParams}[1]{Parameters}
int & {\em \$framework\+Id} & \\
\hline
int & {\em \$limit} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_a3f36be93a3051b76fd781c4d27576bae}\index{ComplianceModel@{ComplianceModel}!getRecentReports@{getRecentReports}}
\index{getRecentReports@{getRecentReports}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{getRecentReports()}{getRecentReports()}}
{\footnotesize\ttfamily \label{class_compliance_model_a3f36be93a3051b76fd781c4d27576bae} 
get\+Recent\+Reports (\begin{DoxyParamCaption}\item[{}]{\$limit}{ = {\ttfamily 5}}\end{DoxyParamCaption})}

Get recent reports


\begin{DoxyParams}[1]{Parameters}
int & {\em \$limit} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_a7276523dbcf22869eb572eaf69c1e792}\index{ComplianceModel@{ComplianceModel}!getReportById@{getReportById}}
\index{getReportById@{getReportById}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{getReportById()}{getReportById()}}
{\footnotesize\ttfamily \label{class_compliance_model_a7276523dbcf22869eb572eaf69c1e792} 
get\+Report\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$report\+Id}{}\end{DoxyParamCaption})}

Get report by ID


\begin{DoxyParams}[1]{Parameters}
int & {\em \$report\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object 
\end{DoxyReturn}
\Hypertarget{class_compliance_model_a77ab529e36423aae6bc0a5b8f5cc6b7a}\index{ComplianceModel@{ComplianceModel}!updateAssetCompliance@{updateAssetCompliance}}
\index{updateAssetCompliance@{updateAssetCompliance}!ComplianceModel@{ComplianceModel}}
\doxysubsubsection{\texorpdfstring{updateAssetCompliance()}{updateAssetCompliance()}}
{\footnotesize\ttfamily \label{class_compliance_model_a77ab529e36423aae6bc0a5b8f5cc6b7a} 
update\+Asset\+Compliance (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Update asset compliance status


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{_compliance_model_8php}{Compliance\+Model.\+php}}\end{DoxyCompactItemize}
