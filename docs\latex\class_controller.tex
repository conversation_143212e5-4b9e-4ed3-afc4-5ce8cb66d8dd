\doxysection{Controller Class Reference}
\hypertarget{class_controller}{}\label{class_controller}\index{Controller@{Controller}}
Inheritance diagram for Controller\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=12.000000cm]{class_controller}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_ac531eb761b130b1925a8bae5c33af2fc}{model}} (\$model)
\item 
\mbox{\hyperlink{class_controller_a11f0e20b30b899d00b009a9bb1afe43d}{view}} (\$view, \$data=\mbox{[}$\,$\mbox{]})
\end{DoxyCompactItemize}
\doxysubsubsection*{Protected Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_a0d92de8136cebc006a407442aab9db0a}{sanitize\+Post\+Data}} (\$data)
\item 
\mbox{\hyperlink{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52}{validate\+Csrf\+Token}} (\$token)
\end{DoxyCompactItemize}


\doxysubsection{Member Function Documentation}
\Hypertarget{class_controller_ac531eb761b130b1925a8bae5c33af2fc}\index{Controller@{Controller}!model@{model}}
\index{model@{model}!Controller@{Controller}}
\doxysubsubsection{\texorpdfstring{model()}{model()}}
{\footnotesize\ttfamily \label{class_controller_ac531eb761b130b1925a8bae5c33af2fc} 
model (\begin{DoxyParamCaption}\item[{}]{\$model}{}\end{DoxyParamCaption})}

Load a model

Dynamically loads and instantiates a model class.


\begin{DoxyParams}[1]{Parameters}
string & {\em \$model} & The name of the model to load \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object The instantiated model object 
\end{DoxyReturn}

\begin{DoxyExceptions}{Exceptions}
{\em Exception} & If the model file doesn\textquotesingle{}t exist \\
\hline
\end{DoxyExceptions}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_controller_a0d92de8136cebc006a407442aab9db0a}\index{Controller@{Controller}!sanitizePostData@{sanitizePostData}}
\index{sanitizePostData@{sanitizePostData}!Controller@{Controller}}
\doxysubsubsection{\texorpdfstring{sanitizePostData()}{sanitizePostData()}}
{\footnotesize\ttfamily \label{class_controller_a0d92de8136cebc006a407442aab9db0a} 
sanitize\+Post\+Data (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [protected]}}

Sanitize POST data to prevent XSS attacks

Uses the \doxylink{class_security}{Security} class to sanitize input data and prevent cross-\/site scripting (XSS) attacks.


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & The POST data to sanitize \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array The sanitized data 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52}\index{Controller@{Controller}!validateCsrfToken@{validateCsrfToken}}
\index{validateCsrfToken@{validateCsrfToken}!Controller@{Controller}}
\doxysubsubsection{\texorpdfstring{validateCsrfToken()}{validateCsrfToken()}}
{\footnotesize\ttfamily \label{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52} 
validate\+Csrf\+Token (\begin{DoxyParamCaption}\item[{}]{\$token}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [protected]}}

Validate CSRF token

Validates a CSRF token to prevent cross-\/site request forgery attacks. Automatically redirects on validation failure.


\begin{DoxyParams}[1]{Parameters}
string & {\em \$token} & The token to validate \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if the token is valid, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_controller_a11f0e20b30b899d00b009a9bb1afe43d}\index{Controller@{Controller}!view@{view}}
\index{view@{view}!Controller@{Controller}}
\doxysubsubsection{\texorpdfstring{view()}{view()}}
{\footnotesize\ttfamily \label{class_controller_a11f0e20b30b899d00b009a9bb1afe43d} 
view (\begin{DoxyParamCaption}\item[{}]{\$view}{, }\item[{}]{\$data}{ = {\ttfamily \mbox{[}\mbox{]}}}\end{DoxyParamCaption})}

Load a view

Loads a view file and passes data to it. Automatically adds CSRF token to the data array for form security.


\begin{DoxyParams}[1]{Parameters}
string & {\em \$view} & The view file path (relative to views directory) \\
\hline
array & {\em \$data} & Associative array of data to pass to the view \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
void 
\end{DoxyReturn}

\begin{DoxyExceptions}{Exceptions}
{\em Exception} & If the view file doesn\textquotesingle{}t exist \\
\hline
\end{DoxyExceptions}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/core/\mbox{\hyperlink{_controller_8php}{Controller.\+php}}\end{DoxyCompactItemize}
