\doxysection{Core Class Reference}
\hypertarget{class_core}{}\label{class_core}\index{Core@{Core}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_core_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_core_accd14bda49a1044b4d8dd93f020f11ee}{get\+Url}} ()
\end{DoxyCompactItemize}
\doxysubsubsection*{Protected Attributes}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_core_aceeb7151993557713abb1c569b13a288}{\$current\+Controller}} = \textquotesingle{}\mbox{\hyperlink{class_pages}{Pages}}\textquotesingle{}
\item 
\mbox{\hyperlink{class_core_a02016be588373507ba01c56139b4ab87}{\$current\+Method}} = \textquotesingle{}index\textquotesingle{}
\item 
\mbox{\hyperlink{class_core_afe68e6fbe7acfbffc0af0c84a1996466}{\$params}} = \mbox{[}$\,$\mbox{]}
\end{DoxyCompactItemize}


\doxysubsection{Detailed Description}
App \doxylink{class_core}{Core} Class Creates URL \& loads core controller URL FORMAT -\/ /controller/method/params 

\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_core_a095c5d389db211932136b53f25f39685}\index{Core@{Core}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Core@{Core}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_core_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_core_accd14bda49a1044b4d8dd93f020f11ee}\index{Core@{Core}!getUrl@{getUrl}}
\index{getUrl@{getUrl}!Core@{Core}}
\doxysubsubsection{\texorpdfstring{getUrl()}{getUrl()}}
{\footnotesize\ttfamily \label{class_core_accd14bda49a1044b4d8dd93f020f11ee} 
get\+Url (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Field Documentation}
\Hypertarget{class_core_aceeb7151993557713abb1c569b13a288}\index{Core@{Core}!\$currentController@{\$currentController}}
\index{\$currentController@{\$currentController}!Core@{Core}}
\doxysubsubsection{\texorpdfstring{\$currentController}{\$currentController}}
{\footnotesize\ttfamily \label{class_core_aceeb7151993557713abb1c569b13a288} 
\$current\+Controller = \textquotesingle{}\mbox{\hyperlink{class_pages}{Pages}}\textquotesingle{}\hspace{0.3cm}{\ttfamily [protected]}}

\Hypertarget{class_core_a02016be588373507ba01c56139b4ab87}\index{Core@{Core}!\$currentMethod@{\$currentMethod}}
\index{\$currentMethod@{\$currentMethod}!Core@{Core}}
\doxysubsubsection{\texorpdfstring{\$currentMethod}{\$currentMethod}}
{\footnotesize\ttfamily \label{class_core_a02016be588373507ba01c56139b4ab87} 
\$current\+Method = \textquotesingle{}index\textquotesingle{}\hspace{0.3cm}{\ttfamily [protected]}}

\Hypertarget{class_core_afe68e6fbe7acfbffc0af0c84a1996466}\index{Core@{Core}!\$params@{\$params}}
\index{\$params@{\$params}!Core@{Core}}
\doxysubsubsection{\texorpdfstring{\$params}{\$params}}
{\footnotesize\ttfamily \label{class_core_afe68e6fbe7acfbffc0af0c84a1996466} 
\$params = \mbox{[}$\,$\mbox{]}\hspace{0.3cm}{\ttfamily [protected]}}



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/core/\mbox{\hyperlink{_core_8php}{Core.\+php}}\end{DoxyCompactItemize}
