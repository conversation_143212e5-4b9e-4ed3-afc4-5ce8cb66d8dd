\doxysection{Dashboard Class Reference}
\hypertarget{class_dashboard}{}\label{class_dashboard}\index{Dashboard@{Dashboard}}
Inheritance diagram for Dashboard\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{class_dashboard}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_dashboard_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_dashboard_a149eb92716c1084a935e04a8d95f7347}{index}} ()
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_ac531eb761b130b1925a8bae5c33af2fc}{model}} (\$model)
\item 
\mbox{\hyperlink{class_controller_a11f0e20b30b899d00b009a9bb1afe43d}{view}} (\$view, \$data=\mbox{[}$\,$\mbox{]})
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Protected Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_a0d92de8136cebc006a407442aab9db0a}{sanitize\+Post\+Data}} (\$data)
\item 
\mbox{\hyperlink{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52}{validate\+Csrf\+Token}} (\$token)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_dashboard_a095c5d389db211932136b53f25f39685}\index{Dashboard@{Dashboard}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Dashboard@{Dashboard}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_dashboard_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_dashboard_a149eb92716c1084a935e04a8d95f7347}\index{Dashboard@{Dashboard}!index@{index}}
\index{index@{index}!Dashboard@{Dashboard}}
\doxysubsubsection{\texorpdfstring{index()}{index()}}
{\footnotesize\ttfamily \label{class_dashboard_a149eb92716c1084a935e04a8d95f7347} 
index (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/controllers/\mbox{\hyperlink{_dashboard_8php}{Dashboard.\+php}}\end{DoxyCompactItemize}
