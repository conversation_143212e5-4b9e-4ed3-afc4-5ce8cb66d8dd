\doxysection{Error\+Log Class Reference}
\hypertarget{class_error_log}{}\label{class_error_log}\index{ErrorLog@{ErrorLog}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_error_log_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_error_log_ab5a561b39013b248d4be5a42f5fa8f48}{ensure\+Error\+Logs\+Table\+Exists}} ()
\item 
\mbox{\hyperlink{class_error_log_ac8d4999f0e2ad3fe19b0bace0e159393}{log\+Error}} (\$level, \$message, \$context=null, \$file=null, \$line=null, \$trace=null)
\item 
\mbox{\hyperlink{class_error_log_a3f92688780dc6f24f4edc5aefdfd7dfe}{get\+Error\+Logs}} (\$limit=20, \$offset=0, \$level=null, \$search=null)
\item 
\mbox{\hyperlink{class_error_log_abba5797fcf8825cdb1a7638e090fbb94}{count\+Error\+Logs}} (\$level=null, \$search=null)
\item 
\mbox{\hyperlink{class_error_log_a3e0cd66d937aa3255f4c60b887f29f3f}{get\+Error\+Log\+By\+Id}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_error_log_a8faa630c237d7353b199e42831e1f80b}{delete\+Error\+Log}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_error_log_a6979c1ed6251a51c8515d3bfacfb8d02}{clear\+Error\+Logs}} ()
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_error_log_a095c5d389db211932136b53f25f39685}\index{ErrorLog@{ErrorLog}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!ErrorLog@{ErrorLog}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_error_log_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_error_log_a6979c1ed6251a51c8515d3bfacfb8d02}\index{ErrorLog@{ErrorLog}!clearErrorLogs@{clearErrorLogs}}
\index{clearErrorLogs@{clearErrorLogs}!ErrorLog@{ErrorLog}}
\doxysubsubsection{\texorpdfstring{clearErrorLogs()}{clearErrorLogs()}}
{\footnotesize\ttfamily \label{class_error_log_a6979c1ed6251a51c8515d3bfacfb8d02} 
clear\+Error\+Logs (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Clear all error logs

\begin{DoxyReturn}{Returns}
bool True on success, false on failure 
\end{DoxyReturn}
\Hypertarget{class_error_log_abba5797fcf8825cdb1a7638e090fbb94}\index{ErrorLog@{ErrorLog}!countErrorLogs@{countErrorLogs}}
\index{countErrorLogs@{countErrorLogs}!ErrorLog@{ErrorLog}}
\doxysubsubsection{\texorpdfstring{countErrorLogs()}{countErrorLogs()}}
{\footnotesize\ttfamily \label{class_error_log_abba5797fcf8825cdb1a7638e090fbb94} 
count\+Error\+Logs (\begin{DoxyParamCaption}\item[{}]{\$level}{ = {\ttfamily null}, }\item[{}]{\$search}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Count all error logs


\begin{DoxyParams}[1]{Parameters}
string & {\em \$level} & Filter by error level \\
\hline
string & {\em \$search} & Search term \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
int 
\end{DoxyReturn}
\Hypertarget{class_error_log_a8faa630c237d7353b199e42831e1f80b}\index{ErrorLog@{ErrorLog}!deleteErrorLog@{deleteErrorLog}}
\index{deleteErrorLog@{deleteErrorLog}!ErrorLog@{ErrorLog}}
\doxysubsubsection{\texorpdfstring{deleteErrorLog()}{deleteErrorLog()}}
{\footnotesize\ttfamily \label{class_error_log_a8faa630c237d7353b199e42831e1f80b} 
delete\+Error\+Log (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Delete error log by ID


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & Error log ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True on success, false on failure 
\end{DoxyReturn}
\Hypertarget{class_error_log_ab5a561b39013b248d4be5a42f5fa8f48}\index{ErrorLog@{ErrorLog}!ensureErrorLogsTableExists@{ensureErrorLogsTableExists}}
\index{ensureErrorLogsTableExists@{ensureErrorLogsTableExists}!ErrorLog@{ErrorLog}}
\doxysubsubsection{\texorpdfstring{ensureErrorLogsTableExists()}{ensureErrorLogsTableExists()}}
{\footnotesize\ttfamily \label{class_error_log_ab5a561b39013b248d4be5a42f5fa8f48} 
ensure\+Error\+Logs\+Table\+Exists (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Ensure the error\+\_\+logs table exists This is called once during initialization to prevent repeated table creation

\begin{DoxyReturn}{Returns}
bool True if the table exists or was created successfully 
\end{DoxyReturn}
\Hypertarget{class_error_log_a3e0cd66d937aa3255f4c60b887f29f3f}\index{ErrorLog@{ErrorLog}!getErrorLogById@{getErrorLogById}}
\index{getErrorLogById@{getErrorLogById}!ErrorLog@{ErrorLog}}
\doxysubsubsection{\texorpdfstring{getErrorLogById()}{getErrorLogById()}}
{\footnotesize\ttfamily \label{class_error_log_a3e0cd66d937aa3255f4c60b887f29f3f} 
get\+Error\+Log\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Get error log by ID


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & Error log ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object\texorpdfstring{$\vert$}{|}bool The error log or false on failure 
\end{DoxyReturn}
\Hypertarget{class_error_log_a3f92688780dc6f24f4edc5aefdfd7dfe}\index{ErrorLog@{ErrorLog}!getErrorLogs@{getErrorLogs}}
\index{getErrorLogs@{getErrorLogs}!ErrorLog@{ErrorLog}}
\doxysubsubsection{\texorpdfstring{getErrorLogs()}{getErrorLogs()}}
{\footnotesize\ttfamily \label{class_error_log_a3f92688780dc6f24f4edc5aefdfd7dfe} 
get\+Error\+Logs (\begin{DoxyParamCaption}\item[{}]{\$limit}{ = {\ttfamily 20}, }\item[{}]{\$offset}{ = {\ttfamily 0}, }\item[{}]{\$level}{ = {\ttfamily null}, }\item[{}]{\$search}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Get all error logs with pagination


\begin{DoxyParams}[1]{Parameters}
int & {\em \$limit} & Number of records to return \\
\hline
int & {\em \$offset} & Offset for pagination \\
\hline
string & {\em \$level} & Filter by error level \\
\hline
string & {\em \$search} & Search term \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_error_log_ac8d4999f0e2ad3fe19b0bace0e159393}\index{ErrorLog@{ErrorLog}!logError@{logError}}
\index{logError@{logError}!ErrorLog@{ErrorLog}}
\doxysubsubsection{\texorpdfstring{logError()}{logError()}}
{\footnotesize\ttfamily \label{class_error_log_ac8d4999f0e2ad3fe19b0bace0e159393} 
log\+Error (\begin{DoxyParamCaption}\item[{}]{\$level}{, }\item[{}]{\$message}{, }\item[{}]{\$context}{ = {\ttfamily null}, }\item[{}]{\$file}{ = {\ttfamily null}, }\item[{}]{\$line}{ = {\ttfamily null}, }\item[{}]{\$trace}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Log an error to the database


\begin{DoxyParams}[1]{Parameters}
string & {\em \$level} & Error level (error, warning, info, debug) \\
\hline
string & {\em \$message} & Error message \\
\hline
array & {\em \$context} & Additional context data \\
\hline
string & {\em \$file} & File where the error occurred \\
\hline
int & {\em \$line} & Line number where the error occurred \\
\hline
string & {\em \$trace} & Stack trace \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
int\texorpdfstring{$\vert$}{|}bool The new error log ID or false on failure 
\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{_error_log_8php}{Error\+Log.\+php}}\end{DoxyCompactItemize}
