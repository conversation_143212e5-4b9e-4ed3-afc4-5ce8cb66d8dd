\doxysection{Error\+Logs Class Reference}
\hypertarget{class_error_logs}{}\label{class_error_logs}\index{ErrorLogs@{ErrorLogs}}
Inheritance diagram for Error\+Logs\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{class_error_logs}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_error_logs_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_error_logs_a65c682611e5c929cdacbce2ff322c9ce}{index}} (\$page=1)
\item 
\mbox{\hyperlink{class_error_logs_a93a99cdd06e6a3044ed7e56db0f9419a}{view}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_error_logs_a2f8258add505482d7f00ea26493a5723}{delete}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_error_logs_aa821bec12eaa7e0f649397c9675ff505}{clear}} ()
\item 
\mbox{\hyperlink{class_error_logs_ad69dd4607977cae05ebe19d1ae604fb1}{test}} ()
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_ac531eb761b130b1925a8bae5c33af2fc}{model}} (\$model)
\item 
\mbox{\hyperlink{class_controller_a11f0e20b30b899d00b009a9bb1afe43d}{view}} (\$view, \$data=\mbox{[}$\,$\mbox{]})
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Protected Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_a0d92de8136cebc006a407442aab9db0a}{sanitize\+Post\+Data}} (\$data)
\item 
\mbox{\hyperlink{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52}{validate\+Csrf\+Token}} (\$token)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_error_logs_a095c5d389db211932136b53f25f39685}\index{ErrorLogs@{ErrorLogs}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!ErrorLogs@{ErrorLogs}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_error_logs_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_error_logs_aa821bec12eaa7e0f649397c9675ff505}\index{ErrorLogs@{ErrorLogs}!clear@{clear}}
\index{clear@{clear}!ErrorLogs@{ErrorLogs}}
\doxysubsubsection{\texorpdfstring{clear()}{clear()}}
{\footnotesize\ttfamily \label{class_error_logs_aa821bec12eaa7e0f649397c9675ff505} 
clear (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Clear all error logs \Hypertarget{class_error_logs_a2f8258add505482d7f00ea26493a5723}\index{ErrorLogs@{ErrorLogs}!delete@{delete}}
\index{delete@{delete}!ErrorLogs@{ErrorLogs}}
\doxysubsubsection{\texorpdfstring{delete()}{delete()}}
{\footnotesize\ttfamily \label{class_error_logs_a2f8258add505482d7f00ea26493a5723} 
delete (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Delete error log


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & Error log ID \\
\hline
\end{DoxyParams}
\Hypertarget{class_error_logs_a65c682611e5c929cdacbce2ff322c9ce}\index{ErrorLogs@{ErrorLogs}!index@{index}}
\index{index@{index}!ErrorLogs@{ErrorLogs}}
\doxysubsubsection{\texorpdfstring{index()}{index()}}
{\footnotesize\ttfamily \label{class_error_logs_a65c682611e5c929cdacbce2ff322c9ce} 
index (\begin{DoxyParamCaption}\item[{}]{\$page}{ = {\ttfamily 1}}\end{DoxyParamCaption})}

Display error logs with pagination and filtering


\begin{DoxyParams}[1]{Parameters}
int & {\em \$page} & Current page number \\
\hline
\end{DoxyParams}
\Hypertarget{class_error_logs_ad69dd4607977cae05ebe19d1ae604fb1}\index{ErrorLogs@{ErrorLogs}!test@{test}}
\index{test@{test}!ErrorLogs@{ErrorLogs}}
\doxysubsubsection{\texorpdfstring{test()}{test()}}
{\footnotesize\ttfamily \label{class_error_logs_ad69dd4607977cae05ebe19d1ae604fb1} 
test (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Test error logging \Hypertarget{class_error_logs_a93a99cdd06e6a3044ed7e56db0f9419a}\index{ErrorLogs@{ErrorLogs}!view@{view}}
\index{view@{view}!ErrorLogs@{ErrorLogs}}
\doxysubsubsection{\texorpdfstring{view()}{view()}}
{\footnotesize\ttfamily \label{class_error_logs_a93a99cdd06e6a3044ed7e56db0f9419a} 
view (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

View error log details


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & Error log ID \\
\hline
\end{DoxyParams}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/controllers/\mbox{\hyperlink{_error_logs_8php}{Error\+Logs.\+php}}\end{DoxyCompactItemize}
