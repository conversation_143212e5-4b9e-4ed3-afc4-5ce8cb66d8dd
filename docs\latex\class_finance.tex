\doxysection{Finance Class Reference}
\hypertarget{class_finance}{}\label{class_finance}\index{Finance@{Finance}}
Inheritance diagram for Finance\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{class_finance}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_finance_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_finance_a149eb92716c1084a935e04a8d95f7347}{index}} ()
\item 
\mbox{\hyperlink{class_finance_a70fafc7500687f64af7bf492d3a13c3c}{asset}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_finance_a2518a250e7a0164d46b5257b2c2f3922}{add\+Cost}} (\$asset\+Id=null)
\item 
\mbox{\hyperlink{class_finance_aada921e0a30211fde5a19573e9825c1f}{budget}} ()
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_ac531eb761b130b1925a8bae5c33af2fc}{model}} (\$model)
\item 
\mbox{\hyperlink{class_controller_a11f0e20b30b899d00b009a9bb1afe43d}{view}} (\$view, \$data=\mbox{[}$\,$\mbox{]})
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Protected Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_a0d92de8136cebc006a407442aab9db0a}{sanitize\+Post\+Data}} (\$data)
\item 
\mbox{\hyperlink{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52}{validate\+Csrf\+Token}} (\$token)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_finance_a095c5d389db211932136b53f25f39685}\index{Finance@{Finance}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Finance@{Finance}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_finance_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_finance_a2518a250e7a0164d46b5257b2c2f3922}\index{Finance@{Finance}!addCost@{addCost}}
\index{addCost@{addCost}!Finance@{Finance}}
\doxysubsubsection{\texorpdfstring{addCost()}{addCost()}}
{\footnotesize\ttfamily \label{class_finance_a2518a250e7a0164d46b5257b2c2f3922} 
add\+Cost (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Add asset cost


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_finance_a70fafc7500687f64af7bf492d3a13c3c}\index{Finance@{Finance}!asset@{asset}}
\index{asset@{asset}!Finance@{Finance}}
\doxysubsubsection{\texorpdfstring{asset()}{asset()}}
{\footnotesize\ttfamily \label{class_finance_a70fafc7500687f64af7bf492d3a13c3c} 
asset (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

\doxylink{class_asset}{Asset} cost details


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_finance_aada921e0a30211fde5a19573e9825c1f}\index{Finance@{Finance}!budget@{budget}}
\index{budget@{budget}!Finance@{Finance}}
\doxysubsubsection{\texorpdfstring{budget()}{budget()}}
{\footnotesize\ttfamily \label{class_finance_aada921e0a30211fde5a19573e9825c1f} 
budget (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Budget forecasting \Hypertarget{class_finance_a149eb92716c1084a935e04a8d95f7347}\index{Finance@{Finance}!index@{index}}
\index{index@{index}!Finance@{Finance}}
\doxysubsubsection{\texorpdfstring{index()}{index()}}
{\footnotesize\ttfamily \label{class_finance_a149eb92716c1084a935e04a8d95f7347} 
index (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\doxylink{class_finance}{Finance} dashboard 

The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/controllers/\mbox{\hyperlink{_finance_8php}{Finance.\+php}}\end{DoxyCompactItemize}
