\doxysection{Finance\+Model Class Reference}
\hypertarget{class_finance_model}{}\label{class_finance_model}\index{FinanceModel@{FinanceModel}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_finance_model_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_finance_model_a1c3b28394728db624c6b6602ff462e87}{add\+Asset\+Cost}} (\$data)
\item 
\mbox{\hyperlink{class_finance_model_a722cf1192462e36e341e8e98d617a024}{get\+Asset\+Costs}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_finance_model_a8e3a0e893bb4973a8ead487c7a2e84e4}{get\+Total\+Cost\+Of\+Ownership}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_finance_model_a8183489a77f4cdce3e1ec2665909d172}{get\+Costs\+By\+Fiscal\+Year}} (\$fiscal\+Year=null)
\item 
\mbox{\hyperlink{class_finance_model_a35fd3a47017a454cba53c16cc6d7f6df}{get\+Monthly\+Costs}} (\$fiscal\+Year=null)
\item 
\mbox{\hyperlink{class_finance_model_ad9f0064083dfe65c08b304490432ecb8}{get\+Costs\+By\+Equipment\+Type}} (\$fiscal\+Year=null)
\item 
\mbox{\hyperlink{class_finance_model_ab6929cf3308d48a3c73d46cdf847a9b0}{calculate\+Depreciation}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_finance_model_afc623d939da88738ae9729d532a45374}{generate\+Budget\+Forecast}} (\$fiscal\+Year=null)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_finance_model_a095c5d389db211932136b53f25f39685}\index{FinanceModel@{FinanceModel}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!FinanceModel@{FinanceModel}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_finance_model_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_finance_model_a1c3b28394728db624c6b6602ff462e87}\index{FinanceModel@{FinanceModel}!addAssetCost@{addAssetCost}}
\index{addAssetCost@{addAssetCost}!FinanceModel@{FinanceModel}}
\doxysubsubsection{\texorpdfstring{addAssetCost()}{addAssetCost()}}
{\footnotesize\ttfamily \label{class_finance_model_a1c3b28394728db624c6b6602ff462e87} 
add\+Asset\+Cost (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Add asset cost


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}
\Hypertarget{class_finance_model_ab6929cf3308d48a3c73d46cdf847a9b0}\index{FinanceModel@{FinanceModel}!calculateDepreciation@{calculateDepreciation}}
\index{calculateDepreciation@{calculateDepreciation}!FinanceModel@{FinanceModel}}
\doxysubsubsection{\texorpdfstring{calculateDepreciation()}{calculateDepreciation()}}
{\footnotesize\ttfamily \label{class_finance_model_ab6929cf3308d48a3c73d46cdf847a9b0} 
calculate\+Depreciation (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

Calculate depreciation for an asset


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_finance_model_afc623d939da88738ae9729d532a45374}\index{FinanceModel@{FinanceModel}!generateBudgetForecast@{generateBudgetForecast}}
\index{generateBudgetForecast@{generateBudgetForecast}!FinanceModel@{FinanceModel}}
\doxysubsubsection{\texorpdfstring{generateBudgetForecast()}{generateBudgetForecast()}}
{\footnotesize\ttfamily \label{class_finance_model_afc623d939da88738ae9729d532a45374} 
generate\+Budget\+Forecast (\begin{DoxyParamCaption}\item[{}]{\$fiscal\+Year}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Generate budget forecast


\begin{DoxyParams}[1]{Parameters}
int & {\em \$fiscal\+Year} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_finance_model_a722cf1192462e36e341e8e98d617a024}\index{FinanceModel@{FinanceModel}!getAssetCosts@{getAssetCosts}}
\index{getAssetCosts@{getAssetCosts}!FinanceModel@{FinanceModel}}
\doxysubsubsection{\texorpdfstring{getAssetCosts()}{getAssetCosts()}}
{\footnotesize\ttfamily \label{class_finance_model_a722cf1192462e36e341e8e98d617a024} 
get\+Asset\+Costs (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

Get costs for an asset


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_finance_model_ad9f0064083dfe65c08b304490432ecb8}\index{FinanceModel@{FinanceModel}!getCostsByEquipmentType@{getCostsByEquipmentType}}
\index{getCostsByEquipmentType@{getCostsByEquipmentType}!FinanceModel@{FinanceModel}}
\doxysubsubsection{\texorpdfstring{getCostsByEquipmentType()}{getCostsByEquipmentType()}}
{\footnotesize\ttfamily \label{class_finance_model_ad9f0064083dfe65c08b304490432ecb8} 
get\+Costs\+By\+Equipment\+Type (\begin{DoxyParamCaption}\item[{}]{\$fiscal\+Year}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Get costs by equipment type


\begin{DoxyParams}[1]{Parameters}
int & {\em \$fiscal\+Year} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_finance_model_a8183489a77f4cdce3e1ec2665909d172}\index{FinanceModel@{FinanceModel}!getCostsByFiscalYear@{getCostsByFiscalYear}}
\index{getCostsByFiscalYear@{getCostsByFiscalYear}!FinanceModel@{FinanceModel}}
\doxysubsubsection{\texorpdfstring{getCostsByFiscalYear()}{getCostsByFiscalYear()}}
{\footnotesize\ttfamily \label{class_finance_model_a8183489a77f4cdce3e1ec2665909d172} 
get\+Costs\+By\+Fiscal\+Year (\begin{DoxyParamCaption}\item[{}]{\$fiscal\+Year}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Get costs by fiscal year


\begin{DoxyParams}[1]{Parameters}
int & {\em \$fiscal\+Year} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_finance_model_a35fd3a47017a454cba53c16cc6d7f6df}\index{FinanceModel@{FinanceModel}!getMonthlyCosts@{getMonthlyCosts}}
\index{getMonthlyCosts@{getMonthlyCosts}!FinanceModel@{FinanceModel}}
\doxysubsubsection{\texorpdfstring{getMonthlyCosts()}{getMonthlyCosts()}}
{\footnotesize\ttfamily \label{class_finance_model_a35fd3a47017a454cba53c16cc6d7f6df} 
get\+Monthly\+Costs (\begin{DoxyParamCaption}\item[{}]{\$fiscal\+Year}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Get monthly costs for a fiscal year


\begin{DoxyParams}[1]{Parameters}
int & {\em \$fiscal\+Year} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_finance_model_a8e3a0e893bb4973a8ead487c7a2e84e4}\index{FinanceModel@{FinanceModel}!getTotalCostOfOwnership@{getTotalCostOfOwnership}}
\index{getTotalCostOfOwnership@{getTotalCostOfOwnership}!FinanceModel@{FinanceModel}}
\doxysubsubsection{\texorpdfstring{getTotalCostOfOwnership()}{getTotalCostOfOwnership()}}
{\footnotesize\ttfamily \label{class_finance_model_a8e3a0e893bb4973a8ead487c7a2e84e4} 
get\+Total\+Cost\+Of\+Ownership (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

Get total cost of ownership for an asset


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{_finance_model_8php}{Finance\+Model.\+php}}\end{DoxyCompactItemize}
