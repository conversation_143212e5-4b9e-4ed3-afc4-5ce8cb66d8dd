\doxysection{Maintenance Class Reference}
\hypertarget{class_maintenance}{}\label{class_maintenance}\index{Maintenance@{Maintenance}}
Inheritance diagram for Maintenance\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{class_maintenance}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_maintenance_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_maintenance_a65c682611e5c929cdacbce2ff322c9ce}{index}} (\$page=1)
\item 
\mbox{\hyperlink{class_maintenance_ada8a565af34521a04a4acda75a445bcb}{history}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_maintenance_a5af77dc86e75e68b764cd874fa55c960}{add}} (\$asset\+Id=null)
\item 
\mbox{\hyperlink{class_maintenance_aafa7994a16f9c00e068c54343174ca82}{recalculate\+Health\+Metrics}} ()
\item 
\mbox{\hyperlink{class_maintenance_a93f0b59a2513f700476e3f8d01c30860}{complete}} (\$asset\+Id, \$maintenance\+Type)
\item 
\mbox{\hyperlink{class_maintenance_ad1c0026170594acf2d25446f4e359832}{guidelines}} ()
\item 
\mbox{\hyperlink{class_maintenance_abf9c215de21af2165cda212eaa586067}{guideline}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_maintenance_a373f7f8cde6c7caec444d7fa1ef459f6}{manage\+Checklist}} (\$guideline\+Id)
\item 
\mbox{\hyperlink{class_maintenance_a80c346d7c2eb6742214d7a2eafbbc6a6}{edit\+Checklist\+Item}} (\$guideline\+Id, \$item\+Id=null)
\item 
\mbox{\hyperlink{class_maintenance_a7f368832baf0b4b89a5a716d417333b3}{delete\+Checklist\+Item}} (\$guideline\+Id, \$item\+Id)
\item 
\mbox{\hyperlink{class_maintenance_accb1821e13b0574c994996c403d6e881}{edit\+Guideline}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}=null)
\item 
\mbox{\hyperlink{class_maintenance_afefb31fb1543b011326767baf0878913}{delete\+Guideline}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_maintenance_a1cefddff0a8d40a82cc589cdf00f5b63}{all\+History}} (\$page=1)
\item 
\mbox{\hyperlink{class_maintenance_ac578e27aa90b225d97576caec289c0f9}{view\+Redirect}} (\$maintenance\+Id)
\item 
\mbox{\hyperlink{class_maintenance_a06c923f74980ab773940970cd420c3af}{view\+Record}} (\$maintenance\+Id)
\item 
\mbox{\hyperlink{class_maintenance_a1f61d95f6b17f24041cf10c08d904a5e}{monitoring}} ()
\item 
\mbox{\hyperlink{class_maintenance_a322e3e8f6158aebfc7f0bc7c23f9f93d}{asset\+Compliance}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_maintenance_adb25853af641d105c381e29e0d2e6409}{guideline\+Implementations}} (\$guideline\+Id, \$page=1)
\item 
\mbox{\hyperlink{class_maintenance_a11fa9a74c36a285318dbac79eda144af}{data\+Integrity\+Check}} ()
\item 
\mbox{\hyperlink{class_maintenance_a9cbd796bd274dad9c2a2ef7eac75dbff}{recreate\+Missing\+Records}} ()
\item 
\mbox{\hyperlink{class_maintenance_a63340b3b2a8d07f4a03d1dc922530f38}{run\+Migration}} (\$guideline\+Id=null)
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_ac531eb761b130b1925a8bae5c33af2fc}{model}} (\$model)
\item 
\mbox{\hyperlink{class_controller_a11f0e20b30b899d00b009a9bb1afe43d}{view}} (\$view, \$data=\mbox{[}$\,$\mbox{]})
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Protected Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_a0d92de8136cebc006a407442aab9db0a}{sanitize\+Post\+Data}} (\$data)
\item 
\mbox{\hyperlink{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52}{validate\+Csrf\+Token}} (\$token)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_maintenance_a095c5d389db211932136b53f25f39685}\index{Maintenance@{Maintenance}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_maintenance_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_maintenance_a5af77dc86e75e68b764cd874fa55c960}\index{Maintenance@{Maintenance}!add@{add}}
\index{add@{add}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{add()}{add()}}
{\footnotesize\ttfamily \label{class_maintenance_a5af77dc86e75e68b764cd874fa55c960} 
add (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Add maintenance record


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_a1cefddff0a8d40a82cc589cdf00f5b63}\index{Maintenance@{Maintenance}!allHistory@{allHistory}}
\index{allHistory@{allHistory}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{allHistory()}{allHistory()}}
{\footnotesize\ttfamily \label{class_maintenance_a1cefddff0a8d40a82cc589cdf00f5b63} 
all\+History (\begin{DoxyParamCaption}\item[{}]{\$page}{ = {\ttfamily 1}}\end{DoxyParamCaption})}

View all maintenance history


\begin{DoxyParams}[1]{Parameters}
int & {\em \$page} & Current page number for pagination \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_a322e3e8f6158aebfc7f0bc7c23f9f93d}\index{Maintenance@{Maintenance}!assetCompliance@{assetCompliance}}
\index{assetCompliance@{assetCompliance}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{assetCompliance()}{assetCompliance()}}
{\footnotesize\ttfamily \label{class_maintenance_a322e3e8f6158aebfc7f0bc7c23f9f93d} 
asset\+Compliance (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

View asset compliance


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_a93f0b59a2513f700476e3f8d01c30860}\index{Maintenance@{Maintenance}!complete@{complete}}
\index{complete@{complete}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{complete()}{complete()}}
{\footnotesize\ttfamily \label{class_maintenance_a93f0b59a2513f700476e3f8d01c30860} 
complete (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{, }\item[{}]{\$maintenance\+Type}{}\end{DoxyParamCaption})}

Mark maintenance as completed


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
string & {\em \$maintenance\+Type} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_a11fa9a74c36a285318dbac79eda144af}\index{Maintenance@{Maintenance}!dataIntegrityCheck@{dataIntegrityCheck}}
\index{dataIntegrityCheck@{dataIntegrityCheck}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{dataIntegrityCheck()}{dataIntegrityCheck()}}
{\footnotesize\ttfamily \label{class_maintenance_a11fa9a74c36a285318dbac79eda144af} 
data\+Integrity\+Check (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Data Integrity Check Tool Identifies and fixes orphaned records in the maintenance\+\_\+guideline\+\_\+implementation table \Hypertarget{class_maintenance_a7f368832baf0b4b89a5a716d417333b3}\index{Maintenance@{Maintenance}!deleteChecklistItem@{deleteChecklistItem}}
\index{deleteChecklistItem@{deleteChecklistItem}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{deleteChecklistItem()}{deleteChecklistItem()}}
{\footnotesize\ttfamily \label{class_maintenance_a7f368832baf0b4b89a5a716d417333b3} 
delete\+Checklist\+Item (\begin{DoxyParamCaption}\item[{}]{\$guideline\+Id}{, }\item[{}]{\$item\+Id}{}\end{DoxyParamCaption})}

Delete a checklist item


\begin{DoxyParams}[1]{Parameters}
int & {\em \$guideline\+Id} & \\
\hline
int & {\em \$item\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_afefb31fb1543b011326767baf0878913}\index{Maintenance@{Maintenance}!deleteGuideline@{deleteGuideline}}
\index{deleteGuideline@{deleteGuideline}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{deleteGuideline()}{deleteGuideline()}}
{\footnotesize\ttfamily \label{class_maintenance_afefb31fb1543b011326767baf0878913} 
delete\+Guideline (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Delete guideline


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_a80c346d7c2eb6742214d7a2eafbbc6a6}\index{Maintenance@{Maintenance}!editChecklistItem@{editChecklistItem}}
\index{editChecklistItem@{editChecklistItem}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{editChecklistItem()}{editChecklistItem()}}
{\footnotesize\ttfamily \label{class_maintenance_a80c346d7c2eb6742214d7a2eafbbc6a6} 
edit\+Checklist\+Item (\begin{DoxyParamCaption}\item[{}]{\$guideline\+Id}{, }\item[{}]{\$item\+Id}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Add or edit a checklist item


\begin{DoxyParams}[1]{Parameters}
int & {\em \$guideline\+Id} & \\
\hline
int & {\em \$item\+Id} & (optional) \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_accb1821e13b0574c994996c403d6e881}\index{Maintenance@{Maintenance}!editGuideline@{editGuideline}}
\index{editGuideline@{editGuideline}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{editGuideline()}{editGuideline()}}
{\footnotesize\ttfamily \label{class_maintenance_accb1821e13b0574c994996c403d6e881} 
edit\+Guideline (\begin{DoxyParamCaption}\item[{}]{\$id}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Add or edit guideline


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_abf9c215de21af2165cda212eaa586067}\index{Maintenance@{Maintenance}!guideline@{guideline}}
\index{guideline@{guideline}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{guideline()}{guideline()}}
{\footnotesize\ttfamily \label{class_maintenance_abf9c215de21af2165cda212eaa586067} 
guideline (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

View guideline details


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_adb25853af641d105c381e29e0d2e6409}\index{Maintenance@{Maintenance}!guidelineImplementations@{guidelineImplementations}}
\index{guidelineImplementations@{guidelineImplementations}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{guidelineImplementations()}{guidelineImplementations()}}
{\footnotesize\ttfamily \label{class_maintenance_adb25853af641d105c381e29e0d2e6409} 
guideline\+Implementations (\begin{DoxyParamCaption}\item[{}]{\$guideline\+Id}{, }\item[{}]{\$page}{ = {\ttfamily 1}}\end{DoxyParamCaption})}

View all implementations of a specific guideline across different maintenance records


\begin{DoxyParams}[1]{Parameters}
int & {\em \$guideline\+Id} & \\
\hline
int & {\em \$page} & Current page number for pagination \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_ad1c0026170594acf2d25446f4e359832}\index{Maintenance@{Maintenance}!guidelines@{guidelines}}
\index{guidelines@{guidelines}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{guidelines()}{guidelines()}}
{\footnotesize\ttfamily \label{class_maintenance_ad1c0026170594acf2d25446f4e359832} 
guidelines (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Guidelines dashboard \Hypertarget{class_maintenance_ada8a565af34521a04a4acda75a445bcb}\index{Maintenance@{Maintenance}!history@{history}}
\index{history@{history}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{history()}{history()}}
{\footnotesize\ttfamily \label{class_maintenance_ada8a565af34521a04a4acda75a445bcb} 
history (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

View maintenance history for an asset


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_a65c682611e5c929cdacbce2ff322c9ce}\index{Maintenance@{Maintenance}!index@{index}}
\index{index@{index}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{index()}{index()}}
{\footnotesize\ttfamily \label{class_maintenance_a65c682611e5c929cdacbce2ff322c9ce} 
index (\begin{DoxyParamCaption}\item[{}]{\$page}{ = {\ttfamily 1}}\end{DoxyParamCaption})}

\doxylink{class_maintenance}{Maintenance} dashboard


\begin{DoxyParams}[1]{Parameters}
int & {\em \$page} & Current page for asset health metrics pagination \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_a373f7f8cde6c7caec444d7fa1ef459f6}\index{Maintenance@{Maintenance}!manageChecklist@{manageChecklist}}
\index{manageChecklist@{manageChecklist}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{manageChecklist()}{manageChecklist()}}
{\footnotesize\ttfamily \label{class_maintenance_a373f7f8cde6c7caec444d7fa1ef459f6} 
manage\+Checklist (\begin{DoxyParamCaption}\item[{}]{\$guideline\+Id}{}\end{DoxyParamCaption})}

Manage checklist items for a guideline


\begin{DoxyParams}[1]{Parameters}
int & {\em \$guideline\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_a1f61d95f6b17f24041cf10c08d904a5e}\index{Maintenance@{Maintenance}!monitoring@{monitoring}}
\index{monitoring@{monitoring}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{monitoring()}{monitoring()}}
{\footnotesize\ttfamily \label{class_maintenance_a1f61d95f6b17f24041cf10c08d904a5e} 
monitoring (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Monitoring dashboard \Hypertarget{class_maintenance_aafa7994a16f9c00e068c54343174ca82}\index{Maintenance@{Maintenance}!recalculateHealthMetrics@{recalculateHealthMetrics}}
\index{recalculateHealthMetrics@{recalculateHealthMetrics}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{recalculateHealthMetrics()}{recalculateHealthMetrics()}}
{\footnotesize\ttfamily \label{class_maintenance_aafa7994a16f9c00e068c54343174ca82} 
recalculate\+Health\+Metrics (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Recalculate health metrics for all assets \Hypertarget{class_maintenance_a9cbd796bd274dad9c2a2ef7eac75dbff}\index{Maintenance@{Maintenance}!recreateMissingRecords@{recreateMissingRecords}}
\index{recreateMissingRecords@{recreateMissingRecords}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{recreateMissingRecords()}{recreateMissingRecords()}}
{\footnotesize\ttfamily \label{class_maintenance_a9cbd796bd274dad9c2a2ef7eac75dbff} 
recreate\+Missing\+Records (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Recreate Missing \doxylink{class_maintenance}{Maintenance} Records Recreates maintenance history records based on implementation records \Hypertarget{class_maintenance_a63340b3b2a8d07f4a03d1dc922530f38}\index{Maintenance@{Maintenance}!runMigration@{runMigration}}
\index{runMigration@{runMigration}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{runMigration()}{runMigration()}}
{\footnotesize\ttfamily \label{class_maintenance_a63340b3b2a8d07f4a03d1dc922530f38} 
run\+Migration (\begin{DoxyParamCaption}\item[{}]{\$guideline\+Id}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Run \doxylink{class_database}{Database} Migration for Guideline Implementation Table Executes the migration script to ensure the table exists and has the correct structure


\begin{DoxyParams}[1]{Parameters}
int & {\em \$guideline\+Id} & Optional guideline ID to redirect back to \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_a06c923f74980ab773940970cd420c3af}\index{Maintenance@{Maintenance}!viewRecord@{viewRecord}}
\index{viewRecord@{viewRecord}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{viewRecord()}{viewRecord()}}
{\footnotesize\ttfamily \label{class_maintenance_a06c923f74980ab773940970cd420c3af} 
view\+Record (\begin{DoxyParamCaption}\item[{}]{\$maintenance\+Id}{}\end{DoxyParamCaption})}

View detailed maintenance record with implemented guidelines


\begin{DoxyParams}[1]{Parameters}
int & {\em \$maintenance\+Id} & \\
\hline
\end{DoxyParams}
\Hypertarget{class_maintenance_ac578e27aa90b225d97576caec289c0f9}\index{Maintenance@{Maintenance}!viewRedirect@{viewRedirect}}
\index{viewRedirect@{viewRedirect}!Maintenance@{Maintenance}}
\doxysubsubsection{\texorpdfstring{viewRedirect()}{viewRedirect()}}
{\footnotesize\ttfamily \label{class_maintenance_ac578e27aa90b225d97576caec289c0f9} 
view\+Redirect (\begin{DoxyParamCaption}\item[{}]{\$maintenance\+Id}{}\end{DoxyParamCaption})}

Redirect to view\+Record method This method exists to handle legacy URLs that use /maintenance/view/ instead of /maintenance/view\+Record/


\begin{DoxyParams}[1]{Parameters}
int & {\em \$maintenance\+Id} & \\
\hline
\end{DoxyParams}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/controllers/\mbox{\hyperlink{_maintenance_8php}{Maintenance.\+php}}\end{DoxyCompactItemize}
