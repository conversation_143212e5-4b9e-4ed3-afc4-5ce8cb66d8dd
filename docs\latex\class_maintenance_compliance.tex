\doxysection{Maintenance\+Compliance Class Reference}
\hypertarget{class_maintenance_compliance}{}\label{class_maintenance_compliance}\index{MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_maintenance_compliance_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_maintenance_compliance_ae004cfdc78ae9803530098e3b3fa8f34}{get\+All\+Compliance\+Status}} ()
\item 
\mbox{\hyperlink{class_maintenance_compliance_a38966b8a791ede8f4a33ada2c45900e4}{get\+Asset\+Compliance\+Status}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_maintenance_compliance_ae41214459099d198fdbf254d1b6d3ea5}{get\+Guideline\+Compliance\+Status}} (\$guideline\+Id)
\item 
\mbox{\hyperlink{class_maintenance_compliance_a53eda7cd291800d6597fc9fd393d9cc9}{get\+Overdue\+Maintenance}} ()
\item 
\mbox{\hyperlink{class_maintenance_compliance_a78151f12d8c609fb337b7c75b901b09a}{get\+Maintenance\+Due\+Soon}} (\$days\+Ahead=30)
\item 
\mbox{\hyperlink{class_maintenance_compliance_ace44f8eca9de68cedee14e5fecd831a7}{get\+Compliant\+Endpoints}} ()
\item 
\mbox{\hyperlink{class_maintenance_compliance_acdfc6a30cfae1bf421ac8843f2da48e6}{update\+Compliance\+Status}} (\$asset\+Id, \$guideline\+Id, \$status, \$last\+Performed\+Date=null, \$next\+Due\+Date=null)
\item 
\mbox{\hyperlink{class_maintenance_compliance_a81dcd7ed7d6a40beead9f10aff4f7b16}{update\+All\+Compliance\+Status}} ()
\item 
\mbox{\hyperlink{class_maintenance_compliance_ac4690ec3327999f4578be947e8809291}{record\+Checklist\+Completion}} (\$maintenance\+History\+Id, \$checklist\+Id, \$completed, \$completed\+By, \$notes=\textquotesingle{}\textquotesingle{})
\item 
\mbox{\hyperlink{class_maintenance_compliance_a3b211ed4bb2c38a6eb9a7a0d6a42afab}{get\+Checklist\+Completion}} (\$maintenance\+History\+Id)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_maintenance_compliance_a095c5d389db211932136b53f25f39685}\index{MaintenanceCompliance@{MaintenanceCompliance}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_maintenance_compliance_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_maintenance_compliance_ae004cfdc78ae9803530098e3b3fa8f34}\index{MaintenanceCompliance@{MaintenanceCompliance}!getAllComplianceStatus@{getAllComplianceStatus}}
\index{getAllComplianceStatus@{getAllComplianceStatus}!MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection{\texorpdfstring{getAllComplianceStatus()}{getAllComplianceStatus()}}
{\footnotesize\ttfamily \label{class_maintenance_compliance_ae004cfdc78ae9803530098e3b3fa8f34} 
get\+All\+Compliance\+Status (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Get compliance status for all assets

\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_compliance_a38966b8a791ede8f4a33ada2c45900e4}\index{MaintenanceCompliance@{MaintenanceCompliance}!getAssetComplianceStatus@{getAssetComplianceStatus}}
\index{getAssetComplianceStatus@{getAssetComplianceStatus}!MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection{\texorpdfstring{getAssetComplianceStatus()}{getAssetComplianceStatus()}}
{\footnotesize\ttfamily \label{class_maintenance_compliance_a38966b8a791ede8f4a33ada2c45900e4} 
get\+Asset\+Compliance\+Status (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

Get compliance status for a specific asset


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_compliance_a3b211ed4bb2c38a6eb9a7a0d6a42afab}\index{MaintenanceCompliance@{MaintenanceCompliance}!getChecklistCompletion@{getChecklistCompletion}}
\index{getChecklistCompletion@{getChecklistCompletion}!MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection{\texorpdfstring{getChecklistCompletion()}{getChecklistCompletion()}}
{\footnotesize\ttfamily \label{class_maintenance_compliance_a3b211ed4bb2c38a6eb9a7a0d6a42afab} 
get\+Checklist\+Completion (\begin{DoxyParamCaption}\item[{}]{\$maintenance\+History\+Id}{}\end{DoxyParamCaption})}

Get checklist completion for a maintenance record


\begin{DoxyParams}[1]{Parameters}
int & {\em \$maintenance\+History\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_compliance_ace44f8eca9de68cedee14e5fecd831a7}\index{MaintenanceCompliance@{MaintenanceCompliance}!getCompliantEndpoints@{getCompliantEndpoints}}
\index{getCompliantEndpoints@{getCompliantEndpoints}!MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection{\texorpdfstring{getCompliantEndpoints()}{getCompliantEndpoints()}}
{\footnotesize\ttfamily \label{class_maintenance_compliance_ace44f8eca9de68cedee14e5fecd831a7} 
get\+Compliant\+Endpoints (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Get compliant endpoints

\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_compliance_ae41214459099d198fdbf254d1b6d3ea5}\index{MaintenanceCompliance@{MaintenanceCompliance}!getGuidelineComplianceStatus@{getGuidelineComplianceStatus}}
\index{getGuidelineComplianceStatus@{getGuidelineComplianceStatus}!MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection{\texorpdfstring{getGuidelineComplianceStatus()}{getGuidelineComplianceStatus()}}
{\footnotesize\ttfamily \label{class_maintenance_compliance_ae41214459099d198fdbf254d1b6d3ea5} 
get\+Guideline\+Compliance\+Status (\begin{DoxyParamCaption}\item[{}]{\$guideline\+Id}{}\end{DoxyParamCaption})}

Get compliance status for a specific guideline across all assets


\begin{DoxyParams}[1]{Parameters}
int & {\em \$guideline\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_compliance_a78151f12d8c609fb337b7c75b901b09a}\index{MaintenanceCompliance@{MaintenanceCompliance}!getMaintenanceDueSoon@{getMaintenanceDueSoon}}
\index{getMaintenanceDueSoon@{getMaintenanceDueSoon}!MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection{\texorpdfstring{getMaintenanceDueSoon()}{getMaintenanceDueSoon()}}
{\footnotesize\ttfamily \label{class_maintenance_compliance_a78151f12d8c609fb337b7c75b901b09a} 
get\+Maintenance\+Due\+Soon (\begin{DoxyParamCaption}\item[{}]{\$days\+Ahead}{ = {\ttfamily 30}}\end{DoxyParamCaption})}

Get maintenance tasks due soon


\begin{DoxyParams}[1]{Parameters}
int & {\em \$days\+Ahead} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_compliance_a53eda7cd291800d6597fc9fd393d9cc9}\index{MaintenanceCompliance@{MaintenanceCompliance}!getOverdueMaintenance@{getOverdueMaintenance}}
\index{getOverdueMaintenance@{getOverdueMaintenance}!MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection{\texorpdfstring{getOverdueMaintenance()}{getOverdueMaintenance()}}
{\footnotesize\ttfamily \label{class_maintenance_compliance_a53eda7cd291800d6597fc9fd393d9cc9} 
get\+Overdue\+Maintenance (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Get overdue maintenance tasks

\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_compliance_ac4690ec3327999f4578be947e8809291}\index{MaintenanceCompliance@{MaintenanceCompliance}!recordChecklistCompletion@{recordChecklistCompletion}}
\index{recordChecklistCompletion@{recordChecklistCompletion}!MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection{\texorpdfstring{recordChecklistCompletion()}{recordChecklistCompletion()}}
{\footnotesize\ttfamily \label{class_maintenance_compliance_ac4690ec3327999f4578be947e8809291} 
record\+Checklist\+Completion (\begin{DoxyParamCaption}\item[{}]{\$maintenance\+History\+Id}{, }\item[{}]{\$checklist\+Id}{, }\item[{}]{\$completed}{, }\item[{}]{\$completed\+By}{, }\item[{}]{\$notes}{ = {\ttfamily \textquotesingle{}\textquotesingle{}}}\end{DoxyParamCaption})}

Record checklist completion


\begin{DoxyParams}[1]{Parameters}
int & {\em \$maintenance\+History\+Id} & \\
\hline
int & {\em \$checklist\+Id} & \\
\hline
bool & {\em \$completed} & \\
\hline
int & {\em \$completed\+By} & \\
\hline
string & {\em \$notes} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}
\Hypertarget{class_maintenance_compliance_a81dcd7ed7d6a40beead9f10aff4f7b16}\index{MaintenanceCompliance@{MaintenanceCompliance}!updateAllComplianceStatus@{updateAllComplianceStatus}}
\index{updateAllComplianceStatus@{updateAllComplianceStatus}!MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection{\texorpdfstring{updateAllComplianceStatus()}{updateAllComplianceStatus()}}
{\footnotesize\ttfamily \label{class_maintenance_compliance_a81dcd7ed7d6a40beead9f10aff4f7b16} 
update\+All\+Compliance\+Status (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Calculate and update compliance status for all assets

\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}
\Hypertarget{class_maintenance_compliance_acdfc6a30cfae1bf421ac8843f2da48e6}\index{MaintenanceCompliance@{MaintenanceCompliance}!updateComplianceStatus@{updateComplianceStatus}}
\index{updateComplianceStatus@{updateComplianceStatus}!MaintenanceCompliance@{MaintenanceCompliance}}
\doxysubsubsection{\texorpdfstring{updateComplianceStatus()}{updateComplianceStatus()}}
{\footnotesize\ttfamily \label{class_maintenance_compliance_acdfc6a30cfae1bf421ac8843f2da48e6} 
update\+Compliance\+Status (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{, }\item[{}]{\$guideline\+Id}{, }\item[{}]{\$status}{, }\item[{}]{\$last\+Performed\+Date}{ = {\ttfamily null}, }\item[{}]{\$next\+Due\+Date}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Update compliance status for an asset and guideline


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
int & {\em \$guideline\+Id} & \\
\hline
string & {\em \$status} & \\
\hline
string & {\em \$last\+Performed\+Date} & \\
\hline
string & {\em \$next\+Due\+Date} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{_maintenance_compliance_8php}{Maintenance\+Compliance.\+php}}\end{DoxyCompactItemize}
