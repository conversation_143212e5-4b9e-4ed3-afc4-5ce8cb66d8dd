\doxysection{Maintenance\+Guideline Class Reference}
\hypertarget{class_maintenance_guideline}{}\label{class_maintenance_guideline}\index{MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_maintenance_guideline_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_maintenance_guideline_acf5491f2d8fdc88257f5a86d9b858c51}{get\+All\+Guidelines}} ()
\item 
\mbox{\hyperlink{class_maintenance_guideline_ad2cc7d08e605aae76fe2d7fb575741b1}{get\+Guidelines\+By\+Equipment\+Type}} (\$equipment\+Type)
\item 
\mbox{\hyperlink{class_maintenance_guideline_a8ce317ffbfd16f85d200877dbb74d8ef}{get\+Guideline\+By\+Id}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_maintenance_guideline_abe2396d040b8fdb1946a63702f30e67c}{get\+Checklist\+Items}} (\$guideline\+Id)
\item 
\mbox{\hyperlink{class_maintenance_guideline_a383b611d9c3469a58ac395f64981c356}{add\+Guideline}} (\$data)
\item 
\mbox{\hyperlink{class_maintenance_guideline_abb781661c951700724bf44af32d02c21}{update\+Guideline}} (\$data)
\item 
\mbox{\hyperlink{class_maintenance_guideline_afefb31fb1543b011326767baf0878913}{delete\+Guideline}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_maintenance_guideline_aa78853d4b8a87ebcd6f89818ac37eaac}{add\+Checklist\+Item}} (\$data)
\item 
\mbox{\hyperlink{class_maintenance_guideline_a0d92535f0a290cdb6dae4eaa018c7b37}{update\+Checklist\+Item}} (\$data)
\item 
\mbox{\hyperlink{class_maintenance_guideline_a67dfc8aaee56ed77b7a15ef682315571}{delete\+Checklist\+Item}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_maintenance_guideline_a8a36b79d222bae4ee9ebec107463a840}{get\+Next\+Step\+Number}} (\$guideline\+Id)
\item 
\mbox{\hyperlink{class_maintenance_guideline_a9237db02c249b6f068d682dd5df1b371}{get\+Checklist\+Item\+By\+Id}} (\$item\+Id, \$guideline\+Id)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_maintenance_guideline_a095c5d389db211932136b53f25f39685}\index{MaintenanceGuideline@{MaintenanceGuideline}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_maintenance_guideline_aa78853d4b8a87ebcd6f89818ac37eaac}\index{MaintenanceGuideline@{MaintenanceGuideline}!addChecklistItem@{addChecklistItem}}
\index{addChecklistItem@{addChecklistItem}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{addChecklistItem()}{addChecklistItem()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_aa78853d4b8a87ebcd6f89818ac37eaac} 
add\+Checklist\+Item (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Add a checklist item to a guideline


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
int\texorpdfstring{$\vert$}{|}bool The new checklist item ID or false on failure 
\end{DoxyReturn}
\Hypertarget{class_maintenance_guideline_a383b611d9c3469a58ac395f64981c356}\index{MaintenanceGuideline@{MaintenanceGuideline}!addGuideline@{addGuideline}}
\index{addGuideline@{addGuideline}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{addGuideline()}{addGuideline()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_a383b611d9c3469a58ac395f64981c356} 
add\+Guideline (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Add a new maintenance guideline


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
int\texorpdfstring{$\vert$}{|}bool The new guideline ID or false on failure 
\end{DoxyReturn}
\Hypertarget{class_maintenance_guideline_a67dfc8aaee56ed77b7a15ef682315571}\index{MaintenanceGuideline@{MaintenanceGuideline}!deleteChecklistItem@{deleteChecklistItem}}
\index{deleteChecklistItem@{deleteChecklistItem}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{deleteChecklistItem()}{deleteChecklistItem()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_a67dfc8aaee56ed77b7a15ef682315571} 
delete\+Checklist\+Item (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Delete a checklist item


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}
\Hypertarget{class_maintenance_guideline_afefb31fb1543b011326767baf0878913}\index{MaintenanceGuideline@{MaintenanceGuideline}!deleteGuideline@{deleteGuideline}}
\index{deleteGuideline@{deleteGuideline}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{deleteGuideline()}{deleteGuideline()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_afefb31fb1543b011326767baf0878913} 
delete\+Guideline (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Delete a maintenance guideline


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}
\Hypertarget{class_maintenance_guideline_acf5491f2d8fdc88257f5a86d9b858c51}\index{MaintenanceGuideline@{MaintenanceGuideline}!getAllGuidelines@{getAllGuidelines}}
\index{getAllGuidelines@{getAllGuidelines}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{getAllGuidelines()}{getAllGuidelines()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_acf5491f2d8fdc88257f5a86d9b858c51} 
get\+All\+Guidelines (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Get all maintenance guidelines

\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_guideline_a9237db02c249b6f068d682dd5df1b371}\index{MaintenanceGuideline@{MaintenanceGuideline}!getChecklistItemById@{getChecklistItemById}}
\index{getChecklistItemById@{getChecklistItemById}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{getChecklistItemById()}{getChecklistItemById()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_a9237db02c249b6f068d682dd5df1b371} 
get\+Checklist\+Item\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$item\+Id}{, }\item[{}]{\$guideline\+Id}{}\end{DoxyParamCaption})}

Get a specific checklist item by ID and guideline ID


\begin{DoxyParams}[1]{Parameters}
int & {\em \$item\+Id} & \\
\hline
int & {\em \$guideline\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object\texorpdfstring{$\vert$}{|}false 
\end{DoxyReturn}
\Hypertarget{class_maintenance_guideline_abe2396d040b8fdb1946a63702f30e67c}\index{MaintenanceGuideline@{MaintenanceGuideline}!getChecklistItems@{getChecklistItems}}
\index{getChecklistItems@{getChecklistItems}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{getChecklistItems()}{getChecklistItems()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_abe2396d040b8fdb1946a63702f30e67c} 
get\+Checklist\+Items (\begin{DoxyParamCaption}\item[{}]{\$guideline\+Id}{}\end{DoxyParamCaption})}

Get checklist items for a guideline


\begin{DoxyParams}[1]{Parameters}
int & {\em \$guideline\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_guideline_a8ce317ffbfd16f85d200877dbb74d8ef}\index{MaintenanceGuideline@{MaintenanceGuideline}!getGuidelineById@{getGuidelineById}}
\index{getGuidelineById@{getGuidelineById}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{getGuidelineById()}{getGuidelineById()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_a8ce317ffbfd16f85d200877dbb74d8ef} 
get\+Guideline\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Get maintenance guideline by ID


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object 
\end{DoxyReturn}
\Hypertarget{class_maintenance_guideline_ad2cc7d08e605aae76fe2d7fb575741b1}\index{MaintenanceGuideline@{MaintenanceGuideline}!getGuidelinesByEquipmentType@{getGuidelinesByEquipmentType}}
\index{getGuidelinesByEquipmentType@{getGuidelinesByEquipmentType}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{getGuidelinesByEquipmentType()}{getGuidelinesByEquipmentType()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_ad2cc7d08e605aae76fe2d7fb575741b1} 
get\+Guidelines\+By\+Equipment\+Type (\begin{DoxyParamCaption}\item[{}]{\$equipment\+Type}{}\end{DoxyParamCaption})}

Get maintenance guidelines by equipment type


\begin{DoxyParams}[1]{Parameters}
string & {\em \$equipment\+Type} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_guideline_a8a36b79d222bae4ee9ebec107463a840}\index{MaintenanceGuideline@{MaintenanceGuideline}!getNextStepNumber@{getNextStepNumber}}
\index{getNextStepNumber@{getNextStepNumber}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{getNextStepNumber()}{getNextStepNumber()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_a8a36b79d222bae4ee9ebec107463a840} 
get\+Next\+Step\+Number (\begin{DoxyParamCaption}\item[{}]{\$guideline\+Id}{}\end{DoxyParamCaption})}

Get the next step number for a guideline


\begin{DoxyParams}[1]{Parameters}
int & {\em \$guideline\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
int 
\end{DoxyReturn}
\Hypertarget{class_maintenance_guideline_a0d92535f0a290cdb6dae4eaa018c7b37}\index{MaintenanceGuideline@{MaintenanceGuideline}!updateChecklistItem@{updateChecklistItem}}
\index{updateChecklistItem@{updateChecklistItem}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{updateChecklistItem()}{updateChecklistItem()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_a0d92535f0a290cdb6dae4eaa018c7b37} 
update\+Checklist\+Item (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Update a checklist item


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}
\Hypertarget{class_maintenance_guideline_abb781661c951700724bf44af32d02c21}\index{MaintenanceGuideline@{MaintenanceGuideline}!updateGuideline@{updateGuideline}}
\index{updateGuideline@{updateGuideline}!MaintenanceGuideline@{MaintenanceGuideline}}
\doxysubsubsection{\texorpdfstring{updateGuideline()}{updateGuideline()}}
{\footnotesize\ttfamily \label{class_maintenance_guideline_abb781661c951700724bf44af32d02c21} 
update\+Guideline (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Update a maintenance guideline


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{_maintenance_guideline_8php}{Maintenance\+Guideline.\+php}}\end{DoxyCompactItemize}
