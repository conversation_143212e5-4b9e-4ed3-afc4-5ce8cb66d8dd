\doxysection{Maintenance\+Model Class Reference}
\hypertarget{class_maintenance_model}{}\label{class_maintenance_model}\index{MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_maintenance_model_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_maintenance_model_a307b0ec1c1343e097ffa8d0a058cc353}{ensure\+Guideline\+Implementation\+Table\+Exists}} ()
\item 
\mbox{\hyperlink{class_maintenance_model_a5028d7b9ec8e052e69d9f683e1ebfd07}{get\+Maintenance\+History}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_maintenance_model_a308b6d3e1f8e959f87bf5ae8f6c93ae6}{get\+Maintenance\+By\+Id}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_maintenance_model_ace2e334dc067d1e99ab1cf4bed40359f}{get\+All\+Maintenance\+History}} (\$limit=null, \$offset=0)
\item 
\mbox{\hyperlink{class_maintenance_model_aa9447fe3595934e7036eeba1eee31bae}{count\+All\+Maintenance\+History}} ()
\item 
\mbox{\hyperlink{class_maintenance_model_a49112508b667299f63c3e43993c89ac1}{add\+Maintenance}} (\$data)
\item 
\mbox{\hyperlink{class_maintenance_model_af337d34247442623d3ef21c81c77b5c7}{get\+Assets\+Due\+For\+Maintenance}} (\$days\+Ahead=30)
\item 
\mbox{\hyperlink{class_maintenance_model_afb3f191cdd7cdb9724de3d59626b162a}{calculate\+Asset\+Health\+Score}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_maintenance_model_a234816f2513b32ba0fea57902e2ca897}{get\+Assets\+With\+Health\+Metrics}} (\$limit=null, \$offset=0, \$search=\textquotesingle{}\textquotesingle{}, \$order\+By=\textquotesingle{}health\+\_\+score\textquotesingle{}, \$order\+Dir=\textquotesingle{}ASC\textquotesingle{})
\item 
\mbox{\hyperlink{class_maintenance_model_a38aac20b8e7f44f2cf503808243794ec}{count\+Assets\+With\+Health\+Metrics}} (\$search=\textquotesingle{}\textquotesingle{})
\item 
\mbox{\hyperlink{class_maintenance_model_a3712d99605d1092dd417d6813adfb838}{get\+All\+Scheduled\+Maintenance}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_maintenance_model_adac8c5043e0bc976a86e615fce9b4a0d}{get\+Scheduled\+Maintenance\+Id}} (\$asset\+Id, \$maintenance\+Type)
\item 
\mbox{\hyperlink{class_maintenance_model_a567f817301870b14b63b9607a8f1c182}{update\+Scheduled\+Maintenance\+Status}} (\$asset\+Id, \$maintenance\+Type)
\item 
\mbox{\hyperlink{class_maintenance_model_abe2396d040b8fdb1946a63702f30e67c}{get\+Checklist\+Items}} (\$guideline\+Id)
\item 
\mbox{\hyperlink{class_maintenance_model_ab5777d866bcf1d449e676c3d7eee1901}{get\+Completed\+Checklist\+Items}} (\$maintenance\+Id, \$guideline\+Id)
\item 
\mbox{\hyperlink{class_maintenance_model_a8dcecd02b7451e4ce6f807170b758049}{get\+All\+Completed\+Checklist\+Items}} (\$maintenance\+Id)
\item 
\mbox{\hyperlink{class_maintenance_model_a95bb0e0eb1c41a1c8bec6ce98a0f7cb6}{record\+Checklist\+Completion}} (\$maintenance\+Id, \$checklist\+Id, \$completed=true, \$completed\+By=null, \$notes=\textquotesingle{}\textquotesingle{})
\item 
\mbox{\hyperlink{class_maintenance_model_a61baaaf1adfacd6574436248f298281e}{log\+Guideline\+Implementation}} (\$maintenance\+Id, \$guideline\+Id)
\item 
\mbox{\hyperlink{class_maintenance_model_aac2a53c38ccfa1c46b885ac7c2811243}{update\+All\+Health\+Metrics}} ()
\item 
\mbox{\hyperlink{class_maintenance_model_ac6be536865b41000e5ca2a1bb8994080}{get\+Implemented\+Guidelines}} (\$maintenance\+Id)
\item 
\mbox{\hyperlink{class_maintenance_model_a4705bd8f1afc9de6401a5c14082ceca9}{get\+Detailed\+Implemented\+Guidelines}} (\$maintenance\+Id)
\item 
\mbox{\hyperlink{class_maintenance_model_a3479744b4309d54812e49f4fdf605c28}{get\+Maintenance\+History\+With\+Guidelines}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_maintenance_model_a712d5e3692faa0c9e6f6cb52079a36bf}{get\+Guideline\+Implementations}} (\$guideline\+Id, \$limit=null, \$offset=0)
\item 
\mbox{\hyperlink{class_maintenance_model_ab101e35ab0da74f49df5f58253d43da3}{check\+Data\+Integrity}} ()
\item 
\mbox{\hyperlink{class_maintenance_model_afded54f2a79d75797c141f1c076d27ca}{fix\+Orphaned\+Records}} ()
\item 
\mbox{\hyperlink{class_maintenance_model_abe614689b30c18a752d2ce6be9e01e49}{find\+Missing\+Maintenance\+Records}} ()
\item 
\mbox{\hyperlink{class_maintenance_model_a9cbd796bd274dad9c2a2ef7eac75dbff}{recreate\+Missing\+Records}} ()
\item 
\mbox{\hyperlink{class_maintenance_model_adcedfb512247d32097f1d74a9e118ee9}{get\+Most\+Implemented\+Guidelines}} (\$limit=null)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_maintenance_model_a095c5d389db211932136b53f25f39685}\index{MaintenanceModel@{MaintenanceModel}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_maintenance_model_a49112508b667299f63c3e43993c89ac1}\index{MaintenanceModel@{MaintenanceModel}!addMaintenance@{addMaintenance}}
\index{addMaintenance@{addMaintenance}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{addMaintenance()}{addMaintenance()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a49112508b667299f63c3e43993c89ac1} 
add\+Maintenance (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Add maintenance record


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
int\texorpdfstring{$\vert$}{|}bool The new maintenance ID or false on failure 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_afb3f191cdd7cdb9724de3d59626b162a}\index{MaintenanceModel@{MaintenanceModel}!calculateAssetHealthScore@{calculateAssetHealthScore}}
\index{calculateAssetHealthScore@{calculateAssetHealthScore}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{calculateAssetHealthScore()}{calculateAssetHealthScore()}}
{\footnotesize\ttfamily \label{class_maintenance_model_afb3f191cdd7cdb9724de3d59626b162a} 
calculate\+Asset\+Health\+Score (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

Calculate health score for an asset


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
float 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_ab101e35ab0da74f49df5f58253d43da3}\index{MaintenanceModel@{MaintenanceModel}!checkDataIntegrity@{checkDataIntegrity}}
\index{checkDataIntegrity@{checkDataIntegrity}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{checkDataIntegrity()}{checkDataIntegrity()}}
{\footnotesize\ttfamily \label{class_maintenance_model_ab101e35ab0da74f49df5f58253d43da3} 
check\+Data\+Integrity (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Check data integrity of the maintenance\+\_\+guideline\+\_\+implementation table Identifies orphaned records (records with no corresponding maintenance\+\_\+history or guideline)

\begin{DoxyReturn}{Returns}
array Results of the integrity check 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_aa9447fe3595934e7036eeba1eee31bae}\index{MaintenanceModel@{MaintenanceModel}!countAllMaintenanceHistory@{countAllMaintenanceHistory}}
\index{countAllMaintenanceHistory@{countAllMaintenanceHistory}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{countAllMaintenanceHistory()}{countAllMaintenanceHistory()}}
{\footnotesize\ttfamily \label{class_maintenance_model_aa9447fe3595934e7036eeba1eee31bae} 
count\+All\+Maintenance\+History (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Count all maintenance history records

\begin{DoxyReturn}{Returns}
int 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a38aac20b8e7f44f2cf503808243794ec}\index{MaintenanceModel@{MaintenanceModel}!countAssetsWithHealthMetrics@{countAssetsWithHealthMetrics}}
\index{countAssetsWithHealthMetrics@{countAssetsWithHealthMetrics}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{countAssetsWithHealthMetrics()}{countAssetsWithHealthMetrics()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a38aac20b8e7f44f2cf503808243794ec} 
count\+Assets\+With\+Health\+Metrics (\begin{DoxyParamCaption}\item[{}]{\$search}{ = {\ttfamily \textquotesingle{}\textquotesingle{}}}\end{DoxyParamCaption})}

Count total assets with health metrics (for pagination)


\begin{DoxyParams}[1]{Parameters}
string & {\em \$search} & Optional search term for filtering \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
int 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a307b0ec1c1343e097ffa8d0a058cc353}\index{MaintenanceModel@{MaintenanceModel}!ensureGuidelineImplementationTableExists@{ensureGuidelineImplementationTableExists}}
\index{ensureGuidelineImplementationTableExists@{ensureGuidelineImplementationTableExists}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{ensureGuidelineImplementationTableExists()}{ensureGuidelineImplementationTableExists()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a307b0ec1c1343e097ffa8d0a058cc353} 
ensure\+Guideline\+Implementation\+Table\+Exists (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Ensure the maintenance\+\_\+guideline\+\_\+implementation table exists This is called once during initialization to prevent repeated table creation

\begin{DoxyReturn}{Returns}
bool True if the table exists or was created successfully 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_abe614689b30c18a752d2ce6be9e01e49}\index{MaintenanceModel@{MaintenanceModel}!findMissingMaintenanceRecords@{findMissingMaintenanceRecords}}
\index{findMissingMaintenanceRecords@{findMissingMaintenanceRecords}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{findMissingMaintenanceRecords()}{findMissingMaintenanceRecords()}}
{\footnotesize\ttfamily \label{class_maintenance_model_abe614689b30c18a752d2ce6be9e01e49} 
find\+Missing\+Maintenance\+Records (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Find missing maintenance history records Identifies implementation records where the maintenance\+\_\+history record is missing

\begin{DoxyReturn}{Returns}
array Results of the search 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_afded54f2a79d75797c141f1c076d27ca}\index{MaintenanceModel@{MaintenanceModel}!fixOrphanedRecords@{fixOrphanedRecords}}
\index{fixOrphanedRecords@{fixOrphanedRecords}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{fixOrphanedRecords()}{fixOrphanedRecords()}}
{\footnotesize\ttfamily \label{class_maintenance_model_afded54f2a79d75797c141f1c076d27ca} 
fix\+Orphaned\+Records (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Fix orphaned records in the maintenance\+\_\+guideline\+\_\+implementation table Removes records with no corresponding maintenance\+\_\+history or guideline

\begin{DoxyReturn}{Returns}
array Results of the fix operation 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a8dcecd02b7451e4ce6f807170b758049}\index{MaintenanceModel@{MaintenanceModel}!getAllCompletedChecklistItems@{getAllCompletedChecklistItems}}
\index{getAllCompletedChecklistItems@{getAllCompletedChecklistItems}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getAllCompletedChecklistItems()}{getAllCompletedChecklistItems()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a8dcecd02b7451e4ce6f807170b758049} 
get\+All\+Completed\+Checklist\+Items (\begin{DoxyParamCaption}\item[{}]{\$maintenance\+Id}{}\end{DoxyParamCaption})}

Get all completed checklist items for a maintenance record


\begin{DoxyParams}[1]{Parameters}
int & {\em \$maintenance\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_ace2e334dc067d1e99ab1cf4bed40359f}\index{MaintenanceModel@{MaintenanceModel}!getAllMaintenanceHistory@{getAllMaintenanceHistory}}
\index{getAllMaintenanceHistory@{getAllMaintenanceHistory}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getAllMaintenanceHistory()}{getAllMaintenanceHistory()}}
{\footnotesize\ttfamily \label{class_maintenance_model_ace2e334dc067d1e99ab1cf4bed40359f} 
get\+All\+Maintenance\+History (\begin{DoxyParamCaption}\item[{}]{\$limit}{ = {\ttfamily null}, }\item[{}]{\$offset}{ = {\ttfamily 0}}\end{DoxyParamCaption})}

Get all maintenance history records


\begin{DoxyParams}[1]{Parameters}
int & {\em \$limit} & Optional limit for number of records to return \\
\hline
int & {\em \$offset} & Optional offset for pagination \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a3712d99605d1092dd417d6813adfb838}\index{MaintenanceModel@{MaintenanceModel}!getAllScheduledMaintenance@{getAllScheduledMaintenance}}
\index{getAllScheduledMaintenance@{getAllScheduledMaintenance}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getAllScheduledMaintenance()}{getAllScheduledMaintenance()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a3712d99605d1092dd417d6813adfb838} 
get\+All\+Scheduled\+Maintenance (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

Get all scheduled maintenance records for an asset


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_af337d34247442623d3ef21c81c77b5c7}\index{MaintenanceModel@{MaintenanceModel}!getAssetsDueForMaintenance@{getAssetsDueForMaintenance}}
\index{getAssetsDueForMaintenance@{getAssetsDueForMaintenance}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getAssetsDueForMaintenance()}{getAssetsDueForMaintenance()}}
{\footnotesize\ttfamily \label{class_maintenance_model_af337d34247442623d3ef21c81c77b5c7} 
get\+Assets\+Due\+For\+Maintenance (\begin{DoxyParamCaption}\item[{}]{\$days\+Ahead}{ = {\ttfamily 30}}\end{DoxyParamCaption})}

Get assets due for maintenance


\begin{DoxyParams}[1]{Parameters}
int & {\em \$days\+Ahead} & Look ahead days \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a234816f2513b32ba0fea57902e2ca897}\index{MaintenanceModel@{MaintenanceModel}!getAssetsWithHealthMetrics@{getAssetsWithHealthMetrics}}
\index{getAssetsWithHealthMetrics@{getAssetsWithHealthMetrics}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getAssetsWithHealthMetrics()}{getAssetsWithHealthMetrics()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a234816f2513b32ba0fea57902e2ca897} 
get\+Assets\+With\+Health\+Metrics (\begin{DoxyParamCaption}\item[{}]{\$limit}{ = {\ttfamily null}, }\item[{}]{\$offset}{ = {\ttfamily 0}, }\item[{}]{\$search}{ = {\ttfamily \textquotesingle{}\textquotesingle{}}, }\item[{}]{\$order\+By}{ = {\ttfamily \textquotesingle{}health\+\_\+score\textquotesingle{}}, }\item[{}]{\$order\+Dir}{ = {\ttfamily \textquotesingle{}ASC\textquotesingle{}}}\end{DoxyParamCaption})}

Get assets with health metrics


\begin{DoxyParams}[1]{Parameters}
int & {\em \$limit} & Optional limit for pagination \\
\hline
int & {\em \$offset} & Optional offset for pagination \\
\hline
string & {\em \$search} & Optional search term for filtering \\
\hline
string & {\em \$order\+By} & Optional column to order by \\
\hline
string & {\em \$order\+Dir} & Optional direction (ASC or DESC) \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_abe2396d040b8fdb1946a63702f30e67c}\index{MaintenanceModel@{MaintenanceModel}!getChecklistItems@{getChecklistItems}}
\index{getChecklistItems@{getChecklistItems}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getChecklistItems()}{getChecklistItems()}}
{\footnotesize\ttfamily \label{class_maintenance_model_abe2396d040b8fdb1946a63702f30e67c} 
get\+Checklist\+Items (\begin{DoxyParamCaption}\item[{}]{\$guideline\+Id}{}\end{DoxyParamCaption})}

Get checklist items for a guideline

Note\+: This method duplicates functionality in \doxylink{class_maintenance_guideline_abe2396d040b8fdb1946a63702f30e67c}{Maintenance\+Guideline\textbackslash{}get\+Checklist\+Items} Consider using \doxylink{class_maintenance_guideline_abe2396d040b8fdb1946a63702f30e67c}{Maintenance\+Guideline\textbackslash{}get\+Checklist\+Items} instead to avoid duplication


\begin{DoxyParams}[1]{Parameters}
int & {\em \$guideline\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_ab5777d866bcf1d449e676c3d7eee1901}\index{MaintenanceModel@{MaintenanceModel}!getCompletedChecklistItems@{getCompletedChecklistItems}}
\index{getCompletedChecklistItems@{getCompletedChecklistItems}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getCompletedChecklistItems()}{getCompletedChecklistItems()}}
{\footnotesize\ttfamily \label{class_maintenance_model_ab5777d866bcf1d449e676c3d7eee1901} 
get\+Completed\+Checklist\+Items (\begin{DoxyParamCaption}\item[{}]{\$maintenance\+Id}{, }\item[{}]{\$guideline\+Id}{}\end{DoxyParamCaption})}

Get completed checklist items for a maintenance record and guideline


\begin{DoxyParams}[1]{Parameters}
int & {\em \$maintenance\+Id} & \\
\hline
int & {\em \$guideline\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a4705bd8f1afc9de6401a5c14082ceca9}\index{MaintenanceModel@{MaintenanceModel}!getDetailedImplementedGuidelines@{getDetailedImplementedGuidelines}}
\index{getDetailedImplementedGuidelines@{getDetailedImplementedGuidelines}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getDetailedImplementedGuidelines()}{getDetailedImplementedGuidelines()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a4705bd8f1afc9de6401a5c14082ceca9} 
get\+Detailed\+Implemented\+Guidelines (\begin{DoxyParamCaption}\item[{}]{\$maintenance\+Id}{}\end{DoxyParamCaption})}

Get detailed information about implemented guidelines for a maintenance record including checklist items


\begin{DoxyParams}[1]{Parameters}
int & {\em \$maintenance\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a712d5e3692faa0c9e6f6cb52079a36bf}\index{MaintenanceModel@{MaintenanceModel}!getGuidelineImplementations@{getGuidelineImplementations}}
\index{getGuidelineImplementations@{getGuidelineImplementations}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getGuidelineImplementations()}{getGuidelineImplementations()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a712d5e3692faa0c9e6f6cb52079a36bf} 
get\+Guideline\+Implementations (\begin{DoxyParamCaption}\item[{}]{\$guideline\+Id}{, }\item[{}]{\$limit}{ = {\ttfamily null}, }\item[{}]{\$offset}{ = {\ttfamily 0}}\end{DoxyParamCaption})}

Get all implementations of a specific guideline across different maintenance records


\begin{DoxyParams}[1]{Parameters}
int & {\em \$guideline\+Id} & \\
\hline
int & {\em \$limit} & Optional limit for number of records to return \\
\hline
int & {\em \$offset} & Optional offset for pagination \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_ac6be536865b41000e5ca2a1bb8994080}\index{MaintenanceModel@{MaintenanceModel}!getImplementedGuidelines@{getImplementedGuidelines}}
\index{getImplementedGuidelines@{getImplementedGuidelines}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getImplementedGuidelines()}{getImplementedGuidelines()}}
{\footnotesize\ttfamily \label{class_maintenance_model_ac6be536865b41000e5ca2a1bb8994080} 
get\+Implemented\+Guidelines (\begin{DoxyParamCaption}\item[{}]{\$maintenance\+Id}{}\end{DoxyParamCaption})}

Get implemented guidelines for a maintenance record


\begin{DoxyParams}[1]{Parameters}
int & {\em \$maintenance\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a308b6d3e1f8e959f87bf5ae8f6c93ae6}\index{MaintenanceModel@{MaintenanceModel}!getMaintenanceById@{getMaintenanceById}}
\index{getMaintenanceById@{getMaintenanceById}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getMaintenanceById()}{getMaintenanceById()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a308b6d3e1f8e959f87bf5ae8f6c93ae6} 
get\+Maintenance\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Get maintenance record by ID


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object\texorpdfstring{$\vert$}{|}false 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a5028d7b9ec8e052e69d9f683e1ebfd07}\index{MaintenanceModel@{MaintenanceModel}!getMaintenanceHistory@{getMaintenanceHistory}}
\index{getMaintenanceHistory@{getMaintenanceHistory}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getMaintenanceHistory()}{getMaintenanceHistory()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a5028d7b9ec8e052e69d9f683e1ebfd07} 
get\+Maintenance\+History (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

Get maintenance history for an asset


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a3479744b4309d54812e49f4fdf605c28}\index{MaintenanceModel@{MaintenanceModel}!getMaintenanceHistoryWithGuidelines@{getMaintenanceHistoryWithGuidelines}}
\index{getMaintenanceHistoryWithGuidelines@{getMaintenanceHistoryWithGuidelines}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getMaintenanceHistoryWithGuidelines()}{getMaintenanceHistoryWithGuidelines()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a3479744b4309d54812e49f4fdf605c28} 
get\+Maintenance\+History\+With\+Guidelines (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

Get maintenance records with implemented guidelines for an asset


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_adcedfb512247d32097f1d74a9e118ee9}\index{MaintenanceModel@{MaintenanceModel}!getMostImplementedGuidelines@{getMostImplementedGuidelines}}
\index{getMostImplementedGuidelines@{getMostImplementedGuidelines}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getMostImplementedGuidelines()}{getMostImplementedGuidelines()}}
{\footnotesize\ttfamily \label{class_maintenance_model_adcedfb512247d32097f1d74a9e118ee9} 
get\+Most\+Implemented\+Guidelines (\begin{DoxyParamCaption}\item[{}]{\$limit}{ = {\ttfamily null}}\end{DoxyParamCaption})}

Get most frequently implemented guidelines Returns a list of guidelines sorted by implementation count


\begin{DoxyParams}[1]{Parameters}
int & {\em \$limit} & Optional limit for number of records to return (null for all records) \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array List of guidelines with implementation counts 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_adac8c5043e0bc976a86e615fce9b4a0d}\index{MaintenanceModel@{MaintenanceModel}!getScheduledMaintenanceId@{getScheduledMaintenanceId}}
\index{getScheduledMaintenanceId@{getScheduledMaintenanceId}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{getScheduledMaintenanceId()}{getScheduledMaintenanceId()}}
{\footnotesize\ttfamily \label{class_maintenance_model_adac8c5043e0bc976a86e615fce9b4a0d} 
get\+Scheduled\+Maintenance\+Id (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{, }\item[{}]{\$maintenance\+Type}{}\end{DoxyParamCaption})}

Get scheduled maintenance record ID


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
string & {\em \$maintenance\+Type} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
int\texorpdfstring{$\vert$}{|}false 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a61baaaf1adfacd6574436248f298281e}\index{MaintenanceModel@{MaintenanceModel}!logGuidelineImplementation@{logGuidelineImplementation}}
\index{logGuidelineImplementation@{logGuidelineImplementation}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{logGuidelineImplementation()}{logGuidelineImplementation()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a61baaaf1adfacd6574436248f298281e} 
log\+Guideline\+Implementation (\begin{DoxyParamCaption}\item[{}]{\$maintenance\+Id}{, }\item[{}]{\$guideline\+Id}{}\end{DoxyParamCaption})}

Log guideline implementation for a maintenance record


\begin{DoxyParams}[1]{Parameters}
int & {\em \$maintenance\+Id} & \\
\hline
int & {\em \$guideline\+Id} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a95bb0e0eb1c41a1c8bec6ce98a0f7cb6}\index{MaintenanceModel@{MaintenanceModel}!recordChecklistCompletion@{recordChecklistCompletion}}
\index{recordChecklistCompletion@{recordChecklistCompletion}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{recordChecklistCompletion()}{recordChecklistCompletion()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a95bb0e0eb1c41a1c8bec6ce98a0f7cb6} 
record\+Checklist\+Completion (\begin{DoxyParamCaption}\item[{}]{\$maintenance\+Id}{, }\item[{}]{\$checklist\+Id}{, }\item[{}]{\$completed}{ = {\ttfamily true}, }\item[{}]{\$completed\+By}{ = {\ttfamily null}, }\item[{}]{\$notes}{ = {\ttfamily \textquotesingle{}\textquotesingle{}}}\end{DoxyParamCaption})}

Record checklist completion


\begin{DoxyParams}[1]{Parameters}
int & {\em \$maintenance\+Id} & \\
\hline
int & {\em \$checklist\+Id} & \\
\hline
bool & {\em \$completed} & \\
\hline
int & {\em \$completed\+By} & \\
\hline
string & {\em \$notes} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a9cbd796bd274dad9c2a2ef7eac75dbff}\index{MaintenanceModel@{MaintenanceModel}!recreateMissingRecords@{recreateMissingRecords}}
\index{recreateMissingRecords@{recreateMissingRecords}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{recreateMissingRecords()}{recreateMissingRecords()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a9cbd796bd274dad9c2a2ef7eac75dbff} 
recreate\+Missing\+Records (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Recreate missing maintenance history records Creates new maintenance records based on implementation records

\begin{DoxyReturn}{Returns}
array Results of the recreation 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_aac2a53c38ccfa1c46b885ac7c2811243}\index{MaintenanceModel@{MaintenanceModel}!updateAllHealthMetrics@{updateAllHealthMetrics}}
\index{updateAllHealthMetrics@{updateAllHealthMetrics}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{updateAllHealthMetrics()}{updateAllHealthMetrics()}}
{\footnotesize\ttfamily \label{class_maintenance_model_aac2a53c38ccfa1c46b885ac7c2811243} 
update\+All\+Health\+Metrics (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Update health metrics for all assets

\begin{DoxyReturn}{Returns}
int Number of assets updated 
\end{DoxyReturn}
\Hypertarget{class_maintenance_model_a567f817301870b14b63b9607a8f1c182}\index{MaintenanceModel@{MaintenanceModel}!updateScheduledMaintenanceStatus@{updateScheduledMaintenanceStatus}}
\index{updateScheduledMaintenanceStatus@{updateScheduledMaintenanceStatus}!MaintenanceModel@{MaintenanceModel}}
\doxysubsubsection{\texorpdfstring{updateScheduledMaintenanceStatus()}{updateScheduledMaintenanceStatus()}}
{\footnotesize\ttfamily \label{class_maintenance_model_a567f817301870b14b63b9607a8f1c182} 
update\+Scheduled\+Maintenance\+Status (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{, }\item[{}]{\$maintenance\+Type}{}\end{DoxyParamCaption})}

Update scheduled maintenance record to completed


\begin{DoxyParams}[1]{Parameters}
int & {\em \$asset\+Id} & \\
\hline
string & {\em \$maintenance\+Type} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{_maintenance_model_8php}{Maintenance\+Model.\+php}}\end{DoxyCompactItemize}
