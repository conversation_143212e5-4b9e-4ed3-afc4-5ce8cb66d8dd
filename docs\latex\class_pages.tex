\doxysection{Pages Class Reference}
\hypertarget{class_pages}{}\label{class_pages}\index{Pages@{Pages}}
Inheritance diagram for Pages\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{class_pages}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_pages_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_pages_a149eb92716c1084a935e04a8d95f7347}{index}} ()
\item 
\mbox{\hyperlink{class_pages_a288fa575528fc7b49c23e125a5605039}{about}} ()
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_ac531eb761b130b1925a8bae5c33af2fc}{model}} (\$model)
\item 
\mbox{\hyperlink{class_controller_a11f0e20b30b899d00b009a9bb1afe43d}{view}} (\$view, \$data=\mbox{[}$\,$\mbox{]})
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Protected Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_a0d92de8136cebc006a407442aab9db0a}{sanitize\+Post\+Data}} (\$data)
\item 
\mbox{\hyperlink{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52}{validate\+Csrf\+Token}} (\$token)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_pages_a095c5d389db211932136b53f25f39685}\index{Pages@{Pages}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Pages@{Pages}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_pages_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Constructor

Initializes the \doxylink{class_pages}{Pages} controller. No special initialization required for static pages.

\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}


\doxysubsection{Member Function Documentation}
\Hypertarget{class_pages_a288fa575528fc7b49c23e125a5605039}\index{Pages@{Pages}!about@{about}}
\index{about@{about}!Pages@{Pages}}
\doxysubsubsection{\texorpdfstring{about()}{about()}}
{\footnotesize\ttfamily \label{class_pages_a288fa575528fc7b49c23e125a5605039} 
about (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Display the about page

Shows information about the application and organization.

\begin{DoxyReturn}{Returns}
void 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_pages_a149eb92716c1084a935e04a8d95f7347}\index{Pages@{Pages}!index@{index}}
\index{index@{index}!Pages@{Pages}}
\doxysubsubsection{\texorpdfstring{index()}{index()}}
{\footnotesize\ttfamily \label{class_pages_a149eb92716c1084a935e04a8d95f7347} 
index (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Display the home page

Shows the main landing page with system statistics and overview. Displays asset count if user is logged in.

\begin{DoxyReturn}{Returns}
void 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/controllers/\mbox{\hyperlink{_pages_8php}{Pages.\+php}}\end{DoxyCompactItemize}
