\doxysection{Permission Class Reference}
\hypertarget{class_permission}{}\label{class_permission}\index{Permission@{Permission}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_permission_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_permission_a6bcadef8c7da37eb84ae95bb9b07e069}{get\+All\+Permissions}} ()
\item 
\mbox{\hyperlink{class_permission_a9a3e9fc5bf30a63366590b19e489a58f}{get\+Permissions\+By\+Category}} (\$category)
\item 
\mbox{\hyperlink{class_permission_a772b8c55b241668e6031bb263f5d832f}{get\+All\+Categories}} ()
\item 
\mbox{\hyperlink{class_permission_a3238707bb055c0edcccbd3aad5bd9a88}{get\+Permission\+By\+Id}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_permission_aef23bd8752f6255ed9ce7e15e41bba42}{get\+Permission\+By\+Name}} (\$name)
\item 
\mbox{\hyperlink{class_permission_aaf3185ee0140ecaef58f1d0c606db1f9}{create\+Permission}} (\$data)
\item 
\mbox{\hyperlink{class_permission_a9778cc0652a4dc666b7b05b3e514946a}{update\+Permission}} (\$data)
\item 
\mbox{\hyperlink{class_permission_a6a294117b98d3b789cd31258bb83e961}{delete\+Permission}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_permission_a5ee9b1302a08d6848721bd2f6f75f3b4}{get\+Roles\+With\+Permission}} (\$permission\+Id)
\end{DoxyCompactItemize}


\doxysubsection{Detailed Description}
\doxylink{class_permission}{Permission} Model Handles database operations for permissions 

\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_permission_a095c5d389db211932136b53f25f39685}\index{Permission@{Permission}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Permission@{Permission}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_permission_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_permission_aaf3185ee0140ecaef58f1d0c606db1f9}\index{Permission@{Permission}!createPermission@{createPermission}}
\index{createPermission@{createPermission}!Permission@{Permission}}
\doxysubsubsection{\texorpdfstring{createPermission()}{createPermission()}}
{\footnotesize\ttfamily \label{class_permission_aaf3185ee0140ecaef58f1d0c606db1f9} 
create\+Permission (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Create a new permission


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \doxylink{class_permission}{Permission} data \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if successful, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_permission_a6a294117b98d3b789cd31258bb83e961}\index{Permission@{Permission}!deletePermission@{deletePermission}}
\index{deletePermission@{deletePermission}!Permission@{Permission}}
\doxysubsubsection{\texorpdfstring{deletePermission()}{deletePermission()}}
{\footnotesize\ttfamily \label{class_permission_a6a294117b98d3b789cd31258bb83e961} 
delete\+Permission (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Delete a permission


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \doxylink{class_permission}{Permission} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if successful, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_permission_a772b8c55b241668e6031bb263f5d832f}\index{Permission@{Permission}!getAllCategories@{getAllCategories}}
\index{getAllCategories@{getAllCategories}!Permission@{Permission}}
\doxysubsubsection{\texorpdfstring{getAllCategories()}{getAllCategories()}}
{\footnotesize\ttfamily \label{class_permission_a772b8c55b241668e6031bb263f5d832f} 
get\+All\+Categories (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Get all permission categories

\begin{DoxyReturn}{Returns}
array Array of category names 
\end{DoxyReturn}
\Hypertarget{class_permission_a6bcadef8c7da37eb84ae95bb9b07e069}\index{Permission@{Permission}!getAllPermissions@{getAllPermissions}}
\index{getAllPermissions@{getAllPermissions}!Permission@{Permission}}
\doxysubsubsection{\texorpdfstring{getAllPermissions()}{getAllPermissions()}}
{\footnotesize\ttfamily \label{class_permission_a6bcadef8c7da37eb84ae95bb9b07e069} 
get\+All\+Permissions (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Get all permissions

\begin{DoxyReturn}{Returns}
array Array of permission objects 
\end{DoxyReturn}
\Hypertarget{class_permission_a3238707bb055c0edcccbd3aad5bd9a88}\index{Permission@{Permission}!getPermissionById@{getPermissionById}}
\index{getPermissionById@{getPermissionById}!Permission@{Permission}}
\doxysubsubsection{\texorpdfstring{getPermissionById()}{getPermissionById()}}
{\footnotesize\ttfamily \label{class_permission_a3238707bb055c0edcccbd3aad5bd9a88} 
get\+Permission\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Get permission by ID


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \doxylink{class_permission}{Permission} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object \doxylink{class_permission}{Permission} object 
\end{DoxyReturn}
\Hypertarget{class_permission_aef23bd8752f6255ed9ce7e15e41bba42}\index{Permission@{Permission}!getPermissionByName@{getPermissionByName}}
\index{getPermissionByName@{getPermissionByName}!Permission@{Permission}}
\doxysubsubsection{\texorpdfstring{getPermissionByName()}{getPermissionByName()}}
{\footnotesize\ttfamily \label{class_permission_aef23bd8752f6255ed9ce7e15e41bba42} 
get\+Permission\+By\+Name (\begin{DoxyParamCaption}\item[{}]{\$name}{}\end{DoxyParamCaption})}

Get permission by name


\begin{DoxyParams}[1]{Parameters}
string & {\em \$name} & \doxylink{class_permission}{Permission} name \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object \doxylink{class_permission}{Permission} object 
\end{DoxyReturn}
\Hypertarget{class_permission_a9a3e9fc5bf30a63366590b19e489a58f}\index{Permission@{Permission}!getPermissionsByCategory@{getPermissionsByCategory}}
\index{getPermissionsByCategory@{getPermissionsByCategory}!Permission@{Permission}}
\doxysubsubsection{\texorpdfstring{getPermissionsByCategory()}{getPermissionsByCategory()}}
{\footnotesize\ttfamily \label{class_permission_a9a3e9fc5bf30a63366590b19e489a58f} 
get\+Permissions\+By\+Category (\begin{DoxyParamCaption}\item[{}]{\$category}{}\end{DoxyParamCaption})}

Get permissions by category


\begin{DoxyParams}[1]{Parameters}
string & {\em \$category} & Category name \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array Array of permission objects 
\end{DoxyReturn}
\Hypertarget{class_permission_a5ee9b1302a08d6848721bd2f6f75f3b4}\index{Permission@{Permission}!getRolesWithPermission@{getRolesWithPermission}}
\index{getRolesWithPermission@{getRolesWithPermission}!Permission@{Permission}}
\doxysubsubsection{\texorpdfstring{getRolesWithPermission()}{getRolesWithPermission()}}
{\footnotesize\ttfamily \label{class_permission_a5ee9b1302a08d6848721bd2f6f75f3b4} 
get\+Roles\+With\+Permission (\begin{DoxyParamCaption}\item[{}]{\$permission\+Id}{}\end{DoxyParamCaption})}

Get all roles that have a specific permission


\begin{DoxyParams}[1]{Parameters}
int & {\em \$permission\+Id} & \doxylink{class_permission}{Permission} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array Array of role objects 
\end{DoxyReturn}
\Hypertarget{class_permission_a9778cc0652a4dc666b7b05b3e514946a}\index{Permission@{Permission}!updatePermission@{updatePermission}}
\index{updatePermission@{updatePermission}!Permission@{Permission}}
\doxysubsubsection{\texorpdfstring{updatePermission()}{updatePermission()}}
{\footnotesize\ttfamily \label{class_permission_a9778cc0652a4dc666b7b05b3e514946a} 
update\+Permission (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Update a permission


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \doxylink{class_permission}{Permission} data \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if successful, false otherwise 
\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{_permission_8php}{Permission.\+php}}\end{DoxyCompactItemize}
