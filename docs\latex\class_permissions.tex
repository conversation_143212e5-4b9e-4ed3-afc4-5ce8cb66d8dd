\doxysection{Permissions Class Reference}
\hypertarget{class_permissions}{}\label{class_permissions}\index{Permissions@{Permissions}}
Inheritance diagram for Permissions\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{class_permissions}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_permissions_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_permissions_a149eb92716c1084a935e04a8d95f7347}{index}} ()
\item 
\mbox{\hyperlink{class_permissions_ae4914d07a9bbe4aede7a5dea759f6287}{show}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_permissions_a837ba24a1c3095ae67613238d866f79a}{add}} ()
\item 
\mbox{\hyperlink{class_permissions_a459ed16587e3a50b39b672c7e473abc5}{edit}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_permissions_a2f8258add505482d7f00ea26493a5723}{delete}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_ac531eb761b130b1925a8bae5c33af2fc}{model}} (\$model)
\item 
\mbox{\hyperlink{class_controller_a11f0e20b30b899d00b009a9bb1afe43d}{view}} (\$view, \$data=\mbox{[}$\,$\mbox{]})
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Protected Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_a0d92de8136cebc006a407442aab9db0a}{sanitize\+Post\+Data}} (\$data)
\item 
\mbox{\hyperlink{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52}{validate\+Csrf\+Token}} (\$token)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_permissions_a095c5d389db211932136b53f25f39685}\index{Permissions@{Permissions}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Permissions@{Permissions}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_permissions_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_permissions_a837ba24a1c3095ae67613238d866f79a}\index{Permissions@{Permissions}!add@{add}}
\index{add@{add}!Permissions@{Permissions}}
\doxysubsubsection{\texorpdfstring{add()}{add()}}
{\footnotesize\ttfamily \label{class_permissions_a837ba24a1c3095ae67613238d866f79a} 
add (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Add a new permission \Hypertarget{class_permissions_a2f8258add505482d7f00ea26493a5723}\index{Permissions@{Permissions}!delete@{delete}}
\index{delete@{delete}!Permissions@{Permissions}}
\doxysubsubsection{\texorpdfstring{delete()}{delete()}}
{\footnotesize\ttfamily \label{class_permissions_a2f8258add505482d7f00ea26493a5723} 
delete (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Delete a permission


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \doxylink{class_permission}{Permission} ID \\
\hline
\end{DoxyParams}
\Hypertarget{class_permissions_a459ed16587e3a50b39b672c7e473abc5}\index{Permissions@{Permissions}!edit@{edit}}
\index{edit@{edit}!Permissions@{Permissions}}
\doxysubsubsection{\texorpdfstring{edit()}{edit()}}
{\footnotesize\ttfamily \label{class_permissions_a459ed16587e3a50b39b672c7e473abc5} 
edit (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Edit a permission


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \doxylink{class_permission}{Permission} ID \\
\hline
\end{DoxyParams}
\Hypertarget{class_permissions_a149eb92716c1084a935e04a8d95f7347}\index{Permissions@{Permissions}!index@{index}}
\index{index@{index}!Permissions@{Permissions}}
\doxysubsubsection{\texorpdfstring{index()}{index()}}
{\footnotesize\ttfamily \label{class_permissions_a149eb92716c1084a935e04a8d95f7347} 
index (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Display all permissions \Hypertarget{class_permissions_ae4914d07a9bbe4aede7a5dea759f6287}\index{Permissions@{Permissions}!show@{show}}
\index{show@{show}!Permissions@{Permissions}}
\doxysubsubsection{\texorpdfstring{show()}{show()}}
{\footnotesize\ttfamily \label{class_permissions_ae4914d07a9bbe4aede7a5dea759f6287} 
show (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Show permission details


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \doxylink{class_permission}{Permission} ID \\
\hline
\end{DoxyParams}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/controllers/\mbox{\hyperlink{controllers_2_permissions_8php}{Permissions.\+php}}\end{DoxyCompactItemize}
