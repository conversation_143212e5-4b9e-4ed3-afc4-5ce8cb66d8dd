\doxysection{Role Class Reference}
\hypertarget{class_role}{}\label{class_role}\index{Role@{Role}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_role_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_role_ad9d41efb9af8f1d2f8d9575069976da0}{get\+All\+Roles}} ()
\item 
\mbox{\hyperlink{class_role_adbabecd774e69faa53d68bb8ddedbd54}{get\+Role\+By\+Id}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_role_a8314906463666f969d42371a864a164e}{get\+Role\+By\+Name}} (\$name)
\item 
\mbox{\hyperlink{class_role_a768411d287ea069df4e0331e6bcefe09}{create\+Role}} (\$data)
\item 
\mbox{\hyperlink{class_role_a5aee45741b09c9eee6771d09a088c220}{update\+Role}} (\$data)
\item 
\mbox{\hyperlink{class_role_a4ef8d531b5037715f076c716eec66f66}{delete\+Role}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_role_acbd22d5de18650bca342da1774129b78}{get\+Role\+Permissions}} (\$role\+Id)
\item 
\mbox{\hyperlink{class_role_af180b7a33eaf553419c164116e7bcdb2}{assign\+Permissions}} (\$role\+Id, \$permission\+Ids)
\item 
\mbox{\hyperlink{class_role_ace6175dc80da953ba86a1f9f30d61ac4}{has\+Permission}} (\$role\+Id, \$permission\+Id)
\item 
\mbox{\hyperlink{class_role_ae5bd1e5322a775264f3a66451069f600}{get\+Users\+With\+Role}} (\$role\+Id)
\item 
\mbox{\hyperlink{class_role_a933ad1bc0d674f271c3f54b8ec236537}{update\+Role\+Last\+Modified}} (\$role\+Id)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_role_a095c5d389db211932136b53f25f39685}\index{Role@{Role}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_role_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Constructor

Initializes the \doxylink{class_role}{Role} model with database connection.

\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}


\doxysubsection{Member Function Documentation}
\Hypertarget{class_role_af180b7a33eaf553419c164116e7bcdb2}\index{Role@{Role}!assignPermissions@{assignPermissions}}
\index{assignPermissions@{assignPermissions}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{assignPermissions()}{assignPermissions()}}
{\footnotesize\ttfamily \label{class_role_af180b7a33eaf553419c164116e7bcdb2} 
assign\+Permissions (\begin{DoxyParamCaption}\item[{}]{\$role\+Id}{, }\item[{}]{\$permission\+Ids}{}\end{DoxyParamCaption})}

Assign permissions to a role


\begin{DoxyParams}[1]{Parameters}
int & {\em \$role\+Id} & \doxylink{class_role}{Role} ID \\
\hline
array & {\em \$permission\+Ids} & Array of permission IDs \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if successful, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_role_a768411d287ea069df4e0331e6bcefe09}\index{Role@{Role}!createRole@{createRole}}
\index{createRole@{createRole}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{createRole()}{createRole()}}
{\footnotesize\ttfamily \label{class_role_a768411d287ea069df4e0331e6bcefe09} 
create\+Role (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Create a new role


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \doxylink{class_role}{Role} data \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if successful, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_role_a4ef8d531b5037715f076c716eec66f66}\index{Role@{Role}!deleteRole@{deleteRole}}
\index{deleteRole@{deleteRole}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{deleteRole()}{deleteRole()}}
{\footnotesize\ttfamily \label{class_role_a4ef8d531b5037715f076c716eec66f66} 
delete\+Role (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Delete a role


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \doxylink{class_role}{Role} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if successful, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_role_ad9d41efb9af8f1d2f8d9575069976da0}\index{Role@{Role}!getAllRoles@{getAllRoles}}
\index{getAllRoles@{getAllRoles}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{getAllRoles()}{getAllRoles()}}
{\footnotesize\ttfamily \label{class_role_ad9d41efb9af8f1d2f8d9575069976da0} 
get\+All\+Roles (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Get all roles

Retrieves all roles from the database ordered by name.

\begin{DoxyReturn}{Returns}
array Array of role objects 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_role_adbabecd774e69faa53d68bb8ddedbd54}\index{Role@{Role}!getRoleById@{getRoleById}}
\index{getRoleById@{getRoleById}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{getRoleById()}{getRoleById()}}
{\footnotesize\ttfamily \label{class_role_adbabecd774e69faa53d68bb8ddedbd54} 
get\+Role\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Get role by ID


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \doxylink{class_role}{Role} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object \doxylink{class_role}{Role} object 
\end{DoxyReturn}
\Hypertarget{class_role_a8314906463666f969d42371a864a164e}\index{Role@{Role}!getRoleByName@{getRoleByName}}
\index{getRoleByName@{getRoleByName}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{getRoleByName()}{getRoleByName()}}
{\footnotesize\ttfamily \label{class_role_a8314906463666f969d42371a864a164e} 
get\+Role\+By\+Name (\begin{DoxyParamCaption}\item[{}]{\$name}{}\end{DoxyParamCaption})}

Get role by name


\begin{DoxyParams}[1]{Parameters}
string & {\em \$name} & \doxylink{class_role}{Role} name \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object \doxylink{class_role}{Role} object 
\end{DoxyReturn}
\Hypertarget{class_role_acbd22d5de18650bca342da1774129b78}\index{Role@{Role}!getRolePermissions@{getRolePermissions}}
\index{getRolePermissions@{getRolePermissions}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{getRolePermissions()}{getRolePermissions()}}
{\footnotesize\ttfamily \label{class_role_acbd22d5de18650bca342da1774129b78} 
get\+Role\+Permissions (\begin{DoxyParamCaption}\item[{}]{\$role\+Id}{}\end{DoxyParamCaption})}

Get all permissions for a role


\begin{DoxyParams}[1]{Parameters}
int & {\em \$role\+Id} & \doxylink{class_role}{Role} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array Array of permission objects 
\end{DoxyReturn}
\Hypertarget{class_role_ae5bd1e5322a775264f3a66451069f600}\index{Role@{Role}!getUsersWithRole@{getUsersWithRole}}
\index{getUsersWithRole@{getUsersWithRole}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{getUsersWithRole()}{getUsersWithRole()}}
{\footnotesize\ttfamily \label{class_role_ae5bd1e5322a775264f3a66451069f600} 
get\+Users\+With\+Role (\begin{DoxyParamCaption}\item[{}]{\$role\+Id}{}\end{DoxyParamCaption})}

Get all users with a specific role


\begin{DoxyParams}[1]{Parameters}
int & {\em \$role\+Id} & \doxylink{class_role}{Role} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array Array of user objects 
\end{DoxyReturn}
\Hypertarget{class_role_ace6175dc80da953ba86a1f9f30d61ac4}\index{Role@{Role}!hasPermission@{hasPermission}}
\index{hasPermission@{hasPermission}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{hasPermission()}{hasPermission()}}
{\footnotesize\ttfamily \label{class_role_ace6175dc80da953ba86a1f9f30d61ac4} 
has\+Permission (\begin{DoxyParamCaption}\item[{}]{\$role\+Id}{, }\item[{}]{\$permission\+Id}{}\end{DoxyParamCaption})}

Check if a role has a specific permission


\begin{DoxyParams}[1]{Parameters}
int & {\em \$role\+Id} & \doxylink{class_role}{Role} ID \\
\hline
int & {\em \$permission\+Id} & \doxylink{class_permission}{Permission} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if the role has the permission, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_role_a5aee45741b09c9eee6771d09a088c220}\index{Role@{Role}!updateRole@{updateRole}}
\index{updateRole@{updateRole}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{updateRole()}{updateRole()}}
{\footnotesize\ttfamily \label{class_role_a5aee45741b09c9eee6771d09a088c220} 
update\+Role (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Update a role


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \doxylink{class_role}{Role} data \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if successful, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_role_a933ad1bc0d674f271c3f54b8ec236537}\index{Role@{Role}!updateRoleLastModified@{updateRoleLastModified}}
\index{updateRoleLastModified@{updateRoleLastModified}!Role@{Role}}
\doxysubsubsection{\texorpdfstring{updateRoleLastModified()}{updateRoleLastModified()}}
{\footnotesize\ttfamily \label{class_role_a933ad1bc0d674f271c3f54b8ec236537} 
update\+Role\+Last\+Modified (\begin{DoxyParamCaption}\item[{}]{\$role\+Id}{}\end{DoxyParamCaption})}

Update the last\+\_\+modified timestamp for a role This is used to track when permissions for a role were last updated


\begin{DoxyParams}[1]{Parameters}
int & {\em \$role\+Id} & \doxylink{class_role}{Role} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if successful, false otherwise 
\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{_role_8php}{Role.\+php}}\end{DoxyCompactItemize}
