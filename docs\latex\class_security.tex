\doxysection{Security Class Reference}
\hypertarget{class_security}{}\label{class_security}\index{Security@{Security}}
\doxysubsubsection*{Static Public Member Functions}
\begin{DoxyCompactItemize}
\item 
static \mbox{\hyperlink{class_security_a70b34e128c2e082e28e4b133bf532fbb}{generate\+Csrf\+Token}} ()
\item 
static \mbox{\hyperlink{class_security_a2efdb8dd02e7a6b3404fcc2efc9373be}{validate\+Csrf\+Token}} (\$token, \$remove\+After\+Validation=true)
\item 
static \mbox{\hyperlink{class_security_a5b86b53ed28d8dc7b1e9fe2a2d21cda8}{escape\+Output}} (\$output)
\item 
static \mbox{\hyperlink{class_security_aa7a83d3a48c6f050ffa947f770c291aa}{sanitize\+Input}} (\$input)
\item 
static \mbox{\hyperlink{class_security_ab7474581f8ab54f56d7c285f873d4512}{sanitize\+Post\+Data}} (\$post\+Data)
\item 
static \mbox{\hyperlink{class_security_a643f5130705b7cbb5981d0edf91b4505}{set\+Security\+Headers}} ()
\end{DoxyCompactItemize}


\doxysubsection{Member Function Documentation}
\Hypertarget{class_security_a5b86b53ed28d8dc7b1e9fe2a2d21cda8}\index{Security@{Security}!escapeOutput@{escapeOutput}}
\index{escapeOutput@{escapeOutput}!Security@{Security}}
\doxysubsubsection{\texorpdfstring{escapeOutput()}{escapeOutput()}}
{\footnotesize\ttfamily \label{class_security_a5b86b53ed28d8dc7b1e9fe2a2d21cda8} 
static escape\+Output (\begin{DoxyParamCaption}\item[{}]{\$output}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Escape output to prevent XSS attacks


\begin{DoxyParams}[1]{Parameters}
string & {\em \$output} & The string to escape \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
string The escaped string 
\end{DoxyReturn}
\Hypertarget{class_security_a70b34e128c2e082e28e4b133bf532fbb}\index{Security@{Security}!generateCsrfToken@{generateCsrfToken}}
\index{generateCsrfToken@{generateCsrfToken}!Security@{Security}}
\doxysubsubsection{\texorpdfstring{generateCsrfToken()}{generateCsrfToken()}}
{\footnotesize\ttfamily \label{class_security_a70b34e128c2e082e28e4b133bf532fbb} 
static generate\+Csrf\+Token (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Generate a CSRF token and store it in the session

Creates a cryptographically secure random token for CSRF protection and stores it in the session with a timestamp for expiration tracking.

\begin{DoxyReturn}{Returns}
string The generated CSRF token 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_security_aa7a83d3a48c6f050ffa947f770c291aa}\index{Security@{Security}!sanitizeInput@{sanitizeInput}}
\index{sanitizeInput@{sanitizeInput}!Security@{Security}}
\doxysubsubsection{\texorpdfstring{sanitizeInput()}{sanitizeInput()}}
{\footnotesize\ttfamily \label{class_security_aa7a83d3a48c6f050ffa947f770c291aa} 
static sanitize\+Input (\begin{DoxyParamCaption}\item[{}]{\$input}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Sanitize input to prevent XSS attacks


\begin{DoxyParams}[1]{Parameters}
string | array & {\em \$input} & The input to sanitize \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
string\texorpdfstring{$\vert$}{|}array The sanitized input 
\end{DoxyReturn}
\Hypertarget{class_security_ab7474581f8ab54f56d7c285f873d4512}\index{Security@{Security}!sanitizePostData@{sanitizePostData}}
\index{sanitizePostData@{sanitizePostData}!Security@{Security}}
\doxysubsubsection{\texorpdfstring{sanitizePostData()}{sanitizePostData()}}
{\footnotesize\ttfamily \label{class_security_ab7474581f8ab54f56d7c285f873d4512} 
static sanitize\+Post\+Data (\begin{DoxyParamCaption}\item[{}]{\$post\+Data}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Sanitize POST data (replacement for deprecated FILTER\+\_\+\+SANITIZE\+\_\+\+STRING)


\begin{DoxyParams}[1]{Parameters}
array & {\em \$post\+Data} & The POST data to sanitize \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array The sanitized POST data 
\end{DoxyReturn}
\Hypertarget{class_security_a643f5130705b7cbb5981d0edf91b4505}\index{Security@{Security}!setSecurityHeaders@{setSecurityHeaders}}
\index{setSecurityHeaders@{setSecurityHeaders}!Security@{Security}}
\doxysubsubsection{\texorpdfstring{setSecurityHeaders()}{setSecurityHeaders()}}
{\footnotesize\ttfamily \label{class_security_a643f5130705b7cbb5981d0edf91b4505} 
static set\+Security\+Headers (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Set security headers for the response \Hypertarget{class_security_a2efdb8dd02e7a6b3404fcc2efc9373be}\index{Security@{Security}!validateCsrfToken@{validateCsrfToken}}
\index{validateCsrfToken@{validateCsrfToken}!Security@{Security}}
\doxysubsubsection{\texorpdfstring{validateCsrfToken()}{validateCsrfToken()}}
{\footnotesize\ttfamily \label{class_security_a2efdb8dd02e7a6b3404fcc2efc9373be} 
static validate\+Csrf\+Token (\begin{DoxyParamCaption}\item[{}]{\$token}{, }\item[{}]{\$remove\+After\+Validation}{ = {\ttfamily true}}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Validate a CSRF token

Checks if the provided token exists in the session and is not expired. Tokens are valid for 2 hours from creation time.


\begin{DoxyParams}[1]{Parameters}
string & {\em \$token} & The token to validate \\
\hline
bool & {\em \$remove\+After\+Validation} & Whether to remove the token after validation (default\+: true) \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if the token is valid, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/helpers/\mbox{\hyperlink{_security_8php}{Security.\+php}}\end{DoxyCompactItemize}
