\doxysection{Security\+Enhancements Class Reference}
\hypertarget{class_security_enhancements}{}\label{class_security_enhancements}\index{SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection*{Static Public Member Functions}
\begin{DoxyCompactItemize}
\item 
static \mbox{\hyperlink{class_security_enhancements_a013913ef660fea9e6f3af59afd767b96}{log\+Security\+Event}} (\$event\+Type, \$description, \$user\+Id=null)
\item 
static \mbox{\hyperlink{class_security_enhancements_a4206d1d37283f2055dcf0f4295a7a693}{record\+Login\+Attempt}} (\$email, \$success, \$user\+Id=null)
\item 
static \mbox{\hyperlink{class_security_enhancements_a2cf9beec2eb67fb4f7662468376c03ef}{is\+Account\+Locked}} (\$user\+Id)
\item 
static \mbox{\hyperlink{class_security_enhancements_ae969c75aed9842b49aa3095c0b8ec0d2}{increment\+Failed\+Login\+Attempts}} (\$user\+Id)
\item 
static \mbox{\hyperlink{class_security_enhancements_ac3e149b33afa4881d81ed72bc8598d05}{reset\+Failed\+Login\+Attempts}} (\$user\+Id)
\item 
static \mbox{\hyperlink{class_security_enhancements_a0695c274c2a2d9dc44179baf89f2f2d0}{lock\+Account}} (\$user\+Id, \$reason=\textquotesingle{}Too many failed login attempts\textquotesingle{})
\item 
static \mbox{\hyperlink{class_security_enhancements_a2958f6a8493757a62052e1b2e6d7e37c}{unlock\+Account}} (\$user\+Id)
\item 
static \mbox{\hyperlink{class_security_enhancements_a6a1d971fda6ba0d428b6627bd57cd7cf}{check\+Password\+Reset\+Rate\+Limit}} (\$email)
\item 
static \mbox{\hyperlink{class_security_enhancements_ad153125ec23b30e3f7401166f6b250e3}{create\+Remember\+Me\+Token}} (\$user\+Id)
\item 
static \mbox{\hyperlink{class_security_enhancements_a2d1905793279ffb8e49cd3a33a8adc7e}{verify\+Remember\+Me\+Token}} (\$selector, \$validator)
\item 
static \mbox{\hyperlink{class_security_enhancements_a8ed7e1d4d635331234fc654f24757b20}{delete\+Remember\+Me\+Token}} (\$selector)
\item 
static \mbox{\hyperlink{class_security_enhancements_aa4fd9e09fb2a0c403858e63983a319d5}{delete\+All\+Remember\+Me\+Tokens}} (\$user\+Id)
\item 
static \mbox{\hyperlink{class_security_enhancements_a75213e4a19f7cf342423d6aa3742735d}{check\+Import\+Rate\+Limit}} (\$user\+Id)
\item 
static \mbox{\hyperlink{class_security_enhancements_ac583ea07fd84f5b1cd5d04f28d7dcd43}{record\+Import\+Operation}} (\$user\+Id, \$file\+Name, \$file\+Size, \$success=true)
\item 
static \mbox{\hyperlink{class_security_enhancements_a791ef10ab64879493bfba71f2216445e}{validate\+File\+Size}} (\$file\+Size)
\item 
static \mbox{\hyperlink{class_security_enhancements_a4244442b4f4abeeddaca69cf96f3d95b}{validate\+Csv\+Content}} (\$file\+Path)
\end{DoxyCompactItemize}
\doxysubsubsection*{Data Fields}
\begin{DoxyCompactItemize}
\item 
const \mbox{\hyperlink{class_security_enhancements_a0c9fe713d3d1b6f166d8768aaf619275}{MAX\+\_\+\+LOGIN\+\_\+\+ATTEMPTS}} = 5
\item 
const \mbox{\hyperlink{class_security_enhancements_a297fc9db6f9eccebc5f8479a2ff3c585}{LOCKOUT\+\_\+\+DURATION}} = 30
\item 
const \mbox{\hyperlink{class_security_enhancements_ab1340b7d8521a3f20538fa27725ec834}{PASSWORD\+\_\+\+RESET\+\_\+\+RATE\+\_\+\+LIMIT}} = 3
\item 
const \mbox{\hyperlink{class_security_enhancements_abb4b1920fd090c41e8a9d3b91ce9f124}{IMPORT\+\_\+\+RATE\+\_\+\+LIMIT}} = 5
\item 
const \mbox{\hyperlink{class_security_enhancements_a7a77c70c17891d528d8d405ba8111c30}{MAX\+\_\+\+IMPORT\+\_\+\+FILE\+\_\+\+SIZE}} = 10485760
\item 
const \mbox{\hyperlink{class_security_enhancements_a3fe72921270c08d48d6b9b95648153e3}{REMEMBER\+\_\+\+ME\+\_\+\+EXPIRY}} = 30
\end{DoxyCompactItemize}


\doxysubsection{Detailed Description}
\doxylink{class_security}{Security} Enhancements Helper Contains methods for enhanced security features 

\doxysubsection{Member Function Documentation}
\Hypertarget{class_security_enhancements_a75213e4a19f7cf342423d6aa3742735d}\index{SecurityEnhancements@{SecurityEnhancements}!checkImportRateLimit@{checkImportRateLimit}}
\index{checkImportRateLimit@{checkImportRateLimit}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{checkImportRateLimit()}{checkImportRateLimit()}}
{\footnotesize\ttfamily \label{class_security_enhancements_a75213e4a19f7cf342423d6aa3742735d} 
static check\+Import\+Rate\+Limit (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Check if import operations are within rate limits


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if within rate limits, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_a6a1d971fda6ba0d428b6627bd57cd7cf}\index{SecurityEnhancements@{SecurityEnhancements}!checkPasswordResetRateLimit@{checkPasswordResetRateLimit}}
\index{checkPasswordResetRateLimit@{checkPasswordResetRateLimit}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{checkPasswordResetRateLimit()}{checkPasswordResetRateLimit()}}
{\footnotesize\ttfamily \label{class_security_enhancements_a6a1d971fda6ba0d428b6627bd57cd7cf} 
static check\+Password\+Reset\+Rate\+Limit (\begin{DoxyParamCaption}\item[{}]{\$email}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Check if password reset requests are within rate limits


\begin{DoxyParams}[1]{Parameters}
string & {\em \$email} & Email address \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if within rate limits, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_ad153125ec23b30e3f7401166f6b250e3}\index{SecurityEnhancements@{SecurityEnhancements}!createRememberMeToken@{createRememberMeToken}}
\index{createRememberMeToken@{createRememberMeToken}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{createRememberMeToken()}{createRememberMeToken()}}
{\footnotesize\ttfamily \label{class_security_enhancements_ad153125ec23b30e3f7401166f6b250e3} 
static create\+Remember\+Me\+Token (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Create a remember me token


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array\texorpdfstring{$\vert$}{|}bool Token data if created successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_aa4fd9e09fb2a0c403858e63983a319d5}\index{SecurityEnhancements@{SecurityEnhancements}!deleteAllRememberMeTokens@{deleteAllRememberMeTokens}}
\index{deleteAllRememberMeTokens@{deleteAllRememberMeTokens}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{deleteAllRememberMeTokens()}{deleteAllRememberMeTokens()}}
{\footnotesize\ttfamily \label{class_security_enhancements_aa4fd9e09fb2a0c403858e63983a319d5} 
static delete\+All\+Remember\+Me\+Tokens (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Delete all remember me tokens for a user


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if deleted successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_a8ed7e1d4d635331234fc654f24757b20}\index{SecurityEnhancements@{SecurityEnhancements}!deleteRememberMeToken@{deleteRememberMeToken}}
\index{deleteRememberMeToken@{deleteRememberMeToken}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{deleteRememberMeToken()}{deleteRememberMeToken()}}
{\footnotesize\ttfamily \label{class_security_enhancements_a8ed7e1d4d635331234fc654f24757b20} 
static delete\+Remember\+Me\+Token (\begin{DoxyParamCaption}\item[{}]{\$selector}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Delete a remember me token


\begin{DoxyParams}[1]{Parameters}
string & {\em \$selector} & Token selector \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if deleted successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_ae969c75aed9842b49aa3095c0b8ec0d2}\index{SecurityEnhancements@{SecurityEnhancements}!incrementFailedLoginAttempts@{incrementFailedLoginAttempts}}
\index{incrementFailedLoginAttempts@{incrementFailedLoginAttempts}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{incrementFailedLoginAttempts()}{incrementFailedLoginAttempts()}}
{\footnotesize\ttfamily \label{class_security_enhancements_ae969c75aed9842b49aa3095c0b8ec0d2} 
static increment\+Failed\+Login\+Attempts (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Increment failed login attempts and lock account if necessary


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if account was locked, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_a2cf9beec2eb67fb4f7662468376c03ef}\index{SecurityEnhancements@{SecurityEnhancements}!isAccountLocked@{isAccountLocked}}
\index{isAccountLocked@{isAccountLocked}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{isAccountLocked()}{isAccountLocked()}}
{\footnotesize\ttfamily \label{class_security_enhancements_a2cf9beec2eb67fb4f7662468376c03ef} 
static is\+Account\+Locked (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Check if an account is locked


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID to check \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if account is locked, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_a0695c274c2a2d9dc44179baf89f2f2d0}\index{SecurityEnhancements@{SecurityEnhancements}!lockAccount@{lockAccount}}
\index{lockAccount@{lockAccount}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{lockAccount()}{lockAccount()}}
{\footnotesize\ttfamily \label{class_security_enhancements_a0695c274c2a2d9dc44179baf89f2f2d0} 
static lock\+Account (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{, }\item[{}]{\$reason}{ = {\ttfamily \textquotesingle{}Too~many~failed~login~attempts\textquotesingle{}}}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Lock a user account


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID to lock \\
\hline
string & {\em \$reason} & Reason for lockout \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if locked successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_a013913ef660fea9e6f3af59afd767b96}\index{SecurityEnhancements@{SecurityEnhancements}!logSecurityEvent@{logSecurityEvent}}
\index{logSecurityEvent@{logSecurityEvent}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{logSecurityEvent()}{logSecurityEvent()}}
{\footnotesize\ttfamily \label{class_security_enhancements_a013913ef660fea9e6f3af59afd767b96} 
static log\+Security\+Event (\begin{DoxyParamCaption}\item[{}]{\$event\+Type}{, }\item[{}]{\$description}{, }\item[{}]{\$user\+Id}{ = {\ttfamily null}}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Log a security event


\begin{DoxyParams}[1]{Parameters}
string & {\em \$event\+Type} & The type of security event \\
\hline
string & {\em \$description} & Description of the event \\
\hline
int | null & {\em \$user\+Id} & \doxylink{class_user}{User} ID (if applicable) \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if logged successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_ac583ea07fd84f5b1cd5d04f28d7dcd43}\index{SecurityEnhancements@{SecurityEnhancements}!recordImportOperation@{recordImportOperation}}
\index{recordImportOperation@{recordImportOperation}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{recordImportOperation()}{recordImportOperation()}}
{\footnotesize\ttfamily \label{class_security_enhancements_ac583ea07fd84f5b1cd5d04f28d7dcd43} 
static record\+Import\+Operation (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{, }\item[{}]{\$file\+Name}{, }\item[{}]{\$file\+Size}{, }\item[{}]{\$success}{ = {\ttfamily true}}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Record an import operation


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
string & {\em \$file\+Name} & Name of the imported file \\
\hline
int & {\em \$file\+Size} & Size of the imported file in bytes \\
\hline
bool & {\em \$success} & Whether the import was successful \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if recorded successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_a4206d1d37283f2055dcf0f4295a7a693}\index{SecurityEnhancements@{SecurityEnhancements}!recordLoginAttempt@{recordLoginAttempt}}
\index{recordLoginAttempt@{recordLoginAttempt}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{recordLoginAttempt()}{recordLoginAttempt()}}
{\footnotesize\ttfamily \label{class_security_enhancements_a4206d1d37283f2055dcf0f4295a7a693} 
static record\+Login\+Attempt (\begin{DoxyParamCaption}\item[{}]{\$email}{, }\item[{}]{\$success}{, }\item[{}]{\$user\+Id}{ = {\ttfamily null}}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Record a login attempt


\begin{DoxyParams}[1]{Parameters}
string & {\em \$email} & Email address used in the attempt \\
\hline
bool & {\em \$success} & Whether the login was successful \\
\hline
int | null & {\em \$user\+Id} & \doxylink{class_user}{User} ID (if successful) \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if recorded successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_ac3e149b33afa4881d81ed72bc8598d05}\index{SecurityEnhancements@{SecurityEnhancements}!resetFailedLoginAttempts@{resetFailedLoginAttempts}}
\index{resetFailedLoginAttempts@{resetFailedLoginAttempts}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{resetFailedLoginAttempts()}{resetFailedLoginAttempts()}}
{\footnotesize\ttfamily \label{class_security_enhancements_ac3e149b33afa4881d81ed72bc8598d05} 
static reset\+Failed\+Login\+Attempts (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Reset failed login attempts counter


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if reset successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_a2958f6a8493757a62052e1b2e6d7e37c}\index{SecurityEnhancements@{SecurityEnhancements}!unlockAccount@{unlockAccount}}
\index{unlockAccount@{unlockAccount}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{unlockAccount()}{unlockAccount()}}
{\footnotesize\ttfamily \label{class_security_enhancements_a2958f6a8493757a62052e1b2e6d7e37c} 
static unlock\+Account (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Unlock a user account


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID to unlock \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if unlocked successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_a4244442b4f4abeeddaca69cf96f3d95b}\index{SecurityEnhancements@{SecurityEnhancements}!validateCsvContent@{validateCsvContent}}
\index{validateCsvContent@{validateCsvContent}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{validateCsvContent()}{validateCsvContent()}}
{\footnotesize\ttfamily \label{class_security_enhancements_a4244442b4f4abeeddaca69cf96f3d95b} 
static validate\+Csv\+Content (\begin{DoxyParamCaption}\item[{}]{\$file\+Path}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Validate CSV file content


\begin{DoxyParams}[1]{Parameters}
string & {\em \$file\+Path} & Path to the CSV file \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool\texorpdfstring{$\vert$}{|}array True if valid, array of errors if invalid 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_a791ef10ab64879493bfba71f2216445e}\index{SecurityEnhancements@{SecurityEnhancements}!validateFileSize@{validateFileSize}}
\index{validateFileSize@{validateFileSize}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{validateFileSize()}{validateFileSize()}}
{\footnotesize\ttfamily \label{class_security_enhancements_a791ef10ab64879493bfba71f2216445e} 
static validate\+File\+Size (\begin{DoxyParamCaption}\item[{}]{\$file\+Size}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Validate file size


\begin{DoxyParams}[1]{Parameters}
int & {\em \$file\+Size} & Size of the file in bytes \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if file size is valid, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_security_enhancements_a2d1905793279ffb8e49cd3a33a8adc7e}\index{SecurityEnhancements@{SecurityEnhancements}!verifyRememberMeToken@{verifyRememberMeToken}}
\index{verifyRememberMeToken@{verifyRememberMeToken}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{verifyRememberMeToken()}{verifyRememberMeToken()}}
{\footnotesize\ttfamily \label{class_security_enhancements_a2d1905793279ffb8e49cd3a33a8adc7e} 
static verify\+Remember\+Me\+Token (\begin{DoxyParamCaption}\item[{}]{\$selector}{, }\item[{}]{\$validator}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}

Verify a remember me token


\begin{DoxyParams}[1]{Parameters}
string & {\em \$selector} & Token selector \\
\hline
string & {\em \$validator} & Token validator \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
int\texorpdfstring{$\vert$}{|}bool \doxylink{class_user}{User} ID if token is valid, false otherwise 
\end{DoxyReturn}


\doxysubsection{Field Documentation}
\Hypertarget{class_security_enhancements_abb4b1920fd090c41e8a9d3b91ce9f124}\index{SecurityEnhancements@{SecurityEnhancements}!IMPORT\_RATE\_LIMIT@{IMPORT\_RATE\_LIMIT}}
\index{IMPORT\_RATE\_LIMIT@{IMPORT\_RATE\_LIMIT}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{IMPORT\_RATE\_LIMIT}{IMPORT\_RATE\_LIMIT}}
{\footnotesize\ttfamily \label{class_security_enhancements_abb4b1920fd090c41e8a9d3b91ce9f124} 
const IMPORT\+\_\+\+RATE\+\_\+\+LIMIT = 5}

\Hypertarget{class_security_enhancements_a297fc9db6f9eccebc5f8479a2ff3c585}\index{SecurityEnhancements@{SecurityEnhancements}!LOCKOUT\_DURATION@{LOCKOUT\_DURATION}}
\index{LOCKOUT\_DURATION@{LOCKOUT\_DURATION}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{LOCKOUT\_DURATION}{LOCKOUT\_DURATION}}
{\footnotesize\ttfamily \label{class_security_enhancements_a297fc9db6f9eccebc5f8479a2ff3c585} 
const LOCKOUT\+\_\+\+DURATION = 30}

\Hypertarget{class_security_enhancements_a7a77c70c17891d528d8d405ba8111c30}\index{SecurityEnhancements@{SecurityEnhancements}!MAX\_IMPORT\_FILE\_SIZE@{MAX\_IMPORT\_FILE\_SIZE}}
\index{MAX\_IMPORT\_FILE\_SIZE@{MAX\_IMPORT\_FILE\_SIZE}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{MAX\_IMPORT\_FILE\_SIZE}{MAX\_IMPORT\_FILE\_SIZE}}
{\footnotesize\ttfamily \label{class_security_enhancements_a7a77c70c17891d528d8d405ba8111c30} 
const MAX\+\_\+\+IMPORT\+\_\+\+FILE\+\_\+\+SIZE = 10485760}

\Hypertarget{class_security_enhancements_a0c9fe713d3d1b6f166d8768aaf619275}\index{SecurityEnhancements@{SecurityEnhancements}!MAX\_LOGIN\_ATTEMPTS@{MAX\_LOGIN\_ATTEMPTS}}
\index{MAX\_LOGIN\_ATTEMPTS@{MAX\_LOGIN\_ATTEMPTS}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{MAX\_LOGIN\_ATTEMPTS}{MAX\_LOGIN\_ATTEMPTS}}
{\footnotesize\ttfamily \label{class_security_enhancements_a0c9fe713d3d1b6f166d8768aaf619275} 
const MAX\+\_\+\+LOGIN\+\_\+\+ATTEMPTS = 5}

\Hypertarget{class_security_enhancements_ab1340b7d8521a3f20538fa27725ec834}\index{SecurityEnhancements@{SecurityEnhancements}!PASSWORD\_RESET\_RATE\_LIMIT@{PASSWORD\_RESET\_RATE\_LIMIT}}
\index{PASSWORD\_RESET\_RATE\_LIMIT@{PASSWORD\_RESET\_RATE\_LIMIT}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{PASSWORD\_RESET\_RATE\_LIMIT}{PASSWORD\_RESET\_RATE\_LIMIT}}
{\footnotesize\ttfamily \label{class_security_enhancements_ab1340b7d8521a3f20538fa27725ec834} 
const PASSWORD\+\_\+\+RESET\+\_\+\+RATE\+\_\+\+LIMIT = 3}

\Hypertarget{class_security_enhancements_a3fe72921270c08d48d6b9b95648153e3}\index{SecurityEnhancements@{SecurityEnhancements}!REMEMBER\_ME\_EXPIRY@{REMEMBER\_ME\_EXPIRY}}
\index{REMEMBER\_ME\_EXPIRY@{REMEMBER\_ME\_EXPIRY}!SecurityEnhancements@{SecurityEnhancements}}
\doxysubsubsection{\texorpdfstring{REMEMBER\_ME\_EXPIRY}{REMEMBER\_ME\_EXPIRY}}
{\footnotesize\ttfamily \label{class_security_enhancements_a3fe72921270c08d48d6b9b95648153e3} 
const REMEMBER\+\_\+\+ME\+\_\+\+EXPIRY = 30}



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/helpers/\mbox{\hyperlink{_security_enhancements_8php}{Security\+Enhancements.\+php}}\end{DoxyCompactItemize}
