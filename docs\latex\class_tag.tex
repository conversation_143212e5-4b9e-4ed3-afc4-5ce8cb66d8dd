\doxysection{Tag Class Reference}
\hypertarget{class_tag}{}\label{class_tag}\index{Tag@{Tag}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_tag_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_tag_ae07173ab06a20e2f5bd928cc0518e01f}{get\+Tags}} ()
\item 
\mbox{\hyperlink{class_tag_a401430eadd174788ff0568d8f0c9c6d8}{get\+Tag\+By\+Id}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_tag_a2e986e3f035b2b0fb56c40778ce1f040}{get\+Tag\+By\+Name}} (\$name)
\item 
\mbox{\hyperlink{class_tag_a4ff2d7b570ab7813d09be22cb72fcd46}{add\+Tag}} (\$data)
\item 
\mbox{\hyperlink{class_tag_a38825dc7a692a32bf2fecb1df70fed9d}{update\+Tag}} (\$data)
\item 
\mbox{\hyperlink{class_tag_a8136d79d1d3aa83567d0baaccdb97335}{delete\+Tag}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_tag_a425a55b885d03edeafb572af23106a46}{get\+Tags\+For\+Asset}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_tag_a2b19e0c9ea97d5b603d6a5a02940c523}{add\+Tag\+To\+Asset}} (\$asset\+Id, \$tag\+Id)
\item 
\mbox{\hyperlink{class_tag_a880f1cf91fb22405339d3e55f1e51afb}{remove\+Tag\+From\+Asset}} (\$asset\+Id, \$tag\+Id)
\item 
\mbox{\hyperlink{class_tag_aaf77d50b5c6361054aca053c5d2d6cf3}{remove\+All\+Tags\+From\+Asset}} (\$asset\+Id)
\item 
\mbox{\hyperlink{class_tag_ae0e9b2e99baafec639b087d1a0ada8b2}{get\+Assets\+By\+Tag}} (\$tag\+Id)
\item 
\mbox{\hyperlink{class_tag_a1188485fb7b7b0abe22f005bed184c4c}{count\+Assets\+With\+Tag}} (\$tag\+Id)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_tag_a095c5d389db211932136b53f25f39685}\index{Tag@{Tag}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_tag_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_tag_a4ff2d7b570ab7813d09be22cb72fcd46}\index{Tag@{Tag}!addTag@{addTag}}
\index{addTag@{addTag}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{addTag()}{addTag()}}
{\footnotesize\ttfamily \label{class_tag_a4ff2d7b570ab7813d09be22cb72fcd46} 
add\+Tag (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

\Hypertarget{class_tag_a2b19e0c9ea97d5b603d6a5a02940c523}\index{Tag@{Tag}!addTagToAsset@{addTagToAsset}}
\index{addTagToAsset@{addTagToAsset}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{addTagToAsset()}{addTagToAsset()}}
{\footnotesize\ttfamily \label{class_tag_a2b19e0c9ea97d5b603d6a5a02940c523} 
add\+Tag\+To\+Asset (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{, }\item[{}]{\$tag\+Id}{}\end{DoxyParamCaption})}

\Hypertarget{class_tag_a1188485fb7b7b0abe22f005bed184c4c}\index{Tag@{Tag}!countAssetsWithTag@{countAssetsWithTag}}
\index{countAssetsWithTag@{countAssetsWithTag}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{countAssetsWithTag()}{countAssetsWithTag()}}
{\footnotesize\ttfamily \label{class_tag_a1188485fb7b7b0abe22f005bed184c4c} 
count\+Assets\+With\+Tag (\begin{DoxyParamCaption}\item[{}]{\$tag\+Id}{}\end{DoxyParamCaption})}

\Hypertarget{class_tag_a8136d79d1d3aa83567d0baaccdb97335}\index{Tag@{Tag}!deleteTag@{deleteTag}}
\index{deleteTag@{deleteTag}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{deleteTag()}{deleteTag()}}
{\footnotesize\ttfamily \label{class_tag_a8136d79d1d3aa83567d0baaccdb97335} 
delete\+Tag (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

\Hypertarget{class_tag_ae0e9b2e99baafec639b087d1a0ada8b2}\index{Tag@{Tag}!getAssetsByTag@{getAssetsByTag}}
\index{getAssetsByTag@{getAssetsByTag}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{getAssetsByTag()}{getAssetsByTag()}}
{\footnotesize\ttfamily \label{class_tag_ae0e9b2e99baafec639b087d1a0ada8b2} 
get\+Assets\+By\+Tag (\begin{DoxyParamCaption}\item[{}]{\$tag\+Id}{}\end{DoxyParamCaption})}

\Hypertarget{class_tag_a401430eadd174788ff0568d8f0c9c6d8}\index{Tag@{Tag}!getTagById@{getTagById}}
\index{getTagById@{getTagById}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{getTagById()}{getTagById()}}
{\footnotesize\ttfamily \label{class_tag_a401430eadd174788ff0568d8f0c9c6d8} 
get\+Tag\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

\Hypertarget{class_tag_a2e986e3f035b2b0fb56c40778ce1f040}\index{Tag@{Tag}!getTagByName@{getTagByName}}
\index{getTagByName@{getTagByName}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{getTagByName()}{getTagByName()}}
{\footnotesize\ttfamily \label{class_tag_a2e986e3f035b2b0fb56c40778ce1f040} 
get\+Tag\+By\+Name (\begin{DoxyParamCaption}\item[{}]{\$name}{}\end{DoxyParamCaption})}

\Hypertarget{class_tag_ae07173ab06a20e2f5bd928cc0518e01f}\index{Tag@{Tag}!getTags@{getTags}}
\index{getTags@{getTags}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{getTags()}{getTags()}}
{\footnotesize\ttfamily \label{class_tag_ae07173ab06a20e2f5bd928cc0518e01f} 
get\+Tags (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_tag_a425a55b885d03edeafb572af23106a46}\index{Tag@{Tag}!getTagsForAsset@{getTagsForAsset}}
\index{getTagsForAsset@{getTagsForAsset}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{getTagsForAsset()}{getTagsForAsset()}}
{\footnotesize\ttfamily \label{class_tag_a425a55b885d03edeafb572af23106a46} 
get\+Tags\+For\+Asset (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

\Hypertarget{class_tag_aaf77d50b5c6361054aca053c5d2d6cf3}\index{Tag@{Tag}!removeAllTagsFromAsset@{removeAllTagsFromAsset}}
\index{removeAllTagsFromAsset@{removeAllTagsFromAsset}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{removeAllTagsFromAsset()}{removeAllTagsFromAsset()}}
{\footnotesize\ttfamily \label{class_tag_aaf77d50b5c6361054aca053c5d2d6cf3} 
remove\+All\+Tags\+From\+Asset (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{}\end{DoxyParamCaption})}

\Hypertarget{class_tag_a880f1cf91fb22405339d3e55f1e51afb}\index{Tag@{Tag}!removeTagFromAsset@{removeTagFromAsset}}
\index{removeTagFromAsset@{removeTagFromAsset}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{removeTagFromAsset()}{removeTagFromAsset()}}
{\footnotesize\ttfamily \label{class_tag_a880f1cf91fb22405339d3e55f1e51afb} 
remove\+Tag\+From\+Asset (\begin{DoxyParamCaption}\item[{}]{\$asset\+Id}{, }\item[{}]{\$tag\+Id}{}\end{DoxyParamCaption})}

\Hypertarget{class_tag_a38825dc7a692a32bf2fecb1df70fed9d}\index{Tag@{Tag}!updateTag@{updateTag}}
\index{updateTag@{updateTag}!Tag@{Tag}}
\doxysubsubsection{\texorpdfstring{updateTag()}{updateTag()}}
{\footnotesize\ttfamily \label{class_tag_a38825dc7a692a32bf2fecb1df70fed9d} 
update\+Tag (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{_tag_8php}{Tag.\+php}}\end{DoxyCompactItemize}
