\doxysection{Tags Class Reference}
\hypertarget{class_tags}{}\label{class_tags}\index{Tags@{Tags}}
Inheritance diagram for Tags\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{class_tags}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_tags_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_tags_a149eb92716c1084a935e04a8d95f7347}{index}} ()
\item 
\mbox{\hyperlink{class_tags_a837ba24a1c3095ae67613238d866f79a}{add}} ()
\item 
\mbox{\hyperlink{class_tags_a459ed16587e3a50b39b672c7e473abc5}{edit}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_tags_a2f8258add505482d7f00ea26493a5723}{delete}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_tags_add8c3d3dd8b6b96ba72c4de1f8b5295f}{get\+Assets}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_ac531eb761b130b1925a8bae5c33af2fc}{model}} (\$model)
\item 
\mbox{\hyperlink{class_controller_a11f0e20b30b899d00b009a9bb1afe43d}{view}} (\$view, \$data=\mbox{[}$\,$\mbox{]})
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Protected Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_a0d92de8136cebc006a407442aab9db0a}{sanitize\+Post\+Data}} (\$data)
\item 
\mbox{\hyperlink{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52}{validate\+Csrf\+Token}} (\$token)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_tags_a095c5d389db211932136b53f25f39685}\index{Tags@{Tags}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Tags@{Tags}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_tags_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



\doxysubsection{Member Function Documentation}
\Hypertarget{class_tags_a837ba24a1c3095ae67613238d866f79a}\index{Tags@{Tags}!add@{add}}
\index{add@{add}!Tags@{Tags}}
\doxysubsubsection{\texorpdfstring{add()}{add()}}
{\footnotesize\ttfamily \label{class_tags_a837ba24a1c3095ae67613238d866f79a} 
add (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_tags_a2f8258add505482d7f00ea26493a5723}\index{Tags@{Tags}!delete@{delete}}
\index{delete@{delete}!Tags@{Tags}}
\doxysubsubsection{\texorpdfstring{delete()}{delete()}}
{\footnotesize\ttfamily \label{class_tags_a2f8258add505482d7f00ea26493a5723} 
delete (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

\Hypertarget{class_tags_a459ed16587e3a50b39b672c7e473abc5}\index{Tags@{Tags}!edit@{edit}}
\index{edit@{edit}!Tags@{Tags}}
\doxysubsubsection{\texorpdfstring{edit()}{edit()}}
{\footnotesize\ttfamily \label{class_tags_a459ed16587e3a50b39b672c7e473abc5} 
edit (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

\Hypertarget{class_tags_add8c3d3dd8b6b96ba72c4de1f8b5295f}\index{Tags@{Tags}!getAssets@{getAssets}}
\index{getAssets@{getAssets}!Tags@{Tags}}
\doxysubsubsection{\texorpdfstring{getAssets()}{getAssets()}}
{\footnotesize\ttfamily \label{class_tags_add8c3d3dd8b6b96ba72c4de1f8b5295f} 
get\+Assets (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

\Hypertarget{class_tags_a149eb92716c1084a935e04a8d95f7347}\index{Tags@{Tags}!index@{index}}
\index{index@{index}!Tags@{Tags}}
\doxysubsubsection{\texorpdfstring{index()}{index()}}
{\footnotesize\ttfamily \label{class_tags_a149eb92716c1084a935e04a8d95f7347} 
index (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/controllers/\mbox{\hyperlink{_tags_8php}{Tags.\+php}}\end{DoxyCompactItemize}
