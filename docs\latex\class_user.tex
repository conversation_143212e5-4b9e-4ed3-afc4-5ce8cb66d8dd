\doxysection{User Class Reference}
\hypertarget{class_user}{}\label{class_user}\index{User@{User}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_user_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_user_ad2bc607329e3fa62d3b95a76e61b650c}{register}} (\$data)
\item 
\mbox{\hyperlink{class_user_aaaffadfaedf0decf75bab8923a8c8781}{login}} (\$email, \$password)
\item 
\mbox{\hyperlink{class_user_af7d72fc4d79d853b8af320c0ce17ebd1}{find\+User\+By\+Email}} (\$email)
\item 
\mbox{\hyperlink{class_user_a5e7d616b80a7b0fb4b8d2736800e29cc}{get\+User\+By\+Id}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_user_a48a9c3492291c38e6113e40e04deb2ed}{get\+All\+Users}} ()
\item 
\mbox{\hyperlink{class_user_a6088f09cdf25effafc0261a893ddc316}{update\+Status}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}, \$status)
\item 
\mbox{\hyperlink{class_user_a9bb4e869d9c4a6466fc110c69e2e6332}{update\+Role}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}, \$role)
\item 
\mbox{\hyperlink{class_user_ab4bfd178ad176ab7336faa21ae3dd807}{update\+Legacy\+Role}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}, \$role)
\item 
\mbox{\hyperlink{class_user_a971afa7848d88a57fe60e6f61fecf3cd}{update\+Password}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}, \$password)
\item 
\mbox{\hyperlink{class_user_ae0957b89a7a07da512227292fd49aa80}{update\+User}} (\$data)
\item 
\mbox{\hyperlink{class_user_a4e25c35f7741620a8309ea1c6a2117e6}{get\+User\+By\+Email}} (\$email)
\item 
\mbox{\hyperlink{class_user_a3df1c9b773342ccee92bf873c0677d45}{create\+Password\+Reset\+Token}} (\$email)
\item 
\mbox{\hyperlink{class_user_a0cb5bf0a7e8f3638609c7df21b5e3234}{verify\+Password\+Reset\+Token}} (\$email, \$token)
\item 
\mbox{\hyperlink{class_user_a6b89115d131a20f0970b7277e6c598b0}{mark\+Token\+As\+Used}} (\$email, \$token)
\item 
\mbox{\hyperlink{class_user_a2fa1b4e2fbf3919435a676d6808e663f}{reset\+Password\+By\+Email}} (\$email, \$password)
\item 
\mbox{\hyperlink{class_user_add8aaf5e6881c74092b138a86de7764f}{create\+Remember\+Me\+Token}} (\$user\+Id)
\item 
\mbox{\hyperlink{class_user_a06e108b031376baedd2ec162923e0af6}{verify\+Remember\+Me\+Token}} (\$selector, \$validator)
\item 
\mbox{\hyperlink{class_user_a755028b25c004b15c7fe00f214297ec4}{delete\+Remember\+Me\+Token}} (\$selector)
\item 
\mbox{\hyperlink{class_user_a49f20a2213cf9dbab0288bbef9fa4de9}{delete\+All\+Remember\+Me\+Tokens}} (\$user\+Id)
\item 
\mbox{\hyperlink{class_user_a5368738110a683cd7a567ae1327cc898}{get\+User\+Roles}} (\$user\+Id)
\item 
\mbox{\hyperlink{class_user_a91222c71c8139bfa1bb5e8f6df776006}{get\+Primary\+Role}} (\$user\+Id)
\item 
\mbox{\hyperlink{class_user_a4c9115ceb83c0aff453a7e11ac9e2a86}{has\+Role}} (\$user\+Id, \$role)
\item 
\mbox{\hyperlink{class_user_a0055c1740759104b1c17445ea2bd581a}{get\+User\+Permissions}} (\$user\+Id)
\item 
\mbox{\hyperlink{class_user_ac30fcf0c0789d2e918af5e0926ee0d9f}{has\+Permission}} (\$user\+Id, \$permission)
\item 
\mbox{\hyperlink{class_user_a4a5ea56c1886bd9841172b60638a6db1}{assign\+Roles}} (\$user\+Id, \$role\+Ids)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_user_a095c5d389db211932136b53f25f39685}\index{User@{User}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!User@{User}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_user_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Constructor

Initializes the \doxylink{class_user}{User} model with database and role model instances.

\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}


\doxysubsection{Member Function Documentation}
\Hypertarget{class_user_a4a5ea56c1886bd9841172b60638a6db1}\index{User@{User}!assignRoles@{assignRoles}}
\index{assignRoles@{assignRoles}!User@{User}}
\doxysubsubsection{\texorpdfstring{assignRoles()}{assignRoles()}}
{\footnotesize\ttfamily \label{class_user_a4a5ea56c1886bd9841172b60638a6db1} 
assign\+Roles (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{, }\item[{}]{\$role\+Ids}{}\end{DoxyParamCaption})}

Assign roles to a user


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
array & {\em \$role\+Ids} & Array of role IDs \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if successful, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_user_a3df1c9b773342ccee92bf873c0677d45}\index{User@{User}!createPasswordResetToken@{createPasswordResetToken}}
\index{createPasswordResetToken@{createPasswordResetToken}!User@{User}}
\doxysubsubsection{\texorpdfstring{createPasswordResetToken()}{createPasswordResetToken()}}
{\footnotesize\ttfamily \label{class_user_a3df1c9b773342ccee92bf873c0677d45} 
create\+Password\+Reset\+Token (\begin{DoxyParamCaption}\item[{}]{\$email}{}\end{DoxyParamCaption})}

Create password reset token

Generates a secure token for password reset functionality. Token expires after 1 hour and replaces any existing tokens.


\begin{DoxyParams}[1]{Parameters}
string & {\em \$email} & The email address to create token for \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
string\texorpdfstring{$\vert$}{|}false The generated token if successful, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_user_add8aaf5e6881c74092b138a86de7764f}\index{User@{User}!createRememberMeToken@{createRememberMeToken}}
\index{createRememberMeToken@{createRememberMeToken}!User@{User}}
\doxysubsubsection{\texorpdfstring{createRememberMeToken()}{createRememberMeToken()}}
{\footnotesize\ttfamily \label{class_user_add8aaf5e6881c74092b138a86de7764f} 
create\+Remember\+Me\+Token (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})}

Create a remember me token for a user


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array\texorpdfstring{$\vert$}{|}bool Token data if created successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_user_a49f20a2213cf9dbab0288bbef9fa4de9}\index{User@{User}!deleteAllRememberMeTokens@{deleteAllRememberMeTokens}}
\index{deleteAllRememberMeTokens@{deleteAllRememberMeTokens}!User@{User}}
\doxysubsubsection{\texorpdfstring{deleteAllRememberMeTokens()}{deleteAllRememberMeTokens()}}
{\footnotesize\ttfamily \label{class_user_a49f20a2213cf9dbab0288bbef9fa4de9} 
delete\+All\+Remember\+Me\+Tokens (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})}

Delete all remember me tokens for a user


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if deleted successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_user_a755028b25c004b15c7fe00f214297ec4}\index{User@{User}!deleteRememberMeToken@{deleteRememberMeToken}}
\index{deleteRememberMeToken@{deleteRememberMeToken}!User@{User}}
\doxysubsubsection{\texorpdfstring{deleteRememberMeToken()}{deleteRememberMeToken()}}
{\footnotesize\ttfamily \label{class_user_a755028b25c004b15c7fe00f214297ec4} 
delete\+Remember\+Me\+Token (\begin{DoxyParamCaption}\item[{}]{\$selector}{}\end{DoxyParamCaption})}

Delete a remember me token


\begin{DoxyParams}[1]{Parameters}
string & {\em \$selector} & Token selector \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if deleted successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_user_af7d72fc4d79d853b8af320c0ce17ebd1}\index{User@{User}!findUserByEmail@{findUserByEmail}}
\index{findUserByEmail@{findUserByEmail}!User@{User}}
\doxysubsubsection{\texorpdfstring{findUserByEmail()}{findUserByEmail()}}
{\footnotesize\ttfamily \label{class_user_af7d72fc4d79d853b8af320c0ce17ebd1} 
find\+User\+By\+Email (\begin{DoxyParamCaption}\item[{}]{\$email}{}\end{DoxyParamCaption})}

Find user by email

Checks if a user exists with the given email address.


\begin{DoxyParams}[1]{Parameters}
string & {\em \$email} & The email address to search for \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if user exists, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_user_a48a9c3492291c38e6113e40e04deb2ed}\index{User@{User}!getAllUsers@{getAllUsers}}
\index{getAllUsers@{getAllUsers}!User@{User}}
\doxysubsubsection{\texorpdfstring{getAllUsers()}{getAllUsers()}}
{\footnotesize\ttfamily \label{class_user_a48a9c3492291c38e6113e40e04deb2ed} 
get\+All\+Users (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Get all users

Retrieves all users from the database ordered by name.

\begin{DoxyReturn}{Returns}
array Array of user objects 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_user_a91222c71c8139bfa1bb5e8f6df776006}\index{User@{User}!getPrimaryRole@{getPrimaryRole}}
\index{getPrimaryRole@{getPrimaryRole}!User@{User}}
\doxysubsubsection{\texorpdfstring{getPrimaryRole()}{getPrimaryRole()}}
{\footnotesize\ttfamily \label{class_user_a91222c71c8139bfa1bb5e8f6df776006} 
get\+Primary\+Role (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})}

Get primary role for a user (for backward compatibility)


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object\texorpdfstring{$\vert$}{|}bool \doxylink{class_role}{Role} object or false if no roles 
\end{DoxyReturn}
\Hypertarget{class_user_a4e25c35f7741620a8309ea1c6a2117e6}\index{User@{User}!getUserByEmail@{getUserByEmail}}
\index{getUserByEmail@{getUserByEmail}!User@{User}}
\doxysubsubsection{\texorpdfstring{getUserByEmail()}{getUserByEmail()}}
{\footnotesize\ttfamily \label{class_user_a4e25c35f7741620a8309ea1c6a2117e6} 
get\+User\+By\+Email (\begin{DoxyParamCaption}\item[{}]{\$email}{}\end{DoxyParamCaption})}

Get user by email (returns user object)

Retrieves a complete user record by email address.


\begin{DoxyParams}[1]{Parameters}
string & {\em \$email} & The email address to search for \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object\texorpdfstring{$\vert$}{|}false \doxylink{class_user}{User} object if found, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_user_a5e7d616b80a7b0fb4b8d2736800e29cc}\index{User@{User}!getUserById@{getUserById}}
\index{getUserById@{getUserById}!User@{User}}
\doxysubsubsection{\texorpdfstring{getUserById()}{getUserById()}}
{\footnotesize\ttfamily \label{class_user_a5e7d616b80a7b0fb4b8d2736800e29cc} 
get\+User\+By\+Id (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Get user by ID

Retrieves a user record by their unique ID.


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & The user ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
object\texorpdfstring{$\vert$}{|}false \doxylink{class_user}{User} object if found, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_user_a0055c1740759104b1c17445ea2bd581a}\index{User@{User}!getUserPermissions@{getUserPermissions}}
\index{getUserPermissions@{getUserPermissions}!User@{User}}
\doxysubsubsection{\texorpdfstring{getUserPermissions()}{getUserPermissions()}}
{\footnotesize\ttfamily \label{class_user_a0055c1740759104b1c17445ea2bd581a} 
get\+User\+Permissions (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})}

Get all permissions for a user


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array Array of permission objects 
\end{DoxyReturn}
\Hypertarget{class_user_a5368738110a683cd7a567ae1327cc898}\index{User@{User}!getUserRoles@{getUserRoles}}
\index{getUserRoles@{getUserRoles}!User@{User}}
\doxysubsubsection{\texorpdfstring{getUserRoles()}{getUserRoles()}}
{\footnotesize\ttfamily \label{class_user_a5368738110a683cd7a567ae1327cc898} 
get\+User\+Roles (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{}\end{DoxyParamCaption})}

Get all roles for a user


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
array Array of role objects 
\end{DoxyReturn}
\Hypertarget{class_user_ac30fcf0c0789d2e918af5e0926ee0d9f}\index{User@{User}!hasPermission@{hasPermission}}
\index{hasPermission@{hasPermission}!User@{User}}
\doxysubsubsection{\texorpdfstring{hasPermission()}{hasPermission()}}
{\footnotesize\ttfamily \label{class_user_ac30fcf0c0789d2e918af5e0926ee0d9f} 
has\+Permission (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{, }\item[{}]{\$permission}{}\end{DoxyParamCaption})}

Check if a user has a specific permission


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
int | string & {\em \$permission} & \doxylink{class_permission}{Permission} ID or name \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if the user has the permission, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_user_a4c9115ceb83c0aff453a7e11ac9e2a86}\index{User@{User}!hasRole@{hasRole}}
\index{hasRole@{hasRole}!User@{User}}
\doxysubsubsection{\texorpdfstring{hasRole()}{hasRole()}}
{\footnotesize\ttfamily \label{class_user_a4c9115ceb83c0aff453a7e11ac9e2a86} 
has\+Role (\begin{DoxyParamCaption}\item[{}]{\$user\+Id}{, }\item[{}]{\$role}{}\end{DoxyParamCaption})}

Check if a user has a specific role


\begin{DoxyParams}[1]{Parameters}
int & {\em \$user\+Id} & \doxylink{class_user}{User} ID \\
\hline
int | string & {\em \$role} & \doxylink{class_role}{Role} ID or name \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if the user has the role, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_user_aaaffadfaedf0decf75bab8923a8c8781}\index{User@{User}!login@{login}}
\index{login@{login}!User@{User}}
\doxysubsubsection{\texorpdfstring{login()}{login()}}
{\footnotesize\ttfamily \label{class_user_aaaffadfaedf0decf75bab8923a8c8781} 
login (\begin{DoxyParamCaption}\item[{}]{\$email}{, }\item[{}]{\$password}{}\end{DoxyParamCaption})}

Authenticate user login

Verifies user credentials and handles security features like account locking and failed login attempt tracking.


\begin{DoxyParams}[1]{Parameters}
string & {\em \$email} & \doxylink{class_user}{User}\textquotesingle{}s email address \\
\hline
string & {\em \$password} & \doxylink{class_user}{User}\textquotesingle{}s password \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
mixed \doxylink{class_user}{User} object on success, \textquotesingle{}inactive\textquotesingle{} if account inactive, \textquotesingle{}locked\textquotesingle{} if locked, false on failure 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_user_a6b89115d131a20f0970b7277e6c598b0}\index{User@{User}!markTokenAsUsed@{markTokenAsUsed}}
\index{markTokenAsUsed@{markTokenAsUsed}!User@{User}}
\doxysubsubsection{\texorpdfstring{markTokenAsUsed()}{markTokenAsUsed()}}
{\footnotesize\ttfamily \label{class_user_a6b89115d131a20f0970b7277e6c598b0} 
mark\+Token\+As\+Used (\begin{DoxyParamCaption}\item[{}]{\$email}{, }\item[{}]{\$token}{}\end{DoxyParamCaption})}

\Hypertarget{class_user_ad2bc607329e3fa62d3b95a76e61b650c}\index{User@{User}!register@{register}}
\index{register@{register}!User@{User}}
\doxysubsubsection{\texorpdfstring{register()}{register()}}
{\footnotesize\ttfamily \label{class_user_ad2bc607329e3fa62d3b95a76e61b650c} 
register (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Register a new user

Creates a new user account with the provided data. Sets default role and status if not specified.


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \doxylink{class_user}{User} registration data containing name, email, password, role, status \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if registration successful, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_user_a2fa1b4e2fbf3919435a676d6808e663f}\index{User@{User}!resetPasswordByEmail@{resetPasswordByEmail}}
\index{resetPasswordByEmail@{resetPasswordByEmail}!User@{User}}
\doxysubsubsection{\texorpdfstring{resetPasswordByEmail()}{resetPasswordByEmail()}}
{\footnotesize\ttfamily \label{class_user_a2fa1b4e2fbf3919435a676d6808e663f} 
reset\+Password\+By\+Email (\begin{DoxyParamCaption}\item[{}]{\$email}{, }\item[{}]{\$password}{}\end{DoxyParamCaption})}

\Hypertarget{class_user_ab4bfd178ad176ab7336faa21ae3dd807}\index{User@{User}!updateLegacyRole@{updateLegacyRole}}
\index{updateLegacyRole@{updateLegacyRole}!User@{User}}
\doxysubsubsection{\texorpdfstring{updateLegacyRole()}{updateLegacyRole()}}
{\footnotesize\ttfamily \label{class_user_ab4bfd178ad176ab7336faa21ae3dd807} 
update\+Legacy\+Role (\begin{DoxyParamCaption}\item[{}]{\$id}{, }\item[{}]{\$role}{}\end{DoxyParamCaption})}

Update the legacy role field in the users table This is used for backward compatibility with the old role system


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \doxylink{class_user}{User} ID \\
\hline
string & {\em \$role} & \doxylink{class_role}{Role} value (\textquotesingle{}admin\textquotesingle{} or \textquotesingle{}user\textquotesingle{}) \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if successful, false otherwise 
\end{DoxyReturn}
\Hypertarget{class_user_a971afa7848d88a57fe60e6f61fecf3cd}\index{User@{User}!updatePassword@{updatePassword}}
\index{updatePassword@{updatePassword}!User@{User}}
\doxysubsubsection{\texorpdfstring{updatePassword()}{updatePassword()}}
{\footnotesize\ttfamily \label{class_user_a971afa7848d88a57fe60e6f61fecf3cd} 
update\+Password (\begin{DoxyParamCaption}\item[{}]{\$id}{, }\item[{}]{\$password}{}\end{DoxyParamCaption})}

Update user password

Updates a user\textquotesingle{}s password with the provided hashed password.


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & The user ID \\
\hline
string & {\em \$password} & The hashed password \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if update successful, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_user_a9bb4e869d9c4a6466fc110c69e2e6332}\index{User@{User}!updateRole@{updateRole}}
\index{updateRole@{updateRole}!User@{User}}
\doxysubsubsection{\texorpdfstring{updateRole()}{updateRole()}}
{\footnotesize\ttfamily \label{class_user_a9bb4e869d9c4a6466fc110c69e2e6332} 
update\+Role (\begin{DoxyParamCaption}\item[{}]{\$id}{, }\item[{}]{\$role}{}\end{DoxyParamCaption})}

Update user role (legacy method)

Updates both the users table and user\+\_\+roles table for backward compatibility. Uses database transactions to ensure data consistency.


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & The user ID \\
\hline
string & {\em \$role} & The role name (\textquotesingle{}admin\textquotesingle{} or \textquotesingle{}user\textquotesingle{}) \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if update successful, false otherwise 
\end{DoxyReturn}

\begin{DoxyExceptions}{Exceptions}
{\em Exception} & If database transaction fails \\
\hline
\end{DoxyExceptions}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_user_a6088f09cdf25effafc0261a893ddc316}\index{User@{User}!updateStatus@{updateStatus}}
\index{updateStatus@{updateStatus}!User@{User}}
\doxysubsubsection{\texorpdfstring{updateStatus()}{updateStatus()}}
{\footnotesize\ttfamily \label{class_user_a6088f09cdf25effafc0261a893ddc316} 
update\+Status (\begin{DoxyParamCaption}\item[{}]{\$id}{, }\item[{}]{\$status}{}\end{DoxyParamCaption})}

Update user status

Updates the status of a user (active/inactive).


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & The user ID \\
\hline
string & {\em \$status} & The new status (\textquotesingle{}active\textquotesingle{} or \textquotesingle{}inactive\textquotesingle{}) \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if update successful, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_user_ae0957b89a7a07da512227292fd49aa80}\index{User@{User}!updateUser@{updateUser}}
\index{updateUser@{updateUser}!User@{User}}
\doxysubsubsection{\texorpdfstring{updateUser()}{updateUser()}}
{\footnotesize\ttfamily \label{class_user_ae0957b89a7a07da512227292fd49aa80} 
update\+User (\begin{DoxyParamCaption}\item[{}]{\$data}{}\end{DoxyParamCaption})}

Update user information

Updates user name and optionally password. Dynamically builds query based on provided data.


\begin{DoxyParams}[1]{Parameters}
array & {\em \$data} & \doxylink{class_user}{User} data containing id, name, and optionally password \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if update successful, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_user_a0cb5bf0a7e8f3638609c7df21b5e3234}\index{User@{User}!verifyPasswordResetToken@{verifyPasswordResetToken}}
\index{verifyPasswordResetToken@{verifyPasswordResetToken}!User@{User}}
\doxysubsubsection{\texorpdfstring{verifyPasswordResetToken()}{verifyPasswordResetToken()}}
{\footnotesize\ttfamily \label{class_user_a0cb5bf0a7e8f3638609c7df21b5e3234} 
verify\+Password\+Reset\+Token (\begin{DoxyParamCaption}\item[{}]{\$email}{, }\item[{}]{\$token}{}\end{DoxyParamCaption})}

\Hypertarget{class_user_a06e108b031376baedd2ec162923e0af6}\index{User@{User}!verifyRememberMeToken@{verifyRememberMeToken}}
\index{verifyRememberMeToken@{verifyRememberMeToken}!User@{User}}
\doxysubsubsection{\texorpdfstring{verifyRememberMeToken()}{verifyRememberMeToken()}}
{\footnotesize\ttfamily \label{class_user_a06e108b031376baedd2ec162923e0af6} 
verify\+Remember\+Me\+Token (\begin{DoxyParamCaption}\item[{}]{\$selector}{, }\item[{}]{\$validator}{}\end{DoxyParamCaption})}

Verify a remember me token


\begin{DoxyParams}[1]{Parameters}
string & {\em \$selector} & Token selector \\
\hline
string & {\em \$validator} & Token validator \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
int\texorpdfstring{$\vert$}{|}bool \doxylink{class_user}{User} ID if token is valid, false otherwise 
\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/models/\mbox{\hyperlink{_user_8php}{User.\+php}}\end{DoxyCompactItemize}
