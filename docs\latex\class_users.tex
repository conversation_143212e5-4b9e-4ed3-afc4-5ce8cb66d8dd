\doxysection{Users Class Reference}
\hypertarget{class_users}{}\label{class_users}\index{Users@{Users}}
Inheritance diagram for Users\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{class_users}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_users_a095c5d389db211932136b53f25f39685}{\+\_\+\+\_\+construct}} ()
\item 
\mbox{\hyperlink{class_users_acc294a6cc8e69743746820e3d15e3f78}{register}} ()
\item 
\mbox{\hyperlink{class_users_aa311da27ba5706f5710cea7706c8eae1}{login}} ()
\item 
\mbox{\hyperlink{class_users_aefc10a3bb76cb1118a8c869e2e3ae03a}{create\+User\+Session}} (\$user, \$remember\+Me=false)
\item 
\mbox{\hyperlink{class_users_a7bd7264f00dc27e7316ac4382a1f0f4c}{profile}} ()
\item 
\mbox{\hyperlink{class_users_a082405d89acd6835c3a7c7a08a7adbab}{logout}} ()
\item 
\mbox{\hyperlink{class_users_ad960fc122349e70360e1c0b72813f6f1}{manage}} ()
\item 
\mbox{\hyperlink{class_users_a32f5d55b63247885ba0ad182f67fdc1c}{toggle\+Status}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_users_a380fe18e9e8d209d022ae5e1d6ba1af8}{toggle\+Role}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_users_a73ca737a6f9f1af62cb8c7a4a53b589d}{roles}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_users_aeca207a42dbe064c5646029f139aa787}{reset\+Password}} (\$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}})
\item 
\mbox{\hyperlink{class_users_a192e204d6fea186666df606760e5169f}{forgot\+Password}} ()
\item 
\mbox{\hyperlink{class_users_abd105defab1eca8a3fb91bca9ba910e4}{reset\+Password\+With\+Token}} ()
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_ac531eb761b130b1925a8bae5c33af2fc}{model}} (\$model)
\item 
\mbox{\hyperlink{class_controller_a11f0e20b30b899d00b009a9bb1afe43d}{view}} (\$view, \$data=\mbox{[}$\,$\mbox{]})
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Protected Member Functions inherited from \mbox{\hyperlink{class_controller}{Controller}}}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_controller_a0d92de8136cebc006a407442aab9db0a}{sanitize\+Post\+Data}} (\$data)
\item 
\mbox{\hyperlink{class_controller_aaf7b7d5aa2f9ec7a1f79646322121f52}{validate\+Csrf\+Token}} (\$token)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{class_users_a095c5d389db211932136b53f25f39685}\index{Users@{Users}!\_\_construct@{\_\_construct}}
\index{\_\_construct@{\_\_construct}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{\_\_construct()}{\_\_construct()}}
{\footnotesize\ttfamily \label{class_users_a095c5d389db211932136b53f25f39685} 
\+\_\+\+\_\+construct (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Constructor

Initializes the \doxylink{class_users}{Users} controller with user model loading.

\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}


\doxysubsection{Member Function Documentation}
\Hypertarget{class_users_aefc10a3bb76cb1118a8c869e2e3ae03a}\index{Users@{Users}!createUserSession@{createUserSession}}
\index{createUserSession@{createUserSession}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{createUserSession()}{createUserSession()}}
{\footnotesize\ttfamily \label{class_users_aefc10a3bb76cb1118a8c869e2e3ae03a} 
create\+User\+Session (\begin{DoxyParamCaption}\item[{}]{\$user}{, }\item[{}]{\$remember\+Me}{ = {\ttfamily false}}\end{DoxyParamCaption})}

\Hypertarget{class_users_a192e204d6fea186666df606760e5169f}\index{Users@{Users}!forgotPassword@{forgotPassword}}
\index{forgotPassword@{forgotPassword}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{forgotPassword()}{forgotPassword()}}
{\footnotesize\ttfamily \label{class_users_a192e204d6fea186666df606760e5169f} 
forgot\+Password (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_users_aa311da27ba5706f5710cea7706c8eae1}\index{Users@{Users}!login@{login}}
\index{login@{login}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{login()}{login()}}
{\footnotesize\ttfamily \label{class_users_aa311da27ba5706f5710cea7706c8eae1} 
login (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_users_a082405d89acd6835c3a7c7a08a7adbab}\index{Users@{Users}!logout@{logout}}
\index{logout@{logout}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{logout()}{logout()}}
{\footnotesize\ttfamily \label{class_users_a082405d89acd6835c3a7c7a08a7adbab} 
logout (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_users_ad960fc122349e70360e1c0b72813f6f1}\index{Users@{Users}!manage@{manage}}
\index{manage@{manage}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{manage()}{manage()}}
{\footnotesize\ttfamily \label{class_users_ad960fc122349e70360e1c0b72813f6f1} 
manage (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_users_a7bd7264f00dc27e7316ac4382a1f0f4c}\index{Users@{Users}!profile@{profile}}
\index{profile@{profile}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{profile()}{profile()}}
{\footnotesize\ttfamily \label{class_users_a7bd7264f00dc27e7316ac4382a1f0f4c} 
profile (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_users_acc294a6cc8e69743746820e3d15e3f78}\index{Users@{Users}!register@{register}}
\index{register@{register}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{register()}{register()}}
{\footnotesize\ttfamily \label{class_users_acc294a6cc8e69743746820e3d15e3f78} 
register (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\doxylink{class_user}{User} registration

Handles both GET (display form) and POST (process registration) requests. Includes comprehensive validation and password strength checking.

\begin{DoxyReturn}{Returns}
void 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{class_users_aeca207a42dbe064c5646029f139aa787}\index{Users@{Users}!resetPassword@{resetPassword}}
\index{resetPassword@{resetPassword}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{resetPassword()}{resetPassword()}}
{\footnotesize\ttfamily \label{class_users_aeca207a42dbe064c5646029f139aa787} 
reset\+Password (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

\Hypertarget{class_users_abd105defab1eca8a3fb91bca9ba910e4}\index{Users@{Users}!resetPasswordWithToken@{resetPasswordWithToken}}
\index{resetPasswordWithToken@{resetPasswordWithToken}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{resetPasswordWithToken()}{resetPasswordWithToken()}}
{\footnotesize\ttfamily \label{class_users_abd105defab1eca8a3fb91bca9ba910e4} 
reset\+Password\+With\+Token (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{class_users_a73ca737a6f9f1af62cb8c7a4a53b589d}\index{Users@{Users}!roles@{roles}}
\index{roles@{roles}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{roles()}{roles()}}
{\footnotesize\ttfamily \label{class_users_a73ca737a6f9f1af62cb8c7a4a53b589d} 
roles (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

Manage roles for a user


\begin{DoxyParams}[1]{Parameters}
int & {\em \$id} & \doxylink{class_user}{User} ID \\
\hline
\end{DoxyParams}
\Hypertarget{class_users_a380fe18e9e8d209d022ae5e1d6ba1af8}\index{Users@{Users}!toggleRole@{toggleRole}}
\index{toggleRole@{toggleRole}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{toggleRole()}{toggleRole()}}
{\footnotesize\ttfamily \label{class_users_a380fe18e9e8d209d022ae5e1d6ba1af8} 
toggle\+Role (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}

\Hypertarget{class_users_a32f5d55b63247885ba0ad182f67fdc1c}\index{Users@{Users}!toggleStatus@{toggleStatus}}
\index{toggleStatus@{toggleStatus}!Users@{Users}}
\doxysubsubsection{\texorpdfstring{toggleStatus()}{toggleStatus()}}
{\footnotesize\ttfamily \label{class_users_a32f5d55b63247885ba0ad182f67fdc1c} 
toggle\+Status (\begin{DoxyParamCaption}\item[{}]{\$id}{}\end{DoxyParamCaption})}



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
app/controllers/\mbox{\hyperlink{_users_8php}{Users.\+php}}\end{DoxyCompactItemize}
