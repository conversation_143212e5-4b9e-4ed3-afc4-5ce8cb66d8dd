\doxysection{app/scripts/create\+\_\+guideline\+\_\+implementation\+\_\+table.php File Reference}
\hypertarget{create__guideline__implementation__table_8php}{}\label{create__guideline__implementation__table_8php}\index{app/scripts/create\_guideline\_implementation\_table.php@{app/scripts/create\_guideline\_implementation\_table.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_a1fa3127fc82f96b1436d871ef02be319}{\$db}} = new \mbox{\hyperlink{class_database}{Database}}()
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_a73004ce9cd673c1bfafd1dc351134797}{\$output}} = \mbox{[}$\,$\mbox{]}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_a397d52f1e5cf2f76672fbfd1d7a65f7e}{\$output}} \mbox{[}$\,$\mbox{]} = "{}=== Maintenance Guideline Implementation Table Migration ==="{}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_abe4cc9788f52e49485473dc699537388}{try}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_a7978dab33fce909f84e229f7a7188f77}{\$table\+Exists}} = \$db-\/$>$row\+Count() $>$ 0
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(! \$table\+Exists) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_a739ac305594911a5539be43a2839ce59}{\$table\+Definition}} = \$db-\/$>$single()
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_a0b57a487f9061adfdb05c16a1beff71c}{\$create\+Table\+Statement}} = \$table\+Definition-\/$>$\{\textquotesingle{}Create Table\textquotesingle{}\} ?? \textquotesingle{}\textquotesingle{}
\item 
\mbox{\hyperlink{check__db_8php_a9ee42195f2b26ca51b7b816b4f28113e}{catch}}(Exception \$\mbox{\hyperlink{output__helper_8php_a18d38faad6177eda235a3d9d28572984}{e}}) \mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}(\$output as \$line) \mbox{\hyperlink{create__guideline__implementation__table_8php_af66546a0dd955f2fffa08383403b1b44}{\$log\+File}} = \textquotesingle{}../logs/migrations.\+log\textquotesingle{}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{create__guideline__implementation__table_8php_a0b57a487f9061adfdb05c16a1beff71c}\index{create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}!\$createTableStatement@{\$createTableStatement}}
\index{\$createTableStatement@{\$createTableStatement}!create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$createTableStatement}{\$createTableStatement}}
{\footnotesize\ttfamily \label{create__guideline__implementation__table_8php_a0b57a487f9061adfdb05c16a1beff71c} 
\$create\+Table\+Statement = \$table\+Definition-\/$>$\{\textquotesingle{}Create Table\textquotesingle{}\} ?? \textquotesingle{}\textquotesingle{}}

\Hypertarget{create__guideline__implementation__table_8php_a1fa3127fc82f96b1436d871ef02be319}\index{create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}!\$db@{\$db}}
\index{\$db@{\$db}!create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$db}{\$db}}
{\footnotesize\ttfamily \label{create__guideline__implementation__table_8php_a1fa3127fc82f96b1436d871ef02be319} 
\$db = new \mbox{\hyperlink{class_database}{Database}}()}

Create Guideline Implementation Table Script

This script ensures that the maintenance\+\_\+guideline\+\_\+implementation table exists and has the correct structure. It also migrates data from maintenance\+\_\+history\+\_\+guidelines if needed. \Hypertarget{create__guideline__implementation__table_8php_af66546a0dd955f2fffa08383403b1b44}\index{create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}!\$logFile@{\$logFile}}
\index{\$logFile@{\$logFile}!create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$logFile}{\$logFile}}
{\footnotesize\ttfamily \label{create__guideline__implementation__table_8php_af66546a0dd955f2fffa08383403b1b44} 
\mbox{\hyperlink{check__db_8php_a9ee42195f2b26ca51b7b816b4f28113e}{catch}}(Exception \$\mbox{\hyperlink{output__helper_8php_a18d38faad6177eda235a3d9d28572984}{e}}) \mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} ( \$output as \$line) \$log\+File = \textquotesingle{}../logs/migrations.\+log\textquotesingle{}}

\Hypertarget{create__guideline__implementation__table_8php_a73004ce9cd673c1bfafd1dc351134797}\index{create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}!\$output@{\$output}}
\index{\$output@{\$output}!create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$output}{\$output}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily \label{create__guideline__implementation__table_8php_a73004ce9cd673c1bfafd1dc351134797} 
\$output = \mbox{[}$\,$\mbox{]}}

\Hypertarget{create__guideline__implementation__table_8php_a397d52f1e5cf2f76672fbfd1d7a65f7e}\index{create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}!\$output@{\$output}}
\index{\$output@{\$output}!create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$output}{\$output}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily \label{create__guideline__implementation__table_8php_a397d52f1e5cf2f76672fbfd1d7a65f7e} 
\$output\mbox{[}$\,$\mbox{]} = "{}=== Maintenance Guideline Implementation Table Migration ==="{}}

\Hypertarget{create__guideline__implementation__table_8php_a739ac305594911a5539be43a2839ce59}\index{create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}!\$tableDefinition@{\$tableDefinition}}
\index{\$tableDefinition@{\$tableDefinition}!create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$tableDefinition}{\$tableDefinition}}
{\footnotesize\ttfamily \label{create__guideline__implementation__table_8php_a739ac305594911a5539be43a2839ce59} 
\$table\+Definition = \$db-\/$>$single()}

\Hypertarget{create__guideline__implementation__table_8php_a7978dab33fce909f84e229f7a7188f77}\index{create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}!\$tableExists@{\$tableExists}}
\index{\$tableExists@{\$tableExists}!create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$tableExists}{\$tableExists}}
{\footnotesize\ttfamily \label{create__guideline__implementation__table_8php_a7978dab33fce909f84e229f7a7188f77} 
\$table\+Exists = \$db-\/$>$row\+Count() $>$ 0}

\Hypertarget{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}\index{create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}!else@{else}}
\index{else@{else}!create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$table\+Exists) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \mbox{\hyperlink{create__guideline__implementation__table_8php_a73004ce9cd673c1bfafd1dc351134797}{\$output}}[]\ =\ \textcolor{stringliteral}{"{}Table\ 'maintenance\_guideline\_implementation'\ already\ exists."{}}}

\end{DoxyCode}
\Hypertarget{create__guideline__implementation__table_8php_abe4cc9788f52e49485473dc699537388}\index{create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}!try@{try}}
\index{try@{try}!create\_guideline\_implementation\_table.php@{create\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{try}{try}}
{\footnotesize\ttfamily \label{create__guideline__implementation__table_8php_abe4cc9788f52e49485473dc699537388} 
try}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ }
\DoxyCodeLine{\ \ \ \ \mbox{\hyperlink{create__guideline__implementation__table_8php_a1fa3127fc82f96b1436d871ef02be319}{\$db}}-\/>query(\textcolor{stringliteral}{"{}SHOW\ TABLES\ LIKE\ 'maintenance\_guideline\_implementation'"{}})}

\end{DoxyCode}
