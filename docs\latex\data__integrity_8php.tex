\doxysection{app/views/maintenance/data\+\_\+integrity.php File Reference}
\hypertarget{data__integrity_8php}{}\label{data__integrity_8php}\index{app/views/maintenance/data\_integrity.php@{app/views/maintenance/data\_integrity.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$data\mbox{[}\textquotesingle{}orphaned\+\_\+count\textquotesingle{}\mbox{]} $>$ 0) \mbox{\hyperlink{data__integrity_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\item 
\mbox{\hyperlink{data__integrity_8php_aa4a55b7a171fee96901323a7f967d98d}{foreach}} ( \$data\mbox{[} \textquotesingle{}orphaned\+\_\+records\textquotesingle{}\mbox{]} as \$record)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!isset(\$record-\/$>$guideline\+\_\+name)) \mbox{\hyperlink{data__integrity_8php_a5d41e27a3c81a5b5443e692e947081bf}{endforeach}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{data__integrity_8php_a5d41e27a3c81a5b5443e692e947081bf}\index{data\_integrity.php@{data\_integrity.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!data\_integrity.php@{data\_integrity.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{data__integrity_8php_a5d41e27a3c81a5b5443e692e947081bf} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!isset( \$record-\/$>$guideline\+\_\+name)) endforeach}

\Hypertarget{data__integrity_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{data\_integrity.php@{data\_integrity.php}!endif@{endif}}
\index{endif@{endif}!data\_integrity.php@{data\_integrity.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{data__integrity_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

\Hypertarget{data__integrity_8php_aa4a55b7a171fee96901323a7f967d98d}\index{data\_integrity.php@{data\_integrity.php}!foreach@{foreach}}
\index{foreach@{foreach}!data\_integrity.php@{data\_integrity.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}}
{\footnotesize\ttfamily \label{data__integrity_8php_aa4a55b7a171fee96901323a7f967d98d} 
foreach(\$data\mbox{[}\textquotesingle{}orphaned\+\_\+records\textquotesingle{}\mbox{]} as \$record) (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}orphaned\+\_\+records\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

