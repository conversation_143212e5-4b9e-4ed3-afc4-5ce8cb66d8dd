\doxysection{app/views/maintenance/edit\+\_\+checklist\+\_\+item.php File Reference}
\hypertarget{edit__checklist__item_8php}{}\label{edit__checklist__item_8php}\index{app/views/maintenance/edit\_checklist\_item.php@{app/views/maintenance/edit\_checklist\_item.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}description\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{edit__checklist__item_8php_a4935fd2459e8c90327ac7b66fdb42e34}{else}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}description\+\_\+err\textquotesingle{}\mbox{]}) actionable description of what needs to be done$<$/p $>$ \mbox{\hyperlink{edit__checklist__item_8php_a753888f9739cf543c28939168518f181}{endif}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{edit__checklist__item_8php_a4935fd2459e8c90327ac7b66fdb42e34}\index{edit\_checklist\_item.php@{edit\_checklist\_item.php}!else@{else}}
\index{else@{else}!edit\_checklist\_item.php@{edit\_checklist\_item.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{edit__checklist__item_8php_a4935fd2459e8c90327ac7b66fdb42e34} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}description\+\_\+err\textquotesingle{}\mbox{]})) else}

\Hypertarget{edit__checklist__item_8php_a753888f9739cf543c28939168518f181}\index{edit\_checklist\_item.php@{edit\_checklist\_item.php}!endif@{endif}}
\index{endif@{endif}!edit\_checklist\_item.php@{edit\_checklist\_item.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{edit__checklist__item_8php_a753888f9739cf543c28939168518f181} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}description\+\_\+err\textquotesingle{}\mbox{]}) actionable description of what needs to be done$<$/p$>$ endif}

