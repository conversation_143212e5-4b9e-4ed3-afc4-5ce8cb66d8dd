\doxysection{app/views/maintenance/edit\+\_\+guideline.php File Reference}
\hypertarget{edit__guideline_8php}{}\label{edit__guideline_8php}\index{app/views/maintenance/edit\_guideline.php@{app/views/maintenance/edit\_guideline.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset(\$data\mbox{[}\textquotesingle{}\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\textquotesingle{}\mbox{]})) \mbox{\hyperlink{edit__guideline_8php_afb987baea30d9977b985960e3abd34d0}{endif}}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} \mbox{\hyperlink{edit__guideline_8php_af7429309c703156f262362327ed1cb9f}{( \$data\mbox{[} \textquotesingle{}equipment\+\_\+types\textquotesingle{}\mbox{]} as \$type)}} ( \$data\mbox{[} \textquotesingle{}equipment\+\_\+type\textquotesingle{}\mbox{]}==\$type-\/$>$equipment\+\_\+type) ? \textquotesingle{}selected\textquotesingle{}
\item 
\mbox{\hyperlink{edit__guideline_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}frequency\+\_\+days\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{edit__guideline_8php_a8ded857e868741656dd62116ebac2ce0}{else}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{edit__guideline_8php_af7429309c703156f262362327ed1cb9f}\index{edit\_guideline.php@{edit\_guideline.php}!( \$data\mbox{[} \textquotesingle{}equipment\_types\textquotesingle{}\mbox{]} as \$type)@{( \$data[ \textquotesingle{}equipment\_types\textquotesingle{}] as \$type)}}
\index{( \$data\mbox{[} \textquotesingle{}equipment\_types\textquotesingle{}\mbox{]} as \$type)@{( \$data[ \textquotesingle{}equipment\_types\textquotesingle{}] as \$type)}!edit\_guideline.php@{edit\_guideline.php}}
\doxysubsubsection{\texorpdfstring{( \$data[ \textquotesingle{}equipment\_types\textquotesingle{}] as \$type)}{( \$data[ 'equipment\_types'] as \$type)}}
{\footnotesize\ttfamily \label{edit__guideline_8php_af7429309c703156f262362327ed1cb9f} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} (\$data\mbox{[}\textquotesingle{}equipment\+\_\+types\textquotesingle{}\mbox{]} as \$type)(\$data\mbox{[}\textquotesingle{}equipment\+\_\+type\textquotesingle{}\mbox{]}==\$type-\/$>$equipment\+\_\+type) ? \textquotesingle{}selected\textquotesingle{} (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}equipment\+\_\+types\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{edit__guideline_8php_a8ded857e868741656dd62116ebac2ce0}\index{edit\_guideline.php@{edit\_guideline.php}!else@{else}}
\index{else@{else}!edit\_guideline.php@{edit\_guideline.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{edit__guideline_8php_a8ded857e868741656dd62116ebac2ce0} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}frequency\+\_\+days\+\_\+err\textquotesingle{}\mbox{]})) else}

\Hypertarget{edit__guideline_8php_a672d9707ef91db026c210f98cc601123}\index{edit\_guideline.php@{edit\_guideline.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!edit\_guideline.php@{edit\_guideline.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{edit__guideline_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{edit__guideline_8php_afb987baea30d9977b985960e3abd34d0}\index{edit\_guideline.php@{edit\_guideline.php}!endif@{endif}}
\index{endif@{endif}!edit\_guideline.php@{edit\_guideline.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{edit__guideline_8php_afb987baea30d9977b985960e3abd34d0} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}importance\+\_\+err\textquotesingle{}\mbox{]})) endif}

