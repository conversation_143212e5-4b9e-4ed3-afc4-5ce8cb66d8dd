\doxysection{app/helpers/email\+\_\+helper.php File Reference}
\hypertarget{email__helper_8php}{}\label{email__helper_8php}\index{app/helpers/email\_helper.php@{app/helpers/email\_helper.php}}
\doxysubsubsection*{Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{email__helper_8php_ac7b4a4ee33f08e191dba634df62d43ce}{send\+Email}} (\$to, \$subject, \$body, \$alt\+Body=\textquotesingle{}\textquotesingle{})
\item 
\mbox{\hyperlink{email__helper_8php_a478f8d1988c6b936dd9722c64f0f01e8}{send\+Password\+Reset\+Email}} (\$to, \$name, \$token)
\end{DoxyCompactItemize}


\doxysubsection{Function Documentation}
\Hypertarget{email__helper_8php_ac7b4a4ee33f08e191dba634df62d43ce}\index{email\_helper.php@{email\_helper.php}!sendEmail@{sendEmail}}
\index{sendEmail@{sendEmail}!email\_helper.php@{email\_helper.php}}
\doxysubsubsection{\texorpdfstring{sendEmail()}{sendEmail()}}
{\footnotesize\ttfamily \label{email__helper_8php_ac7b4a4ee33f08e191dba634df62d43ce} 
send\+Email (\begin{DoxyParamCaption}\item[{}]{\$to}{, }\item[{}]{\$subject}{, }\item[{}]{\$body}{, }\item[{}]{\$alt\+Body}{ = {\ttfamily \textquotesingle{}\textquotesingle{}}}\end{DoxyParamCaption})}

Send an email using PHPMailer


\begin{DoxyParams}[1]{Parameters}
string & {\em \$to} & Recipient email address \\
\hline
string & {\em \$subject} & Email subject \\
\hline
string & {\em \$body} & Email body (HTML) \\
\hline
string & {\em \$alt\+Body} & Plain text alternative body \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if email sent successfully, false otherwise 
\end{DoxyReturn}
\Hypertarget{email__helper_8php_a478f8d1988c6b936dd9722c64f0f01e8}\index{email\_helper.php@{email\_helper.php}!sendPasswordResetEmail@{sendPasswordResetEmail}}
\index{sendPasswordResetEmail@{sendPasswordResetEmail}!email\_helper.php@{email\_helper.php}}
\doxysubsubsection{\texorpdfstring{sendPasswordResetEmail()}{sendPasswordResetEmail()}}
{\footnotesize\ttfamily \label{email__helper_8php_a478f8d1988c6b936dd9722c64f0f01e8} 
send\+Password\+Reset\+Email (\begin{DoxyParamCaption}\item[{}]{\$to}{, }\item[{}]{\$name}{, }\item[{}]{\$token}{}\end{DoxyParamCaption})}

Send a password reset email


\begin{DoxyParams}[1]{Parameters}
string & {\em \$to} & Recipient email address \\
\hline
string & {\em \$name} & Recipient name \\
\hline
string & {\em \$token} & Password reset token \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if email sent successfully, false otherwise 
\end{DoxyReturn}
