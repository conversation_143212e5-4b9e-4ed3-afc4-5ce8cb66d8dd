\doxysection{app/helpers/error\+\_\+handler.php File Reference}
\hypertarget{error__handler_8php}{}\label{error__handler_8php}\index{app/helpers/error\_handler.php@{app/helpers/error\_handler.php}}
\doxysubsubsection*{Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{error__handler_8php_a03e29e7b07739ac892f00eaba52b2ac6}{custom\+Error\+Handler}} (\$errno, \$errstr, \$errfile, \$errline)
\item 
\mbox{\hyperlink{error__handler_8php_a0bd9e5161069e68fa292a678d6cb955d}{custom\+Exception\+Handler}} (\$exception)
\item 
\mbox{\hyperlink{error__handler_8php_a1ec8538ecc12eddc0c5b7af771bc47d6}{get\+\_\+error\+\_\+type}} (\$errno)
\end{DoxyCompactItemize}


\doxysubsection{Function Documentation}
\Hypertarget{error__handler_8php_a03e29e7b07739ac892f00eaba52b2ac6}\index{error\_handler.php@{error\_handler.php}!customErrorHandler@{customErrorHandler}}
\index{customErrorHandler@{customErrorHandler}!error\_handler.php@{error\_handler.php}}
\doxysubsubsection{\texorpdfstring{customErrorHandler()}{customErrorHandler()}}
{\footnotesize\ttfamily \label{error__handler_8php_a03e29e7b07739ac892f00eaba52b2ac6} 
custom\+Error\+Handler (\begin{DoxyParamCaption}\item[{}]{\$errno}{, }\item[{}]{\$errstr}{, }\item[{}]{\$errfile}{, }\item[{}]{\$errline}{}\end{DoxyParamCaption})}

Custom error handler function


\begin{DoxyParams}[1]{Parameters}
int & {\em \$errno} & Error number \\
\hline
string & {\em \$errstr} & Error message \\
\hline
string & {\em \$errfile} & File where the error occurred \\
\hline
int & {\em \$errline} & Line number where the error occurred \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool 
\end{DoxyReturn}
\Hypertarget{error__handler_8php_a0bd9e5161069e68fa292a678d6cb955d}\index{error\_handler.php@{error\_handler.php}!customExceptionHandler@{customExceptionHandler}}
\index{customExceptionHandler@{customExceptionHandler}!error\_handler.php@{error\_handler.php}}
\doxysubsubsection{\texorpdfstring{customExceptionHandler()}{customExceptionHandler()}}
{\footnotesize\ttfamily \label{error__handler_8php_a0bd9e5161069e68fa292a678d6cb955d} 
custom\+Exception\+Handler (\begin{DoxyParamCaption}\item[{}]{\$exception}{}\end{DoxyParamCaption})}

Custom exception handler function


\begin{DoxyParams}[1]{Parameters}
Exception & {\em \$exception} & The exception object \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
void 
\end{DoxyReturn}
\Hypertarget{error__handler_8php_a1ec8538ecc12eddc0c5b7af771bc47d6}\index{error\_handler.php@{error\_handler.php}!get\_error\_type@{get\_error\_type}}
\index{get\_error\_type@{get\_error\_type}!error\_handler.php@{error\_handler.php}}
\doxysubsubsection{\texorpdfstring{get\_error\_type()}{get\_error\_type()}}
{\footnotesize\ttfamily \label{error__handler_8php_a1ec8538ecc12eddc0c5b7af771bc47d6} 
get\+\_\+error\+\_\+type (\begin{DoxyParamCaption}\item[{}]{\$errno}{}\end{DoxyParamCaption})}

Get error type name from error number


\begin{DoxyParams}[1]{Parameters}
int & {\em \$errno} & Error number \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
string Error type name 
\end{DoxyReturn}
