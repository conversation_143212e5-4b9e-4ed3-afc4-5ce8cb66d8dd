\doxysection{fix\+\_\+guideline\+\_\+implementation\+\_\+table.\+php File Reference}
\hypertarget{fix__guideline__implementation__table_8php}{}\label{fix__guideline__implementation__table_8php}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_a73004ce9cd673c1bfafd1dc351134797}{\$output}} = \mbox{[}$\,$\mbox{]}
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_a397d52f1e5cf2f76672fbfd1d7a65f7e}{\$output}} \mbox{[}$\,$\mbox{]} = "{}$<$h1$>$\mbox{\hyperlink{class_maintenance}{Maintenance}} Guideline Implementation Table Fix$<$/h1$>$"{}
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_abe4cc9788f52e49485473dc699537388}{try}}
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_a011800c63ece4cbbfa77136a20607023}{\$options}}
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_a5766efd703cef0e00bfc06b3f3acbe0e}{\$pdo}} = new PDO(\$dsn, \mbox{\hyperlink{config_8php_a1d1d99f8e08f387d84fe9848f3357156}{DB\+\_\+\+USER}}, \mbox{\hyperlink{config_8php_a8bb9c4546d91667cfa61879d83127a92}{DB\+\_\+\+PASS}}, \$options)
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_af27a9140d5f2658693e7fd107f716449}{\$stmt}} = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}maintenance\+\_\+guideline\+\_\+implementation\textquotesingle{}"{})
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_a7978dab33fce909f84e229f7a7188f77}{\$table\+Exists}} = \$stmt-\/$>$row\+Count() $>$ 0
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(! \$table\+Exists) \mbox{\hyperlink{fix__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}}
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_a112ef069ddc0454086e3d1e6d8d55d07}{\$result}} = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_a739ac305594911a5539be43a2839ce59}{\$table\+Definition}} = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_a0b57a487f9061adfdb05c16a1beff71c}{\$create\+Table\+Statement}} = \$table\+Definition\mbox{[}\textquotesingle{}Create Table\textquotesingle{}\mbox{]} ?? \textquotesingle{}\textquotesingle{}
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_a7c0c259120a0307a68a6af6fc4920bbf}{\$orphaned\+Records}} = \$stmt-\/$>$fetch\+All(PDO\+::\+FETCH\+\_\+\+ASSOC)
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_a4c02af40dc23ebcbfdf20ae145ef9824}{catch}} (PDOException \$\mbox{\hyperlink{output__helper_8php_a18d38faad6177eda235a3d9d28572984}{e}})
\item 
\mbox{\hyperlink{fix__guideline__implementation__table_8php_ae47c9cdcb9158b51999009c1d97b68a6}{foreach}} ( \$output as \$line)
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{fix__guideline__implementation__table_8php_a0b57a487f9061adfdb05c16a1beff71c}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!\$createTableStatement@{\$createTableStatement}}
\index{\$createTableStatement@{\$createTableStatement}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$createTableStatement}{\$createTableStatement}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_a0b57a487f9061adfdb05c16a1beff71c} 
\$create\+Table\+Statement = \$table\+Definition\mbox{[}\textquotesingle{}Create Table\textquotesingle{}\mbox{]} ?? \textquotesingle{}\textquotesingle{}}

\Hypertarget{fix__guideline__implementation__table_8php_a011800c63ece4cbbfa77136a20607023}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!\$options@{\$options}}
\index{\$options@{\$options}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$options}{\$options}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_a011800c63ece4cbbfa77136a20607023} 
\$options}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{=\ [}
\DoxyCodeLine{\ \ \ \ \ \ \ \ PDO::ATTR\_ERRMODE\ =>\ PDO::ERRMODE\_EXCEPTION,}
\DoxyCodeLine{\ \ \ \ \ \ \ \ PDO::ATTR\_DEFAULT\_FETCH\_MODE\ =>\ PDO::FETCH\_OBJ,}
\DoxyCodeLine{\ \ \ \ \ \ \ \ PDO::ATTR\_EMULATE\_PREPARES\ =>\ \textcolor{keyword}{false},}
\DoxyCodeLine{\ \ \ \ ]}

\end{DoxyCode}
\Hypertarget{fix__guideline__implementation__table_8php_a7c0c259120a0307a68a6af6fc4920bbf}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!\$orphanedRecords@{\$orphanedRecords}}
\index{\$orphanedRecords@{\$orphanedRecords}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$orphanedRecords}{\$orphanedRecords}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_a7c0c259120a0307a68a6af6fc4920bbf} 
\$orphaned\+Records = \$stmt-\/$>$fetch\+All(PDO\+::\+FETCH\+\_\+\+ASSOC)}

\Hypertarget{fix__guideline__implementation__table_8php_a73004ce9cd673c1bfafd1dc351134797}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!\$output@{\$output}}
\index{\$output@{\$output}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$output}{\$output}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_a73004ce9cd673c1bfafd1dc351134797} 
\$output = \mbox{[}$\,$\mbox{]}}

Fix Guideline Implementation Table Script

This script ensures that the maintenance\+\_\+guideline\+\_\+implementation table exists and has the correct structure. It also checks for and fixes any data integrity issues. \Hypertarget{fix__guideline__implementation__table_8php_a397d52f1e5cf2f76672fbfd1d7a65f7e}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!\$output@{\$output}}
\index{\$output@{\$output}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$output}{\$output}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_a397d52f1e5cf2f76672fbfd1d7a65f7e} 
\$output\mbox{[}$\,$\mbox{]} = "{}$<$h1$>$\mbox{\hyperlink{class_maintenance}{Maintenance}} Guideline Implementation Table Fix$<$/h1$>$"{}}

\Hypertarget{fix__guideline__implementation__table_8php_a5766efd703cef0e00bfc06b3f3acbe0e}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!\$pdo@{\$pdo}}
\index{\$pdo@{\$pdo}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$pdo}{\$pdo}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_a5766efd703cef0e00bfc06b3f3acbe0e} 
\$pdo = new PDO(\$dsn, \mbox{\hyperlink{config_8php_a1d1d99f8e08f387d84fe9848f3357156}{DB\+\_\+\+USER}}, \mbox{\hyperlink{config_8php_a8bb9c4546d91667cfa61879d83127a92}{DB\+\_\+\+PASS}}, \$options)}

\Hypertarget{fix__guideline__implementation__table_8php_a112ef069ddc0454086e3d1e6d8d55d07}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!\$result@{\$result}}
\index{\$result@{\$result}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$result}{\$result}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_a112ef069ddc0454086e3d1e6d8d55d07} 
\$result = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)}

\Hypertarget{fix__guideline__implementation__table_8php_af27a9140d5f2658693e7fd107f716449}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!\$stmt@{\$stmt}}
\index{\$stmt@{\$stmt}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$stmt}{\$stmt}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_af27a9140d5f2658693e7fd107f716449} 
\$stmt = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}maintenance\+\_\+guideline\+\_\+implementation\textquotesingle{}"{})}

\Hypertarget{fix__guideline__implementation__table_8php_a739ac305594911a5539be43a2839ce59}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!\$tableDefinition@{\$tableDefinition}}
\index{\$tableDefinition@{\$tableDefinition}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$tableDefinition}{\$tableDefinition}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_a739ac305594911a5539be43a2839ce59} 
\$table\+Definition = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)}

\Hypertarget{fix__guideline__implementation__table_8php_a7978dab33fce909f84e229f7a7188f77}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!\$tableExists@{\$tableExists}}
\index{\$tableExists@{\$tableExists}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{\$tableExists}{\$tableExists}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_a7978dab33fce909f84e229f7a7188f77} 
\$table\+Exists = \$stmt-\/$>$row\+Count() $>$ 0}

\Hypertarget{fix__guideline__implementation__table_8php_a4c02af40dc23ebcbfdf20ae145ef9824}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!catch@{catch}}
\index{catch@{catch}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{catch}{catch}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_a4c02af40dc23ebcbfdf20ae145ef9824} 
catch(PDOException \$\mbox{\hyperlink{output__helper_8php_a18d38faad6177eda235a3d9d28572984}{e}}) (\begin{DoxyParamCaption}\item[{PDOException}]{\$e}{}\end{DoxyParamCaption})}

\Hypertarget{fix__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!else@{else}}
\index{else@{else}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$table\+Exists) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \mbox{\hyperlink{create__guideline__implementation__table_8php_a73004ce9cd673c1bfafd1dc351134797}{\$output}}[]\ =\ \textcolor{stringliteral}{"{}<p\ style='color:green'>maintenance\_guideline\_implementation\ table\ exists.</p>"{}}}

\end{DoxyCode}
\Hypertarget{fix__guideline__implementation__table_8php_ae47c9cdcb9158b51999009c1d97b68a6}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!foreach@{foreach}}
\index{foreach@{foreach}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_ae47c9cdcb9158b51999009c1d97b68a6} 
foreach(\$output as \$line) (\begin{DoxyParamCaption}\item[{}]{\$output as}{}\end{DoxyParamCaption})}

\Hypertarget{fix__guideline__implementation__table_8php_abe4cc9788f52e49485473dc699537388}\index{fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}!try@{try}}
\index{try@{try}!fix\_guideline\_implementation\_table.php@{fix\_guideline\_implementation\_table.php}}
\doxysubsubsection{\texorpdfstring{try}{try}}
{\footnotesize\ttfamily \label{fix__guideline__implementation__table_8php_abe4cc9788f52e49485473dc699537388} 
try}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ }
\DoxyCodeLine{\ \ \ \ \$dsn\ =\ \textcolor{stringliteral}{'mysql:host='}\ .\ \mbox{\hyperlink{config_8php_a293363d7988627f671958e2d908c202a}{DB\_HOST}}\ .\ \textcolor{stringliteral}{';dbname='}\ .\ \mbox{\hyperlink{config_8php_ab5db0d3504f917f268614c50b02c53e2}{DB\_NAME}}}

\end{DoxyCode}
