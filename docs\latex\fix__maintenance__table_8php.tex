\doxysection{fix\+\_\+maintenance\+\_\+table.\+php File Reference}
\hypertarget{fix__maintenance__table_8php}{}\label{fix__maintenance__table_8php}\index{fix\_maintenance\_table.php@{fix\_maintenance\_table.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{fix__maintenance__table_8php_abe4cc9788f52e49485473dc699537388}{try}}
\item 
\mbox{\hyperlink{fix__maintenance__table_8php_af27a9140d5f2658693e7fd107f716449}{\$stmt}} = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}maintenance\+\_\+guideline\+\_\+implementation\textquotesingle{}"{})
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$stmt-\/$>$row\+Count()==0) \mbox{\hyperlink{fix__maintenance__table_8php_a92f193b1731ffd6c94a43e39bc2f9bda}{else}}
\item 
\mbox{\hyperlink{fix__maintenance__table_8php_a4c121970b716fbbc2b7e7325f7ae933a}{\$table\+Info}} = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)
\item 
\mbox{\hyperlink{fix__maintenance__table_8php_a0b57a487f9061adfdb05c16a1beff71c}{\$create\+Table\+Statement}} = \$table\+Info\mbox{[}\textquotesingle{}Create Table\textquotesingle{}\mbox{]} ?? \textquotesingle{}\textquotesingle{}
\item 
\mbox{\hyperlink{fix__maintenance__table_8php_a112ef069ddc0454086e3d1e6d8d55d07}{\$result}} = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{fix__maintenance__table_8php_a0b57a487f9061adfdb05c16a1beff71c}\index{fix\_maintenance\_table.php@{fix\_maintenance\_table.php}!\$createTableStatement@{\$createTableStatement}}
\index{\$createTableStatement@{\$createTableStatement}!fix\_maintenance\_table.php@{fix\_maintenance\_table.php}}
\doxysubsubsection{\texorpdfstring{\$createTableStatement}{\$createTableStatement}}
{\footnotesize\ttfamily \label{fix__maintenance__table_8php_a0b57a487f9061adfdb05c16a1beff71c} 
\$create\+Table\+Statement = \$table\+Info\mbox{[}\textquotesingle{}Create Table\textquotesingle{}\mbox{]} ?? \textquotesingle{}\textquotesingle{}}

\Hypertarget{fix__maintenance__table_8php_a112ef069ddc0454086e3d1e6d8d55d07}\index{fix\_maintenance\_table.php@{fix\_maintenance\_table.php}!\$result@{\$result}}
\index{\$result@{\$result}!fix\_maintenance\_table.php@{fix\_maintenance\_table.php}}
\doxysubsubsection{\texorpdfstring{\$result}{\$result}}
{\footnotesize\ttfamily \label{fix__maintenance__table_8php_a112ef069ddc0454086e3d1e6d8d55d07} 
\$result = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)}

\Hypertarget{fix__maintenance__table_8php_af27a9140d5f2658693e7fd107f716449}\index{fix\_maintenance\_table.php@{fix\_maintenance\_table.php}!\$stmt@{\$stmt}}
\index{\$stmt@{\$stmt}!fix\_maintenance\_table.php@{fix\_maintenance\_table.php}}
\doxysubsubsection{\texorpdfstring{\$stmt}{\$stmt}}
{\footnotesize\ttfamily \label{fix__maintenance__table_8php_af27a9140d5f2658693e7fd107f716449} 
\$stmt = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}maintenance\+\_\+guideline\+\_\+implementation\textquotesingle{}"{})}

\Hypertarget{fix__maintenance__table_8php_a4c121970b716fbbc2b7e7325f7ae933a}\index{fix\_maintenance\_table.php@{fix\_maintenance\_table.php}!\$tableInfo@{\$tableInfo}}
\index{\$tableInfo@{\$tableInfo}!fix\_maintenance\_table.php@{fix\_maintenance\_table.php}}
\doxysubsubsection{\texorpdfstring{\$tableInfo}{\$tableInfo}}
{\footnotesize\ttfamily \label{fix__maintenance__table_8php_a4c121970b716fbbc2b7e7325f7ae933a} 
\$table\+Info = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)}

\Hypertarget{fix__maintenance__table_8php_a92f193b1731ffd6c94a43e39bc2f9bda}\index{fix\_maintenance\_table.php@{fix\_maintenance\_table.php}!else@{else}}
\index{else@{else}!fix\_maintenance\_table.php@{fix\_maintenance\_table.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{fix__maintenance__table_8php_a92f193b1731ffd6c94a43e39bc2f9bda} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (strpos( \$create\+Table\+Statement, \textquotesingle{}UNIQUE KEY\textquotesingle{}) !==false) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ echo\ \textcolor{stringliteral}{"{}<p\ style='color:green'>maintenance\_guideline\_implementation\ table\ exists.</p>"{}}}

\end{DoxyCode}
\Hypertarget{fix__maintenance__table_8php_abe4cc9788f52e49485473dc699537388}\index{fix\_maintenance\_table.php@{fix\_maintenance\_table.php}!try@{try}}
\index{try@{try}!fix\_maintenance\_table.php@{fix\_maintenance\_table.php}}
\doxysubsubsection{\texorpdfstring{try}{try}}
{\footnotesize\ttfamily \label{fix__maintenance__table_8php_abe4cc9788f52e49485473dc699537388} 
try}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ }
\DoxyCodeLine{\ \ \ \ \mbox{\hyperlink{fix__guideline__implementation__table_8php_a5766efd703cef0e00bfc06b3f3acbe0e}{\$pdo}}\ =\ \textcolor{keyword}{new}\ PDO(\textcolor{stringliteral}{'mysql:host='}\ .\ \mbox{\hyperlink{config_8php_a293363d7988627f671958e2d908c202a}{DB\_HOST}}\ .\ \textcolor{stringliteral}{';dbname='}\ .\ \mbox{\hyperlink{config_8php_ab5db0d3504f917f268614c50b02c53e2}{DB\_NAME}},\ \mbox{\hyperlink{config_8php_a1d1d99f8e08f387d84fe9848f3357156}{DB\_USER}},\ \mbox{\hyperlink{config_8php_a8bb9c4546d91667cfa61879d83127a92}{DB\_PASS}})}

\end{DoxyCode}
