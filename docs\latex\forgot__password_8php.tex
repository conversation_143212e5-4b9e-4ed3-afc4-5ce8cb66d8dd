\doxysection{app/views/users/forgot\+\_\+password.php File Reference}
\hypertarget{forgot__password_8php}{}\label{forgot__password_8php}\index{app/views/users/forgot\_password.php@{app/views/users/forgot\_password.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty( \$data\mbox{[} \textquotesingle{}email\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}email\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{forgot__password_8php_a2d6e409ccdf1e70a6ff005a2b74cdca3}{endif}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{forgot__password_8php_a2d6e409ccdf1e70a6ff005a2b74cdca3}\index{forgot\_password.php@{forgot\_password.php}!endif@{endif}}
\index{endif@{endif}!forgot\_password.php@{forgot\_password.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{forgot__password_8php_a2d6e409ccdf1e70a6ff005a2b74cdca3} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}captcha\+\_\+err\textquotesingle{}\mbox{]})) endif}

