\doxysection{app/views/maintenance/guideline.php File Reference}
\hypertarget{guideline_8php}{}\label{guideline_8php}\index{app/views/maintenance/guideline.php@{app/views/maintenance/guideline.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{guideline_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b}{\$importance\+Class}} = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{guideline_8php_acce0ffd4737c5884a796b27d1efa48b5}{switch}} ( \$data\mbox{[} \textquotesingle{}guideline\textquotesingle{}\mbox{]}-\/$>$importance)
\item 
\mbox{\hyperlink{guideline_8php_ace2bc155e2c2e60f4ca6e084433104a5}{if}} (isset( \$data\mbox{[} \textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]}) \&\&!empty( \$data\mbox{[} \textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]}))
\item 
\mbox{\hyperlink{guideline_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6}{\$total\+Assets}} = (\$data\mbox{[}\textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]}-\/$>$compliant\+\_\+count ?? 0) + (\$data\mbox{[}\textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]}-\/$>$non\+\_\+compliant\+\_\+count ?? 0)
\item 
\mbox{\hyperlink{guideline_8php_a2d4ce20a31e47694d0105327b8502fe6}{\$compliance\+Percentage}} = \$total\+Assets $>$ 0 ? round((\$data\mbox{[}\textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]}-\/$>$compliant\+\_\+count ?? 0) / \$total\+Assets \texorpdfstring{$\ast$}{*} 100) \+: 0
\item 
\mbox{\hyperlink{guideline_8php_a326d890de202dd1ba5a2d20b910f6e70}{\$compliance\+Class}} = \textquotesingle{}text-\/red-\/600\textquotesingle{}
\item 
\mbox{\hyperlink{guideline_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset(\$data\mbox{[}\textquotesingle{}checklist\+\_\+items\textquotesingle{}\mbox{]}) \&\&!empty(\$data\mbox{[}\textquotesingle{}checklist\+\_\+items\textquotesingle{}\mbox{]})) \mbox{\hyperlink{guideline_8php_a9084991b222cd72d17cb4af9bb4a81bf}{\$display\+Index}} = 0
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}(\$data\mbox{[}\textquotesingle{}checklist\+\_\+items\textquotesingle{}\mbox{]} as \$item)(in\+\_\+array(\$item-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}, \$displayed\+Item\+Ids)) \mbox{\hyperlink{guideline_8php_a3ca62e5bb65d68baeeba9a691736e665}{\$displayed\+Item\+Ids}} \mbox{[}$\,$\mbox{]} = \$item-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}
\item 
\mbox{\hyperlink{guideline_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{guideline_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{guideline_8php_a326d890de202dd1ba5a2d20b910f6e70}\index{guideline.php@{guideline.php}!\$complianceClass@{\$complianceClass}}
\index{\$complianceClass@{\$complianceClass}!guideline.php@{guideline.php}}
\doxysubsubsection{\texorpdfstring{\$complianceClass}{\$complianceClass}}
{\footnotesize\ttfamily \label{guideline_8php_a326d890de202dd1ba5a2d20b910f6e70} 
\$compliance\+Class = \textquotesingle{}text-\/red-\/600\textquotesingle{}}

\Hypertarget{guideline_8php_a2d4ce20a31e47694d0105327b8502fe6}\index{guideline.php@{guideline.php}!\$compliancePercentage@{\$compliancePercentage}}
\index{\$compliancePercentage@{\$compliancePercentage}!guideline.php@{guideline.php}}
\doxysubsubsection{\texorpdfstring{\$compliancePercentage}{\$compliancePercentage}}
{\footnotesize\ttfamily \label{guideline_8php_a2d4ce20a31e47694d0105327b8502fe6} 
\$compliance\+Percentage = \$total\+Assets $>$ 0 ? round((\$data\mbox{[}\textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]}-\/$>$compliant\+\_\+count ?? 0) / \$total\+Assets \texorpdfstring{$\ast$}{*} 100) \+: 0}

\Hypertarget{guideline_8php_a3ca62e5bb65d68baeeba9a691736e665}\index{guideline.php@{guideline.php}!\$displayedItemIds@{\$displayedItemIds}}
\index{\$displayedItemIds@{\$displayedItemIds}!guideline.php@{guideline.php}}
\doxysubsubsection{\texorpdfstring{\$displayedItemIds}{\$displayedItemIds}}
{\footnotesize\ttfamily \label{guideline_8php_a3ca62e5bb65d68baeeba9a691736e665} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} ( \$data\mbox{[} \textquotesingle{}checklist\+\_\+items\textquotesingle{}\mbox{]} as \$item) (in\+\_\+array( \$item-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}, \$displayed\+Item\+Ids)) \$displayed\+Item\+Ids\mbox{[}$\,$\mbox{]} = \$item-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}}

\Hypertarget{guideline_8php_a9084991b222cd72d17cb4af9bb4a81bf}\index{guideline.php@{guideline.php}!\$displayIndex@{\$displayIndex}}
\index{\$displayIndex@{\$displayIndex}!guideline.php@{guideline.php}}
\doxysubsubsection{\texorpdfstring{\$displayIndex}{\$displayIndex}}
{\footnotesize\ttfamily \label{guideline_8php_a9084991b222cd72d17cb4af9bb4a81bf} 
\$display\+Index = 0}

\Hypertarget{guideline_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b}\index{guideline.php@{guideline.php}!\$importanceClass@{\$importanceClass}}
\index{\$importanceClass@{\$importanceClass}!guideline.php@{guideline.php}}
\doxysubsubsection{\texorpdfstring{\$importanceClass}{\$importanceClass}}
{\footnotesize\ttfamily \label{guideline_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b} 
\$importance\+Class = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}}

\Hypertarget{guideline_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6}\index{guideline.php@{guideline.php}!\$totalAssets@{\$totalAssets}}
\index{\$totalAssets@{\$totalAssets}!guideline.php@{guideline.php}}
\doxysubsubsection{\texorpdfstring{\$totalAssets}{\$totalAssets}}
{\footnotesize\ttfamily \label{guideline_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6} 
\$total\+Assets = (\$data\mbox{[}\textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]}-\/$>$compliant\+\_\+count ?? 0) + (\$data\mbox{[}\textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]}-\/$>$non\+\_\+compliant\+\_\+count ?? 0)}

\Hypertarget{guideline_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{guideline.php@{guideline.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!guideline.php@{guideline.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{guideline_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{guideline_8php_a672d9707ef91db026c210f98cc601123}\index{guideline.php@{guideline.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!guideline.php@{guideline.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{guideline_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{guideline_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{guideline.php@{guideline.php}!endif@{endif}}
\index{endif@{endif}!guideline.php@{guideline.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{guideline_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

\Hypertarget{guideline_8php_ace2bc155e2c2e60f4ca6e084433104a5}\index{guideline.php@{guideline.php}!if@{if}}
\index{if@{if}!guideline.php@{guideline.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{guideline_8php_ace2bc155e2c2e60f4ca6e084433104a5} 
if( \$compliance\+Percentage $>$=90) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if(\$compliance\+Percentage $>$=70) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if(\$compliance\+Percentage $>$=50) (\begin{DoxyParamCaption}\item[{isset( \$data\mbox{[} \textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]}) \&\&!empty( \$data\mbox{[} \textquotesingle{}compliance\+\_\+status\textquotesingle{}\mbox{]})}]{}{}\end{DoxyParamCaption})}

\Hypertarget{guideline_8php_acce0ffd4737c5884a796b27d1efa48b5}\index{guideline.php@{guideline.php}!switch@{switch}}
\index{switch@{switch}!guideline.php@{guideline.php}}
\doxysubsubsection{\texorpdfstring{switch}{switch}}
{\footnotesize\ttfamily \label{guideline_8php_acce0ffd4737c5884a796b27d1efa48b5} 
switch(\$data\mbox{[}\textquotesingle{}guideline\textquotesingle{}\mbox{]}-\/$>$importance) (\begin{DoxyParamCaption}\item[{}]{\$data-\/$>$}{\mbox{[} \textquotesingle{}guideline\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

