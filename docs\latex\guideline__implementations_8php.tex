\doxysection{app/views/maintenance/guideline\+\_\+implementations.php File Reference}
\hypertarget{guideline__implementations_8php}{}\label{guideline__implementations_8php}\index{app/views/maintenance/guideline\_implementations.php@{app/views/maintenance/guideline\_implementations.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{guideline__implementations_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b}{\$importance\+Class}} = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{guideline__implementations_8php_a2d00dcd83fafcc8bda8c9f54822ecfdb}{switch}} ( \$data\mbox{[} \textquotesingle{}guideline\textquotesingle{}\mbox{]}-\/$>$importance)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}implementations\textquotesingle{}\mbox{]}))(\$data\mbox{[}\textquotesingle{}implementations\textquotesingle{}\mbox{]} as \$implementation)(\$implementation-\/$>$implemented\+\_\+date) ? date(\textquotesingle{}M \mbox{\hyperlink{maintenance_2add_8php_aa6e74d4a6956f60f154d17e5f1a247eb}{j}} \mbox{\hyperlink{maintenance_2add_8php_af684b39ee5e37a5a84094eb9c37e94e2}{Y}} N A \mbox{\hyperlink{guideline__implementations_8php_a2c60048091118c9f9090c304d09b0e5c}{h}}
\item 
if(!empty(\$data\mbox{[}\textquotesingle{}implementations\textquotesingle{}\mbox{]}))(\$data\mbox{[}\textquotesingle{}implementations\textquotesingle{}\mbox{]} as \$implementation)(\$implementation-\/$>$implemented\+\_\+date) ? date(\textquotesingle{}M \mbox{\hyperlink{maintenance_2add_8php_aa6e74d4a6956f60f154d17e5f1a247eb}{j}} \mbox{\hyperlink{maintenance_2add_8php_af684b39ee5e37a5a84094eb9c37e94e2}{Y}} N A strtotime( \$implementation-\/$>$implemented\+\_\+date)) \mbox{\hyperlink{guideline__implementations_8php_afdce259f9a4417669a271883cd011b0f}{if}} (isset( \$implementation-\/$>$computer\+\_\+host\+\_\+name) \&\&isset( \$implementation-\/$>$equipment\+\_\+type))
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{guideline__implementations_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{guideline__implementations_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}{\+\_\+\+\_\+pad1\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{guideline__implementations_8php_aed2d37b4e8da3f52103ae96ce9d26d82}{\+\_\+\+\_\+pad2\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset(\$implementation-\/$>$maintenance\+\_\+id)) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset( \$implementation-\/$>$asset\+\_\+id)) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!isset(\$implementation-\/$>$maintenance\+\_\+id) \&\&!isset(\$implementation-\/$>$asset\+\_\+id)) \mbox{\hyperlink{guideline__implementations_8php_a7a2f290836f6cfc3034c179440448395}{endforeach}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$data\mbox{[} \textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]} $>$ 1) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$data\mbox{[}\textquotesingle{}current\+\_\+page\textquotesingle{}\mbox{]}$<$ \$data\mbox{[}\textquotesingle{}total\+\_\+pages\textquotesingle{}\mbox{]}) \mbox{\hyperlink{guideline__implementations_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{guideline__implementations_8php_ad3aa1069376b85bd4e503b216d54b18d}{\+\_\+\+\_\+pad3\+\_\+\+\_\+}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{guideline__implementations_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b}\index{guideline\_implementations.php@{guideline\_implementations.php}!\$importanceClass@{\$importanceClass}}
\index{\$importanceClass@{\$importanceClass}!guideline\_implementations.php@{guideline\_implementations.php}}
\doxysubsubsection{\texorpdfstring{\$importanceClass}{\$importanceClass}}
{\footnotesize\ttfamily \label{guideline__implementations_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b} 
\$importance\+Class = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}}

\Hypertarget{guideline__implementations_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{guideline\_implementations.php@{guideline\_implementations.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!guideline\_implementations.php@{guideline\_implementations.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{guideline__implementations_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{guideline__implementations_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}\index{guideline\_implementations.php@{guideline\_implementations.php}!\_\_pad1\_\_@{\_\_pad1\_\_}}
\index{\_\_pad1\_\_@{\_\_pad1\_\_}!guideline\_implementations.php@{guideline\_implementations.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad1\_\_}{\_\_pad1\_\_}}
{\footnotesize\ttfamily \label{guideline__implementations_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad1\+\_\+\+\_\+}

\Hypertarget{guideline__implementations_8php_aed2d37b4e8da3f52103ae96ce9d26d82}\index{guideline\_implementations.php@{guideline\_implementations.php}!\_\_pad2\_\_@{\_\_pad2\_\_}}
\index{\_\_pad2\_\_@{\_\_pad2\_\_}!guideline\_implementations.php@{guideline\_implementations.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad2\_\_}{\_\_pad2\_\_}}
{\footnotesize\ttfamily \label{guideline__implementations_8php_aed2d37b4e8da3f52103ae96ce9d26d82} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad2\+\_\+\+\_\+}

\Hypertarget{guideline__implementations_8php_ad3aa1069376b85bd4e503b216d54b18d}\index{guideline\_implementations.php@{guideline\_implementations.php}!\_\_pad3\_\_@{\_\_pad3\_\_}}
\index{\_\_pad3\_\_@{\_\_pad3\_\_}!guideline\_implementations.php@{guideline\_implementations.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad3\_\_}{\_\_pad3\_\_}}
{\footnotesize\ttfamily \label{guideline__implementations_8php_ad3aa1069376b85bd4e503b216d54b18d} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad3\+\_\+\+\_\+}

\Hypertarget{guideline__implementations_8php_a7a2f290836f6cfc3034c179440448395}\index{guideline\_implementations.php@{guideline\_implementations.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!guideline\_implementations.php@{guideline\_implementations.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{guideline__implementations_8php_a7a2f290836f6cfc3034c179440448395} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset( \$implementation-\/$>$maintenance\+\_\+id)) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset(\$implementation-\/$>$asset\+\_\+id)) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!isset( \$implementation-\/$>$maintenance\+\_\+id) \&\&!isset( \$implementation-\/$>$asset\+\_\+id)) endforeach}

\Hypertarget{guideline__implementations_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{guideline\_implementations.php@{guideline\_implementations.php}!endif@{endif}}
\index{endif@{endif}!guideline\_implementations.php@{guideline\_implementations.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{guideline__implementations_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{guideline__implementations_8php_a2c60048091118c9f9090c304d09b0e5c}\index{guideline\_implementations.php@{guideline\_implementations.php}!h@{h}}
\index{h@{h}!guideline\_implementations.php@{guideline\_implementations.php}}
\doxysubsubsection{\texorpdfstring{h}{h}}
{\footnotesize\ttfamily \label{guideline__implementations_8php_a2c60048091118c9f9090c304d09b0e5c} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty( \$data\mbox{[} \textquotesingle{}implementations\textquotesingle{}\mbox{]})) ( \$data\mbox{[} \textquotesingle{}implementations\textquotesingle{}\mbox{]} as \$implementation) ( \$implementation-\/$>$implemented\+\_\+date) ? date( \textquotesingle{}M \mbox{\hyperlink{maintenance_2add_8php_aa6e74d4a6956f60f154d17e5f1a247eb}{j}} \mbox{\hyperlink{maintenance_2add_8php_af684b39ee5e37a5a84094eb9c37e94e2}{Y}} N A h}

\Hypertarget{guideline__implementations_8php_afdce259f9a4417669a271883cd011b0f}\index{guideline\_implementations.php@{guideline\_implementations.php}!if@{if}}
\index{if@{if}!guideline\_implementations.php@{guideline\_implementations.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{guideline__implementations_8php_afdce259f9a4417669a271883cd011b0f} 
if(!empty( \$data\mbox{[} \textquotesingle{}implementations\textquotesingle{}\mbox{]}))( \$data\mbox{[} \textquotesingle{}implementations\textquotesingle{}\mbox{]} as \$implementation)( \$implementation-\/$>$implemented\+\_\+date) ? date( \textquotesingle{}M \mbox{\hyperlink{maintenance_2add_8php_aa6e74d4a6956f60f154d17e5f1a247eb}{j}} \mbox{\hyperlink{maintenance_2add_8php_af684b39ee5e37a5a84094eb9c37e94e2}{Y}} N A strtotime(\$implementation-\/$>$implemented\+\_\+date)) if(isset(\$implementation-\/$>$computer\+\_\+host\+\_\+name) \&\&isset(\$implementation-\/$>$equipment\+\_\+type)) (\begin{DoxyParamCaption}\item[{isset( \$implementation-\/$>$computer\+\_\+host\+\_\+name) \&\&isset( \$implementation-\/$>$equipment\+\_\+type)}]{}{}\end{DoxyParamCaption})}

\Hypertarget{guideline__implementations_8php_a2d00dcd83fafcc8bda8c9f54822ecfdb}\index{guideline\_implementations.php@{guideline\_implementations.php}!switch@{switch}}
\index{switch@{switch}!guideline\_implementations.php@{guideline\_implementations.php}}
\doxysubsubsection{\texorpdfstring{switch}{switch}}
{\footnotesize\ttfamily \label{guideline__implementations_8php_a2d00dcd83fafcc8bda8c9f54822ecfdb} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset(\$implementation-\/$>$status)) switch(\$implementation-\/$>$status) (\begin{DoxyParamCaption}\item[{}]{\$data-\/$>$}{\mbox{[} \textquotesingle{}guideline\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

