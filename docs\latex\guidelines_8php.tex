\doxysection{app/views/maintenance/guidelines.php File Reference}
\hypertarget{guidelines_8php}{}\label{guidelines_8php}\index{app/views/maintenance/guidelines.php@{app/views/maintenance/guidelines.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{guidelines_8php_afbbbbd3342729ba99f577b836cf3f479}{\$equipment\+Types}} = \mbox{[}$\,$\mbox{]}
\item 
\mbox{\hyperlink{guidelines_8php_ad7dd844225544a6bbbc3d05fd86bd40d}{foreach}} ( \$data\mbox{[} \textquotesingle{}guidelines\textquotesingle{}\mbox{]} as \$guideline)
\item 
\mbox{\hyperlink{guidelines_8php_a2c1bcb77c0686b142a36054fc8c0ff82}{\$critical\+Count}} = 0
\item 
\mbox{\hyperlink{guidelines_8php_ad7dd844225544a6bbbc3d05fd86bd40d}{foreach}} ( \$data\mbox{[} \textquotesingle{}guidelines\textquotesingle{}\mbox{]} as \$guideline)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{guidelines_8php_a568b6957bf610739c8755ee9539e8f60}{(count( \$data\mbox{[} \textquotesingle{}guidelines\textquotesingle{}\mbox{]}) $>$ 0)}} ( \$data\mbox{[} \textquotesingle{}guidelines\textquotesingle{}\mbox{]} as \$guideline)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$guideline-\/$>$frequency\+\_\+days$<$ 30) else \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$guideline-\/$>$frequency\+\_\+days$<$ 365) \mbox{\hyperlink{guidelines_8php_acce42e56c42b7e5c124a396db855895c}{else}}
\item 
\mbox{\hyperlink{guidelines_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b}{\$importance\+Class}} = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}
\item 
if(\$guideline-\/$>$importance==\textquotesingle{}critical\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if( \$guideline-\/$>$importance==\textquotesingle{}high\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{guidelines_8php_aba5673371203cfb0eb42925f370ee6de}{if}} ( \$guideline-\/$>$importance==\textquotesingle{}low\textquotesingle{})
\item 
\mbox{\hyperlink{guidelines_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{guidelines_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{guidelines_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{guidelines_8php_a2c1bcb77c0686b142a36054fc8c0ff82}\index{guidelines.php@{guidelines.php}!\$criticalCount@{\$criticalCount}}
\index{\$criticalCount@{\$criticalCount}!guidelines.php@{guidelines.php}}
\doxysubsubsection{\texorpdfstring{\$criticalCount}{\$criticalCount}}
{\footnotesize\ttfamily \label{guidelines_8php_a2c1bcb77c0686b142a36054fc8c0ff82} 
\$critical\+Count = 0}

\Hypertarget{guidelines_8php_afbbbbd3342729ba99f577b836cf3f479}\index{guidelines.php@{guidelines.php}!\$equipmentTypes@{\$equipmentTypes}}
\index{\$equipmentTypes@{\$equipmentTypes}!guidelines.php@{guidelines.php}}
\doxysubsubsection{\texorpdfstring{\$equipmentTypes}{\$equipmentTypes}}
{\footnotesize\ttfamily \label{guidelines_8php_afbbbbd3342729ba99f577b836cf3f479} 
\$equipment\+Types = \mbox{[}$\,$\mbox{]}}

\Hypertarget{guidelines_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b}\index{guidelines.php@{guidelines.php}!\$importanceClass@{\$importanceClass}}
\index{\$importanceClass@{\$importanceClass}!guidelines.php@{guidelines.php}}
\doxysubsubsection{\texorpdfstring{\$importanceClass}{\$importanceClass}}
{\footnotesize\ttfamily \label{guidelines_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b} 
\$importance\+Class = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}}

\Hypertarget{guidelines_8php_a568b6957bf610739c8755ee9539e8f60}\index{guidelines.php@{guidelines.php}!(count( \$data\mbox{[} \textquotesingle{}guidelines\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}guidelines\textquotesingle{}]) $>$ 0)}}
\index{(count( \$data\mbox{[} \textquotesingle{}guidelines\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}guidelines\textquotesingle{}]) $>$ 0)}!guidelines.php@{guidelines.php}}
\doxysubsubsection{\texorpdfstring{(count( \$data[ \textquotesingle{}guidelines\textquotesingle{}]) $>$ 0)}{(count( \$data[ 'guidelines']) > 0)}}
{\footnotesize\ttfamily \label{guidelines_8php_a568b6957bf610739c8755ee9539e8f60} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (count(\$data\mbox{[}\textquotesingle{}guidelines\textquotesingle{}\mbox{]}) $>$ 0)(\$data\mbox{[}\textquotesingle{}guidelines\textquotesingle{}\mbox{]} as \$guideline) (\begin{DoxyParamCaption}\item[{count( \$data\mbox{[} \textquotesingle{}guidelines\textquotesingle{}\mbox{]})}]{}{, }\item[{0}]{}{}\end{DoxyParamCaption})}

\Hypertarget{guidelines_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{guidelines.php@{guidelines.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!guidelines.php@{guidelines.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{guidelines_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{guidelines_8php_acce42e56c42b7e5c124a396db855895c}\index{guidelines.php@{guidelines.php}!else@{else}}
\index{else@{else}!guidelines.php@{guidelines.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{guidelines_8php_acce42e56c42b7e5c124a396db855895c} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$guideline-\/$>$frequency\+\_\+days$<$ 30) else \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$guideline-\/$>$frequency\+\_\+days$<$ 365) else (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ echo\ floor(\$guideline-\/>frequency\_days\ /\ 365)\ .\ \textcolor{stringliteral}{'\ years'}}

\end{DoxyCode}
\Hypertarget{guidelines_8php_a672d9707ef91db026c210f98cc601123}\index{guidelines.php@{guidelines.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!guidelines.php@{guidelines.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{guidelines_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{guidelines_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{guidelines.php@{guidelines.php}!endif@{endif}}
\index{endif@{endif}!guidelines.php@{guidelines.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{guidelines_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

\Hypertarget{guidelines_8php_ad7dd844225544a6bbbc3d05fd86bd40d}\index{guidelines.php@{guidelines.php}!foreach@{foreach}}
\index{foreach@{foreach}!guidelines.php@{guidelines.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily \label{guidelines_8php_ad7dd844225544a6bbbc3d05fd86bd40d} 
foreach(\$data\mbox{[}\textquotesingle{}guidelines\textquotesingle{}\mbox{]} as \$guideline) (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}guidelines\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{guidelines_8php_ad7dd844225544a6bbbc3d05fd86bd40d}\index{guidelines.php@{guidelines.php}!foreach@{foreach}}
\index{foreach@{foreach}!guidelines.php@{guidelines.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily \label{guidelines_8php_ad7dd844225544a6bbbc3d05fd86bd40d} 
foreach(\$data\mbox{[}\textquotesingle{}guidelines\textquotesingle{}\mbox{]} as \$guideline) (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}guidelines\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{guidelines_8php_aba5673371203cfb0eb42925f370ee6de}\index{guidelines.php@{guidelines.php}!if@{if}}
\index{if@{if}!guidelines.php@{guidelines.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{guidelines_8php_aba5673371203cfb0eb42925f370ee6de} 
if( \$guideline-\/$>$importance==\textquotesingle{}critical\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if(\$guideline-\/$>$importance==\textquotesingle{}high\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if(\$guideline-\/$>$importance==\textquotesingle{}low\textquotesingle{}) (\begin{DoxyParamCaption}\item[{}]{\$guideline-\/$>$}{ = {\ttfamily =~\textquotesingle{}low\textquotesingle{}}}\end{DoxyParamCaption})}

