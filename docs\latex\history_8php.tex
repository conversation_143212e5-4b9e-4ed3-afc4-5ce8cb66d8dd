\doxysection{app/views/maintenance/history.php File Reference}
\hypertarget{history_8php}{}\label{history_8php}\index{app/views/maintenance/history.php@{app/views/maintenance/history.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{history_8php_a1ccb94cadb093164e431630a7020d49f}{\$health\+Score}} = \$data\mbox{[}\textquotesingle{}health\+\_\+metrics\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}health\+\_\+score\textquotesingle{}\mbox{]}
\item 
\mbox{\hyperlink{history_8php_ab82a04539e3f3415ec35adedf87b141f}{\$bg\+Color}} = \textquotesingle{}bg-\/red-\/100\textquotesingle{}
\item 
\mbox{\hyperlink{history_8php_ab99343d4912650d1363355564aa6c179}{\$text\+Color}} = \textquotesingle{}text-\/red-\/500\textquotesingle{}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$health\+Score $>$=90) elseif( \$health\+Score $>$=70) \mbox{\hyperlink{history_8php_a5d2ef56084da76900b54dc6e24111a1f}{elseif}} ( \$health\+Score $>$=50)
\item 
\mbox{\hyperlink{history_8php_ab7a9279b820a48336935e6399ca4449d}{\$remaining\+Days}} = \$data\mbox{[}\textquotesingle{}health\+\_\+metrics\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}estimated\+\_\+remaining\+\_\+life\textquotesingle{}\mbox{]}
\item 
\mbox{\hyperlink{history_8php_a3499cf00dcf227701f7c64cd355f97ab}{\$remaining\+Text}} = \textquotesingle{}\textquotesingle{}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$remaining\+Days $>$ 365) \mbox{\hyperlink{app_2views_2compliance_2index_8php_a2fe92cbc651f6f645279ed84f54274dd}{elseif}}(\$remaining\+Days $>$ 30) \mbox{\hyperlink{history_8php_ab69592dceb9ac068ae47b1fbab27393e}{else}}
\item 
\mbox{\hyperlink{history_8php_ad0ae4ebc0aa0cdb2a8c4137e7284f6ed}{\$failure\+Prob}} = \$data\mbox{[}\textquotesingle{}health\+\_\+metrics\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}failure\+\_\+probability\textquotesingle{}\mbox{]}
\item 
\mbox{\hyperlink{history_8php_a5d5245acca2b1882e8d22272d0f95462}{\$fp\+Bg\+Color}} = \textquotesingle{}bg-\/green-\/100\textquotesingle{}
\item 
\mbox{\hyperlink{history_8php_a583d8afa12d4cd1cfb8d6ae5adc43a4b}{\$fp\+Text\+Color}} = \textquotesingle{}text-\/green-\/500\textquotesingle{}
\item 
\mbox{\hyperlink{history_8php_ad91b289b04487d30b11cd3dc57b4dee5}{\$type\+Class}} = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{history_8php_a0762c7751ac35e246e17b4993490dff6}{switch}} ( \$record-\/$>$maintenance\+\_\+type)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}implemented\+\_\+guidelines\textquotesingle{}\mbox{]}\mbox{[}\$record-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\mbox{]}))(\$data\mbox{[}\textquotesingle{}implemented\+\_\+guidelines\textquotesingle{}\mbox{]}\mbox{[}\$record-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\mbox{]} as \$guideline) \mbox{\hyperlink{history_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{history_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\item 
\mbox{\hyperlink{history_8php_aab9b724306b055e8e4ed6d1e1f1653f1}{\$status\+Class}} = \textquotesingle{}bg-\/gray-\/100 text-\/gray-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{history_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$data\mbox{[}\textquotesingle{}health\+\_\+metrics\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}health\+\_\+score\textquotesingle{}\mbox{]}$<$ 70) \mbox{\hyperlink{maintenance_2add_8php_af684b39ee5e37a5a84094eb9c37e94e2}{Y}} M \mbox{\hyperlink{history_8php_a151ebc11c7bfaffb7cbcfc761c61eec2}{j}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{history_8php_ab82a04539e3f3415ec35adedf87b141f}\index{history.php@{history.php}!\$bgColor@{\$bgColor}}
\index{\$bgColor@{\$bgColor}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{\$bgColor}{\$bgColor}}
{\footnotesize\ttfamily \label{history_8php_ab82a04539e3f3415ec35adedf87b141f} 
\$bg\+Color = \textquotesingle{}bg-\/red-\/100\textquotesingle{}}

\Hypertarget{history_8php_ad0ae4ebc0aa0cdb2a8c4137e7284f6ed}\index{history.php@{history.php}!\$failureProb@{\$failureProb}}
\index{\$failureProb@{\$failureProb}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{\$failureProb}{\$failureProb}}
{\footnotesize\ttfamily \label{history_8php_ad0ae4ebc0aa0cdb2a8c4137e7284f6ed} 
\$failure\+Prob = \$data\mbox{[}\textquotesingle{}health\+\_\+metrics\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}failure\+\_\+probability\textquotesingle{}\mbox{]}}

\Hypertarget{history_8php_a5d5245acca2b1882e8d22272d0f95462}\index{history.php@{history.php}!\$fpBgColor@{\$fpBgColor}}
\index{\$fpBgColor@{\$fpBgColor}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{\$fpBgColor}{\$fpBgColor}}
{\footnotesize\ttfamily \label{history_8php_a5d5245acca2b1882e8d22272d0f95462} 
\$fp\+Bg\+Color = \textquotesingle{}bg-\/green-\/100\textquotesingle{}}

\Hypertarget{history_8php_a583d8afa12d4cd1cfb8d6ae5adc43a4b}\index{history.php@{history.php}!\$fpTextColor@{\$fpTextColor}}
\index{\$fpTextColor@{\$fpTextColor}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{\$fpTextColor}{\$fpTextColor}}
{\footnotesize\ttfamily \label{history_8php_a583d8afa12d4cd1cfb8d6ae5adc43a4b} 
\$fp\+Text\+Color = \textquotesingle{}text-\/green-\/500\textquotesingle{}}

\Hypertarget{history_8php_a1ccb94cadb093164e431630a7020d49f}\index{history.php@{history.php}!\$healthScore@{\$healthScore}}
\index{\$healthScore@{\$healthScore}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{\$healthScore}{\$healthScore}}
{\footnotesize\ttfamily \label{history_8php_a1ccb94cadb093164e431630a7020d49f} 
\$health\+Score = \$data\mbox{[}\textquotesingle{}health\+\_\+metrics\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}health\+\_\+score\textquotesingle{}\mbox{]}}

\Hypertarget{history_8php_ab7a9279b820a48336935e6399ca4449d}\index{history.php@{history.php}!\$remainingDays@{\$remainingDays}}
\index{\$remainingDays@{\$remainingDays}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{\$remainingDays}{\$remainingDays}}
{\footnotesize\ttfamily \label{history_8php_ab7a9279b820a48336935e6399ca4449d} 
\$remaining\+Days = \$data\mbox{[}\textquotesingle{}health\+\_\+metrics\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}estimated\+\_\+remaining\+\_\+life\textquotesingle{}\mbox{]}}

\Hypertarget{history_8php_a3499cf00dcf227701f7c64cd355f97ab}\index{history.php@{history.php}!\$remainingText@{\$remainingText}}
\index{\$remainingText@{\$remainingText}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{\$remainingText}{\$remainingText}}
{\footnotesize\ttfamily \label{history_8php_a3499cf00dcf227701f7c64cd355f97ab} 
\$remaining\+Text = \textquotesingle{}\textquotesingle{}}

\Hypertarget{history_8php_aab9b724306b055e8e4ed6d1e1f1653f1}\index{history.php@{history.php}!\$statusClass@{\$statusClass}}
\index{\$statusClass@{\$statusClass}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{\$statusClass}{\$statusClass}}
{\footnotesize\ttfamily \label{history_8php_aab9b724306b055e8e4ed6d1e1f1653f1} 
\$status\+Class = \textquotesingle{}bg-\/gray-\/100 text-\/gray-\/800\textquotesingle{}}

\Hypertarget{history_8php_ab99343d4912650d1363355564aa6c179}\index{history.php@{history.php}!\$textColor@{\$textColor}}
\index{\$textColor@{\$textColor}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{\$textColor}{\$textColor}}
{\footnotesize\ttfamily \label{history_8php_ab99343d4912650d1363355564aa6c179} 
\$text\+Color = \textquotesingle{}text-\/red-\/500\textquotesingle{}}

\Hypertarget{history_8php_ad91b289b04487d30b11cd3dc57b4dee5}\index{history.php@{history.php}!\$typeClass@{\$typeClass}}
\index{\$typeClass@{\$typeClass}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{\$typeClass}{\$typeClass}}
{\footnotesize\ttfamily \label{history_8php_ad91b289b04487d30b11cd3dc57b4dee5} 
\$type\+Class = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}}

\Hypertarget{history_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{history.php@{history.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{history_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{history_8php_ab69592dceb9ac068ae47b1fbab27393e}\index{history.php@{history.php}!else@{else}}
\index{else@{else}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{history_8php_ab69592dceb9ac068ae47b1fbab27393e} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$remaining\+Days $>$ 365) \mbox{\hyperlink{app_2views_2compliance_2index_8php_a2fe92cbc651f6f645279ed84f54274dd}{elseif}} ( \$remaining\+Days $>$ 30) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \mbox{\hyperlink{history_8php_a3499cf00dcf227701f7c64cd355f97ab}{\$remainingText}}\ =\ \mbox{\hyperlink{history_8php_ab7a9279b820a48336935e6399ca4449d}{\$remainingDays}}\ .\ \textcolor{stringliteral}{'\ day'}\ .\ (\mbox{\hyperlink{history_8php_ab7a9279b820a48336935e6399ca4449d}{\$remainingDays}}\ >\ 1\ ?\ \textcolor{charliteral}{'s'}\ :\ \textcolor{stringliteral}{''})}

\end{DoxyCode}
\Hypertarget{history_8php_a5d2ef56084da76900b54dc6e24111a1f}\index{history.php@{history.php}!elseif@{elseif}}
\index{elseif@{elseif}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{elseif}{elseif}}
{\footnotesize\ttfamily \label{history_8php_a5d2ef56084da76900b54dc6e24111a1f} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$failure\+Prob $>$=60) elseif(\$failure\+Prob $>$=30) (\begin{DoxyParamCaption}\item[{}]{\$health\+Score $>$=}{}\end{DoxyParamCaption})}

\Hypertarget{history_8php_a672d9707ef91db026c210f98cc601123}\index{history.php@{history.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{history_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{history_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{history.php@{history.php}!endif@{endif}}
\index{endif@{endif}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{history_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

\Hypertarget{history_8php_a151ebc11c7bfaffb7cbcfc761c61eec2}\index{history.php@{history.php}!j@{j}}
\index{j@{j}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{j}{j}}
{\footnotesize\ttfamily \label{history_8php_a151ebc11c7bfaffb7cbcfc761c61eec2} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$data\mbox{[} \textquotesingle{}health\+\_\+metrics\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}health\+\_\+score\textquotesingle{}\mbox{]}$<$ 70) \mbox{\hyperlink{maintenance_2add_8php_af684b39ee5e37a5a84094eb9c37e94e2}{Y}} M j (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{history_8php_a0762c7751ac35e246e17b4993490dff6}\index{history.php@{history.php}!switch@{switch}}
\index{switch@{switch}!history.php@{history.php}}
\doxysubsubsection{\texorpdfstring{switch}{switch}}
{\footnotesize\ttfamily \label{history_8php_a0762c7751ac35e246e17b4993490dff6} 
switch(\$record-\/$>$status) (\begin{DoxyParamCaption}\item[{}]{\$record-\/$>$}{}\end{DoxyParamCaption})}

