\doxysection{app/views/assets/import.php File Reference}
\hypertarget{import_8php}{}\label{import_8php}\index{app/views/assets/import.php@{app/views/assets/import.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
$<$ i class="{}fas fa-\/vial mr-\/2"{}$>$$<$/i $>$ Test Upload$<$/a $>$$<$/div $>$$<$/div $>$ \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset(\$data\mbox{[}\textquotesingle{}error\textquotesingle{}\mbox{]})) \mbox{\hyperlink{import_8php_a9c1e0a5ef165bf15233727051cc9816a}{endif}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{import_8php_a9c1e0a5ef165bf15233727051cc9816a}\index{import.php@{import.php}!endif@{endif}}
\index{endif@{endif}!import.php@{import.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{import_8php_a9c1e0a5ef165bf15233727051cc9816a} 
$<$ i class="{}fas fa-\/vial mr-\/2"{}$>$$<$/i $>$ Test Upload$<$/a $>$$<$/div $>$$<$/div $>$ \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (isset( \$data\mbox{[} \textquotesingle{}error\textquotesingle{}\mbox{]})) endif}

