\doxysection{import\+\_\+csv.\+php File Reference}
\hypertarget{import__csv_8php}{}\label{import__csv_8php}\index{import\_csv.php@{import\_csv.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{import__csv_8php_abe4cc9788f52e49485473dc699537388}{try}}
\item 
\mbox{\hyperlink{check__db_8php_a9ee42195f2b26ca51b7b816b4f28113e}{catch}}(PDOException \$\mbox{\hyperlink{output__helper_8php_a18d38faad6177eda235a3d9d28572984}{e}}) \mbox{\hyperlink{import__csv_8php_a3e7241ecae2d76e9bca9c774781d1880}{\$csv\+File}} = \textquotesingle{}FO II \+\_\+\+ICT ASSET INVENTORY.\+csv\textquotesingle{}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!file\+\_\+exists(\$csv\+File)) \mbox{\hyperlink{import__csv_8php_aa72ed5abe27f57130dd8ab9ee3889f22}{\$file}} = fopen(\$csv\+File, \textquotesingle{}r\textquotesingle{})
\item 
for(\$i=0; \$i$<$ 7; \$i++) \mbox{\hyperlink{import__csv_8php_a21f9f43b345ece3b727d24158bda1bfd}{\$sql}}
\item 
\mbox{\hyperlink{import__csv_8php_af27a9140d5f2658693e7fd107f716449}{\$stmt}} = \$pdo-\/$>$prepare(\$sql)
\item 
\mbox{\hyperlink{import__csv_8php_a09df382259cc3cee673a691e821d0186}{\$import\+Count}} = 0
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{import__csv_8php_a3e7241ecae2d76e9bca9c774781d1880}\index{import\_csv.php@{import\_csv.php}!\$csvFile@{\$csvFile}}
\index{\$csvFile@{\$csvFile}!import\_csv.php@{import\_csv.php}}
\doxysubsubsection{\texorpdfstring{\$csvFile}{\$csvFile}}
{\footnotesize\ttfamily \label{import__csv_8php_a3e7241ecae2d76e9bca9c774781d1880} 
\mbox{\hyperlink{check__db_8php_a9ee42195f2b26ca51b7b816b4f28113e}{catch}} (PDOException \$\mbox{\hyperlink{output__helper_8php_a18d38faad6177eda235a3d9d28572984}{e}}) \$csv\+File = \textquotesingle{}FO II \+\_\+\+ICT ASSET INVENTORY.\+csv\textquotesingle{}}

\Hypertarget{import__csv_8php_aa72ed5abe27f57130dd8ab9ee3889f22}\index{import\_csv.php@{import\_csv.php}!\$file@{\$file}}
\index{\$file@{\$file}!import\_csv.php@{import\_csv.php}}
\doxysubsubsection{\texorpdfstring{\$file}{\$file}}
{\footnotesize\ttfamily \label{import__csv_8php_aa72ed5abe27f57130dd8ab9ee3889f22} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!file\+\_\+exists( \$csv\+File)) \$file = fopen(\$csv\+File, \textquotesingle{}r\textquotesingle{})}

\Hypertarget{import__csv_8php_a09df382259cc3cee673a691e821d0186}\index{import\_csv.php@{import\_csv.php}!\$importCount@{\$importCount}}
\index{\$importCount@{\$importCount}!import\_csv.php@{import\_csv.php}}
\doxysubsubsection{\texorpdfstring{\$importCount}{\$importCount}}
{\footnotesize\ttfamily \label{import__csv_8php_a09df382259cc3cee673a691e821d0186} 
\$import\+Count = 0}

\Hypertarget{import__csv_8php_a21f9f43b345ece3b727d24158bda1bfd}\index{import\_csv.php@{import\_csv.php}!\$sql@{\$sql}}
\index{\$sql@{\$sql}!import\_csv.php@{import\_csv.php}}
\doxysubsubsection{\texorpdfstring{\$sql}{\$sql}}
{\footnotesize\ttfamily \label{import__csv_8php_a21f9f43b345ece3b727d24158bda1bfd} 
for ( \$i=0;\$i$<$ 7;\$i++) \$sql (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{=\ \textcolor{stringliteral}{"{}INSERT\ INTO\ assets\ (inventory\_date,\ site\_name,\ employee\_name,\ active\_directory\_name,\ position,\ program\_section,\ computer\_host\_name,\ equipment\_type,\ acquisition\_type,\ operating\_system,\ administration\_type,\ xdr\_installed,\ device\_custodian,\ remarks,\ par\_number,\ serial\_number,\ acquisition\_date,\ estimated\_useful\_life)\ }}
\DoxyCodeLine{\textcolor{stringliteral}{\ \ \ \ \ \ \ \ VALUES\ (:inventory\_date,\ :site\_name,\ :employee\_name,\ :active\_directory\_name,\ :position,\ :program\_section,\ :computer\_host\_name,\ :equipment\_type,\ :acquisition\_type,\ :operating\_system,\ :administration\_type,\ :xdr\_installed,\ :device\_custodian,\ :remarks,\ :par\_number,\ :serial\_number,\ :acquisition\_date,\ :estimated\_useful\_life)"{}}}

\end{DoxyCode}
\Hypertarget{import__csv_8php_af27a9140d5f2658693e7fd107f716449}\index{import\_csv.php@{import\_csv.php}!\$stmt@{\$stmt}}
\index{\$stmt@{\$stmt}!import\_csv.php@{import\_csv.php}}
\doxysubsubsection{\texorpdfstring{\$stmt}{\$stmt}}
{\footnotesize\ttfamily \label{import__csv_8php_af27a9140d5f2658693e7fd107f716449} 
\$stmt = \$pdo-\/$>$prepare(\$sql)}

\Hypertarget{import__csv_8php_abe4cc9788f52e49485473dc699537388}\index{import\_csv.php@{import\_csv.php}!try@{try}}
\index{try@{try}!import\_csv.php@{import\_csv.php}}
\doxysubsubsection{\texorpdfstring{try}{try}}
{\footnotesize\ttfamily \label{import__csv_8php_abe4cc9788f52e49485473dc699537388} 
try}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \mbox{\hyperlink{fix__guideline__implementation__table_8php_a5766efd703cef0e00bfc06b3f3acbe0e}{\$pdo}}\ =\ \textcolor{keyword}{new}\ PDO(\textcolor{stringliteral}{'mysql:host='}\ .\ \mbox{\hyperlink{config_8php_a293363d7988627f671958e2d908c202a}{DB\_HOST}}\ .\ \textcolor{stringliteral}{';dbname='}\ .\ \mbox{\hyperlink{config_8php_ab5db0d3504f917f268614c50b02c53e2}{DB\_NAME}},\ \mbox{\hyperlink{config_8php_a1d1d99f8e08f387d84fe9848f3357156}{DB\_USER}},\ \mbox{\hyperlink{config_8php_a8bb9c4546d91667cfa61879d83127a92}{DB\_PASS}})}

\end{DoxyCode}
