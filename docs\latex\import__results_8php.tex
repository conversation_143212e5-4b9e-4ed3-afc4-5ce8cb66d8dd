\doxysection{app/views/assets/import\+\_\+results.php File Reference}
\hypertarget{import__results_8php}{}\label{import__results_8php}\index{app/views/assets/import\_results.php@{app/views/assets/import\_results.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{import__results_8php_aed48ab316475c4b7ca1bc65c043c87f4}{if}} (isset( \$data\mbox{[} \textquotesingle{}results\textquotesingle{}\mbox{]}) \&\&is\+\_\+array( \$data\mbox{[} \textquotesingle{}results\textquotesingle{}\mbox{]}))
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{import__results_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset(\$data\mbox{[}\textquotesingle{}results\textquotesingle{}\mbox{]}) \&\&is\+\_\+array(\$data\mbox{[}\textquotesingle{}results\textquotesingle{}\mbox{]}) \&\&!empty(\$data\mbox{[}\textquotesingle{}results\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}errors\textquotesingle{}\mbox{]}))(\$data\mbox{[}\textquotesingle{}results\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}errors\textquotesingle{}\mbox{]} as \$error) \mbox{\hyperlink{import__results_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{import__results_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{import__results_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{import\_results.php@{import\_results.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!import\_results.php@{import\_results.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{import__results_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{import__results_8php_a672d9707ef91db026c210f98cc601123}\index{import\_results.php@{import\_results.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!import\_results.php@{import\_results.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{import__results_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{import__results_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{import\_results.php@{import\_results.php}!endif@{endif}}
\index{endif@{endif}!import\_results.php@{import\_results.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{import__results_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

\Hypertarget{import__results_8php_aed48ab316475c4b7ca1bc65c043c87f4}\index{import\_results.php@{import\_results.php}!if@{if}}
\index{if@{if}!import\_results.php@{import\_results.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{import__results_8php_aed48ab316475c4b7ca1bc65c043c87f4} 
if(isset(\$data\mbox{[}\textquotesingle{}results\textquotesingle{}\mbox{]}) \&\&is\+\_\+array(\$data\mbox{[}\textquotesingle{}results\textquotesingle{}\mbox{]})) (\begin{DoxyParamCaption}\item[{isset( \$data\mbox{[} \textquotesingle{}results\textquotesingle{}\mbox{]}) \&\&is\+\_\+array( \$data\mbox{[} \textquotesingle{}results\textquotesingle{}\mbox{]})}]{}{}\end{DoxyParamCaption})}

