\doxysection{app/views/users/login.php File Reference}
\hypertarget{login_8php}{}\label{login_8php}\index{app/views/users/login.php@{app/views/users/login.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty( \$data\mbox{[} \textquotesingle{}email\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}email\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{login_8php_a733d6bc78642281c0abc4adc9d5b5f44}{endif}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{login_8php_a733d6bc78642281c0abc4adc9d5b5f44}\index{login.php@{login.php}!endif@{endif}}
\index{endif@{endif}!login.php@{login.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{login_8php_a733d6bc78642281c0abc4adc9d5b5f44} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]})) endif}

