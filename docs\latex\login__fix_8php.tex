\doxysection{login\+\_\+fix.\+php File Reference}
\hypertarget{login__fix_8php}{}\label{login__fix_8php}\index{login\_fix.php@{login\_fix.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\mbox{\hyperlink{session__helper_8php_a33bdd79e5da367ebddd4cfbdbbfc7cff}{is\+Logged\+In}}()) \mbox{\hyperlink{login__fix_8php_a13feb61b92e1e15e598806dc6e51f328}{else}}
\item 
\mbox{\hyperlink{login__fix_8php_abe4cc9788f52e49485473dc699537388}{try}}
\item 
\mbox{\hyperlink{login__fix_8php_af27a9140d5f2658693e7fd107f716449}{\$stmt}} = \$pdo-\/$>$query("{}SELECT \texorpdfstring{$\ast$}{*} FROM users WHERE email = \textquotesingle{}admin@example.\+com\textquotesingle{}"{})
\item 
\mbox{\hyperlink{login__fix_8php_ad3b3c64b25fbbb6aec24407e4333aa71}{\$admin}} = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)
\item 
\mbox{\hyperlink{login__fix_8php_a607686ef9f99ea7c42f4f3dd3dbb2b0d}{\$password}} = password\+\_\+hash(\textquotesingle{}password123\textquotesingle{}, PASSWORD\+\_\+\+DEFAULT)
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{login__fix_8php_ad3b3c64b25fbbb6aec24407e4333aa71}\index{login\_fix.php@{login\_fix.php}!\$admin@{\$admin}}
\index{\$admin@{\$admin}!login\_fix.php@{login\_fix.php}}
\doxysubsubsection{\texorpdfstring{\$admin}{\$admin}}
{\footnotesize\ttfamily \label{login__fix_8php_ad3b3c64b25fbbb6aec24407e4333aa71} 
\$admin = \$stmt-\/$>$fetch(PDO\+::\+FETCH\+\_\+\+ASSOC)}

\Hypertarget{login__fix_8php_a607686ef9f99ea7c42f4f3dd3dbb2b0d}\index{login\_fix.php@{login\_fix.php}!\$password@{\$password}}
\index{\$password@{\$password}!login\_fix.php@{login\_fix.php}}
\doxysubsubsection{\texorpdfstring{\$password}{\$password}}
{\footnotesize\ttfamily \label{login__fix_8php_a607686ef9f99ea7c42f4f3dd3dbb2b0d} 
\$password = password\+\_\+hash(\textquotesingle{}password123\textquotesingle{}, PASSWORD\+\_\+\+DEFAULT)}

\Hypertarget{login__fix_8php_af27a9140d5f2658693e7fd107f716449}\index{login\_fix.php@{login\_fix.php}!\$stmt@{\$stmt}}
\index{\$stmt@{\$stmt}!login\_fix.php@{login\_fix.php}}
\doxysubsubsection{\texorpdfstring{\$stmt}{\$stmt}}
{\footnotesize\ttfamily \label{login__fix_8php_af27a9140d5f2658693e7fd107f716449} 
\$stmt = \$pdo-\/$>$query("{}SELECT \texorpdfstring{$\ast$}{*} FROM users WHERE email = \textquotesingle{}admin@example.\+com\textquotesingle{}"{})}

\Hypertarget{login__fix_8php_a13feb61b92e1e15e598806dc6e51f328}\index{login\_fix.php@{login\_fix.php}!else@{else}}
\index{else@{else}!login\_fix.php@{login\_fix.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{login__fix_8php_a13feb61b92e1e15e598806dc6e51f328} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$stmt-\/$>$execute()) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ echo\ \textcolor{stringliteral}{"{}<p\ style='color:red'>✗\ User\ is\ not\ logged\ in</p>"{}}}

\end{DoxyCode}
\Hypertarget{login__fix_8php_abe4cc9788f52e49485473dc699537388}\index{login\_fix.php@{login\_fix.php}!try@{try}}
\index{try@{try}!login\_fix.php@{login\_fix.php}}
\doxysubsubsection{\texorpdfstring{try}{try}}
{\footnotesize\ttfamily \label{login__fix_8php_abe4cc9788f52e49485473dc699537388} 
try}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \mbox{\hyperlink{fix__guideline__implementation__table_8php_a5766efd703cef0e00bfc06b3f3acbe0e}{\$pdo}}\ =\ \textcolor{keyword}{new}\ PDO(\textcolor{stringliteral}{'mysql:host='}\ .\ \mbox{\hyperlink{config_8php_a293363d7988627f671958e2d908c202a}{DB\_HOST}}\ .\ \textcolor{stringliteral}{';dbname='}\ .\ \mbox{\hyperlink{config_8php_ab5db0d3504f917f268614c50b02c53e2}{DB\_NAME}},\ \mbox{\hyperlink{config_8php_a1d1d99f8e08f387d84fe9848f3357156}{DB\_USER}},\ \mbox{\hyperlink{config_8php_a8bb9c4546d91667cfa61879d83127a92}{DB\_PASS}})}

\end{DoxyCode}
