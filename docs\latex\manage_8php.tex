\doxysection{app/views/users/manage.php File Reference}
\hypertarget{manage_8php}{}\label{manage_8php}\index{app/views/users/manage.php@{app/views/users/manage.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}( \$data\mbox{[} \textquotesingle{}users\textquotesingle{}\mbox{]} as \$user) \mbox{\hyperlink{manage_8php_aaba99e56d85eb963ae741afc28ba2424}{if}} ( \$user-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}==\$\+\_\+\+SESSION\mbox{[} \textquotesingle{}user\+\_\+id\textquotesingle{}\mbox{]})
\item 
\mbox{\hyperlink{manage_8php_a77e4ffd6c4f34ba51bd783e131d75684}{endforeach}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{manage_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$user-\/$>$status==\textquotesingle{}active\textquotesingle{}) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$user-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}} !=\$\+\_\+\+SESSION \mbox{\hyperlink{manage_8php_a9b54768bcf2217f756f6b02ed5b35c68}{endif}} \mbox{[}\textquotesingle{}user\+\_\+id\textquotesingle{}\mbox{]} \&\&\mbox{\hyperlink{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}{has\+Permission}}(\textquotesingle{}manage\+\_\+users\textquotesingle{}))
\item 
toggle role btn text indigo \mbox{\hyperlink{manage_8php_a4d4d37cf09fd3898b6b03000ae59b1f0}{hover}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{manage_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{manage.php@{manage.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!manage.php@{manage.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{manage_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{manage_8php_a77e4ffd6c4f34ba51bd783e131d75684}\index{manage.php@{manage.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!manage.php@{manage.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{manage_8php_a77e4ffd6c4f34ba51bd783e131d75684} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (\mbox{\hyperlink{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}{has\+Permission}}( \textquotesingle{}manage\+\_\+users\textquotesingle{})) endforeach}

\Hypertarget{manage_8php_a9b54768bcf2217f756f6b02ed5b35c68}\index{manage.php@{manage.php}!endif@{endif}}
\index{endif@{endif}!manage.php@{manage.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{manage_8php_a9b54768bcf2217f756f6b02ed5b35c68} 
endif}

\Hypertarget{manage_8php_a4d4d37cf09fd3898b6b03000ae59b1f0}\index{manage.php@{manage.php}!hover@{hover}}
\index{hover@{hover}!manage.php@{manage.php}}
\doxysubsubsection{\texorpdfstring{hover}{hover}}
{\footnotesize\ttfamily \label{manage_8php_a4d4d37cf09fd3898b6b03000ae59b1f0} 
toggle role btn text indigo hover}

\Hypertarget{manage_8php_aaba99e56d85eb963ae741afc28ba2424}\index{manage.php@{manage.php}!if@{if}}
\index{if@{if}!manage.php@{manage.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{manage_8php_aaba99e56d85eb963ae741afc28ba2424} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}(\$data\mbox{[}\textquotesingle{}users\textquotesingle{}\mbox{]} as \$user) if(\$user-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}==\$\+\_\+\+SESSION\mbox{[}\textquotesingle{}user\+\_\+id\textquotesingle{}\mbox{]}) (\begin{DoxyParamCaption}\item[{}]{\$user-\/$>$}{ = {\ttfamily =~\$\+\_\+SESSION\mbox{[}\textquotesingle{}user\+\_\+id\textquotesingle{}\mbox{]}}}\end{DoxyParamCaption})}

