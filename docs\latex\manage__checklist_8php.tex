\doxysection{app/views/maintenance/manage\+\_\+checklist.php File Reference}
\hypertarget{manage__checklist_8php}{}\label{manage__checklist_8php}\index{app/views/maintenance/manage\_checklist.php@{app/views/maintenance/manage\_checklist.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset( \$data\mbox{[} \textquotesingle{}checklist\+\_\+items\textquotesingle{}\mbox{]}) \&\&!empty( \$data\mbox{[} \textquotesingle{}checklist\+\_\+items\textquotesingle{}\mbox{]})) \mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}(\$data\mbox{[}\textquotesingle{}checklist\+\_\+items\textquotesingle{}\mbox{]} as \$item)(in\+\_\+array(\$item-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}, \$displayed\+Item\+Ids)) \mbox{\hyperlink{manage__checklist_8php_a049c263ceeedfb51bd740f8cdb2804c9}{\$displayed\+Item\+Ids}} \mbox{[}$\,$\mbox{]} = \$item-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}
\item 
\mbox{\hyperlink{manage__checklist_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{manage__checklist_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{manage__checklist_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{manage__checklist_8php_a049c263ceeedfb51bd740f8cdb2804c9}\index{manage\_checklist.php@{manage\_checklist.php}!\$displayedItemIds@{\$displayedItemIds}}
\index{\$displayedItemIds@{\$displayedItemIds}!manage\_checklist.php@{manage\_checklist.php}}
\doxysubsubsection{\texorpdfstring{\$displayedItemIds}{\$displayedItemIds}}
{\footnotesize\ttfamily \label{manage__checklist_8php_a049c263ceeedfb51bd740f8cdb2804c9} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset(\$data\mbox{[}\textquotesingle{}checklist\+\_\+items\textquotesingle{}\mbox{]}) \&\&!empty(\$data\mbox{[}\textquotesingle{}checklist\+\_\+items\textquotesingle{}\mbox{]})) \mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} ( \$data\mbox{[} \textquotesingle{}checklist\+\_\+items\textquotesingle{}\mbox{]} as \$item) (in\+\_\+array( \$item-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}, \$displayed\+Item\+Ids)) \$displayed\+Item\+Ids\mbox{[}$\,$\mbox{]} = \$item-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}}

\Hypertarget{manage__checklist_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{manage\_checklist.php@{manage\_checklist.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!manage\_checklist.php@{manage\_checklist.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{manage__checklist_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{manage__checklist_8php_a672d9707ef91db026c210f98cc601123}\index{manage\_checklist.php@{manage\_checklist.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!manage\_checklist.php@{manage\_checklist.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{manage__checklist_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{manage__checklist_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{manage\_checklist.php@{manage\_checklist.php}!endif@{endif}}
\index{endif@{endif}!manage\_checklist.php@{manage\_checklist.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{manage__checklist_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

