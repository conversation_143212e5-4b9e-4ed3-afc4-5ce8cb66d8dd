\chapter{Asset Visibility System}
\hypertarget{md__r_e_a_d_m_e}{}\label{md__r_e_a_d_m_e}\index{Asset Visibility System@{Asset Visibility System}}
\label{md__r_e_a_d_m_e_autotoc_md15}%
\Hypertarget{md__r_e_a_d_m_e_autotoc_md15}%


A PHP OOP MVC application for managing ICT assets inventory.\hypertarget{md__r_e_a_d_m_e_autotoc_md16}{}\doxysection{\texorpdfstring{Features}{Features}}\label{md__r_e_a_d_m_e_autotoc_md16}

\begin{DoxyItemize}
\item \doxylink{class_user}{User} authentication (login/register)
\item \doxylink{class_asset}{Asset} management (CRUD operations)
\item CSV import functionality for bulk asset uploads
\item Search functionality
\item Responsive design with Bootstrap and Shad\+CN UI
\item j\+Query for enhanced interactivity
\end{DoxyItemize}\hypertarget{md__r_e_a_d_m_e_autotoc_md17}{}\doxysection{\texorpdfstring{Requirements}{Requirements}}\label{md__r_e_a_d_m_e_autotoc_md17}

\begin{DoxyItemize}
\item PHP 7.\+4 or higher
\item My\+SQL 5.\+7 or higher
\item Apache web server with mod\+\_\+rewrite enabled
\end{DoxyItemize}\hypertarget{md__r_e_a_d_m_e_autotoc_md18}{}\doxysection{\texorpdfstring{Installation}{Installation}}\label{md__r_e_a_d_m_e_autotoc_md18}

\begin{DoxyEnumerate}
\item Clone the repository to your web server\textquotesingle{}s document root\+: 
\begin{DoxyCode}{0}
\DoxyCodeLine{git\ clone\ https://github.com/jmmaguigad/asset\_visibility.git}

\end{DoxyCode}

\item Create a My\+SQL database\+: 
\begin{DoxyCode}{0}
\DoxyCodeLine{mysql\ -\/u\ root\ -\/p}
\DoxyCodeLine{CREATE\ DATABASE\ asset\_visibility;}
\DoxyCodeLine{exit;}

\end{DoxyCode}

\item Import the database schema\+: 
\begin{DoxyCode}{0}
\DoxyCodeLine{mysql\ -\/u\ root\ -\/p\ asset\_visibility\ <\ database.sql}

\end{DoxyCode}

\item Configure the database connection in {\ttfamily \doxylink{config_8php}{app/config/config.\+php}}\+: 
\begin{DoxyCode}{0}
\DoxyCodeLine{define(\textcolor{stringliteral}{'DB\_HOST'},\ \textcolor{stringliteral}{'localhost'});}
\DoxyCodeLine{define(\textcolor{stringliteral}{'DB\_USER'},\ \textcolor{stringliteral}{'your\_username'});}
\DoxyCodeLine{define(\textcolor{stringliteral}{'DB\_PASS'},\ \textcolor{stringliteral}{'your\_password'});}
\DoxyCodeLine{define(\textcolor{stringliteral}{'DB\_NAME'},\ \textcolor{stringliteral}{'asset\_visibility'});}

\end{DoxyCode}

\item Configure the URL root in {\ttfamily \doxylink{config_8php}{app/config/config.\+php}}\+: 
\begin{DoxyCode}{0}
\DoxyCodeLine{define(\textcolor{stringliteral}{'URLROOT'},\ \textcolor{stringliteral}{'http://localhost/asset\_visibility'});}

\end{DoxyCode}

\item Import data from the CSV file\+: 
\begin{DoxyCode}{0}
\DoxyCodeLine{php\ import\_csv.php}

\end{DoxyCode}

\item Set up Apache virtual host (optional)\+: 
\begin{DoxyCode}{0}
\DoxyCodeLine{<VirtualHost\ *:80>}
\DoxyCodeLine{\ \ \ \ ServerName\ asset\_visibility.local}
\DoxyCodeLine{\ \ \ \ DocumentRoot\ /path/to/asset\_visibility}
\DoxyCodeLine{\ \ \ \ <Directory\ /path/to/asset\_visibility>}
\DoxyCodeLine{\ \ \ \ \ \ \ \ Options\ Indexes\ FollowSymLinks\ MultiViews}
\DoxyCodeLine{\ \ \ \ \ \ \ \ AllowOverride\ All}
\DoxyCodeLine{\ \ \ \ \ \ \ \ Require\ all\ granted}
\DoxyCodeLine{\ \ \ \ </Directory>}
\DoxyCodeLine{</VirtualHost>}

\end{DoxyCode}

\item Add the domain to your hosts file (optional)\+: 
\begin{DoxyCode}{0}
\DoxyCodeLine{127.0.0.1\ asset\_visibility.local}

\end{DoxyCode}

\end{DoxyEnumerate}\hypertarget{md__r_e_a_d_m_e_autotoc_md19}{}\doxysection{\texorpdfstring{Usage}{Usage}}\label{md__r_e_a_d_m_e_autotoc_md19}

\begin{DoxyEnumerate}
\item Navigate to the application URL (e.\+g., \href{http://localhost/asset_visibility}{\texttt{ http\+://localhost/asset\+\_\+visibility}})
\item Login with the default admin account\+:
\begin{DoxyItemize}
\item Email\+: \href{mailto:<EMAIL>}{\texttt{ admin@example.\+com}}
\item Password\+: password123
\end{DoxyItemize}
\item Start managing your assets!
\end{DoxyEnumerate}\hypertarget{md__r_e_a_d_m_e_autotoc_md20}{}\doxysubsection{\texorpdfstring{Importing Assets from CSV}{Importing Assets from CSV}}\label{md__r_e_a_d_m_e_autotoc_md20}

\begin{DoxyEnumerate}
\item Login as an administrator
\item Navigate to the \doxylink{class_assets}{Assets} page
\item Click on the "{}\+Import CSV"{} button
\item Upload your CSV file following the required format
\item Adjust the number of header rows to skip if needed
\item Click "{}\+Import"{} to process the file
\end{DoxyEnumerate}

The CSV file should have the following columns in order\+:
\begin{DoxyItemize}
\item Date
\item Site Name
\item Employee Name
\item Active Directory Name
\item Position
\item Program/\+Section
\item Computer/\+Host Name (required)
\item Type of Equipment (required)
\item Acquisition Type
\item Operating System
\item Administration Type
\item XDR Installed (Yes/\+No)
\item Device Custodian
\item Remarks
\item PAR Number
\item Serial Number (required)
\item Acquisition Date
\item Estimated Useful Life
\end{DoxyItemize}\hypertarget{md__r_e_a_d_m_e_autotoc_md21}{}\doxysection{\texorpdfstring{Directory Structure}{Directory Structure}}\label{md__r_e_a_d_m_e_autotoc_md21}

\begin{DoxyCode}{0}
\DoxyCodeLine{asset\_visibility/}
\DoxyCodeLine{├──\ app/}
\DoxyCodeLine{│\ \ \ ├──\ bootstrap.php}
\DoxyCodeLine{│\ \ \ ├──\ config/}
\DoxyCodeLine{│\ \ \ ├──\ controllers/}
\DoxyCodeLine{│\ \ \ ├──\ core/}
\DoxyCodeLine{│\ \ \ ├──\ helpers/}
\DoxyCodeLine{│\ \ \ ├──\ models/}
\DoxyCodeLine{│\ \ \ └──\ views/}
\DoxyCodeLine{├──\ public/}
\DoxyCodeLine{│\ \ \ ├──\ css/}
\DoxyCodeLine{│\ \ \ ├──\ js/}
\DoxyCodeLine{│\ \ \ ├──\ img/}
\DoxyCodeLine{│\ \ \ ├──\ .htaccess}
\DoxyCodeLine{│\ \ \ └──\ index.php}
\DoxyCodeLine{├──\ .htaccess}
\DoxyCodeLine{├──\ database.sql}
\DoxyCodeLine{└──\ import\_csv.php}

\end{DoxyCode}
\hypertarget{md__r_e_a_d_m_e_autotoc_md22}{}\doxysection{\texorpdfstring{License}{License}}\label{md__r_e_a_d_m_e_autotoc_md22}
This project is licensed under the MIT License. 