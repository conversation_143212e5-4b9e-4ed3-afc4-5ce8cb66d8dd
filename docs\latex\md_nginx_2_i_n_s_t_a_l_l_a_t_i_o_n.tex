\chapter{Nginx Installation Guide for Asset Visibility System}
\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n}{}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n}\index{Nginx Installation Guide for Asset Visibility System@{Nginx Installation Guide for Asset Visibility System}}
\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md0}%
\Hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md0}%


This guide will help you set up the \doxylink{class_asset}{Asset} Visibility System with Nginx as the web server.\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md1}{}\doxysection{\texorpdfstring{Prerequisites}{Prerequisites}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md1}

\begin{DoxyItemize}
\item Nginx installed on your server
\item PHP 8.\+0 or higher with PHP-\/\+FPM
\item My\+SQL or Maria\+DB
\item The \doxylink{class_asset}{Asset} Visibility System codebase
\end{DoxyItemize}\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md2}{}\doxysection{\texorpdfstring{Installation Steps}{Installation Steps}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md2}
\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md3}{}\doxysubsection{\texorpdfstring{1. Install Nginx and PHP-\/\+FPM}{1. Install Nginx and PHP-\/\+FPM}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md3}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ For\ Ubuntu/Debian}
\DoxyCodeLine{sudo\ apt\ update}
\DoxyCodeLine{sudo\ apt\ install\ nginx\ php-\/fpm\ php-\/mysql\ php-\/mbstring\ php-\/xml\ php-\/curl}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ For\ CentOS/RHEL}
\DoxyCodeLine{sudo\ yum\ install\ epel-\/release}
\DoxyCodeLine{sudo\ yum\ install\ nginx\ php-\/fpm\ php-\/mysql\ php-\/mbstring\ php-\/xml\ php-\/curl}

\end{DoxyCode}
\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md4}{}\doxysubsection{\texorpdfstring{2. Configure PHP-\/\+FPM}{2. Configure PHP-\/\+FPM}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md4}
Check your PHP-\/\+FPM version and configuration\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{php\ -\/v}

\end{DoxyCode}


Edit the PHP-\/\+FPM configuration if needed\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ For\ Ubuntu/Debian\ (adjust\ the\ PHP\ version\ as\ needed)}
\DoxyCodeLine{sudo\ nano\ /etc/php/8.0/fpm/php.ini}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ For\ CentOS/RHEL}
\DoxyCodeLine{sudo\ nano\ /etc/php.ini}

\end{DoxyCode}


Recommended PHP settings\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{upload\_max\_filesize\ =\ 20M}
\DoxyCodeLine{post\_max\_size\ =\ 20M}
\DoxyCodeLine{memory\_limit\ =\ 256M}
\DoxyCodeLine{max\_execution\_time\ =\ 120}
\DoxyCodeLine{date.timezone\ =\ "{}Your/Timezone"{}\ \ \#\ e.g.,\ "{}America/New\_York"{}}

\end{DoxyCode}


Restart PHP-\/\+FPM\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ For\ Ubuntu/Debian}
\DoxyCodeLine{sudo\ systemctl\ restart\ php8.0-\/fpm}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ For\ CentOS/RHEL}
\DoxyCodeLine{sudo\ systemctl\ restart\ php-\/fpm}

\end{DoxyCode}
\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md5}{}\doxysubsection{\texorpdfstring{3. Configure Nginx}{3. Configure Nginx}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md5}
Copy the provided Nginx configuration file to the Nginx sites directory\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ For\ Ubuntu/Debian}
\DoxyCodeLine{sudo\ cp\ nginx/asset\_visibility.conf\ /etc/nginx/sites-\/available/}
\DoxyCodeLine{sudo\ ln\ -\/s\ /etc/nginx/sites-\/available/asset\_visibility.conf\ /etc/nginx/sites-\/enabled/}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ For\ CentOS/RHEL}
\DoxyCodeLine{sudo\ cp\ nginx/asset\_visibility.conf\ /etc/nginx/conf.d/}

\end{DoxyCode}


Edit the configuration file to match your server setup\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ For\ Ubuntu/Debian}
\DoxyCodeLine{sudo\ nano\ /etc/nginx/sites-\/available/asset\_visibility.conf}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ For\ CentOS/RHEL}
\DoxyCodeLine{sudo\ nano\ /etc/nginx/conf.d/asset\_visibility.conf}

\end{DoxyCode}


Make sure to update\+:
\begin{DoxyItemize}
\item {\ttfamily server\+\_\+name} -\/ Set to your domain or IP address
\item {\ttfamily root} -\/ Set to the actual path of your \doxylink{class_asset}{Asset} Visibility System
\item {\ttfamily fastcgi\+\_\+pass} -\/ Set to the correct PHP-\/\+FPM socket or TCP address
\end{DoxyItemize}\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md6}{}\doxysubsection{\texorpdfstring{4. Set Up Local Host (Development Environment)}{4. Set Up Local Host (Development Environment)}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md6}
If you\textquotesingle{}re setting up on a local development machine, add the domain to your hosts file\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ For\ Windows}
\DoxyCodeLine{\#\ Edit\ C:\(\backslash\)Windows\(\backslash\)System32\(\backslash\)drivers\(\backslash\)etc\(\backslash\)hosts}
\DoxyCodeLine{\#\ Add:\ 127.0.0.1\ asset\_visibility.local}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ For\ Linux/Mac}
\DoxyCodeLine{sudo\ nano\ /etc/hosts}
\DoxyCodeLine{\#\ Add:\ 127.0.0.1\ asset\_visibility.local}

\end{DoxyCode}
\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md7}{}\doxysubsection{\texorpdfstring{5. Test and Restart Nginx}{5. Test and Restart Nginx}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md7}
Test the Nginx configuration\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{sudo\ nginx\ -\/t}

\end{DoxyCode}


If the test is successful, restart Nginx\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{sudo\ systemctl\ restart\ nginx}

\end{DoxyCode}
\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md8}{}\doxysubsection{\texorpdfstring{6. Set Proper Permissions}{6. Set Proper Permissions}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md8}
Make sure Nginx can read the files and PHP-\/\+FPM can write to necessary directories\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Adjust\ the\ user\ and\ group\ as\ needed\ (www-\/data\ for\ Ubuntu,\ nginx\ for\ CentOS)}
\DoxyCodeLine{sudo\ chown\ -\/R\ www-\/data:www-\/data\ /path/to/asset\_visibility}
\DoxyCodeLine{sudo\ find\ /path/to/asset\_visibility\ -\/type\ d\ -\/exec\ chmod\ 755\ \{\}\ \(\backslash\);}
\DoxyCodeLine{sudo\ find\ /path/to/asset\_visibility\ -\/type\ f\ -\/exec\ chmod\ 644\ \{\}\ \(\backslash\);}

\end{DoxyCode}
\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md9}{}\doxysubsection{\texorpdfstring{7. Update Application Configuration}{7. Update Application Configuration}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md9}
Update the {\ttfamily \doxylink{config_8php}{app/config/config.\+php}} file to match your new setup\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{define(\textcolor{stringliteral}{'URLROOT'},\ \textcolor{stringliteral}{'http://asset\_visibility.local'});\ \textcolor{comment}{//\ Change\ to\ your\ domain}}

\end{DoxyCode}
\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md10}{}\doxysubsection{\texorpdfstring{8. Setting Up SSL (Optional but Recommended)}{8. Setting Up SSL (Optional but Recommended)}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md10}
Generate SSL certificates (or use Let\textquotesingle{}s Encrypt)\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Using\ Let's\ Encrypt}
\DoxyCodeLine{sudo\ apt\ install\ certbot\ python3-\/certbot-\/nginx}
\DoxyCodeLine{sudo\ certbot\ -\/-\/nginx\ -\/d\ asset\_visibility.local}

\end{DoxyCode}


Or use self-\/signed certificates for development\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{sudo\ mkdir\ -\/p\ /etc/nginx/ssl}
\DoxyCodeLine{sudo\ openssl\ req\ -\/x509\ -\/nodes\ -\/days\ 365\ -\/newkey\ rsa:2048\ -\/keyout\ /etc/nginx/ssl/asset\_visibility.key\ -\/out\ /etc/nginx/ssl/asset\_visibility.crt}

\end{DoxyCode}


Then uncomment the HTTPS server block in the Nginx configuration file and restart Nginx.\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md11}{}\doxysection{\texorpdfstring{Troubleshooting}{Troubleshooting}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md11}
\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md12}{}\doxysubsection{\texorpdfstring{Common Issues}{Common Issues}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md12}

\begin{DoxyEnumerate}
\item {\bfseries{403 Forbidden Error}}
\begin{DoxyItemize}
\item Check file permissions
\item Verify Nginx user has access to the files
\end{DoxyItemize}
\item {\bfseries{404 Not Found Error}}
\begin{DoxyItemize}
\item Check the {\ttfamily root} directive in the Nginx configuration
\item Verify the rewrite rules
\end{DoxyItemize}
\item {\bfseries{502 Bad Gateway Error}}
\begin{DoxyItemize}
\item Check if PHP-\/\+FPM is running
\item Verify the {\ttfamily fastcgi\+\_\+pass} directive
\end{DoxyItemize}
\item {\bfseries{Blank Page or PHP Errors}}
\begin{DoxyItemize}
\item Check PHP error logs
\item Enable error reporting in PHP
\end{DoxyItemize}
\end{DoxyEnumerate}\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md13}{}\doxysubsection{\texorpdfstring{Checking Logs}{Checking Logs}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md13}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Nginx\ error\ logs}
\DoxyCodeLine{sudo\ tail\ -\/f\ /var/log/nginx/error.log}
\DoxyCodeLine{sudo\ tail\ -\/f\ /var/log/nginx/asset\_visibility\_error.log}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ PHP-\/FPM\ logs}
\DoxyCodeLine{sudo\ tail\ -\/f\ /var/log/php8.0-\/fpm.log\ \ \#\ Adjust\ version\ as\ needed}

\end{DoxyCode}
\hypertarget{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md14}{}\doxysection{\texorpdfstring{Security Considerations}{Security Considerations}}\label{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n_autotoc_md14}

\begin{DoxyEnumerate}
\item {\bfseries{Firewall Configuration}}
\begin{DoxyItemize}
\item Allow only ports 80 and 443
\item Use UFW or firewalld to manage firewall rules
\end{DoxyItemize}
\item {\bfseries{Regular Updates}}
\begin{DoxyItemize}
\item Keep Nginx, PHP, and all dependencies updated
\end{DoxyItemize}
\item {\bfseries{Use HTTPS}}
\begin{DoxyItemize}
\item Always use SSL/\+TLS in production
\item Redirect HTTP to HTTPS
\end{DoxyItemize}
\item {\bfseries{Secure Headers}}
\begin{DoxyItemize}
\item The provided configuration includes security headers
\item Consider using additional headers as needed 
\end{DoxyItemize}
\end{DoxyEnumerate}