\doxysection{app/views/maintenance/monitoring.php File Reference}
\hypertarget{monitoring_8php}{}\label{monitoring_8php}\index{app/views/maintenance/monitoring.php@{app/views/maintenance/monitoring.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{monitoring_8php_aaf8006d608d2a4f1d18029ca0974e3b3}{\$total\+Compliance}} = \$data\mbox{[}\textquotesingle{}compliant\+\_\+count\textquotesingle{}\mbox{]} + \$data\mbox{[}\textquotesingle{}due\+\_\+soon\+\_\+count\textquotesingle{}\mbox{]} + \$data\mbox{[}\textquotesingle{}overdue\+\_\+count\textquotesingle{}\mbox{]} + \$data\mbox{[}\textquotesingle{}not\+\_\+applicable\+\_\+count\textquotesingle{}\mbox{]}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$total\+Compliance $>$ 0) \mbox{\hyperlink{monitoring_8php_a42c52794fac97a1493ac79713f561aea}{else}}
\item 
\mbox{\hyperlink{monitoring_8php_abddda9a408bfb52fd323be438cd3fe94}{\$due\+Soon\+Percent}} = 0
\item 
\mbox{\hyperlink{monitoring_8php_ac563375536f300b2749efe560e300a49}{\$overdue\+Percent}} = 0
\item 
\mbox{\hyperlink{monitoring_8php_ad60a350defbdae74bc48faa36a85c480}{\$not\+Applicable\+Percent}} = 0
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{monitoring_8php_ad65be5df1eedfe40f4fcfe99e3200095}{(count( \$data\mbox{[} \textquotesingle{}overdue\+\_\+maintenance\textquotesingle{}\mbox{]}) $>$ 0)}} ( \$data\mbox{[} \textquotesingle{}overdue\+\_\+maintenance\textquotesingle{}\mbox{]} as \$item)
\item 
\mbox{\hyperlink{monitoring_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b}{\$importance\+Class}} = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}
\item 
if(\$item-\/$>$importance==\textquotesingle{}critical\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if( \$item-\/$>$importance==\textquotesingle{}high\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{monitoring_8php_a424696754ad463bb909e68f85c19b462}{if}} ( \$item-\/$>$importance==\textquotesingle{}low\textquotesingle{})
\item 
\mbox{\hyperlink{monitoring_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{monitoring_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{monitoring_8php_a3279e125a4b737c6dc2c979becc04b30}{(count( \$data\mbox{[} \textquotesingle{}maintenance\+\_\+due\+\_\+soon\textquotesingle{}\mbox{]}) $>$ 0)}} ( \$data\mbox{[} \textquotesingle{}maintenance\+\_\+due\+\_\+soon\textquotesingle{}\mbox{]} as \$item)
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{monitoring_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}{\+\_\+\+\_\+pad1\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{monitoring_8php_a0d7d9cb40393c201098a44cb3d4afe32}{(count( \$data\mbox{[} \textquotesingle{}compliant\+\_\+endpoints\textquotesingle{}\mbox{]}) $>$ 0)}} ( \$data\mbox{[} \textquotesingle{}compliant\+\_\+endpoints\textquotesingle{}\mbox{]} as \$item)
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{monitoring_8php_aed2d37b4e8da3f52103ae96ce9d26d82}{\+\_\+\+\_\+pad2\+\_\+\+\_\+}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{monitoring_8php_abddda9a408bfb52fd323be438cd3fe94}\index{monitoring.php@{monitoring.php}!\$dueSoonPercent@{\$dueSoonPercent}}
\index{\$dueSoonPercent@{\$dueSoonPercent}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{\$dueSoonPercent}{\$dueSoonPercent}}
{\footnotesize\ttfamily \label{monitoring_8php_abddda9a408bfb52fd323be438cd3fe94} 
\$due\+Soon\+Percent = 0}

\Hypertarget{monitoring_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b}\index{monitoring.php@{monitoring.php}!\$importanceClass@{\$importanceClass}}
\index{\$importanceClass@{\$importanceClass}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{\$importanceClass}{\$importanceClass}}
{\footnotesize\ttfamily \label{monitoring_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b} 
\$importance\+Class = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}}

\Hypertarget{monitoring_8php_ad60a350defbdae74bc48faa36a85c480}\index{monitoring.php@{monitoring.php}!\$notApplicablePercent@{\$notApplicablePercent}}
\index{\$notApplicablePercent@{\$notApplicablePercent}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{\$notApplicablePercent}{\$notApplicablePercent}}
{\footnotesize\ttfamily \label{monitoring_8php_ad60a350defbdae74bc48faa36a85c480} 
\$not\+Applicable\+Percent = 0}

\Hypertarget{monitoring_8php_ac563375536f300b2749efe560e300a49}\index{monitoring.php@{monitoring.php}!\$overduePercent@{\$overduePercent}}
\index{\$overduePercent@{\$overduePercent}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{\$overduePercent}{\$overduePercent}}
{\footnotesize\ttfamily \label{monitoring_8php_ac563375536f300b2749efe560e300a49} 
\$overdue\+Percent = 0}

\Hypertarget{monitoring_8php_aaf8006d608d2a4f1d18029ca0974e3b3}\index{monitoring.php@{monitoring.php}!\$totalCompliance@{\$totalCompliance}}
\index{\$totalCompliance@{\$totalCompliance}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{\$totalCompliance}{\$totalCompliance}}
{\footnotesize\ttfamily \label{monitoring_8php_aaf8006d608d2a4f1d18029ca0974e3b3} 
\$total\+Compliance = \$data\mbox{[}\textquotesingle{}compliant\+\_\+count\textquotesingle{}\mbox{]} + \$data\mbox{[}\textquotesingle{}due\+\_\+soon\+\_\+count\textquotesingle{}\mbox{]} + \$data\mbox{[}\textquotesingle{}overdue\+\_\+count\textquotesingle{}\mbox{]} + \$data\mbox{[}\textquotesingle{}not\+\_\+applicable\+\_\+count\textquotesingle{}\mbox{]}}

\Hypertarget{monitoring_8php_a0d7d9cb40393c201098a44cb3d4afe32}\index{monitoring.php@{monitoring.php}!(count( \$data\mbox{[} \textquotesingle{}compliant\_endpoints\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}compliant\_endpoints\textquotesingle{}]) $>$ 0)}}
\index{(count( \$data\mbox{[} \textquotesingle{}compliant\_endpoints\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}compliant\_endpoints\textquotesingle{}]) $>$ 0)}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{(count( \$data[ \textquotesingle{}compliant\_endpoints\textquotesingle{}]) $>$ 0)}{(count( \$data[ 'compliant\_endpoints']) > 0)}}
{\footnotesize\ttfamily \label{monitoring_8php_a0d7d9cb40393c201098a44cb3d4afe32} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (count(\$data\mbox{[}\textquotesingle{}compliant\+\_\+endpoints\textquotesingle{}\mbox{]}) $>$ 0)(\$data\mbox{[}\textquotesingle{}compliant\+\_\+endpoints\textquotesingle{}\mbox{]} as \$item) (\begin{DoxyParamCaption}\item[{count( \$data\mbox{[} \textquotesingle{}compliant\+\_\+endpoints\textquotesingle{}\mbox{]})}]{}{, }\item[{0}]{}{}\end{DoxyParamCaption})}

\Hypertarget{monitoring_8php_a3279e125a4b737c6dc2c979becc04b30}\index{monitoring.php@{monitoring.php}!(count( \$data\mbox{[} \textquotesingle{}maintenance\_due\_soon\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}maintenance\_due\_soon\textquotesingle{}]) $>$ 0)}}
\index{(count( \$data\mbox{[} \textquotesingle{}maintenance\_due\_soon\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}maintenance\_due\_soon\textquotesingle{}]) $>$ 0)}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{(count( \$data[ \textquotesingle{}maintenance\_due\_soon\textquotesingle{}]) $>$ 0)}{(count( \$data[ 'maintenance\_due\_soon']) > 0)}}
{\footnotesize\ttfamily \label{monitoring_8php_a3279e125a4b737c6dc2c979becc04b30} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (count(\$data\mbox{[}\textquotesingle{}maintenance\+\_\+due\+\_\+soon\textquotesingle{}\mbox{]}) $>$ 0)(\$data\mbox{[}\textquotesingle{}maintenance\+\_\+due\+\_\+soon\textquotesingle{}\mbox{]} as \$item) (\begin{DoxyParamCaption}\item[{count( \$data\mbox{[} \textquotesingle{}maintenance\+\_\+due\+\_\+soon\textquotesingle{}\mbox{]})}]{}{, }\item[{0}]{}{}\end{DoxyParamCaption})}

\Hypertarget{monitoring_8php_ad65be5df1eedfe40f4fcfe99e3200095}\index{monitoring.php@{monitoring.php}!(count( \$data\mbox{[} \textquotesingle{}overdue\_maintenance\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}overdue\_maintenance\textquotesingle{}]) $>$ 0)}}
\index{(count( \$data\mbox{[} \textquotesingle{}overdue\_maintenance\textquotesingle{}\mbox{]}) $>$ 0)@{(count( \$data[ \textquotesingle{}overdue\_maintenance\textquotesingle{}]) $>$ 0)}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{(count( \$data[ \textquotesingle{}overdue\_maintenance\textquotesingle{}]) $>$ 0)}{(count( \$data[ 'overdue\_maintenance']) > 0)}}
{\footnotesize\ttfamily \label{monitoring_8php_ad65be5df1eedfe40f4fcfe99e3200095} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (count(\$data\mbox{[}\textquotesingle{}overdue\+\_\+maintenance\textquotesingle{}\mbox{]}) $>$ 0)(\$data\mbox{[}\textquotesingle{}overdue\+\_\+maintenance\textquotesingle{}\mbox{]} as \$item) (\begin{DoxyParamCaption}\item[{count( \$data\mbox{[} \textquotesingle{}overdue\+\_\+maintenance\textquotesingle{}\mbox{]})}]{}{, }\item[{0}]{}{}\end{DoxyParamCaption})}

\Hypertarget{monitoring_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{monitoring.php@{monitoring.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{monitoring_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{monitoring_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}\index{monitoring.php@{monitoring.php}!\_\_pad1\_\_@{\_\_pad1\_\_}}
\index{\_\_pad1\_\_@{\_\_pad1\_\_}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad1\_\_}{\_\_pad1\_\_}}
{\footnotesize\ttfamily \label{monitoring_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad1\+\_\+\+\_\+}

\Hypertarget{monitoring_8php_aed2d37b4e8da3f52103ae96ce9d26d82}\index{monitoring.php@{monitoring.php}!\_\_pad2\_\_@{\_\_pad2\_\_}}
\index{\_\_pad2\_\_@{\_\_pad2\_\_}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad2\_\_}{\_\_pad2\_\_}}
{\footnotesize\ttfamily \label{monitoring_8php_aed2d37b4e8da3f52103ae96ce9d26d82} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad2\+\_\+\+\_\+}

\Hypertarget{monitoring_8php_a42c52794fac97a1493ac79713f561aea}\index{monitoring.php@{monitoring.php}!else@{else}}
\index{else@{else}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{monitoring_8php_a42c52794fac97a1493ac79713f561aea} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$total\+Compliance $>$ 0) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ }
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \$compliantPercent\ =\ 0}

\end{DoxyCode}
\Hypertarget{monitoring_8php_a672d9707ef91db026c210f98cc601123}\index{monitoring.php@{monitoring.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{monitoring_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{monitoring_8php_a424696754ad463bb909e68f85c19b462}\index{monitoring.php@{monitoring.php}!if@{if}}
\index{if@{if}!monitoring.php@{monitoring.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{monitoring_8php_a424696754ad463bb909e68f85c19b462} 
if( \$item-\/$>$importance==\textquotesingle{}critical\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if(\$item-\/$>$importance==\textquotesingle{}high\textquotesingle{}) \mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} if(\$item-\/$>$importance==\textquotesingle{}low\textquotesingle{}) (\begin{DoxyParamCaption}\item[{}]{\$item-\/$>$}{ = {\ttfamily =~\textquotesingle{}low\textquotesingle{}}}\end{DoxyParamCaption})}

