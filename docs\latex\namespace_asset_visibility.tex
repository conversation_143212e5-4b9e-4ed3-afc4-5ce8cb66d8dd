\doxysection{Asset\+Visibility Namespace Reference}
\hypertarget{namespace_asset_visibility}{}\label{namespace_asset_visibility}\index{AssetVisibility@{AssetVisibility}}


\doxysubsection{Detailed Description}
\doxylink{class_assets}{Assets} \doxylink{class_controller}{Controller}

Handles all asset-\/related operations including CRUD operations, search functionality, import/export, and bulk operations.

\begin{DoxyAuthor}{Author}
\doxylink{class_asset}{Asset} Visibility Development Team 
\end{DoxyAuthor}
\begin{DoxyVersion}{Version}
1.\+0.\+0 
\end{DoxyVersion}
\begin{DoxySince}{Since}
1.\+0.\+0
\end{DoxySince}
\doxylink{class_pages}{Pages} \doxylink{class_controller}{Controller}

Handles static pages and general content pages for the application. This includes the home page, about page, and other informational pages.

\begin{DoxyAuthor}{Author}
\doxylink{class_asset}{Asset} Visibility Development Team 
\end{DoxyAuthor}
\begin{DoxyVersion}{Version}
1.\+0.\+0 
\end{DoxyVersion}
\begin{DoxySince}{Since}
1.\+0.\+0
\end{DoxySince}
\doxylink{class_users}{Users} \doxylink{class_controller}{Controller}

Handles all user-\/related operations including authentication, registration, profile management, and user administration.

\begin{DoxyAuthor}{Author}
\doxylink{class_asset}{Asset} Visibility Development Team 
\end{DoxyAuthor}
\begin{DoxyVersion}{Version}
1.\+0.\+0 
\end{DoxyVersion}
\begin{DoxySince}{Since}
1.\+0.\+0
\end{DoxySince}
Base \doxylink{class_controller}{Controller} Class

This is the base controller class that all other controllers extend. It provides common functionality for loading models and views.

\begin{DoxyAuthor}{Author}
\doxylink{class_asset}{Asset} Visibility Development Team 
\end{DoxyAuthor}
\begin{DoxyVersion}{Version}
1.\+0.\+0 
\end{DoxyVersion}
\begin{DoxySince}{Since}
1.\+0.\+0
\end{DoxySince}
PDO \doxylink{class_database}{Database} Class

Provides a secure database abstraction layer using PDO with prepared statements. Includes features for pagination, parameter binding, and SQL injection protection.

\begin{DoxyAuthor}{Author}
\doxylink{class_asset}{Asset} Visibility Development Team 
\end{DoxyAuthor}
\begin{DoxyVersion}{Version}
1.\+0.\+0 
\end{DoxyVersion}
\begin{DoxySince}{Since}
1.\+0.\+0
\end{DoxySince}
\doxylink{class_security}{Security} Helper Class

Provides comprehensive security functionality including CSRF protection, XSS prevention, input sanitization, and security headers management.

\begin{DoxyAuthor}{Author}
\doxylink{class_asset}{Asset} Visibility Development Team 
\end{DoxyAuthor}
\begin{DoxyVersion}{Version}
1.\+0.\+0 
\end{DoxyVersion}
\begin{DoxySince}{Since}
1.\+0.\+0
\end{DoxySince}
Session Helper Functions

Provides session management, authentication, and permission checking functionality for the \doxylink{class_asset}{Asset} Visibility application.

\begin{DoxyAuthor}{Author}
\doxylink{class_asset}{Asset} Visibility Development Team 
\end{DoxyAuthor}
\begin{DoxyVersion}{Version}
1.\+0.\+0 
\end{DoxyVersion}
\begin{DoxySince}{Since}
1.\+0.\+0
\end{DoxySince}
\doxylink{class_asset}{Asset} Model

Handles all asset-\/related database operations including CRUD operations, search functionality, import/export, and asset history tracking.

\begin{DoxyAuthor}{Author}
\doxylink{class_asset}{Asset} Visibility Development Team 
\end{DoxyAuthor}
\begin{DoxyVersion}{Version}
1.\+0.\+0 
\end{DoxyVersion}
\begin{DoxySince}{Since}
1.\+0.\+0
\end{DoxySince}
\doxylink{class_role}{Role} Model

Handles all role-\/related database operations including CRUD operations, permission management, and user-\/role associations.

\begin{DoxyAuthor}{Author}
\doxylink{class_asset}{Asset} Visibility Development Team 
\end{DoxyAuthor}
\begin{DoxyVersion}{Version}
1.\+0.\+0 
\end{DoxyVersion}
\begin{DoxySince}{Since}
1.\+0.\+0
\end{DoxySince}
\doxylink{class_user}{User} Model

Handles all user-\/related database operations including authentication, registration, role management, and permission checking.

\begin{DoxyAuthor}{Author}
\doxylink{class_asset}{Asset} Visibility Development Team 
\end{DoxyAuthor}
\begin{DoxyVersion}{Version}
1.\+0.\+0 
\end{DoxyVersion}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
