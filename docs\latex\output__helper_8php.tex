\doxysection{app/helpers/output\+\_\+helper.php File Reference}
\hypertarget{output__helper_8php}{}\label{output__helper_8php}\index{app/helpers/output\_helper.php@{app/helpers/output\_helper.php}}
\doxysubsubsection*{Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{output__helper_8php_a18d38faad6177eda235a3d9d28572984}{e}} (\$output)
\item 
\mbox{\hyperlink{output__helper_8php_a8e7a9215f6f4f47e8dda9cba0ca31664}{safe\+\_\+echo}} (\$output)
\item 
\mbox{\hyperlink{output__helper_8php_a1ea7ef3a26ba61cde214d4a03d9ecc37}{safe\+\_\+echo\+\_\+nl2br}} (\$output)
\item 
\mbox{\hyperlink{output__helper_8php_a6c900ca36f5aa693d1463711751111b7}{safe\+\_\+echo\+\_\+raw}} (\$output)
\end{DoxyCompactItemize}


\doxysubsection{Function Documentation}
\Hypertarget{output__helper_8php_a18d38faad6177eda235a3d9d28572984}\index{output\_helper.php@{output\_helper.php}!e@{e}}
\index{e@{e}!output\_helper.php@{output\_helper.php}}
\doxysubsubsection{\texorpdfstring{e()}{e()}}
{\footnotesize\ttfamily \label{output__helper_8php_a18d38faad6177eda235a3d9d28572984} 
e (\begin{DoxyParamCaption}\item[{}]{\$output}{}\end{DoxyParamCaption})}

Output Helper Contains functions for escaping output to prevent XSS attacks Escape HTML output to prevent XSS attacks


\begin{DoxyParams}[1]{Parameters}
string & {\em \$output} & The string to escape \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
string The escaped string 
\end{DoxyReturn}
\Hypertarget{output__helper_8php_a8e7a9215f6f4f47e8dda9cba0ca31664}\index{output\_helper.php@{output\_helper.php}!safe\_echo@{safe\_echo}}
\index{safe\_echo@{safe\_echo}!output\_helper.php@{output\_helper.php}}
\doxysubsubsection{\texorpdfstring{safe\_echo()}{safe\_echo()}}
{\footnotesize\ttfamily \label{output__helper_8php_a8e7a9215f6f4f47e8dda9cba0ca31664} 
safe\+\_\+echo (\begin{DoxyParamCaption}\item[{}]{\$output}{}\end{DoxyParamCaption})}

Safely output HTML content


\begin{DoxyParams}[1]{Parameters}
string & {\em \$output} & The string to output \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
void 
\end{DoxyReturn}
\Hypertarget{output__helper_8php_a1ea7ef3a26ba61cde214d4a03d9ecc37}\index{output\_helper.php@{output\_helper.php}!safe\_echo\_nl2br@{safe\_echo\_nl2br}}
\index{safe\_echo\_nl2br@{safe\_echo\_nl2br}!output\_helper.php@{output\_helper.php}}
\doxysubsubsection{\texorpdfstring{safe\_echo\_nl2br()}{safe\_echo\_nl2br()}}
{\footnotesize\ttfamily \label{output__helper_8php_a1ea7ef3a26ba61cde214d4a03d9ecc37} 
safe\+\_\+echo\+\_\+nl2br (\begin{DoxyParamCaption}\item[{}]{\$output}{}\end{DoxyParamCaption})}

Safely output HTML content with line breaks


\begin{DoxyParams}[1]{Parameters}
string & {\em \$output} & The string to output \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
void 
\end{DoxyReturn}
\Hypertarget{output__helper_8php_a6c900ca36f5aa693d1463711751111b7}\index{output\_helper.php@{output\_helper.php}!safe\_echo\_raw@{safe\_echo\_raw}}
\index{safe\_echo\_raw@{safe\_echo\_raw}!output\_helper.php@{output\_helper.php}}
\doxysubsubsection{\texorpdfstring{safe\_echo\_raw()}{safe\_echo\_raw()}}
{\footnotesize\ttfamily \label{output__helper_8php_a6c900ca36f5aa693d1463711751111b7} 
safe\+\_\+echo\+\_\+raw (\begin{DoxyParamCaption}\item[{}]{\$output}{}\end{DoxyParamCaption})}

Safely output HTML content with no escaping (for trusted HTML) Use with extreme caution -\/ only for content that is guaranteed to be safe


\begin{DoxyParams}[1]{Parameters}
string & {\em \$output} & The string to output \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
void 
\end{DoxyReturn}
