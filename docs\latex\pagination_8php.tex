\doxysection{app/views/inc/pagination.php File Reference}
\hypertarget{pagination_8php}{}\label{pagination_8php}\index{app/views/inc/pagination.php@{app/views/inc/pagination.php}}
\doxysubsubsection*{Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{pagination_8php_ac7739cd67fc2b6f9404e19dffbd2432b}{pagination\+Url}} (\$page, \$query\+Params)
\end{DoxyCompactItemize}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(isset(\$data\mbox{[}\textquotesingle{}pagination\textquotesingle{}\mbox{]}) \&\& \$data\mbox{[}\textquotesingle{}pagination\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}last\+Page\textquotesingle{}\mbox{]} $>$ 1) \mbox{\hyperlink{pagination_8php_a6b131a0bac3dbe16421d586922963846}{\$current\+Page}} = \$pagination\mbox{[}\textquotesingle{}page\textquotesingle{}\mbox{]}
\item 
\mbox{\hyperlink{pagination_8php_af8dea370eb48bd0145447252ffd04e4f}{\$last\+Page}} = \$pagination\mbox{[}\textquotesingle{}last\+Page\textquotesingle{}\mbox{]}
\item 
\mbox{\hyperlink{pagination_8php_a9f947559eb28e03070173d49dafc44a1}{\$query\+Params}} = \$\+\_\+\+GET
\item 
\mbox{\hyperlink{pagination_8php_a7141618604c4edd13033c36a8c97bb60}{\$start\+Page}} = max(1, \$current\+Page -\/ 2)
\item 
\mbox{\hyperlink{pagination_8php_ae12f8ad07b55a483cd345c1261d56a92}{\$end\+Page}} = min(\$last\+Page, \$start\+Page + 4)
\item 
if(\$end\+Page -\/ \$start\+Page$<$ 4 \&\& \$start\+Page $>$ 1) if( \$current\+Page $>$ 1 \mbox{\hyperlink{pagination_8php_aea4423f2a1453df3beda2db93281783e}{if}} )( \$current\+Page$<$ \$last\+Page)
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$current\+Page $>$ 1) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$start\+Page $>$ 1)(\$start\+Page $>$ 2) \mbox{\hyperlink{pagination_8php_a76322bf3984bf5542a977dfd27d2a2f9}{endif}}
\item 
for(\$i=\$start\+Page; \$i$<$=\$end\+Page; \$i++)(\$i==\$current\+Page) \mbox{\hyperlink{pagination_8php_aab654bd74b463b61f008b1077ce27b3c}{else}}
\item 
\mbox{\hyperlink{pagination_8php_ae8fdc27183f296411bac00ed522ee1ac}{endfor}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{pagination_8php_a83bf805250f2ee633dd2ce7c36af3412}{( \$end\+Page$<$ \$last\+Page)}} ( \$end\+Page$<$ \$last\+Page -\/ 1)
\end{DoxyCompactItemize}


\doxysubsection{Function Documentation}
\Hypertarget{pagination_8php_ac7739cd67fc2b6f9404e19dffbd2432b}\index{pagination.php@{pagination.php}!paginationUrl@{paginationUrl}}
\index{paginationUrl@{paginationUrl}!pagination.php@{pagination.php}}
\doxysubsubsection{\texorpdfstring{paginationUrl()}{paginationUrl()}}
{\footnotesize\ttfamily \label{pagination_8php_ac7739cd67fc2b6f9404e19dffbd2432b} 
pagination\+Url (\begin{DoxyParamCaption}\item[{}]{\$page}{, }\item[{}]{\$query\+Params}{}\end{DoxyParamCaption})}



\doxysubsection{Variable Documentation}
\Hypertarget{pagination_8php_a6b131a0bac3dbe16421d586922963846}\index{pagination.php@{pagination.php}!\$currentPage@{\$currentPage}}
\index{\$currentPage@{\$currentPage}!pagination.php@{pagination.php}}
\doxysubsubsection{\texorpdfstring{\$currentPage}{\$currentPage}}
{\footnotesize\ttfamily \label{pagination_8php_a6b131a0bac3dbe16421d586922963846} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (isset( \$data\mbox{[} \textquotesingle{}pagination\textquotesingle{}\mbox{]}) \&\&\$data\mbox{[} \textquotesingle{}pagination\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}last\+Page\textquotesingle{}\mbox{]} $>$ 1) \$current\+Page = \$pagination\mbox{[}\textquotesingle{}page\textquotesingle{}\mbox{]}}

Pagination Component

Displays pagination controls for tables

Required parameters in \$data\+:
\begin{DoxyItemize}
\item pagination\+: array containing\+:
\begin{DoxyItemize}
\item total\+: total number of items
\item page\+: current page number
\item per\+Page\+: number of items per page
\item last\+Page\+: last page number 
\end{DoxyItemize}
\end{DoxyItemize}\Hypertarget{pagination_8php_ae12f8ad07b55a483cd345c1261d56a92}\index{pagination.php@{pagination.php}!\$endPage@{\$endPage}}
\index{\$endPage@{\$endPage}!pagination.php@{pagination.php}}
\doxysubsubsection{\texorpdfstring{\$endPage}{\$endPage}}
{\footnotesize\ttfamily \label{pagination_8php_ae12f8ad07b55a483cd345c1261d56a92} 
\$end\+Page = min(\$last\+Page, \$start\+Page + 4)}

\Hypertarget{pagination_8php_af8dea370eb48bd0145447252ffd04e4f}\index{pagination.php@{pagination.php}!\$lastPage@{\$lastPage}}
\index{\$lastPage@{\$lastPage}!pagination.php@{pagination.php}}
\doxysubsubsection{\texorpdfstring{\$lastPage}{\$lastPage}}
{\footnotesize\ttfamily \label{pagination_8php_af8dea370eb48bd0145447252ffd04e4f} 
\$last\+Page = \$pagination\mbox{[}\textquotesingle{}last\+Page\textquotesingle{}\mbox{]}}

\Hypertarget{pagination_8php_a9f947559eb28e03070173d49dafc44a1}\index{pagination.php@{pagination.php}!\$queryParams@{\$queryParams}}
\index{\$queryParams@{\$queryParams}!pagination.php@{pagination.php}}
\doxysubsubsection{\texorpdfstring{\$queryParams}{\$queryParams}}
{\footnotesize\ttfamily \label{pagination_8php_a9f947559eb28e03070173d49dafc44a1} 
for ( \$i=\$start\+Page;\$i$<$=\$end\+Page;\$i++) ( \$i==\$current\+Page \$query\+Params = \$\+\_\+\+GET}

\Hypertarget{pagination_8php_a7141618604c4edd13033c36a8c97bb60}\index{pagination.php@{pagination.php}!\$startPage@{\$startPage}}
\index{\$startPage@{\$startPage}!pagination.php@{pagination.php}}
\doxysubsubsection{\texorpdfstring{\$startPage}{\$startPage}}
{\footnotesize\ttfamily \label{pagination_8php_a7141618604c4edd13033c36a8c97bb60} 
\$start\+Page = max(1, \$current\+Page -\/ 2)}

\Hypertarget{pagination_8php_a83bf805250f2ee633dd2ce7c36af3412}\index{pagination.php@{pagination.php}!( \$endPage$<$ \$lastPage)@{( \$endPage$<$ \$lastPage)}}
\index{( \$endPage$<$ \$lastPage)@{( \$endPage$<$ \$lastPage)}!pagination.php@{pagination.php}}
\doxysubsubsection{\texorpdfstring{( \$endPage$<$ \$lastPage)}{( \$endPage< \$lastPage)}}
{\footnotesize\ttfamily \label{pagination_8php_a83bf805250f2ee633dd2ce7c36af3412} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (\$end\+Page$<$ \$last\+Page)(\$end\+Page$<$ \$last\+Page -\/ 1) (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\Hypertarget{pagination_8php_aab654bd74b463b61f008b1077ce27b3c}\index{pagination.php@{pagination.php}!else@{else}}
\index{else@{else}!pagination.php@{pagination.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{pagination_8php_aab654bd74b463b61f008b1077ce27b3c} 
for ( \$i=\$start\+Page;\$i$<$=\$end\+Page;\$i++) ( \$i==\$current\+Page) else}

\Hypertarget{pagination_8php_ae8fdc27183f296411bac00ed522ee1ac}\index{pagination.php@{pagination.php}!endfor@{endfor}}
\index{endfor@{endfor}!pagination.php@{pagination.php}}
\doxysubsubsection{\texorpdfstring{endfor}{endfor}}
{\footnotesize\ttfamily \label{pagination_8php_ae8fdc27183f296411bac00ed522ee1ac} 
endfor}

\Hypertarget{pagination_8php_a76322bf3984bf5542a977dfd27d2a2f9}\index{pagination.php@{pagination.php}!endif@{endif}}
\index{endif@{endif}!pagination.php@{pagination.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{pagination_8php_a76322bf3984bf5542a977dfd27d2a2f9} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$current\+Page$<$ \$last\+Page) endif}

\Hypertarget{pagination_8php_aea4423f2a1453df3beda2db93281783e}\index{pagination.php@{pagination.php}!if@{if}}
\index{if@{if}!pagination.php@{pagination.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{pagination_8php_aea4423f2a1453df3beda2db93281783e} 
if( \$end\+Page -\/ \$start\+Page$<$ 4 \&\&\$start\+Page $>$ 1) if(\$current\+Page $>$ 1 if) (\$current\+Page$<$ \$last\+Page) (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

