\doxysection{app/views/maintenance/recreate\+\_\+records.php File Reference}
\hypertarget{recreate__records_8php}{}\label{recreate__records_8php}\index{app/views/maintenance/recreate\_records.php@{app/views/maintenance/recreate\_records.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$data\mbox{[}\textquotesingle{}missing\+\_\+count\textquotesingle{}\mbox{]} $>$ 0) \mbox{\hyperlink{recreate__records_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\item 
\mbox{\hyperlink{recreate__records_8php_a23e60fa7b320ff2d653449f371869e71}{foreach}} ( \$data\mbox{[} \textquotesingle{}missing\+\_\+records\textquotesingle{}\mbox{]} as \$record)
\item 
\mbox{\hyperlink{recreate__records_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{recreate__records_8php_ac7dbeff683c582b94d5af5b4b496c11c}{foreach}} ( \$data\mbox{[} \textquotesingle{}recreated\+\_\+records\textquotesingle{}\mbox{]} as \$record)
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{recreate__records_8php_a672d9707ef91db026c210f98cc601123}\index{recreate\_records.php@{recreate\_records.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!recreate\_records.php@{recreate\_records.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{recreate__records_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{recreate__records_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{recreate\_records.php@{recreate\_records.php}!endif@{endif}}
\index{endif@{endif}!recreate\_records.php@{recreate\_records.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{recreate__records_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

\Hypertarget{recreate__records_8php_a23e60fa7b320ff2d653449f371869e71}\index{recreate\_records.php@{recreate\_records.php}!foreach@{foreach}}
\index{foreach@{foreach}!recreate\_records.php@{recreate\_records.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily \label{recreate__records_8php_a23e60fa7b320ff2d653449f371869e71} 
foreach(\$data\mbox{[}\textquotesingle{}missing\+\_\+records\textquotesingle{}\mbox{]} as \$record) (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}missing\+\_\+records\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{recreate__records_8php_ac7dbeff683c582b94d5af5b4b496c11c}\index{recreate\_records.php@{recreate\_records.php}!foreach@{foreach}}
\index{foreach@{foreach}!recreate\_records.php@{recreate\_records.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily \label{recreate__records_8php_ac7dbeff683c582b94d5af5b4b496c11c} 
foreach(\$data\mbox{[}\textquotesingle{}recreated\+\_\+records\textquotesingle{}\mbox{]} as \$record) (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}recreated\+\_\+records\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

