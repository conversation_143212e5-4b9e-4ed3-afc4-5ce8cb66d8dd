  % Handle batch mode
  % to overcome problems with too many open files
  \let\mypdfximage\pdfximage\def\pdfximage{\immediate\mypdfximage}
  \RequirePackage{iftex}
  \ifLuaTeX
    \directlua{pdf.setminorversion(7)}
  \fi
  \ifXeTeX
    \special{pdf:minorversion 7}
  \fi
  \ifPDFTeX
    \pdfminorversion=7
  \fi
  % Set document class depending on configuration
  \documentclass[twoside]{book}
  %% moved from doxygen.sty due to workaround for LaTex 2019 version and unmaintained tabu package
  \usepackage{ifthen}
  \ifx\requestedLaTeXdate\undefined
    \usepackage{array}
  \else
    \usepackage{array}[=2016-10-06]
  \fi
  %%
  % Packages required by doxygen
  \makeatletter
  \providecommand\IfFormatAtLeastTF{\@ifl@t@r\fmtversion}
  % suppress package identification of infwarerr as it contains the word "warning"
  \let\@@protected@wlog\protected@wlog
  \def\protected@wlog#1{\wlog{package info suppressed}}
  \RequirePackage{infwarerr}
  \let\protected@wlog\@@protected@wlog
  \makeatother
  \IfFormatAtLeastTF{2016/01/01}{}{\usepackage{fixltx2e}} % for \textsubscript
  \ifPDFTeX
    \IfFormatAtLeastTF{2015/01/01}{\pdfsuppresswarningpagegroup=1}{}
  \fi
  \usepackage{doxygen}
  \usepackage{graphicx}
  \iftutex
    \usepackage{fontspec}
    \defaultfontfeatures{Ligatures={TeX}}
    \usepackage{unicode-math}
  \else
    \usepackage[utf8]{inputenc}
  \fi
  \usepackage{makeidx}
  \PassOptionsToPackage{warn}{textcomp}
  \usepackage{textcomp}
  \usepackage[nointegrals]{wasysym}
  \usepackage{ifxetex}
  % NLS support packages
  % Define default fonts
  % Font selection
  \iftutex
  \else
    \usepackage[T1]{fontenc}
  \fi
  % set main and monospaced font
  \usepackage[scaled=.90]{helvet}
\usepackage{courier}
\renewcommand{\familydefault}{\sfdefault}
  \doxyallsectionsfont{%
    \fontseries{bc}\selectfont%
    \color{darkgray}%
  }
  \renewcommand{\DoxyLabelFont}{%
    \fontseries{bc}\selectfont%
    \color{darkgray}%
  }
  \newcommand{\+}{\discretionary{\mbox{\scriptsize$\hookleftarrow$}}{}{}}
   % Arguments of doxygenemoji:
   % 1) ':<text>:' form of the emoji, already LaTeX-escaped
   % 2) file with the name of the emoji without the .png extension
   % in case image exist use this otherwise use the ':<text>:' form
   \newcommand{\doxygenemoji}[2]{%
     \IfFileExists{./#2.png}{\raisebox{-0.1em}{\includegraphics[height=0.9em]{./#2.png}}}{#1}%
   }
  % Page & text layout
  \usepackage{geometry}
  \geometry{%
    a4paper,%
    top=2.5cm,%
    bottom=2.5cm,%
    left=2.5cm,%
    right=2.5cm%
  }
  \usepackage{changepage}
  % Allow a bit of overflow to go unnoticed by other means
  \tolerance=750
  \hfuzz=15pt
  \hbadness=750
  \setlength{\emergencystretch}{15pt}
  \setlength{\parindent}{0cm}
  \newcommand{\doxynormalparskip}{\setlength{\parskip}{3ex plus 2ex minus 2ex}}
  \newcommand{\doxytocparskip}{\setlength{\parskip}{1ex plus 0ex minus 0ex}}
  \doxynormalparskip
  % Redefine paragraph/subparagraph environments, using sectsty fonts
  \makeatletter
  \renewcommand{\paragraph}{%
    \@startsection{paragraph}{4}{0ex}{-1.0ex}{1.0ex}{%
      \normalfont\normalsize\bfseries\SS@parafont%
    }%
  }
  \renewcommand{\subparagraph}{%
    \@startsection{subparagraph}{5}{0ex}{-1.0ex}{1.0ex}{%
      \normalfont\normalsize\bfseries\SS@subparafont%
    }%
  }
  \makeatother
  \makeatletter
  \newcommand\hrulefilll{\leavevmode\leaders\hrule\hskip 0pt plus 1filll\kern\z@}
  \makeatother
  % Headers & footers
  \usepackage{fancyhdr}
  \pagestyle{fancyplain}
  \renewcommand{\footrulewidth}{0.4pt}
  \fancypagestyle{fancyplain}{
    \fancyhf{}
    \fancyhead[LE, RO]{\bfseries\thepage}
    \fancyhead[LO]{\bfseries\rightmark}
    \fancyhead[RE]{\bfseries\leftmark}
    \fancyfoot[LO, RE]{\bfseries\scriptsize Generated by Doxygen }
  }
  \fancypagestyle{plain}{
    \fancyhf{}
    \fancyfoot[LO, RE]{\bfseries\scriptsize Generated by Doxygen }
    \renewcommand{\headrulewidth}{0pt}
  }
  \pagestyle{fancyplain}
  \renewcommand{\chaptermark}[1]{%
    \markboth{#1}{}%
  }
  \renewcommand{\sectionmark}[1]{%
    \markright{\thesection\ #1}%
  }
  % ToC, LoF, LoT, bibliography, and index
  % Indices & bibliography
  \usepackage[numbers]{natbib}
  \usepackage[titles]{tocloft}
  \setcounter{tocdepth}{3}
  \setcounter{secnumdepth}{5}
  % creating indexes
  \makeindex
  \ifPDFTeX
\usepackage{newunicodechar}
  \makeatletter
    \def\doxynewunicodechar#1#2{%
    \@tempswafalse
    \edef\nuc@tempa{\detokenize{#1}}%
    \if\relax\nuc@tempa\relax
      \nuc@emptyargerr
    \else
      \edef\@tempb{\expandafter\@car\nuc@tempa\@nil}%
      \nuc@check
      \if@tempswa
        \@namedef{u8:\nuc@tempa}{#2}%
      \fi
    \fi
  }
  \makeatother
  \doxynewunicodechar{⁻}{${}^{-}$}% Superscript minus
  \doxynewunicodechar{²}{${}^{2}$}% Superscript two
  \doxynewunicodechar{³}{${}^{3}$}% Superscript three
\fi
  % Hyperlinks
    % Hyperlinks (required, but should be loaded last)
    \ifPDFTeX
      \usepackage[pdftex,pagebackref=true]{hyperref}
    \else
      \ifXeTeX
        \usepackage[xetex,pagebackref=true]{hyperref}
      \else
        \ifLuaTeX
          \usepackage[luatex,pagebackref=true]{hyperref}
        \else
          \usepackage[ps2pdf,pagebackref=true]{hyperref}
        \fi
      \fi
    \fi
    \hypersetup{%
      colorlinks=true,%
      linkcolor=blue,%
      citecolor=blue,%
      unicode,%
      pdftitle={EVIS},%
      pdfsubject={}%
    }
  % Custom commands used by the header
  % Custom commands
  \newcommand{\clearemptydoublepage}{%
    \newpage{\pagestyle{empty}\cleardoublepage}%
  }
  % caption style definition
  \usepackage{caption}
  \captionsetup{labelsep=space,justification=centering,font={bf},singlelinecheck=off,skip=4pt,position=top}
  % in page table of contents
  \IfFormatAtLeastTF{2023/05/01}{\usepackage[deeplevels]{etoc}}{\usepackage[deeplevels]{etoc_doxygen}}
  \etocsettocstyle{\doxytocparskip}{\doxynormalparskip}
  \etocsetlevel{subsubsubsection}{4}
  \etocsetlevel{subsubsubsubsection}{5}
  \etocsetlevel{subsubsubsubsubsection}{6}
  \etocsetlevel{subsubsubsubsubsubsection}{7}
  \etocsetlevel{paragraph}{8}
  \etocsetlevel{subparagraph}{9}
  % prevent numbers overlap the titles in toc
  \renewcommand{\numberline}[1]{#1~}
% End of preamble, now comes the document contents
%===== C O N T E N T S =====
\begin{document}
  \raggedbottom
  % Titlepage & ToC
    % To avoid duplicate page anchors due to reuse of same numbers for
    % the index (be it as roman numbers)
    \hypersetup{pageanchor=false,
                bookmarksnumbered=true,
                pdfencoding=unicode
               }
  \pagenumbering{alph}
  \begin{titlepage}
  \vspace*{7cm}
  \begin{center}%
  {\Large EVIS}\\
  [1ex]\large 1.\+0 \\
  \vspace*{1cm}
  {\large Generated by Doxygen 1.13.2}\\
  \end{center}
  \end{titlepage}
  \clearemptydoublepage
  \pagenumbering{roman}
  \tableofcontents
  \clearemptydoublepage
  \pagenumbering{arabic}
  % re-enable anchors again
  \hypersetup{pageanchor=true}
%--- Begin generated contents ---
\input{md_nginx_2_i_n_s_t_a_l_l_a_t_i_o_n}
\input{md__r_e_a_d_m_e}
\chapter{Namespace Index}
\input{namespaces}
\chapter{Hierarchical Index}
\input{hierarchy}
\chapter{Data Structure Index}
\input{annotated}
\chapter{File Index}
\input{files}
\chapter{Namespace Documentation}
\input{namespace_asset_visibility}
\chapter{Data Structure Documentation}
\input{class_asset}
\input{class_asset_history}
\input{class_assets}
\input{class_compliance}
\input{class_compliance_model}
\input{class_controller}
\input{class_core}
\input{class_dashboard}
\input{class_database}
\input{class_error_log}
\input{class_error_logs}
\input{class_finance}
\input{class_finance_model}
\input{class_maintenance}
\input{class_maintenance_compliance}
\input{class_maintenance_guideline}
\input{class_maintenance_model}
\input{class_pages}
\input{class_permission}
\input{class_permissions}
\input{class_role}
\input{class_roles}
\input{class_security}
\input{class_security_enhancements}
\input{class_tag}
\input{class_tags}
\input{class_user}
\input{class_users}
\chapter{File Documentation}
\input{bootstrap_8php}
\input{config_8php}
\input{_assets_8php}
\input{_compliance_8php}
\input{_dashboard_8php}
\input{_error_logs_8php}
\input{_finance_8php}
\input{_maintenance_8php}
\input{_pages_8php}
\input{controllers_2_permissions_8php}
\input{views_2roles_2_permissions_8php}
\input{controllers_2_roles_8php}
\input{views_2users_2_roles_8php}
\input{_tags_8php}
\input{_users_8php}
\input{_controller_8php}
\input{_core_8php}
\input{_database_8php}
\input{email__helper_8php}
\input{error__handler_8php}
\input{output__helper_8php}
\input{_security_8php}
\input{_security_enhancements_8php}
\input{session__helper_8php}
\input{models_2_asset_8php}
\input{views_2compliance_2_asset_8php}
\input{_asset_history_8php}
\input{_compliance_model_8php}
\input{_error_log_8php}
\input{_finance_model_8php}
\input{_maintenance_compliance_8php}
\input{_maintenance_guideline_8php}
\input{_maintenance_model_8php}
\input{_permission_8php}
\input{_role_8php}
\input{_tag_8php}
\input{_user_8php}
\input{create__guideline__implementation__table_8php}
\input{assets_2add_8php}
\input{maintenance_2add_8php}
\input{permissions_2add_8php}
\input{roles_2add_8php}
\input{tags_2add_8php}
\input{assets_2edit_8php}
\input{permissions_2edit_8php}
\input{roles_2edit_8php}
\input{tags_2edit_8php}
\input{import_8php}
\input{import__results_8php}
\input{app_2views_2assets_2index_8php}
\input{app_2views_2compliance_2index_8php}
\input{app_2views_2dashboard_2index_8php}
\input{app_2views_2error__logs_2index_8php}
\input{app_2views_2finance_2index_8php}
\input{app_2views_2maintenance_2index_8php}
\input{app_2views_2pages_2index_8php}
\input{app_2views_2permissions_2index_8php}
\input{app_2views_2roles_2index_8php}
\input{app_2views_2tags_2index_8php}
\input{public_2index_8php}
\input{search_8php}
\input{assets_2show_8php}
\input{permissions_2show_8php}
\input{roles_2show_8php}
\input{test__upload_8php}
\input{report_8php}
\input{update_8php}
\input{view_8php}
\input{footer_8php}
\input{header_8php}
\input{navbar_8php}
\input{pagination_8php}
\input{all__history_8php}
\input{asset__compliance_8php}
\input{data__integrity_8php}
\input{edit__checklist__item_8php}
\input{edit__guideline_8php}
\input{guideline_8php}
\input{guideline__implementations_8php}
\input{guidelines_8php}
\input{history_8php}
\input{manage__checklist_8php}
\input{monitoring_8php}
\input{recreate__records_8php}
\input{view__record_8php}
\input{about_8php}
\input{error_8php}
\input{forgot__password_8php}
\input{login_8php}
\input{manage_8php}
\input{profile_8php}
\input{register_8php}
\input{reset__password_8php}
\input{reset__password__form_8php}
\input{check__db_8php}
\input{public_2check__db_8php}
\input{check__implementation__table_8php}
\input{check__maintenance__table_8php}
\input{check__table_8php}
\input{fix__guideline__implementation__table_8php}
\input{fix__maintenance__table_8php}
\input{import__csv_8php}
\input{login__fix_8php}
\input{_i_n_s_t_a_l_l_a_t_i_o_n_8md}
\input{check__assets_8php}
\input{_r_e_a_d_m_e_8md}
\input{test__db__connection_8php}
\input{test__file__upload_8php}
%--- End generated contents ---
% Index
  \backmatter
  \newpage
  \phantomsection
  \clearemptydoublepage
  \addcontentsline{toc}{chapter}{\indexname}
  \printindex
% Required for some languages (in combination with latexdocumentpre from the header)
\end{document}
