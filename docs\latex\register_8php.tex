\doxysection{app/views/users/register.php File Reference}
\hypertarget{register_8php}{}\label{register_8php}\index{app/views/users/register.php@{app/views/users/register.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty( \$data\mbox{[} \textquotesingle{}name\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}name\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{register_8php_a64410dead16b2816b2f31c3df96935c3}{endif}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty( \$data\mbox{[} \textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{register_8php_a7e77afec1fc0f87305414d77bdc8cbad}{else}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty( \$data\mbox{[} \textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]}) \mbox{\hyperlink{register_8php_a5227b73106de5719f8e6830383ace299}{lowercase}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty( \$data\mbox{[} \textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]} \mbox{\hyperlink{register_8php_aa0ecf0eda943d7316f4a313104e2d008}{number}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{register_8php_a7e77afec1fc0f87305414d77bdc8cbad}\index{register.php@{register.php}!else@{else}}
\index{else@{else}!register.php@{register.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{register_8php_a7e77afec1fc0f87305414d77bdc8cbad} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]})) else}

\Hypertarget{register_8php_a64410dead16b2816b2f31c3df96935c3}\index{register.php@{register.php}!endif@{endif}}
\index{endif@{endif}!register.php@{register.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{register_8php_a64410dead16b2816b2f31c3df96935c3} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}confirm\+\_\+password\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}confirm\+\_\+password\+\_\+err\textquotesingle{}\mbox{]})) endif}

\Hypertarget{register_8php_a5227b73106de5719f8e6830383ace299}\index{register.php@{register.php}!lowercase@{lowercase}}
\index{lowercase@{lowercase}!register.php@{register.php}}
\doxysubsubsection{\texorpdfstring{lowercase}{lowercase}}
{\footnotesize\ttfamily \label{register_8php_a5227b73106de5719f8e6830383ace299} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]}) lowercase}

\Hypertarget{register_8php_aa0ecf0eda943d7316f4a313104e2d008}\index{register.php@{register.php}!number@{number}}
\index{number@{number}!register.php@{register.php}}
\doxysubsubsection{\texorpdfstring{number}{number}}
{\footnotesize\ttfamily \label{register_8php_aa0ecf0eda943d7316f4a313104e2d008} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$data\mbox{[}\textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]})) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}password\+\_\+err\textquotesingle{}\mbox{]} number}

