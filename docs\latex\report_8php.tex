\doxysection{app/views/compliance/report.php File Reference}
\hypertarget{report_8php}{}\label{report_8php}\index{app/views/compliance/report.php@{app/views/compliance/report.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{report_8php_ad25bb0d600debde6675a0e92c0333146}{\$overall\+Compliance}} = \$data\mbox{[}\textquotesingle{}report\textquotesingle{}\mbox{]}-\/$>$report\+\_\+data-\/$>$overall\+\_\+compliance
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$overall\+Compliance $>$=90) \mbox{\hyperlink{app_2views_2compliance_2index_8php_a2fe92cbc651f6f645279ed84f54274dd}{elseif}}(\$overall\+Compliance $>$=70) \mbox{\hyperlink{report_8php_a284a2c36de7e8c8d980bc3f704ee0523}{else}}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} ( \$data\mbox{[} \textquotesingle{}report\textquotesingle{}\mbox{]}-\/$>$report\+\_\+data-\/$>$asset\+\_\+summary as \$asset) = \$forecast\mbox{[}\textquotesingle{}projected\+\_\+amount\textquotesingle{}\mbox{]}
\item 
\mbox{\hyperlink{report_8php_a73dbf60765f3b9036cf082caad668726}{\$total\+Controls}} = \$asset-\/$>$total\+\_\+controls
\item 
\mbox{\hyperlink{report_8php_ada4b4b6268421c9e025f977cae774b92}{\$applicable\+Controls}} = \$total\+Controls -\/ \$asset-\/$>$not\+\_\+applicable\+\_\+count
\item 
\mbox{\hyperlink{report_8php_a2d4ce20a31e47694d0105327b8502fe6}{\$compliance\+Percentage}} = \$applicable\+Controls $>$ 0 ? (\$asset-\/$>$compliant\+\_\+count / \$applicable\+Controls) \texorpdfstring{$\ast$}{*} 100 \+: 0
\item 
\mbox{\hyperlink{report_8php_ab99343d4912650d1363355564aa6c179}{\$text\+Color}} = \textquotesingle{}text-\/red-\/500\textquotesingle{}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}( \$compliance\+Percentage $>$=90) \mbox{\hyperlink{report_8php_aef76637aa1522ed86c0454bd6c1a50fd}{elseif}} ( \$compliance\+Percentage $>$=70)
\item 
\mbox{\hyperlink{report_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{report_8php_a426e5ef5de550913c16700467762c1e9}{foreach}} ( \$data\mbox{[} \textquotesingle{}report\textquotesingle{}\mbox{]}-\/$>$report\+\_\+data-\/$>$control\+\_\+summary as \$control)
\item 
\mbox{\hyperlink{report_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6}{\$total\+Assets}} = \$control-\/$>$total\+\_\+assets
\item 
\mbox{\hyperlink{report_8php_a3707071a7a6abab5047d40ecf2edab61}{\$applicable\+Assets}} = \$total\+Assets -\/ \$control-\/$>$not\+\_\+applicable\+\_\+count
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{report_8php_a3707071a7a6abab5047d40ecf2edab61}\index{report.php@{report.php}!\$applicableAssets@{\$applicableAssets}}
\index{\$applicableAssets@{\$applicableAssets}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{\$applicableAssets}{\$applicableAssets}}
{\footnotesize\ttfamily \label{report_8php_a3707071a7a6abab5047d40ecf2edab61} 
\$applicable\+Assets = \$total\+Assets -\/ \$control-\/$>$not\+\_\+applicable\+\_\+count}

\Hypertarget{report_8php_ada4b4b6268421c9e025f977cae774b92}\index{report.php@{report.php}!\$applicableControls@{\$applicableControls}}
\index{\$applicableControls@{\$applicableControls}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{\$applicableControls}{\$applicableControls}}
{\footnotesize\ttfamily \label{report_8php_ada4b4b6268421c9e025f977cae774b92} 
\$applicable\+Controls = \$total\+Controls -\/ \$asset-\/$>$not\+\_\+applicable\+\_\+count}

\Hypertarget{report_8php_a2d4ce20a31e47694d0105327b8502fe6}\index{report.php@{report.php}!\$compliancePercentage@{\$compliancePercentage}}
\index{\$compliancePercentage@{\$compliancePercentage}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{\$compliancePercentage}{\$compliancePercentage}}
{\footnotesize\ttfamily \label{report_8php_a2d4ce20a31e47694d0105327b8502fe6} 
\$compliance\+Percentage = \$applicable\+Controls $>$ 0 ? (\$asset-\/$>$compliant\+\_\+count / \$applicable\+Controls) \texorpdfstring{$\ast$}{*} 100 \+: 0}

\Hypertarget{report_8php_ad25bb0d600debde6675a0e92c0333146}\index{report.php@{report.php}!\$overallCompliance@{\$overallCompliance}}
\index{\$overallCompliance@{\$overallCompliance}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{\$overallCompliance}{\$overallCompliance}}
{\footnotesize\ttfamily \label{report_8php_ad25bb0d600debde6675a0e92c0333146} 
\$overall\+Compliance = \$data\mbox{[}\textquotesingle{}report\textquotesingle{}\mbox{]}-\/$>$report\+\_\+data-\/$>$overall\+\_\+compliance}

\Hypertarget{report_8php_ab99343d4912650d1363355564aa6c179}\index{report.php@{report.php}!\$textColor@{\$textColor}}
\index{\$textColor@{\$textColor}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{\$textColor}{\$textColor}}
{\footnotesize\ttfamily \label{report_8php_ab99343d4912650d1363355564aa6c179} 
\$text\+Color = \textquotesingle{}text-\/red-\/500\textquotesingle{}}

\Hypertarget{report_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6}\index{report.php@{report.php}!\$totalAssets@{\$totalAssets}}
\index{\$totalAssets@{\$totalAssets}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{\$totalAssets}{\$totalAssets}}
{\footnotesize\ttfamily \label{report_8php_a3b9d42daa53a6dc9fb6156d46d3f63e6} 
\$total\+Assets = \$control-\/$>$total\+\_\+assets}

\Hypertarget{report_8php_a73dbf60765f3b9036cf082caad668726}\index{report.php@{report.php}!\$totalControls@{\$totalControls}}
\index{\$totalControls@{\$totalControls}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{\$totalControls}{\$totalControls}}
{\footnotesize\ttfamily \label{report_8php_a73dbf60765f3b9036cf082caad668726} 
\$total\+Controls = \$asset-\/$>$total\+\_\+controls}

\Hypertarget{report_8php_a284a2c36de7e8c8d980bc3f704ee0523}\index{report.php@{report.php}!else@{else}}
\index{else@{else}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{report_8php_a284a2c36de7e8c8d980bc3f704ee0523} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$overall\+Compliance $>$=90) \mbox{\hyperlink{app_2views_2compliance_2index_8php_a2fe92cbc651f6f645279ed84f54274dd}{elseif}} ( \$overall\+Compliance $>$=70) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ echo\ \textcolor{stringliteral}{'text-\/red-\/600'}}

\end{DoxyCode}
\Hypertarget{report_8php_aef76637aa1522ed86c0454bd6c1a50fd}\index{report.php@{report.php}!elseif@{elseif}}
\index{elseif@{elseif}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{elseif}{elseif}}
{\footnotesize\ttfamily \label{report_8php_aef76637aa1522ed86c0454bd6c1a50fd} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$compliance\+Percentage $>$=90) elseif(\$compliance\+Percentage $>$=70) (\begin{DoxyParamCaption}\item[{}]{\$compliance\+Percentage $>$=}{}\end{DoxyParamCaption})}

\Hypertarget{report_8php_a672d9707ef91db026c210f98cc601123}\index{report.php@{report.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{report_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}\index{report.php@{report.php}!foreach@{foreach}}
\index{foreach@{foreach}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily \label{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021} 
foreach (\begin{DoxyParamCaption}\item[{}]{\$data-\/$>$report\+\_\+data-\/$>$asset\+\_\+summary as}{\mbox{[}\textquotesingle{}report\textquotesingle{}\mbox{]}}\end{DoxyParamCaption}) = \$forecast\mbox{[}\textquotesingle{}projected\+\_\+amount\textquotesingle{}\mbox{]}}

\Hypertarget{report_8php_a426e5ef5de550913c16700467762c1e9}\index{report.php@{report.php}!foreach@{foreach}}
\index{foreach@{foreach}!report.php@{report.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily \label{report_8php_a426e5ef5de550913c16700467762c1e9} 
foreach(\$data\mbox{[}\textquotesingle{}report\textquotesingle{}\mbox{]}-\/$>$report\+\_\+data-\/$>$control\+\_\+summary as \$control) (\begin{DoxyParamCaption}\item[{}]{\$data-\/$>$report\+\_\+data-\/$>$control\+\_\+summary as}{\mbox{[} \textquotesingle{}report\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

