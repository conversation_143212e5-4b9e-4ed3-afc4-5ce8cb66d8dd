\doxysection{app/views/assets/search.php File Reference}
\hypertarget{search_8php}{}\label{search_8php}\index{app/views/assets/search.php@{app/views/assets/search.php}}
\doxysubsubsection*{Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(count( \$data\mbox{[} \textquotesingle{}assets\textquotesingle{}\mbox{]}) $>$ 0)( \$field, \$current\+Sort, \$current\+Order) \mbox{\hyperlink{search_8php_ad02b5389c04582e80c7de1ab48955b77}{get\+Sort\+Indicator}} (\$field, \$current\+Sort, \$current\+Order)
\end{DoxyCompactItemize}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!isset(\$data\mbox{[}\textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]})\texorpdfstring{$\vert$}{|}\texorpdfstring{$\vert$}{|}! \$data\mbox{[}\textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]})(empty(\$data\mbox{[}\textquotesingle{}term\textquotesingle{}\mbox{]})) \mbox{\hyperlink{search_8php_af8c725106f89f888eb62471868f675f6}{else}}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} \mbox{\hyperlink{search_8php_aefc614adca80226b3fa8c61efe9e812b}{( \$data\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}equipment\+\_\+types\textquotesingle{}\mbox{]} as \$type)}} (isset( \$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}equipment\+\_\+type\textquotesingle{}\mbox{]}) \&\&\$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}equipment\+\_\+type\textquotesingle{}\mbox{]}==\$type-\/$>$equipment\+\_\+type) ? \textquotesingle{}selected\textquotesingle{}
\item 
\mbox{\hyperlink{search_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} \mbox{\hyperlink{search_8php_afbb5860222327979c6ac93aef21c0591}{( \$data\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}site\+\_\+names\textquotesingle{}\mbox{]} as \$site)}} (isset( \$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}site\+\_\+name\textquotesingle{}\mbox{]}) \&\&\$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}site\+\_\+name\textquotesingle{}\mbox{]}==\$site-\/$>$site\+\_\+name) ? \textquotesingle{}selected\textquotesingle{}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} \mbox{\hyperlink{search_8php_a769cb414d72ae3111d1c1d0a7ca3639a}{( \$data\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}operating\+\_\+systems\textquotesingle{}\mbox{]} as \$os)}} (isset( \$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}operating\+\_\+system\textquotesingle{}\mbox{]}) \&\&\$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}operating\+\_\+system\textquotesingle{}\mbox{]}==\$os-\/$>$operating\+\_\+system) ? \textquotesingle{}selected\textquotesingle{}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} \mbox{\hyperlink{search_8php_a171d039a7479dd10161072b0a682c87f}{( \$data\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}program\+\_\+sections\textquotesingle{}\mbox{]} as \$section)}} (isset( \$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}program\+\_\+section\textquotesingle{}\mbox{]}) \&\&\$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}program\+\_\+section\textquotesingle{}\mbox{]}==\$section-\/$>$program\+\_\+section) ? \textquotesingle{}selected\textquotesingle{}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} \mbox{\hyperlink{search_8php_ad85aac63a81d7c2ac50aca0943a7d320}{( \$data\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}administration\+\_\+types\textquotesingle{}\mbox{]} as \$type)}} (isset( \$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}administration\+\_\+type\textquotesingle{}\mbox{]}) \&\&\$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}administration\+\_\+type\textquotesingle{}\mbox{]}==\$type-\/$>$administration\+\_\+type) ? \textquotesingle{}selected\textquotesingle{}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} \mbox{\hyperlink{search_8php_affe50d4c06fe2b1388f190dbbc30c6ee}{( \$data\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}xdr\+\_\+options\textquotesingle{}\mbox{]} as \$option)}} (isset( \$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}xdr\+\_\+installed\textquotesingle{}\mbox{]}) \&\&\$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}xdr\+\_\+installed\textquotesingle{}\mbox{]}==\$option-\/$>$xdr\+\_\+installed) ? \textquotesingle{}selected\textquotesingle{}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} \mbox{\hyperlink{search_8php_a9d32cc8a805b1b28a351dfe876bfb668}{( \$data\mbox{[} \textquotesingle{}tags\textquotesingle{}\mbox{]} as \$tag)}} (isset( \$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}tag\+\_\+id\textquotesingle{}\mbox{]}) \&\&\$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}tag\+\_\+id\textquotesingle{}\mbox{]}==\$tag-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}) ? \textquotesingle{}selected\textquotesingle{}
\item 
\mbox{\hyperlink{search_8php_a704178ddfa9f900a42449584b09b856f}{if}} (isset( \$data\mbox{[} \textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]}) \&\&\$data\mbox{[} \textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]} \&\&!empty( \$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]}))
\item 
\mbox{\hyperlink{search_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\mbox{\hyperlink{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}{has\+Permission}}(\textquotesingle{}export\+\_\+assets\textquotesingle{}))(isset(\$data\mbox{[}\textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]}) \&\& \$data\mbox{[}\textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]} \&\&!empty(\$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]})) we need to build the export URL with all parameters$<$ a href="{}$<$?php echo \mbox{\hyperlink{config_8php_a598166266a84ff3ecf84ef6f206ceefe}{URLROOT}}; ?$>$ assets export \mbox{\hyperlink{search_8php_a663ff282593675a5d351416419b26cbb}{advanced}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{search_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{search_8php_a20c4a031eb504739e03f6014cdfb0055}{\$tag\+Model}} = new \mbox{\hyperlink{class_tag}{Tag}}()
\item 
\mbox{\hyperlink{search_8php_a74cea71d3e2cbaff24dfb33210faa0f7}{\$asset\+Id}} = isset(\$asset-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}) ? \$asset-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}} \+: \$asset\mbox{[}\textquotesingle{}\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\textquotesingle{}\mbox{]}
\item 
\mbox{\hyperlink{search_8php_abf61640e87147db95185d5d01f3b66d1}{\$asset\+Tags}} = \$tag\+Model-\/$>$get\+Tags\+For\+Asset(\$asset\+Id)
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{search_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}{\+\_\+\+\_\+pad1\+\_\+\+\_\+}}
\end{DoxyCompactItemize}


\doxysubsection{Function Documentation}
\Hypertarget{search_8php_ad02b5389c04582e80c7de1ab48955b77}\index{search.php@{search.php}!getSortIndicator@{getSortIndicator}}
\index{getSortIndicator@{getSortIndicator}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{getSortIndicator()}{getSortIndicator()}}
{\footnotesize\ttfamily \label{search_8php_ad02b5389c04582e80c7de1ab48955b77} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(count(\$data\mbox{[}\textquotesingle{}assets\textquotesingle{}\mbox{]}) $>$ 0)(\$field, \$current\+Sort, \$current\+Order) get\+Sort\+Indicator (\begin{DoxyParamCaption}\item[{}]{\$field}{, }\item[{}]{\$current\+Sort}{, }\item[{}]{\$current\+Order}{}\end{DoxyParamCaption})}



\doxysubsection{Variable Documentation}
\Hypertarget{search_8php_a74cea71d3e2cbaff24dfb33210faa0f7}\index{search.php@{search.php}!\$assetId@{\$assetId}}
\index{\$assetId@{\$assetId}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{\$assetId}{\$assetId}}
{\footnotesize\ttfamily \label{search_8php_a74cea71d3e2cbaff24dfb33210faa0f7} 
\$asset\+Id = isset(\$asset-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}) ? \$asset-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}} \+: \$asset\mbox{[}\textquotesingle{}\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}\textquotesingle{}\mbox{]}}

\Hypertarget{search_8php_abf61640e87147db95185d5d01f3b66d1}\index{search.php@{search.php}!\$assetTags@{\$assetTags}}
\index{\$assetTags@{\$assetTags}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{\$assetTags}{\$assetTags}}
{\footnotesize\ttfamily \label{search_8php_abf61640e87147db95185d5d01f3b66d1} 
\$asset\+Tags = \$tag\+Model-\/$>$get\+Tags\+For\+Asset(\$asset\+Id)}

\Hypertarget{search_8php_a20c4a031eb504739e03f6014cdfb0055}\index{search.php@{search.php}!\$tagModel@{\$tagModel}}
\index{\$tagModel@{\$tagModel}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{\$tagModel}{\$tagModel}}
{\footnotesize\ttfamily \label{search_8php_a20c4a031eb504739e03f6014cdfb0055} 
\$tag\+Model = new \mbox{\hyperlink{class_tag}{Tag}}()}

\Hypertarget{search_8php_ad85aac63a81d7c2ac50aca0943a7d320}\index{search.php@{search.php}!( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}administration\_types\textquotesingle{}\mbox{]} as \$type)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}administration\_types\textquotesingle{}] as \$type)}}
\index{( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}administration\_types\textquotesingle{}\mbox{]} as \$type)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}administration\_types\textquotesingle{}] as \$type)}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}administration\_types\textquotesingle{}] as \$type)}{( \$data[ 'filter\_options'][ 'administration\_types'] as \$type)}}
{\footnotesize\ttfamily \label{search_8php_ad85aac63a81d7c2ac50aca0943a7d320} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} (\$data\mbox{[}\textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}administration\+\_\+types\textquotesingle{}\mbox{]} as \$type)(isset(\$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}administration\+\_\+type\textquotesingle{}\mbox{]}) \&\& \$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}administration\+\_\+type\textquotesingle{}\mbox{]}==\$type-\/$>$administration\+\_\+type) ? \textquotesingle{}selected\textquotesingle{} (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}administration\+\_\+types\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{search_8php_aefc614adca80226b3fa8c61efe9e812b}\index{search.php@{search.php}!( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}equipment\_types\textquotesingle{}\mbox{]} as \$type)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}equipment\_types\textquotesingle{}] as \$type)}}
\index{( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}equipment\_types\textquotesingle{}\mbox{]} as \$type)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}equipment\_types\textquotesingle{}] as \$type)}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}equipment\_types\textquotesingle{}] as \$type)}{( \$data[ 'filter\_options'][ 'equipment\_types'] as \$type)}}
{\footnotesize\ttfamily \label{search_8php_aefc614adca80226b3fa8c61efe9e812b} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} (\$data\mbox{[}\textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}equipment\+\_\+types\textquotesingle{}\mbox{]} as \$type)(isset(\$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}equipment\+\_\+type\textquotesingle{}\mbox{]}) \&\& \$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}equipment\+\_\+type\textquotesingle{}\mbox{]}==\$type-\/$>$equipment\+\_\+type) ? \textquotesingle{}selected\textquotesingle{} (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}equipment\+\_\+types\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{search_8php_a769cb414d72ae3111d1c1d0a7ca3639a}\index{search.php@{search.php}!( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}operating\_systems\textquotesingle{}\mbox{]} as \$os)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}operating\_systems\textquotesingle{}] as \$os)}}
\index{( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}operating\_systems\textquotesingle{}\mbox{]} as \$os)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}operating\_systems\textquotesingle{}] as \$os)}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}operating\_systems\textquotesingle{}] as \$os)}{( \$data[ 'filter\_options'][ 'operating\_systems'] as \$os)}}
{\footnotesize\ttfamily \label{search_8php_a769cb414d72ae3111d1c1d0a7ca3639a} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} (\$data\mbox{[}\textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}operating\+\_\+systems\textquotesingle{}\mbox{]} as \$os)(isset(\$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}operating\+\_\+system\textquotesingle{}\mbox{]}) \&\& \$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}operating\+\_\+system\textquotesingle{}\mbox{]}==\$os-\/$>$operating\+\_\+system) ? \textquotesingle{}selected\textquotesingle{} (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}operating\+\_\+systems\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{search_8php_a171d039a7479dd10161072b0a682c87f}\index{search.php@{search.php}!( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}program\_sections\textquotesingle{}\mbox{]} as \$section)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}program\_sections\textquotesingle{}] as \$section)}}
\index{( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}program\_sections\textquotesingle{}\mbox{]} as \$section)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}program\_sections\textquotesingle{}] as \$section)}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}program\_sections\textquotesingle{}] as \$section)}{( \$data[ 'filter\_options'][ 'program\_sections'] as \$section)}}
{\footnotesize\ttfamily \label{search_8php_a171d039a7479dd10161072b0a682c87f} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} (\$data\mbox{[}\textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}program\+\_\+sections\textquotesingle{}\mbox{]} as \$section)(isset(\$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}program\+\_\+section\textquotesingle{}\mbox{]}) \&\& \$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}program\+\_\+section\textquotesingle{}\mbox{]}==\$section-\/$>$program\+\_\+section) ? \textquotesingle{}selected\textquotesingle{} (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}program\+\_\+sections\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{search_8php_afbb5860222327979c6ac93aef21c0591}\index{search.php@{search.php}!( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}site\_names\textquotesingle{}\mbox{]} as \$site)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}site\_names\textquotesingle{}] as \$site)}}
\index{( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}site\_names\textquotesingle{}\mbox{]} as \$site)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}site\_names\textquotesingle{}] as \$site)}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}site\_names\textquotesingle{}] as \$site)}{( \$data[ 'filter\_options'][ 'site\_names'] as \$site)}}
{\footnotesize\ttfamily \label{search_8php_afbb5860222327979c6ac93aef21c0591} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} (\$data\mbox{[}\textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}site\+\_\+names\textquotesingle{}\mbox{]} as \$site)(isset(\$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}site\+\_\+name\textquotesingle{}\mbox{]}) \&\& \$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}site\+\_\+name\textquotesingle{}\mbox{]}==\$site-\/$>$site\+\_\+name) ? \textquotesingle{}selected\textquotesingle{} (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}site\+\_\+names\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{search_8php_affe50d4c06fe2b1388f190dbbc30c6ee}\index{search.php@{search.php}!( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}xdr\_options\textquotesingle{}\mbox{]} as \$option)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}xdr\_options\textquotesingle{}] as \$option)}}
\index{( \$data\mbox{[} \textquotesingle{}filter\_options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}xdr\_options\textquotesingle{}\mbox{]} as \$option)@{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}xdr\_options\textquotesingle{}] as \$option)}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{( \$data[ \textquotesingle{}filter\_options\textquotesingle{}][ \textquotesingle{}xdr\_options\textquotesingle{}] as \$option)}{( \$data[ 'filter\_options'][ 'xdr\_options'] as \$option)}}
{\footnotesize\ttfamily \label{search_8php_affe50d4c06fe2b1388f190dbbc30c6ee} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} (\$data\mbox{[}\textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}xdr\+\_\+options\textquotesingle{}\mbox{]} as \$option)(isset(\$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}xdr\+\_\+installed\textquotesingle{}\mbox{]}) \&\& \$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}xdr\+\_\+installed\textquotesingle{}\mbox{]}==\$option-\/$>$xdr\+\_\+installed) ? \textquotesingle{}selected\textquotesingle{} (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}filter\+\_\+options\textquotesingle{}\mbox{]}\mbox{[} \textquotesingle{}xdr\+\_\+options\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{search_8php_a9d32cc8a805b1b28a351dfe876bfb668}\index{search.php@{search.php}!( \$data\mbox{[} \textquotesingle{}tags\textquotesingle{}\mbox{]} as \$tag)@{( \$data[ \textquotesingle{}tags\textquotesingle{}] as \$tag)}}
\index{( \$data\mbox{[} \textquotesingle{}tags\textquotesingle{}\mbox{]} as \$tag)@{( \$data[ \textquotesingle{}tags\textquotesingle{}] as \$tag)}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{( \$data[ \textquotesingle{}tags\textquotesingle{}] as \$tag)}{( \$data[ 'tags'] as \$tag)}}
{\footnotesize\ttfamily \label{search_8php_a9d32cc8a805b1b28a351dfe876bfb668} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} (\$data\mbox{[}\textquotesingle{}tags\textquotesingle{}\mbox{]} as \$tag)(isset(\$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}tag\+\_\+id\textquotesingle{}\mbox{]}) \&\& \$data\mbox{[}\textquotesingle{}params\textquotesingle{}\mbox{]}\mbox{[}\textquotesingle{}tag\+\_\+id\textquotesingle{}\mbox{]}==\$tag-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}) ? \textquotesingle{}selected\textquotesingle{} (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}tags\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{search_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{search.php@{search.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{search_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{search_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead}\index{search.php@{search.php}!\_\_pad1\_\_@{\_\_pad1\_\_}}
\index{\_\_pad1\_\_@{\_\_pad1\_\_}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad1\_\_}{\_\_pad1\_\_}}
{\footnotesize\ttfamily \label{search_8php_ae8b4bb1441c6ab4dcb28a37bc46c8ead} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad1\+\_\+\+\_\+}

\Hypertarget{search_8php_a663ff282593675a5d351416419b26cbb}\index{search.php@{search.php}!advanced@{advanced}}
\index{advanced@{advanced}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{advanced}{advanced}}
{\footnotesize\ttfamily \label{search_8php_a663ff282593675a5d351416419b26cbb} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (\mbox{\hyperlink{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}{has\+Permission}}( \textquotesingle{}export\+\_\+assets\textquotesingle{})) (isset( \$data\mbox{[} \textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]}) \&\&\$data\mbox{[} \textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]} \&\&!empty( \$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]})) we need to build the export URL with all parameters$<$a href="{}$<$?php echo \mbox{\hyperlink{config_8php_a598166266a84ff3ecf84ef6f206ceefe}{URLROOT}}; ?$>$ assets export advanced}

\Hypertarget{search_8php_af8c725106f89f888eb62471868f675f6}\index{search.php@{search.php}!else@{else}}
\index{else@{else}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{search_8php_af8c725106f89f888eb62471868f675f6} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!isset( \$data\mbox{[} \textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]})\texorpdfstring{$\vert$}{|}\texorpdfstring{$\vert$}{|}! \$data\mbox{[} \textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]}) (empty( \$data\mbox{[} \textquotesingle{}term\textquotesingle{}\mbox{]})) else}

\Hypertarget{search_8php_a672d9707ef91db026c210f98cc601123}\index{search.php@{search.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{search_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{search_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{search.php@{search.php}!endif@{endif}}
\index{endif@{endif}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{search_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

\Hypertarget{search_8php_a704178ddfa9f900a42449584b09b856f}\index{search.php@{search.php}!if@{if}}
\index{if@{if}!search.php@{search.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{search_8php_a704178ddfa9f900a42449584b09b856f} 
if(\$key==\textquotesingle{}tag\+\_\+id\textquotesingle{}) (\begin{DoxyParamCaption}\item[{isset( \$data\mbox{[} \textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]}) \&\&\$data \&\&!empty( \$data\mbox{[} \textquotesingle{}params\textquotesingle{}\mbox{]})}]{}{\mbox{[} \textquotesingle{}is\+\_\+advanced\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{=>\ \textcolor{stringliteral}{'Host\ Name'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'employee\_name'}\ =>\ \textcolor{stringliteral}{'Employee\ Name'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'equipment\_type'}\ =>\ \textcolor{stringliteral}{'Equipment\ Type'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'site\_name'}\ =>\ \textcolor{stringliteral}{'Site\ Name'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'operating\_system'}\ =>\ \textcolor{stringliteral}{'Operating\ System'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'program\_section'}\ =>\ \textcolor{stringliteral}{'Program\ Section'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'administration\_type'}\ =>\ \textcolor{stringliteral}{'Administration\ Type'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'xdr\_installed'}\ =>\ \textcolor{stringliteral}{'XDR\ Installed'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'acquisition\_date\_from'}\ =>\ \textcolor{stringliteral}{'Acquisition\ Date\ From'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'acquisition\_date\_to'}\ =>\ \textcolor{stringliteral}{'Acquisition\ Date\ To'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'inventory\_date\_from'}\ =>\ \textcolor{stringliteral}{'Inventory\ Date\ From'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'inventory\_date\_to'}\ =>\ \textcolor{stringliteral}{'Inventory\ Date\ To'},}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{'tag\_id'}\ =>\ \textcolor{stringliteral}{'Tag'}}
\DoxyCodeLine{\ \ \ \ \ \ \ \ \ \ \ \ ]}

\end{DoxyCode}
