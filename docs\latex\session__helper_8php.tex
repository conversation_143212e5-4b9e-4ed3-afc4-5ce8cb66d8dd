\doxysection{app/helpers/session\+\_\+helper.php File Reference}
\hypertarget{session__helper_8php}{}\label{session__helper_8php}\index{app/helpers/session\_helper.php@{app/helpers/session\_helper.php}}
\doxysubsubsection*{Namespaces}
\begin{DoxyCompactItemize}
\item 
namespace \mbox{\hyperlink{namespace_asset_visibility}{Asset\+Visibility}}
\end{DoxyCompactItemize}
\doxysubsubsection*{Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{session__helper_8php_a944d856551c6f577255c4c853fc9ee49}{flash}} (\$name=\textquotesingle{}\textquotesingle{}, \$message=\textquotesingle{}\textquotesingle{}, \$class=\textquotesingle{}bg-\/green-\/100 text-\/green-\/800\textquotesingle{})
\item 
\mbox{\hyperlink{session__helper_8php_a33bdd79e5da367ebddd4cfbdbbfc7cff}{is\+Logged\+In}} ()
\item 
\mbox{\hyperlink{session__helper_8php_aabf23b66cd362adaa508de5bfb22706a}{is\+Admin}} ()
\item 
\mbox{\hyperlink{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}{has\+Permission}} (\$permission)
\item 
\mbox{\hyperlink{session__helper_8php_a53a5bca218d3879ec04ebc1cb2c2bb56}{has\+Role}} (\$role)
\item 
\mbox{\hyperlink{session__helper_8php_abde6b371dbe38c869ffdd4684d046a4a}{clear\+Permission\+Cache}} ()
\item 
\mbox{\hyperlink{session__helper_8php_a346f134a5d2ca39e76810b757cf6255d}{clear\+Global\+Permission\+Cache}} ()
\item 
\mbox{\hyperlink{session__helper_8php_aa7e78bf5572fe3a109a59d4a63e0fa34}{redirect}} (\$page)
\end{DoxyCompactItemize}


\doxysubsection{Function Documentation}
\Hypertarget{session__helper_8php_a346f134a5d2ca39e76810b757cf6255d}\index{session\_helper.php@{session\_helper.php}!clearGlobalPermissionCache@{clearGlobalPermissionCache}}
\index{clearGlobalPermissionCache@{clearGlobalPermissionCache}!session\_helper.php@{session\_helper.php}}
\doxysubsubsection{\texorpdfstring{clearGlobalPermissionCache()}{clearGlobalPermissionCache()}}
{\footnotesize\ttfamily \label{session__helper_8php_a346f134a5d2ca39e76810b757cf6255d} 
clear\+Global\+Permission\+Cache (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Clear permission cache for all users by updating a global timestamp

Forces all users to refresh their permissions on next check by updating a global cache timestamp file.

\begin{DoxyReturn}{Returns}
void 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{session__helper_8php_abde6b371dbe38c869ffdd4684d046a4a}\index{session\_helper.php@{session\_helper.php}!clearPermissionCache@{clearPermissionCache}}
\index{clearPermissionCache@{clearPermissionCache}!session\_helper.php@{session\_helper.php}}
\doxysubsubsection{\texorpdfstring{clearPermissionCache()}{clearPermissionCache()}}
{\footnotesize\ttfamily \label{session__helper_8php_abde6b371dbe38c869ffdd4684d046a4a} 
clear\+Permission\+Cache (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Clear the permission and role cache

Clears the current user\textquotesingle{}s cached permissions and roles. Call this when user permissions or roles are updated.

\begin{DoxyReturn}{Returns}
void 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{session__helper_8php_a944d856551c6f577255c4c853fc9ee49}\index{session\_helper.php@{session\_helper.php}!flash@{flash}}
\index{flash@{flash}!session\_helper.php@{session\_helper.php}}
\doxysubsubsection{\texorpdfstring{flash()}{flash()}}
{\footnotesize\ttfamily \label{session__helper_8php_a944d856551c6f577255c4c853fc9ee49} 
flash (\begin{DoxyParamCaption}\item[{}]{\$name}{ = {\ttfamily \textquotesingle{}\textquotesingle{}}, }\item[{}]{\$message}{ = {\ttfamily \textquotesingle{}\textquotesingle{}}, }\item[{}]{\$class}{ = {\ttfamily \textquotesingle{}bg-\/green-\/100~text-\/green-\/800\textquotesingle{}}}\end{DoxyParamCaption})}

Flash message helper

Manages flash messages using Sweet\+Alert2 for user notifications. Supports setting and displaying messages with different alert types.


\begin{DoxyParams}[1]{Parameters}
string & {\em \$name} & The name/key for the flash message \\
\hline
string & {\em \$message} & The message content (empty to display existing message) \\
\hline
string & {\em \$class} & CSS class for styling (determines alert type) \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
void 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f}\index{session\_helper.php@{session\_helper.php}!hasPermission@{hasPermission}}
\index{hasPermission@{hasPermission}!session\_helper.php@{session\_helper.php}}
\doxysubsubsection{\texorpdfstring{hasPermission()}{hasPermission()}}
{\footnotesize\ttfamily \label{session__helper_8php_a4da2a6a1e77331cc90a7d38bba8c442f} 
has\+Permission (\begin{DoxyParamCaption}\item[{}]{\$permission}{}\end{DoxyParamCaption})}

Check if the current user has a specific permission


\begin{DoxyParams}[1]{Parameters}
string & {\em \$permission} & \doxylink{class_permission}{Permission} name to check \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if the user has the permission, false otherwise 
\end{DoxyReturn}
\Hypertarget{session__helper_8php_a53a5bca218d3879ec04ebc1cb2c2bb56}\index{session\_helper.php@{session\_helper.php}!hasRole@{hasRole}}
\index{hasRole@{hasRole}!session\_helper.php@{session\_helper.php}}
\doxysubsubsection{\texorpdfstring{hasRole()}{hasRole()}}
{\footnotesize\ttfamily \label{session__helper_8php_a53a5bca218d3879ec04ebc1cb2c2bb56} 
has\+Role (\begin{DoxyParamCaption}\item[{}]{\$role}{}\end{DoxyParamCaption})}

Check if the current user has a specific role


\begin{DoxyParams}[1]{Parameters}
string & {\em \$role} & \doxylink{class_role}{Role} name to check \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
bool True if the user has the role, false otherwise 
\end{DoxyReturn}
\Hypertarget{session__helper_8php_aabf23b66cd362adaa508de5bfb22706a}\index{session\_helper.php@{session\_helper.php}!isAdmin@{isAdmin}}
\index{isAdmin@{isAdmin}!session\_helper.php@{session\_helper.php}}
\doxysubsubsection{\texorpdfstring{isAdmin()}{isAdmin()}}
{\footnotesize\ttfamily \label{session__helper_8php_aabf23b66cd362adaa508de5bfb22706a} 
is\+Admin (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Check if user is an administrator

Checks both the new role-\/based system and legacy session role for backward compatibility.

\begin{DoxyReturn}{Returns}
bool True if user is an administrator, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{session__helper_8php_a33bdd79e5da367ebddd4cfbdbbfc7cff}\index{session\_helper.php@{session\_helper.php}!isLoggedIn@{isLoggedIn}}
\index{isLoggedIn@{isLoggedIn}!session\_helper.php@{session\_helper.php}}
\doxysubsubsection{\texorpdfstring{isLoggedIn()}{isLoggedIn()}}
{\footnotesize\ttfamily \label{session__helper_8php_a33bdd79e5da367ebddd4cfbdbbfc7cff} 
is\+Logged\+In (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

Check if user is logged in

Checks both session data and remember me cookies to determine if a user is authenticated.

\begin{DoxyReturn}{Returns}
bool True if user is logged in, false otherwise 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
\Hypertarget{session__helper_8php_aa7e78bf5572fe3a109a59d4a63e0fa34}\index{session\_helper.php@{session\_helper.php}!redirect@{redirect}}
\index{redirect@{redirect}!session\_helper.php@{session\_helper.php}}
\doxysubsubsection{\texorpdfstring{redirect()}{redirect()}}
{\footnotesize\ttfamily \label{session__helper_8php_aa7e78bf5572fe3a109a59d4a63e0fa34} 
redirect (\begin{DoxyParamCaption}\item[{}]{\$page}{}\end{DoxyParamCaption})}

Redirect to a specific page

Performs HTTP redirect to the specified page with proper URL construction. Handles both relative and absolute URLs correctly.


\begin{DoxyParams}[1]{Parameters}
string & {\em \$page} & The page/\+URL to redirect to \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
void This function exits the script after redirect 
\end{DoxyReturn}
\begin{DoxySince}{Since}
1.\+0.\+0 
\end{DoxySince}
