\doxysection{test\+\_\+db\+\_\+connection.\+php File Reference}
\hypertarget{test__db__connection_8php}{}\label{test__db__connection_8php}\index{test\_db\_connection.php@{test\_db\_connection.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{test__db__connection_8php_abe4cc9788f52e49485473dc699537388}{try}}
\item 
\mbox{\hyperlink{test__db__connection_8php_a5766efd703cef0e00bfc06b3f3acbe0e}{\$pdo}} = new PDO(\$dsn, \mbox{\hyperlink{config_8php_a1d1d99f8e08f387d84fe9848f3357156}{DB\+\_\+\+USER}}, \mbox{\hyperlink{config_8php_a8bb9c4546d91667cfa61879d83127a92}{DB\+\_\+\+PASS}})
\item 
\mbox{\hyperlink{test__db__connection_8php_af27a9140d5f2658693e7fd107f716449}{\$stmt}} = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}assets\textquotesingle{}"{})
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$stmt-\/$>$row\+Count() $>$ 0) \mbox{\hyperlink{test__db__connection_8php_aafa8ceaa4835e12cb74eb7408d8b3a32}{else}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{test__db__connection_8php_a5766efd703cef0e00bfc06b3f3acbe0e}\index{test\_db\_connection.php@{test\_db\_connection.php}!\$pdo@{\$pdo}}
\index{\$pdo@{\$pdo}!test\_db\_connection.php@{test\_db\_connection.php}}
\doxysubsubsection{\texorpdfstring{\$pdo}{\$pdo}}
{\footnotesize\ttfamily \label{test__db__connection_8php_a5766efd703cef0e00bfc06b3f3acbe0e} 
\$pdo = new PDO(\$dsn, \mbox{\hyperlink{config_8php_a1d1d99f8e08f387d84fe9848f3357156}{DB\+\_\+\+USER}}, \mbox{\hyperlink{config_8php_a8bb9c4546d91667cfa61879d83127a92}{DB\+\_\+\+PASS}})}

\Hypertarget{test__db__connection_8php_af27a9140d5f2658693e7fd107f716449}\index{test\_db\_connection.php@{test\_db\_connection.php}!\$stmt@{\$stmt}}
\index{\$stmt@{\$stmt}!test\_db\_connection.php@{test\_db\_connection.php}}
\doxysubsubsection{\texorpdfstring{\$stmt}{\$stmt}}
{\footnotesize\ttfamily \label{test__db__connection_8php_af27a9140d5f2658693e7fd107f716449} 
\$stmt = \$pdo-\/$>$query("{}SHOW TABLES LIKE \textquotesingle{}assets\textquotesingle{}"{})}

\Hypertarget{test__db__connection_8php_aafa8ceaa4835e12cb74eb7408d8b3a32}\index{test\_db\_connection.php@{test\_db\_connection.php}!else@{else}}
\index{else@{else}!test\_db\_connection.php@{test\_db\_connection.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{test__db__connection_8php_aafa8ceaa4835e12cb74eb7408d8b3a32} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$stmt-\/$>$row\+Count() $>$ 0) else}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ \ \ \ \ echo\ \textcolor{stringliteral}{"{}<p\ style='color:red;'>✗\ Assets\ table\ does\ not\ exist</p>"{}}}

\end{DoxyCode}
\Hypertarget{test__db__connection_8php_abe4cc9788f52e49485473dc699537388}\index{test\_db\_connection.php@{test\_db\_connection.php}!try@{try}}
\index{try@{try}!test\_db\_connection.php@{test\_db\_connection.php}}
\doxysubsubsection{\texorpdfstring{try}{try}}
{\footnotesize\ttfamily \label{test__db__connection_8php_abe4cc9788f52e49485473dc699537388} 
try}

{\bfseries Initial value\+:}
\begin{DoxyCode}{0}
\DoxyCodeLine{\{}
\DoxyCodeLine{\ \ \ \ }
\DoxyCodeLine{\ \ \ \ \$dsn\ =\ \textcolor{stringliteral}{'mysql:host='}\ .\ \mbox{\hyperlink{config_8php_a293363d7988627f671958e2d908c202a}{DB\_HOST}}\ .\ \textcolor{stringliteral}{';dbname='}\ .\ \mbox{\hyperlink{config_8php_ab5db0d3504f917f268614c50b02c53e2}{DB\_NAME}}}

\end{DoxyCode}
