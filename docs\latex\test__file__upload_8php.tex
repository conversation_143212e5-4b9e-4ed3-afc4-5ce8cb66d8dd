\doxysection{test\+\_\+file\+\_\+upload.\+php File Reference}
\hypertarget{test__file__upload_8php}{}\label{test__file__upload_8php}\index{test\_file\_upload.php@{test\_file\_upload.php}}
\doxysubsubsection*{Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{test__file__upload_8php_a714ce50db18b1104062399cdec8486c6}{get\+Upload\+Error\+Message}} (\$error\+Code)
\end{DoxyCompactItemize}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{test__file__upload_8php_a76101de3df453e22e2df687ad2e74a13}{\$upload\+Dir}} = ini\+\_\+get(\textquotesingle{}upload\+\_\+tmp\+\_\+dir\textquotesingle{}) ?\+: sys\+\_\+get\+\_\+temp\+\_\+dir()
\item 
\mbox{\hyperlink{test__file__upload_8php_a69dafa0546695386cd61c36aa16cca44}{if}} ( \$\+\_\+\+SERVER\mbox{[} \textquotesingle{}REQUEST\+\_\+\+METHOD\textquotesingle{}\mbox{]}===\textquotesingle{}POST\textquotesingle{} \&\&isset( \$\+\_\+\+FILES\mbox{[} \textquotesingle{}test\+\_\+file\textquotesingle{}\mbox{]}))
\end{DoxyCompactItemize}


\doxysubsection{Function Documentation}
\Hypertarget{test__file__upload_8php_a714ce50db18b1104062399cdec8486c6}\index{test\_file\_upload.php@{test\_file\_upload.php}!getUploadErrorMessage@{getUploadErrorMessage}}
\index{getUploadErrorMessage@{getUploadErrorMessage}!test\_file\_upload.php@{test\_file\_upload.php}}
\doxysubsubsection{\texorpdfstring{getUploadErrorMessage()}{getUploadErrorMessage()}}
{\footnotesize\ttfamily \label{test__file__upload_8php_a714ce50db18b1104062399cdec8486c6} 
get\+Upload\+Error\+Message (\begin{DoxyParamCaption}\item[{}]{\$error\+Code}{}\end{DoxyParamCaption})}



\doxysubsection{Variable Documentation}
\Hypertarget{test__file__upload_8php_a76101de3df453e22e2df687ad2e74a13}\index{test\_file\_upload.php@{test\_file\_upload.php}!\$uploadDir@{\$uploadDir}}
\index{\$uploadDir@{\$uploadDir}!test\_file\_upload.php@{test\_file\_upload.php}}
\doxysubsubsection{\texorpdfstring{\$uploadDir}{\$uploadDir}}
{\footnotesize\ttfamily \label{test__file__upload_8php_a76101de3df453e22e2df687ad2e74a13} 
\$upload\+Dir = ini\+\_\+get(\textquotesingle{}upload\+\_\+tmp\+\_\+dir\textquotesingle{}) ?\+: sys\+\_\+get\+\_\+temp\+\_\+dir()}

\Hypertarget{test__file__upload_8php_a69dafa0546695386cd61c36aa16cca44}\index{test\_file\_upload.php@{test\_file\_upload.php}!if@{if}}
\index{if@{if}!test\_file\_upload.php@{test\_file\_upload.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{test__file__upload_8php_a69dafa0546695386cd61c36aa16cca44} 
if(\$\+\_\+\+SERVER\mbox{[}\textquotesingle{}REQUEST\+\_\+\+METHOD\textquotesingle{}\mbox{]}===\textquotesingle{}POST\textquotesingle{} \&\&isset(\$\+\_\+\+FILES\mbox{[}\textquotesingle{}test\+\_\+file\textquotesingle{}\mbox{]})) (\begin{DoxyParamCaption}\item[{}]{\$\+\_\+\+SERVER}{\mbox{[} \textquotesingle{}\+REQUEST\+\_\+\+METHOD\textquotesingle{}\mbox{]} = {\ttfamily ==~\textquotesingle{}POST\textquotesingle{}~\&\&~isset(\$\+\_\+FILES\mbox{[}\textquotesingle{}test\+\_\+file\textquotesingle{}\mbox{]})}}\end{DoxyParamCaption})}

