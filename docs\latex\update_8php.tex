\doxysection{app/views/compliance/update.php File Reference}
\hypertarget{update_8php}{}\label{update_8php}\index{app/views/compliance/update.php@{app/views/compliance/update.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$data\mbox{[}\textquotesingle{}control\textquotesingle{}\mbox{]}-\/$>$implementation\+\_\+guidance) \mbox{\hyperlink{update_8php_aab34b63f1568c2b385b9ad1abaa80eef}{endif}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{update_8php_aab34b63f1568c2b385b9ad1abaa80eef}\index{update.php@{update.php}!endif@{endif}}
\index{endif@{endif}!update.php@{update.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{update_8php_aab34b63f1568c2b385b9ad1abaa80eef} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty( \$data\mbox{[} \textquotesingle{}status\+\_\+err\textquotesingle{}\mbox{]})) endif}

