\doxysection{app/views/error\+\_\+logs/view.php File Reference}
\hypertarget{view_8php}{}\label{view_8php}\index{app/views/error\_logs/view.php@{app/views/error\_logs/view.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{view_8php_ae6507b0dfbff66ac3a6b4dfd6c4d1a34}{\$level\+Class}} = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{view_8php_ab79dec0f9fdcc05f90072eb2dbc59e56}{switch}} ( \$data\mbox{[} \textquotesingle{}error\+\_\+log\textquotesingle{}\mbox{]}-\/$>$level)
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{view_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(\$data\mbox{[}\textquotesingle{}error\+\_\+log\textquotesingle{}\mbox{]}-\/$>$trace) \mbox{\hyperlink{view_8php_a5075cf3cbf9ce1cfa0d52924033abf51}{else}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{view_8php_ae6507b0dfbff66ac3a6b4dfd6c4d1a34}\index{view.php@{view.php}!\$levelClass@{\$levelClass}}
\index{\$levelClass@{\$levelClass}!view.php@{view.php}}
\doxysubsubsection{\texorpdfstring{\$levelClass}{\$levelClass}}
{\footnotesize\ttfamily \label{view_8php_ae6507b0dfbff66ac3a6b4dfd6c4d1a34} 
\$level\+Class = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}}

\Hypertarget{view_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{view.php@{view.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!view.php@{view.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{view_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{view_8php_a5075cf3cbf9ce1cfa0d52924033abf51}\index{view.php@{view.php}!else@{else}}
\index{else@{else}!view.php@{view.php}}
\doxysubsubsection{\texorpdfstring{else}{else}}
{\footnotesize\ttfamily \label{view_8php_a5075cf3cbf9ce1cfa0d52924033abf51} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} ( \$data\mbox{[} \textquotesingle{}error\+\_\+log\textquotesingle{}\mbox{]}-\/$>$user\+\_\+agent) else}

\Hypertarget{view_8php_ab79dec0f9fdcc05f90072eb2dbc59e56}\index{view.php@{view.php}!switch@{switch}}
\index{switch@{switch}!view.php@{view.php}}
\doxysubsubsection{\texorpdfstring{switch}{switch}}
{\footnotesize\ttfamily \label{view_8php_ab79dec0f9fdcc05f90072eb2dbc59e56} 
switch(\$data\mbox{[}\textquotesingle{}error\+\_\+log\textquotesingle{}\mbox{]}-\/$>$level) (\begin{DoxyParamCaption}\item[{}]{\$data-\/$>$}{\mbox{[} \textquotesingle{}error\+\_\+log\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

