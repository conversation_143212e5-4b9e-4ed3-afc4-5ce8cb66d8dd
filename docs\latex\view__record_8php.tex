\doxysection{app/views/maintenance/view\+\_\+record.php File Reference}
\hypertarget{view__record_8php}{}\label{view__record_8php}\index{app/views/maintenance/view\_record.php@{app/views/maintenance/view\_record.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{view__record_8php_a65afead519af968d2d7dc5397dab937e}{if}} (isset( \$\+\_\+\+GET\mbox{[} \textquotesingle{}debug\textquotesingle{}\mbox{]}) \&\&\$\+\_\+\+GET\mbox{[} \textquotesingle{}debug\textquotesingle{}\mbox{]}==1)
\item 
\mbox{\hyperlink{view__record_8php_a65f6f567bea1128b76e0c5c69ab57907}{foreach}} ( \$data\mbox{[} \textquotesingle{}completed\+\_\+checklist\+\_\+items\textquotesingle{}\mbox{]} as \$guideline\+Id=$>$ \$items)
\item 
\mbox{\hyperlink{view__record_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\item 
\mbox{\hyperlink{view__record_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}{endif}}
\item 
\mbox{\hyperlink{view__record_8php_ad91b289b04487d30b11cd3dc57b4dee5}{\$type\+Class}} = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{view__record_8php_ac3b4e985ec4050d6034cf1af22dfda1f}{switch}} ( \$data\mbox{[} \textquotesingle{}maintenance\+\_\+record\textquotesingle{}\mbox{]}-\/$>$maintenance\+\_\+type)
\item 
\mbox{\hyperlink{view__record_8php_aab9b724306b055e8e4ed6d1e1f1653f1}{\$status\+Class}} = \textquotesingle{}bg-\/gray-\/100 text-\/gray-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{view__record_8php_a59fb42f3b0dc7ba1d0e2a57b6dd4cf4d}{switch}} ( \$data\mbox{[} \textquotesingle{}maintenance\+\_\+record\textquotesingle{}\mbox{]}-\/$>$status)
\item 
\mbox{\hyperlink{view__record_8php_a1fa3127fc82f96b1436d871ef02be319}{\$db}} = new \mbox{\hyperlink{class_database}{Database}}()
\item 
\mbox{\hyperlink{view__record_8php_a27ae3a2b96c15043fb33a4686ae3f15f}{\$count\+Result}} = \$db-\/$>$single()
\item 
\mbox{\hyperlink{view__record_8php_ab690e0531aea866478f7d205048d16c3}{\$guideline\+Count}} = \$count\+Result-\/$>$count
\item 
\mbox{\hyperlink{view__record_8php_a2adffdd84d6ab1ab2ce17da57327f4fc}{\$implemented\+Guidelines}} = \$db-\/$>$result\+Set()
\item 
\mbox{\hyperlink{view__record_8php_a5a164a6a39728ebceba09a9a68a15839}{\$guideline\+Details}} = \mbox{[}$\,$\mbox{]}
\item 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty( \$implemented\+Guidelines)) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} \mbox{\hyperlink{view__record_8php_a844929e9dc05dd77d3b6b756032a6d16}{(!empty( \$guideline\+Details))}} ( \$guideline\+Details as \$index=$>$ \$guideline)
\item 
\mbox{\hyperlink{view__record_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b}{\$importance\+Class}} = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{view__record_8php_a4fc34755212ee7f3d75abc75c55a9ad4}{\$completed\+Items}} = \mbox{[}$\,$\mbox{]}
\item 
\mbox{\hyperlink{view__record_8php_a8ba3d54e0f8c662f4482d113c94577e6}{\$guideline\+Id}} = \$guideline-\/$>$guideline\+\_\+id ?? \$guideline-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{view__record_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{view__record_8php_a4fc34755212ee7f3d75abc75c55a9ad4}\index{view\_record.php@{view\_record.php}!\$completedItems@{\$completedItems}}
\index{\$completedItems@{\$completedItems}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{\$completedItems}{\$completedItems}}
{\footnotesize\ttfamily \label{view__record_8php_a4fc34755212ee7f3d75abc75c55a9ad4} 
\$completed\+Items = \mbox{[}$\,$\mbox{]}}

\Hypertarget{view__record_8php_a27ae3a2b96c15043fb33a4686ae3f15f}\index{view\_record.php@{view\_record.php}!\$countResult@{\$countResult}}
\index{\$countResult@{\$countResult}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{\$countResult}{\$countResult}}
{\footnotesize\ttfamily \label{view__record_8php_a27ae3a2b96c15043fb33a4686ae3f15f} 
\$count\+Result = \$db-\/$>$single()}

\Hypertarget{view__record_8php_a1fa3127fc82f96b1436d871ef02be319}\index{view\_record.php@{view\_record.php}!\$db@{\$db}}
\index{\$db@{\$db}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{\$db}{\$db}}
{\footnotesize\ttfamily \label{view__record_8php_a1fa3127fc82f96b1436d871ef02be319} 
\$db = new \mbox{\hyperlink{class_database}{Database}}()}

\Hypertarget{view__record_8php_ab690e0531aea866478f7d205048d16c3}\index{view\_record.php@{view\_record.php}!\$guidelineCount@{\$guidelineCount}}
\index{\$guidelineCount@{\$guidelineCount}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{\$guidelineCount}{\$guidelineCount}}
{\footnotesize\ttfamily \label{view__record_8php_ab690e0531aea866478f7d205048d16c3} 
\$guideline\+Count = \$count\+Result-\/$>$count}

\Hypertarget{view__record_8php_a5a164a6a39728ebceba09a9a68a15839}\index{view\_record.php@{view\_record.php}!\$guidelineDetails@{\$guidelineDetails}}
\index{\$guidelineDetails@{\$guidelineDetails}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{\$guidelineDetails}{\$guidelineDetails}}
{\footnotesize\ttfamily \label{view__record_8php_a5a164a6a39728ebceba09a9a68a15839} 
\$guideline\+Details = \mbox{[}$\,$\mbox{]}}

\Hypertarget{view__record_8php_a8ba3d54e0f8c662f4482d113c94577e6}\index{view\_record.php@{view\_record.php}!\$guidelineId@{\$guidelineId}}
\index{\$guidelineId@{\$guidelineId}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{\$guidelineId}{\$guidelineId}}
{\footnotesize\ttfamily \label{view__record_8php_a8ba3d54e0f8c662f4482d113c94577e6} 
\$guideline\+Id = \$guideline-\/$>$guideline\+\_\+id ?? \$guideline-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}}

\Hypertarget{view__record_8php_a2adffdd84d6ab1ab2ce17da57327f4fc}\index{view\_record.php@{view\_record.php}!\$implementedGuidelines@{\$implementedGuidelines}}
\index{\$implementedGuidelines@{\$implementedGuidelines}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{\$implementedGuidelines}{\$implementedGuidelines}}
{\footnotesize\ttfamily \label{view__record_8php_a2adffdd84d6ab1ab2ce17da57327f4fc} 
\$implemented\+Guidelines = \$db-\/$>$result\+Set()}

\Hypertarget{view__record_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b}\index{view\_record.php@{view\_record.php}!\$importanceClass@{\$importanceClass}}
\index{\$importanceClass@{\$importanceClass}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{\$importanceClass}{\$importanceClass}}
{\footnotesize\ttfamily \label{view__record_8php_a9b4b8d3eb38c434be02d3e95ff1fb83b} 
\$importance\+Class = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}}

\Hypertarget{view__record_8php_aab9b724306b055e8e4ed6d1e1f1653f1}\index{view\_record.php@{view\_record.php}!\$statusClass@{\$statusClass}}
\index{\$statusClass@{\$statusClass}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{\$statusClass}{\$statusClass}}
{\footnotesize\ttfamily \label{view__record_8php_aab9b724306b055e8e4ed6d1e1f1653f1} 
\$status\+Class = \textquotesingle{}bg-\/gray-\/100 text-\/gray-\/800\textquotesingle{}}

\Hypertarget{view__record_8php_ad91b289b04487d30b11cd3dc57b4dee5}\index{view\_record.php@{view\_record.php}!\$typeClass@{\$typeClass}}
\index{\$typeClass@{\$typeClass}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{\$typeClass}{\$typeClass}}
{\footnotesize\ttfamily \label{view__record_8php_ad91b289b04487d30b11cd3dc57b4dee5} 
\$type\+Class = \textquotesingle{}bg-\/blue-\/100 text-\/blue-\/800\textquotesingle{}}

\Hypertarget{view__record_8php_a844929e9dc05dd77d3b6b756032a6d16}\index{view\_record.php@{view\_record.php}!("!empty( \$guidelineDetails))@{("!empty( \$guidelineDetails))}}
\index{("!empty( \$guidelineDetails))@{("!empty( \$guidelineDetails))}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{("!empty( \$guidelineDetails))}{(!empty( \$guidelineDetails))}}
{\footnotesize\ttfamily \label{view__record_8php_a844929e9dc05dd77d3b6b756032a6d16} 
\mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}}(!empty(\$implemented\+Guidelines)) \mbox{\hyperlink{bootstrap_8php_af75becf76e03572890c9841a9295e925}{if}} (!empty(\$guideline\+Details))(\$guideline\+Details as \$index=$>$ \$guideline) (\begin{DoxyParamCaption}\item[{!}]{empty}{ \$guideline\+Details}\end{DoxyParamCaption})}

\Hypertarget{view__record_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{view\_record.php@{view\_record.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{view__record_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{view__record_8php_a672d9707ef91db026c210f98cc601123}\index{view\_record.php@{view\_record.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{view__record_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{view__record_8php_a82cd33ca97ff99f2fcc5e9c81d65251b}\index{view\_record.php@{view\_record.php}!endif@{endif}}
\index{endif@{endif}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{endif}{endif}}
{\footnotesize\ttfamily \label{view__record_8php_a82cd33ca97ff99f2fcc5e9c81d65251b} 
endif}

\Hypertarget{view__record_8php_a65f6f567bea1128b76e0c5c69ab57907}\index{view\_record.php@{view\_record.php}!foreach@{foreach}}
\index{foreach@{foreach}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{foreach}{foreach}}
{\footnotesize\ttfamily \label{view__record_8php_a65f6f567bea1128b76e0c5c69ab57907} 
foreach(\$completed\+Items as \$item) (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[}\textquotesingle{}completed\+\_\+checklist\+\_\+items\textquotesingle{}\mbox{]}, }\item[{}]{\$items}{}\end{DoxyParamCaption})}

\Hypertarget{view__record_8php_a65afead519af968d2d7dc5397dab937e}\index{view\_record.php@{view\_record.php}!if@{if}}
\index{if@{if}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{view__record_8php_a65afead519af968d2d7dc5397dab937e} 
if(isset(\$\+\_\+\+GET\mbox{[}\textquotesingle{}debug\textquotesingle{}\mbox{]}) \&\& \$\+\_\+\+GET\mbox{[}\textquotesingle{}debug\textquotesingle{}\mbox{]}==1) (\begin{DoxyParamCaption}\item[{isset(\$\+\_\+\+GET\mbox{[}\textquotesingle{}debug\textquotesingle{}\mbox{]}) \&\&}]{\$\+\_\+\+GET}{\mbox{[} \textquotesingle{}debug\textquotesingle{}\mbox{]} = {\ttfamily =~1}}\end{DoxyParamCaption})}

\Hypertarget{view__record_8php_ac3b4e985ec4050d6034cf1af22dfda1f}\index{view\_record.php@{view\_record.php}!switch@{switch}}
\index{switch@{switch}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{switch}{switch}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily \label{view__record_8php_ac3b4e985ec4050d6034cf1af22dfda1f} 
switch(\$guideline-\/$>$importance) (\begin{DoxyParamCaption}\item[{}]{\$data-\/$>$}{\mbox{[} \textquotesingle{}maintenance\+\_\+record\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{view__record_8php_a59fb42f3b0dc7ba1d0e2a57b6dd4cf4d}\index{view\_record.php@{view\_record.php}!switch@{switch}}
\index{switch@{switch}!view\_record.php@{view\_record.php}}
\doxysubsubsection{\texorpdfstring{switch}{switch}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily \label{view__record_8php_a59fb42f3b0dc7ba1d0e2a57b6dd4cf4d} 
switch(\$data\mbox{[}\textquotesingle{}maintenance\+\_\+record\textquotesingle{}\mbox{]}-\/$>$status) (\begin{DoxyParamCaption}\item[{}]{\$data-\/$>$}{\mbox{[} \textquotesingle{}maintenance\+\_\+record\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

