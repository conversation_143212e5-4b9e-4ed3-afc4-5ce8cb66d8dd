\doxysection{app/views/compliance/asset.php File Reference}
\hypertarget{views_2compliance_2_asset_8php}{}\label{views_2compliance_2_asset_8php}\index{app/views/compliance/asset.php@{app/views/compliance/asset.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}}(\$data\mbox{[}\textquotesingle{}controls\+\_\+with\+\_\+status\textquotesingle{}\mbox{]} as \$item) \mbox{\hyperlink{views_2compliance_2_asset_8php_abc855ff8c77ebcc4df83e516aeeb260c}{\$status}} = \$item\mbox{[}\textquotesingle{}status\textquotesingle{}\mbox{]}
\item 
\mbox{\hyperlink{views_2compliance_2_asset_8php_aab9b724306b055e8e4ed6d1e1f1653f1}{\$status\+Class}} = \textquotesingle{}bg-\/yellow-\/100 text-\/yellow-\/800\textquotesingle{}
\item 
\mbox{\hyperlink{views_2compliance_2_asset_8php_ac51d4079cb386b1268110b732f7f9405}{\$status\+Text}} = \textquotesingle{}In Progress\textquotesingle{}
\item 
\mbox{\hyperlink{views_2compliance_2_asset_8php_a923f59a44929874949e3efc46150087a}{if}} ( \$status)
\item 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \mbox{\hyperlink{views_2compliance_2_asset_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}{\+\_\+\+\_\+pad0\+\_\+\+\_\+}}
\item 
\mbox{\hyperlink{views_2compliance_2_asset_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{views_2compliance_2_asset_8php_abc855ff8c77ebcc4df83e516aeeb260c}\index{asset.php@{asset.php}!\$status@{\$status}}
\index{\$status@{\$status}!asset.php@{asset.php}}
\doxysubsubsection{\texorpdfstring{\$status}{\$status}}
{\footnotesize\ttfamily \label{views_2compliance_2_asset_8php_abc855ff8c77ebcc4df83e516aeeb260c} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} ( \$data\mbox{[} \textquotesingle{}controls\+\_\+with\+\_\+status\textquotesingle{}\mbox{]} as \$item) \$status = \$item\mbox{[}\textquotesingle{}status\textquotesingle{}\mbox{]}}

\Hypertarget{views_2compliance_2_asset_8php_aab9b724306b055e8e4ed6d1e1f1653f1}\index{asset.php@{asset.php}!\$statusClass@{\$statusClass}}
\index{\$statusClass@{\$statusClass}!asset.php@{asset.php}}
\doxysubsubsection{\texorpdfstring{\$statusClass}{\$statusClass}}
{\footnotesize\ttfamily \label{views_2compliance_2_asset_8php_aab9b724306b055e8e4ed6d1e1f1653f1} 
\$status\+Class = \textquotesingle{}bg-\/yellow-\/100 text-\/yellow-\/800\textquotesingle{}}

\Hypertarget{views_2compliance_2_asset_8php_ac51d4079cb386b1268110b732f7f9405}\index{asset.php@{asset.php}!\$statusText@{\$statusText}}
\index{\$statusText@{\$statusText}!asset.php@{asset.php}}
\doxysubsubsection{\texorpdfstring{\$statusText}{\$statusText}}
{\footnotesize\ttfamily \label{views_2compliance_2_asset_8php_ac51d4079cb386b1268110b732f7f9405} 
\$status\+Text = \textquotesingle{}In Progress\textquotesingle{}}

\Hypertarget{views_2compliance_2_asset_8php_a8e01dcc96c43199448ee66f7c2ae8ea6}\index{asset.php@{asset.php}!\_\_pad0\_\_@{\_\_pad0\_\_}}
\index{\_\_pad0\_\_@{\_\_pad0\_\_}!asset.php@{asset.php}}
\doxysubsubsection{\texorpdfstring{\_\_pad0\_\_}{\_\_pad0\_\_}}
{\footnotesize\ttfamily \label{views_2compliance_2_asset_8php_a8e01dcc96c43199448ee66f7c2ae8ea6} 
\mbox{\hyperlink{create__guideline__implementation__table_8php_ac3cd95c062d95e026a5559fcf9d8a3bf}{else}} \+\_\+\+\_\+pad0\+\_\+\+\_\+}

\Hypertarget{views_2compliance_2_asset_8php_a672d9707ef91db026c210f98cc601123}\index{asset.php@{asset.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!asset.php@{asset.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{views_2compliance_2_asset_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

\Hypertarget{views_2compliance_2_asset_8php_a923f59a44929874949e3efc46150087a}\index{asset.php@{asset.php}!if@{if}}
\index{if@{if}!asset.php@{asset.php}}
\doxysubsubsection{\texorpdfstring{if}{if}}
{\footnotesize\ttfamily \label{views_2compliance_2_asset_8php_a923f59a44929874949e3efc46150087a} 
if(\$status) (\begin{DoxyParamCaption}\item[{}]{\$status}{}\end{DoxyParamCaption})}

