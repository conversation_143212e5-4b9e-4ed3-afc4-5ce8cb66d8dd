\doxysection{app/views/users/roles.php File Reference}
\hypertarget{views_2users_2_roles_8php}{}\label{views_2users_2_roles_8php}\index{app/views/users/roles.php@{app/views/users/roles.php}}
\doxysubsubsection*{Variables}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} \mbox{\hyperlink{views_2users_2_roles_8php_a490f231e34fb2bad81c67a2270d9fbd0}{( \$data\mbox{[} \textquotesingle{}all\+Roles\textquotesingle{}\mbox{]} as \$role)}} ( \$role-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}, \$data\mbox{[} \textquotesingle{}user\+Role\+Ids\textquotesingle{}\mbox{]}) ? \textquotesingle{}checked\textquotesingle{}
\item 
\mbox{\hyperlink{views_2users_2_roles_8php_a672d9707ef91db026c210f98cc601123}{endforeach}}
\end{DoxyCompactItemize}


\doxysubsection{Variable Documentation}
\Hypertarget{views_2users_2_roles_8php_a490f231e34fb2bad81c67a2270d9fbd0}\index{roles.php@{roles.php}!( \$data\mbox{[} \textquotesingle{}allRoles\textquotesingle{}\mbox{]} as \$role)@{( \$data[ \textquotesingle{}allRoles\textquotesingle{}] as \$role)}}
\index{( \$data\mbox{[} \textquotesingle{}allRoles\textquotesingle{}\mbox{]} as \$role)@{( \$data[ \textquotesingle{}allRoles\textquotesingle{}] as \$role)}!roles.php@{roles.php}}
\doxysubsubsection{\texorpdfstring{( \$data[ \textquotesingle{}allRoles\textquotesingle{}] as \$role)}{( \$data[ 'allRoles'] as \$role)}}
{\footnotesize\ttfamily \label{views_2users_2_roles_8php_a490f231e34fb2bad81c67a2270d9fbd0} 
\mbox{\hyperlink{report_8php_a52b109dcfbeb9d1d9daaacdd457d3021}{foreach}} (\$data\mbox{[}\textquotesingle{}all\+Roles\textquotesingle{}\mbox{]} as \$role)(\$role-\/$>$\mbox{\hyperlink{maintenance_2add_8php_a0bee1c6028cca051cae04a7f46b36ab4}{id}}, \$data\mbox{[}\textquotesingle{}user\+Role\+Ids\textquotesingle{}\mbox{]}) ? \textquotesingle{}checked\textquotesingle{} (\begin{DoxyParamCaption}\item[{}]{\$data as}{\mbox{[} \textquotesingle{}all\+Roles\textquotesingle{}\mbox{]}}\end{DoxyParamCaption})}

\Hypertarget{views_2users_2_roles_8php_a672d9707ef91db026c210f98cc601123}\index{roles.php@{roles.php}!endforeach@{endforeach}}
\index{endforeach@{endforeach}!roles.php@{roles.php}}
\doxysubsubsection{\texorpdfstring{endforeach}{endforeach}}
{\footnotesize\ttfamily \label{views_2users_2_roles_8php_a672d9707ef91db026c210f98cc601123} 
endforeach}

