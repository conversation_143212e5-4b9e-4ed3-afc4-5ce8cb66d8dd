<?php
/**
 * Fix Guideline Implementation Table Script
 * 
 * This script ensures that the maintenance_guideline_implementation table exists
 * and has the correct structure. It also checks for and fixes any data integrity issues.
 */

// Initialize
$output = [];
$output[] = "<h1>Maintenance Guideline Implementation Table Fix</h1>";
$output[] = "<p>Running checks and fixes for the maintenance_guideline_implementation table...</p>";

// Database connection parameters
require_once 'app/config/config.php';

try {
    // Connect to database
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    
    $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
    $output[] = "<p style='color:green'>Database connection successful.</p>";
    
    // Check if table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        $output[] = "<p>Table does not exist. Creating the table...</p>";
        
        // Create the table with the correct structure (no UNIQUE constraint)
        $sql = "CREATE TABLE IF NOT EXISTS maintenance_guideline_implementation (
            id INT AUTO_INCREMENT PRIMARY KEY,
            maintenance_id INT NOT NULL,
            guideline_id INT NOT NULL,
            implemented_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_maintenance_guideline (maintenance_id, guideline_id)
        ) ENGINE=InnoDB";
        
        $pdo->exec($sql);
        $output[] = "<p style='color:green'>Table created successfully with INDEX (not UNIQUE).</p>";
    } else {
        $output[] = "<p style='color:green'>maintenance_guideline_implementation table exists.</p>";
        
        // Check for records
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM maintenance_guideline_implementation");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $output[] = "<p>Number of records: <strong>" . $result['count'] . "</strong></p>";
        
        // Check if the table has the correct structure
        $stmt = $pdo->query("SHOW CREATE TABLE maintenance_guideline_implementation");
        $tableDefinition = $stmt->fetch(PDO::FETCH_ASSOC);
        $createTableStatement = $tableDefinition['Create Table'] ?? '';
        
        // Check if the table has a UNIQUE constraint that should be removed
        if (strpos($createTableStatement, 'UNIQUE KEY') !== false) {
            $output[] = "<p style='color:orange'>Warning: Table has a UNIQUE KEY constraint that may prevent multiple implementations of the same guideline. Attempting to fix...</p>";
            
            // Get the current data
            $stmt = $pdo->query("SELECT * FROM maintenance_guideline_implementation");
            $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $output[] = "<p>Backed up " . count($records) . " records.</p>";
            
            // Create a new table with the correct structure
            try {
                $sql = "CREATE TABLE maintenance_guideline_implementation_new (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    maintenance_id INT NOT NULL,
                    guideline_id INT NOT NULL,
                    implemented_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_maintenance_guideline (maintenance_id, guideline_id)
                )";
                $pdo->exec($sql);
                $output[] = "<p style='color:green'>Created new table with correct structure.</p>";
                
                // Copy data to the new table
                if (count($records) > 0) {
                    $insertSql = "INSERT INTO maintenance_guideline_implementation_new 
                                 (maintenance_id, guideline_id, implemented_date) VALUES ";
                    $values = [];
                    $params = [];
                    
                    foreach ($records as $i => $record) {
                        $values[] = "(:maintenance_id_$i, :guideline_id_$i, :implemented_date_$i)";
                        $params["maintenance_id_$i"] = $record['maintenance_id'];
                        $params["guideline_id_$i"] = $record['guideline_id'];
                        $params["implemented_date_$i"] = $record['implemented_date'];
                    }
                    
                    $insertSql .= implode(', ', $values);
                    $stmt = $pdo->prepare($insertSql);
                    $stmt->execute($params);
                    $output[] = "<p style='color:green'>Copied " . count($records) . " records to new table.</p>";
                }
                
                // Rename tables
                $pdo->exec("DROP TABLE maintenance_guideline_implementation");
                $pdo->exec("RENAME TABLE maintenance_guideline_implementation_new TO maintenance_guideline_implementation");
                $output[] = "<p style='color:green'>Replaced old table with new table.</p>";
                
            } catch (Exception $e) {
                $output[] = "<p style='color:red'>Error fixing table structure: " . $e->getMessage() . "</p>";
            }
        } else {
            $output[] = "<p style='color:green'>Table structure looks good (no UNIQUE constraint).</p>";
        }
    }
    
    // Check for orphaned records (no corresponding maintenance_history or guideline)
    $stmt = $pdo->query("SELECT i.*, g.name as guideline_name
                         FROM maintenance_guideline_implementation i
                         LEFT JOIN maintenance_history m ON i.maintenance_id = m.id
                         LEFT JOIN maintenance_guidelines g ON i.guideline_id = g.id
                         WHERE m.id IS NULL OR g.id IS NULL");
    $orphanedRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($orphanedRecords) > 0) {
        $output[] = "<p style='color:orange'>Warning: Found " . count($orphanedRecords) . " orphaned records (missing maintenance history or guideline).</p>";
        $output[] = "<p>You can fix these records using the Data Integrity Check tool in the maintenance section.</p>";
    } else {
        $output[] = "<p style='color:green'>No orphaned records found. Data integrity looks good.</p>";
    }
    
    // Final check to ensure the table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        $output[] = "<h2 style='color:green'>✓ Success: The maintenance_guideline_implementation table is properly set up.</h2>";
        $output[] = "<p>You should now be able to view guideline implementation history correctly.</p>";
    } else {
        $output[] = "<h2 style='color:red'>✗ Error: Failed to create or verify the maintenance_guideline_implementation table.</h2>";
        $output[] = "<p>Please contact your system administrator for assistance.</p>";
    }
    
} catch (PDOException $e) {
    $output[] = "<h2 style='color:red'>Database Error</h2>";
    $output[] = "<p>" . $e->getMessage() . "</p>";
}

// Display output
echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Guideline Implementation Table</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
        h1 { color: #333; }
        h2 { margin-top: 20px; }
        p { margin: 10px 0; }
    </style>
</head>
<body>";

foreach ($output as $line) {
    echo $line . "\n";
}

echo "<p><a href='maintenance/guidelines'>Return to Guidelines</a></p>";
echo "</body></html>";
