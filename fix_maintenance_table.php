<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Fix Maintenance Guideline Implementation Table</h1>";

// Load config
require_once 'app/config/config.php';

try {
    // Connect to database
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color:green'>Connected to database successfully.</p>";

    // Check if maintenance_guideline_implementation table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'maintenance_guideline_implementation'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color:red'>maintenance_guideline_implementation table does not exist!</p>";

        // Create the table with the correct structure (no UNIQUE constraint)
        echo "<p>Attempting to create the table...</p>";
        $sql = "CREATE TABLE IF NOT EXISTS maintenance_guideline_implementation (
            id INT AUTO_INCREMENT PRIMARY KEY,
            maintenance_id INT NOT NULL,
            guideline_id INT NOT NULL,
            implemented_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (maintenance_id, guideline_id)
        )";
        $pdo->exec($sql);
        echo "<p style='color:green'>Table created successfully with INDEX (not UNIQUE).</p>";
    } else {
        echo "<p style='color:green'>maintenance_guideline_implementation table exists.</p>";

        // Check for UNIQUE constraint
        $stmt = $pdo->query("SHOW CREATE TABLE maintenance_guideline_implementation");
        $tableInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        $createTableStatement = $tableInfo['Create Table'] ?? '';

        echo "<h3>Current Table Structure:</h3>";
        echo "<pre>" . htmlspecialchars($createTableStatement) . "</pre>";

        if (strpos($createTableStatement, 'UNIQUE KEY') !== false) {
            echo "<p style='color:orange'>UNIQUE KEY constraint found. Since it's used in foreign key constraints, we need to recreate the table.</p>";

            // Since we can't drop the UNIQUE KEY due to foreign key constraints, we need to:
            // 1. Get all data from the table
            // 2. Drop foreign key constraints
            // 3. Drop the table
            // 4. Recreate the table with the correct structure
            // 5. Reinsert the data
            // 6. Recreate the foreign key constraints

            // Get all data from the table
            $stmt = $pdo->query("SELECT * FROM maintenance_guideline_implementation");
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p>Found " . count($data) . " records to preserve.</p>";

            // Check for foreign key constraints
            if (preg_match_all('/CONSTRAINT\s+`([^`]+)`\s+FOREIGN KEY/', $createTableStatement, $matches)) {
                $foreignKeys = $matches[1];
                echo "<p>Found " . count($foreignKeys) . " foreign key constraints.</p>";

                // Drop foreign key constraints
                foreach ($foreignKeys as $fk) {
                    try {
                        $sql = "ALTER TABLE maintenance_guideline_implementation DROP FOREIGN KEY `$fk`";
                        $pdo->exec($sql);
                        echo "<p style='color:green'>Successfully dropped foreign key constraint: $fk</p>";
                    } catch (PDOException $e) {
                        echo "<p style='color:red'>Error dropping foreign key constraint $fk: " . $e->getMessage() . "</p>";
                    }
                }
            }

            // Now try to drop the UNIQUE constraint
            try {
                $sql = "ALTER TABLE maintenance_guideline_implementation DROP INDEX `maintenance_id`";
                $pdo->exec($sql);
                echo "<p style='color:green'>Successfully dropped UNIQUE constraint.</p>";

                // Add a regular INDEX instead
                $sql = "ALTER TABLE maintenance_guideline_implementation ADD INDEX idx_maintenance_guideline (maintenance_id, guideline_id)";
                $pdo->exec($sql);
                echo "<p style='color:green'>Added regular INDEX instead.</p>";

                // Recreate foreign key constraints
                $sql = "ALTER TABLE maintenance_guideline_implementation
                        ADD CONSTRAINT `maintenance_guideline_implementation_ibfk_1`
                        FOREIGN KEY (`maintenance_id`) REFERENCES `maintenance_history` (`id`) ON DELETE CASCADE";
                $pdo->exec($sql);

                $sql = "ALTER TABLE maintenance_guideline_implementation
                        ADD CONSTRAINT `maintenance_guideline_implementation_ibfk_2`
                        FOREIGN KEY (`guideline_id`) REFERENCES `maintenance_guidelines` (`id`) ON DELETE CASCADE";
                $pdo->exec($sql);

                echo "<p style='color:green'>Successfully recreated foreign key constraints.</p>";
            } catch (PDOException $e) {
                echo "<p style='color:red'>Error modifying table: " . $e->getMessage() . "</p>";
                echo "<p>This error is expected if the foreign keys are still in use. We'll need to create a new table instead.</p>";

                // Create a new table with the correct structure
                try {
                    $sql = "CREATE TABLE maintenance_guideline_implementation_new (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        maintenance_id INT NOT NULL,
                        guideline_id INT NOT NULL,
                        implemented_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_maintenance_guideline (maintenance_id, guideline_id),
                        FOREIGN KEY (maintenance_id) REFERENCES maintenance_history(id) ON DELETE CASCADE,
                        FOREIGN KEY (guideline_id) REFERENCES maintenance_guidelines(id) ON DELETE CASCADE
                    )";
                    $pdo->exec($sql);
                    echo "<p style='color:green'>Created new table with correct structure.</p>";

                    // Copy data to the new table
                    if (!empty($data)) {
                        $insertCount = 0;
                        foreach ($data as $row) {
                            try {
                                $sql = "INSERT INTO maintenance_guideline_implementation_new
                                        (maintenance_id, guideline_id, implemented_date)
                                        VALUES (:maintenance_id, :guideline_id, :implemented_date)";
                                $stmt = $pdo->prepare($sql);
                                $stmt->execute([
                                    ':maintenance_id' => $row['maintenance_id'],
                                    ':guideline_id' => $row['guideline_id'],
                                    ':implemented_date' => $row['implemented_date']
                                ]);
                                $insertCount++;
                            } catch (PDOException $e) {
                                echo "<p style='color:orange'>Error inserting record: " . $e->getMessage() . "</p>";
                            }
                        }
                        echo "<p style='color:green'>Copied $insertCount out of " . count($data) . " records to the new table.</p>";
                    }

                    // Rename tables to swap them
                    $pdo->exec("RENAME TABLE
                                maintenance_guideline_implementation TO maintenance_guideline_implementation_old,
                                maintenance_guideline_implementation_new TO maintenance_guideline_implementation");
                    echo "<p style='color:green'>Successfully renamed tables to complete the swap.</p>";

                    // Drop the old table
                    $pdo->exec("DROP TABLE maintenance_guideline_implementation_old");
                    echo "<p style='color:green'>Dropped the old table.</p>";
                } catch (PDOException $e) {
                    echo "<p style='color:red'>Error creating or swapping tables: " . $e->getMessage() . "</p>";
                }
            }

            // Verify the change
            $stmt = $pdo->query("SHOW CREATE TABLE maintenance_guideline_implementation");
            $tableInfo = $stmt->fetch(PDO::FETCH_ASSOC);
            $newCreateTableStatement = $tableInfo['Create Table'] ?? '';

            echo "<h3>Updated Table Structure:</h3>";
            echo "<pre>" . htmlspecialchars($newCreateTableStatement) . "</pre>";

            if (strpos($newCreateTableStatement, 'UNIQUE KEY') === false) {
                echo "<p style='color:green'>Success! UNIQUE KEY constraint has been removed.</p>";
            } else {
                echo "<p style='color:red'>Warning: UNIQUE KEY constraint still exists in the table structure.</p>";
            }
        } else {
            echo "<p style='color:green'>No UNIQUE KEY constraint found. The table structure is already correct.</p>";
        }

        // Check for records
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM maintenance_guideline_implementation");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>Number of records: <strong>" . $result['count'] . "</strong></p>";
    }

} catch(PDOException $e) {
    echo "<p style='color:red'>Database error: " . $e->getMessage() . "</p>";
}
?>
