<?php
// Load config
require_once 'app/config/config.php';

// Connect to database
try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Connected to database successfully.\n";
} catch(PDOException $e) {
    die("ERROR: Could not connect. " . $e->getMessage());
}

// Path to CSV file
$csvFile = 'FO II _ICT ASSET INVENTORY.csv';

// Check if file exists
if (!file_exists($csvFile)) {
    die("CSV file not found: $csvFile");
}

// Open the CSV file
$file = fopen($csvFile, 'r');

// Skip header rows (first 7 rows)
for ($i = 0; $i < 7; $i++) {
    fgetcsv($file);
}

// Prepare SQL statement
$sql = "INSERT INTO assets (inventory_date, site_name, employee_name, active_directory_name, position, program_section, computer_host_name, equipment_type, acquisition_type, operating_system, administration_type, xdr_installed, device_custodian, remarks, par_number, serial_number, acquisition_date, estimated_useful_life) 
        VALUES (:inventory_date, :site_name, :employee_name, :active_directory_name, :position, :program_section, :computer_host_name, :equipment_type, :acquisition_type, :operating_system, :administration_type, :xdr_installed, :device_custodian, :remarks, :par_number, :serial_number, :acquisition_date, :estimated_useful_life)";

$stmt = $pdo->prepare($sql);

// Counter for imported records
$importCount = 0;

// Process each row
while (($row = fgetcsv($file)) !== FALSE) {
    // Skip empty rows
    if (empty($row[0]) && empty($row[1]) && empty($row[2])) {
        continue;
    }
    
    // Map CSV columns to database fields
    $data = [
        'inventory_date' => !empty($row[0]) ? date('Y-m-d', strtotime($row[0])) : NULL,
        'site_name' => !empty($row[1]) ? $row[1] : NULL,
        'employee_name' => !empty($row[2]) ? $row[2] : NULL,
        'active_directory_name' => !empty($row[3]) ? $row[3] : NULL,
        'position' => !empty($row[4]) ? $row[4] : NULL,
        'program_section' => !empty($row[5]) ? $row[5] : NULL,
        'computer_host_name' => !empty($row[6]) ? $row[6] : NULL,
        'equipment_type' => !empty($row[7]) ? $row[7] : NULL,
        'acquisition_type' => !empty($row[8]) ? $row[8] : NULL,
        'operating_system' => !empty($row[9]) ? $row[9] : NULL,
        'administration_type' => !empty($row[10]) ? $row[10] : NULL,
        'xdr_installed' => !empty($row[11]) ? $row[11] : NULL,
        'device_custodian' => !empty($row[12]) ? $row[12] : NULL,
        'remarks' => !empty($row[13]) ? $row[13] : NULL,
        'par_number' => !empty($row[14]) ? $row[14] : NULL,
        'serial_number' => !empty($row[15]) ? $row[15] : NULL,
        'acquisition_date' => !empty($row[16]) ? date('Y-m-d', strtotime($row[16])) : NULL,
        'estimated_useful_life' => !empty($row[17]) ? date('Y-m-d', strtotime($row[17])) : NULL
    ];
    
    // Skip if essential fields are missing
    if (empty($data['computer_host_name']) || empty($data['equipment_type']) || empty($data['serial_number'])) {
        continue;
    }
    
    try {
        // Execute the statement
        $stmt->execute($data);
        $importCount++;
        echo "Imported asset: {$data['computer_host_name']}\n";
    } catch(PDOException $e) {
        echo "ERROR: Could not import asset {$data['computer_host_name']}. " . $e->getMessage() . "\n";
    }
}

// Close the file
fclose($file);

echo "Import completed. Total records imported: $importCount\n";
