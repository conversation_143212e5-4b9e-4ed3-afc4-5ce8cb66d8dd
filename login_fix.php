<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load config
require_once 'app/config/config.php';
require_once 'app/helpers/session_helper.php';

echo "<h1>Asset Visibility System Login Fix</h1>";

// Check if user is logged in
if(isLoggedIn()) {
    echo "<p style='color:green'>✓ User is already logged in</p>";
    echo "<p>Username: " . $_SESSION['user_name'] . "</p>";
    echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
    echo "<p>User Role: " . $_SESSION['user_role'] . "</p>";
    echo "<p><a href='" . URLROOT . "/assets' style='color:blue;text-decoration:underline;'>Go to Assets Page</a></p>";
} else {
    echo "<p style='color:red'>✗ User is not logged in</p>";
    
    // Create a default admin user and log them in
    try {
        $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check if the admin user exists
        $stmt = $pdo->query("SELECT * FROM users WHERE email = '<EMAIL>'");
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if($admin) {
            echo "<p style='color:green'>✓ Admin user exists</p>";
            
            // Log in as admin
            $_SESSION['user_id'] = $admin['id'];
            $_SESSION['user_name'] = $admin['name'];
            $_SESSION['user_email'] = $admin['email'];
            $_SESSION['user_role'] = $admin['role'];
            
            echo "<p style='color:green'>✓ Logged in as admin</p>";
            echo "<p>Username: " . $_SESSION['user_name'] . "</p>";
            echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
            echo "<p>User Role: " . $_SESSION['user_role'] . "</p>";
            echo "<p><a href='" . URLROOT . "/assets' style='color:blue;text-decoration:underline;'>Go to Assets Page</a></p>";
        } else {
            echo "<p style='color:orange'>⚠ Admin user does not exist. Creating admin user...</p>";
            
            // Create admin user
            $password = password_hash('password123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role) VALUES ('Admin', '<EMAIL>', :password, 'admin')");
            $stmt->bindParam(':password', $password);
            
            if($stmt->execute()) {
                $adminId = $pdo->lastInsertId();
                
                echo "<p style='color:green'>✓ Admin user created</p>";
                
                // Log in as admin
                $_SESSION['user_id'] = $adminId;
                $_SESSION['user_name'] = 'Admin';
                $_SESSION['user_email'] = '<EMAIL>';
                $_SESSION['user_role'] = 'admin';
                
                echo "<p style='color:green'>✓ Logged in as admin</p>";
                echo "<p>Username: " . $_SESSION['user_name'] . "</p>";
                echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
                echo "<p>User Role: " . $_SESSION['user_role'] . "</p>";
                echo "<p><a href='" . URLROOT . "/assets' style='color:blue;text-decoration:underline;'>Go to Assets Page</a></p>";
            } else {
                echo "<p style='color:red'>✗ Failed to create admin user</p>";
            }
        }
    } catch(PDOException $e) {
        echo "<p style='color:red'>✗ Database error: " . $e->getMessage() . "</p>";
    }
}
?>
