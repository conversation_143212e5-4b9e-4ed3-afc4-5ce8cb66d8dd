# Nginx Installation Guide for Asset Visibility System

This guide will help you set up the Asset Visibility System with <PERSON>in<PERSON> as the web server.

## Prerequisites

- Nginx installed on your server
- PHP 8.0 or higher with PHP-FPM
- MySQL or MariaDB
- The Asset Visibility System codebase

## Installation Steps

### 1. Install Nginx and PHP-FPM

```bash
# For Ubuntu/Debian
sudo apt update
sudo apt install nginx php-fpm php-mysql php-mbstring php-xml php-curl

# For CentOS/RHEL
sudo yum install epel-release
sudo yum install nginx php-fpm php-mysql php-mbstring php-xml php-curl
```

### 2. Configure PHP-FPM

Check your PHP-FPM version and configuration:

```bash
php -v
```

Edit the PHP-FPM configuration if needed:

```bash
# For Ubuntu/Debian (adjust the PHP version as needed)
sudo nano /etc/php/8.0/fpm/php.ini

# For CentOS/RHEL
sudo nano /etc/php.ini
```

Recommended PHP settings:

```ini
upload_max_filesize = 20M
post_max_size = 20M
memory_limit = 256M
max_execution_time = 120
date.timezone = "Your/Timezone"  # e.g., "America/New_York"
```

Restart PHP-FPM:

```bash
# For Ubuntu/Debian
sudo systemctl restart php8.0-fpm

# For CentOS/RHEL
sudo systemctl restart php-fpm
```

### 3. Configure Nginx

Copy the provided Nginx configuration file to the Nginx sites directory:

```bash
# For Ubuntu/Debian
sudo cp nginx/asset_visibility.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/asset_visibility.conf /etc/nginx/sites-enabled/

# For CentOS/RHEL
sudo cp nginx/asset_visibility.conf /etc/nginx/conf.d/
```

Edit the configuration file to match your server setup:

```bash
# For Ubuntu/Debian
sudo nano /etc/nginx/sites-available/asset_visibility.conf

# For CentOS/RHEL
sudo nano /etc/nginx/conf.d/asset_visibility.conf
```

Make sure to update:
- `server_name` - Set to your domain or IP address
- `root` - Set to the actual path of your Asset Visibility System
- `fastcgi_pass` - Set to the correct PHP-FPM socket or TCP address

### 4. Set Up Local Host (Development Environment)

If you're setting up on a local development machine, add the domain to your hosts file:

```bash
# For Windows
# Edit C:\Windows\System32\drivers\etc\hosts
# Add: 127.0.0.1 asset_visibility.local

# For Linux/Mac
sudo nano /etc/hosts
# Add: 127.0.0.1 asset_visibility.local
```

### 5. Test and Restart Nginx

Test the Nginx configuration:

```bash
sudo nginx -t
```

If the test is successful, restart Nginx:

```bash
sudo systemctl restart nginx
```

### 6. Set Proper Permissions

Make sure Nginx can read the files and PHP-FPM can write to necessary directories:

```bash
# Adjust the user and group as needed (www-data for Ubuntu, nginx for CentOS)
sudo chown -R www-data:www-data /path/to/asset_visibility
sudo find /path/to/asset_visibility -type d -exec chmod 755 {} \;
sudo find /path/to/asset_visibility -type f -exec chmod 644 {} \;
```

### 7. Update Application Configuration

Update the `app/config/config.php` file to match your new setup:

```php
define('URLROOT', 'http://asset_visibility.local'); // Change to your domain
```

### 8. Setting Up SSL (Optional but Recommended)

Generate SSL certificates (or use Let's Encrypt):

```bash
# Using Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d asset_visibility.local
```

Or use self-signed certificates for development:

```bash
sudo mkdir -p /etc/nginx/ssl
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout /etc/nginx/ssl/asset_visibility.key -out /etc/nginx/ssl/asset_visibility.crt
```

Then uncomment the HTTPS server block in the Nginx configuration file and restart Nginx.

## Troubleshooting

### Common Issues

1. **403 Forbidden Error**
   - Check file permissions
   - Verify Nginx user has access to the files

2. **404 Not Found Error**
   - Check the `root` directive in the Nginx configuration
   - Verify the rewrite rules

3. **502 Bad Gateway Error**
   - Check if PHP-FPM is running
   - Verify the `fastcgi_pass` directive

4. **Blank Page or PHP Errors**
   - Check PHP error logs
   - Enable error reporting in PHP

### Checking Logs

```bash
# Nginx error logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/asset_visibility_error.log

# PHP-FPM logs
sudo tail -f /var/log/php8.0-fpm.log  # Adjust version as needed
```

## Security Considerations

1. **Firewall Configuration**
   - Allow only ports 80 and 443
   - Use UFW or firewalld to manage firewall rules

2. **Regular Updates**
   - Keep Nginx, PHP, and all dependencies updated

3. **Use HTTPS**
   - Always use SSL/TLS in production
   - Redirect HTTP to HTTPS

4. **Secure Headers**
   - The provided configuration includes security headers
   - Consider using additional headers as needed
