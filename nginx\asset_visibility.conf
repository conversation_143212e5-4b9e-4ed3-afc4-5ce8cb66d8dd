server {
    listen 80;
    server_name asset_visibility.local; # Change this to your domain or IP address

    # Root directory of the application
    root /var/www/html/asset_visibility;
    index index.php;

    # Logging
    access_log /var/log/nginx/asset_visibility_access.log;
    error_log /var/log/nginx/asset_visibility_error.log;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # If using HTTPS, add these headers
    # add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Main location block
    location / {
        try_files $uri $uri/ /public/index.php?$args;
    }

    # Redirect to public folder
    location = / {
        return 301 /public/;
    }

    # Handle public folder
    location /public/ {
        try_files $uri $uri/ /public/index.php?url=$uri&$args;
    }

    # Deny access to .htaccess files
    location ~ /\.ht {
        deny all;
    }

    # Deny access to hidden files
    location ~ /\. {
        deny all;
    }

    # PHP handling
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock; # Adjust this to match your PHP-FPM socket
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    # Static files caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    # Deny access to sensitive files
    location ~* \.(json|lock|git|svn|hg|env|example)$ {
        deny all;
    }

    # Deny access to app directory
    location ^~ /app/ {
        deny all;
    }

    # Deny access to vendor directory
    location ^~ /vendor/ {
        deny all;
    }
}

# HTTPS server configuration (uncomment when you have SSL certificates)
# server {
#     listen 443 ssl http2;
#     server_name asset_visibility.local;
#
#     # SSL certificates
#     ssl_certificate /etc/nginx/ssl/asset_visibility.crt;
#     ssl_certificate_key /etc/nginx/ssl/asset_visibility.key;
#
#     # SSL settings
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_prefer_server_ciphers on;
#     ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
#     ssl_session_timeout 1d;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_tickets off;
#
#     # OCSP Stapling
#     ssl_stapling on;
#     ssl_stapling_verify on;
#     resolver ******* ******* valid=300s;
#     resolver_timeout 5s;
#
#     # Security headers
#     add_header X-Frame-Options "SAMEORIGIN" always;
#     add_header X-XSS-Protection "1; mode=block" always;
#     add_header X-Content-Type-Options "nosniff" always;
#     add_header Referrer-Policy "strict-origin-when-cross-origin" always;
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
#
#     # Include the same configuration as the HTTP server
#     root /var/www/html/asset_visibility;
#     index index.php;
#
#     # Rest of the configuration is the same as the HTTP server
#     # ...
# }
