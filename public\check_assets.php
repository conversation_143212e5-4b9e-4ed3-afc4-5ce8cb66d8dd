<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Asset Table Check</h1>";

// Load config
require_once '../app/config/config.php';

try {
    // Connect to database
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color:green'>Connected to database successfully.</p>";
    
    // Check if assets table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'assets'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color:red'>Assets table does not exist!</p>";
        exit;
    }
    
    echo "<p>Assets table exists.</p>";
    
    // Check table structure
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Table Structure:</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        foreach ($column as $key => $value) {
            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Count records
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM assets");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>Record Count:</h2>";
    echo "<p>Number of assets in database: <strong>" . $result['count'] . "</strong></p>";
    
    if ($result['count'] == 0) {
        echo "<p style='color:red'><strong>No assets found in the database. This explains why no data is shown in the table.</strong></p>";
        echo "<p>You need to add assets to the database or import them from a CSV file.</p>";
    } else {
        // Show sample data
        $stmt = $pdo->query("SELECT * FROM assets LIMIT 3");
        $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h2>Sample Data:</h2>";
        echo "<table border='1' cellpadding='5'>";
        
        // Headers
        echo "<tr>";
        foreach (array_keys($assets[0]) as $header) {
            echo "<th>" . htmlspecialchars($header) . "</th>";
        }
        echo "</tr>";
        
        // Data rows
        foreach ($assets as $asset) {
            echo "<tr>";
            foreach ($asset as $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
} catch(PDOException $e) {
    echo "<p style='color:red'>Database error: " . $e->getMessage() . "</p>";
}
?>
