/* Dropdown menu fix */
.relative.group {
    position: relative;
}

.group:hover .group-hover\:block {
    display: block !important;
}

/* Ensure dropdown is above other content */
.absolute.left-0.mt-2.w-48.bg-white.rounded-md.shadow-lg.py-1.z-10.hidden.group-hover\:block {
    z-index: 50 !important;
    position: absolute !important;
}

/* Add a slight delay to prevent accidental closing */
.group:hover .group-hover\:block {
    animation-delay: 0.1s;
}

/* Add pointer events to ensure hover works properly */
.relative.group, 
.absolute.left-0.mt-2.w-48.bg-white.rounded-md.shadow-lg.py-1.z-10.hidden.group-hover\:block {
    pointer-events: auto;
}

/* Ensure the dropdown stays visible when hovering over it */
.absolute.left-0.mt-2.w-48.bg-white.rounded-md.shadow-lg.py-1.z-10.hidden.group-hover\:block:hover {
    display: block !important;
}
