body {
    font-family: '<PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
}

.btn-floating {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    text-align: center;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-floating i {
    font-size: 24px;
}

.card-asset {
    transition: all 0.3s ease;
}

.card-asset:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Enhanced search form styles */
.search-form {
    margin-bottom: 20px;
}

/* Modal animation styles */
#advanced-search-modal .transform {
    transition: transform 0.2s ease-out, opacity 0.2s ease-out;
    transform: translateY(4px);
    opacity: 0;
}

#advanced-search-modal .transform.translate-y-0 {
    transform: translateY(0);
    opacity: 1;
}

#modal-backdrop {
    transition: opacity 0.2s ease-out;
    opacity: 0;
}

#modal-backdrop.opacity-100 {
    opacity: 1;
}

/* Search input focus styles */
#search-input:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
    outline: none;
}

.asset-details dt {
    font-weight: bold;
}

.asset-details dd {
    margin-bottom: 10px;
}

.invalid-feedback {
    display: block;
}

.table-responsive {
    overflow-x: auto;
}

@media (max-width: 768px) {
    .btn-floating {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
    }
}

/* Tag styles */
.tag-item {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    margin-right: 5px;
    margin-bottom: 5px;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 14px;
}

.remove-tag-btn {
    margin-left: 5px;
    color: #ff0000;
    font-weight: bold;
    cursor: pointer;
    border: none;
    background: transparent;
}

.remove-tag-btn:hover {
    color: #cc0000;
}

.tags-container {
    margin-bottom: 10px;
}

/* Sorting styles */
.sort-link {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.sort-indicator {
    margin-left: 5px;
    font-size: 0.75em;
}

th a {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    color: inherit;
}

th a:hover {
    text-decoration: underline;
}
