@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
 
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
 
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
 
    --ring: 215 20.2% 65.1%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
 
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
 
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
 
    --ring: 217.2 32.6% 17.5%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Custom styles for ShadCN UI components */
.btn {
  @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
}

.btn-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90;
}

.btn-destructive {
  @apply bg-destructive text-destructive-foreground hover:bg-destructive/90;
}

.btn-outline {
  @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
}

.btn-ghost {
  @apply hover:bg-accent hover:text-accent-foreground;
}

.btn-link {
  @apply text-primary underline-offset-4 hover:underline;
}

.btn-lg {
  @apply h-11 rounded-md px-8;
}

.btn-sm {
  @apply h-9 rounded-md px-3;
}

.btn-icon {
  @apply h-10 w-10;
}

.card {
  @apply rounded-lg border bg-card text-card-foreground shadow-sm;
}

.card-header {
  @apply flex flex-col space-y-1.5 p-6;
}

.card-title {
  @apply text-2xl font-semibold leading-none tracking-tight;
}

.card-description {
  @apply text-sm text-muted-foreground;
}

.card-content {
  @apply p-6 pt-0;
}

.card-footer {
  @apply flex items-center p-6 pt-0;
}

.input {
  @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.label {
  @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
}

.table {
  @apply w-full caption-bottom text-sm;
}

.table-header {
  @apply [&_tr]:border-b;
}

.table-body {
  @apply [&_tr:last-child]:border-0;
}

.table-footer {
  @apply bg-primary text-primary-foreground font-medium;
}

.table-row {
  @apply border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted;
}

.table-head {
  @apply h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0;
}

.table-cell {
  @apply p-4 align-middle [&:has([role=checkbox])]:pr-0;
}

.table-caption {
  @apply mt-4 text-sm text-muted-foreground;
}

.alert {
  @apply relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground;
}

.alert-destructive {
  @apply border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive;
}

.alert-success {
  @apply border-green-500/50 text-green-600 dark:border-green-500 [&>svg]:text-green-600;
}

.alert-warning {
  @apply border-yellow-500/50 text-yellow-600 dark:border-yellow-500 [&>svg]:text-yellow-600;
}

.alert-info {
  @apply border-blue-500/50 text-blue-600 dark:border-blue-500 [&>svg]:text-blue-600;
}

/* Custom utility classes */
.btn-floating {
  @apply fixed bottom-8 right-8 h-14 w-14 rounded-full bg-primary text-primary-foreground shadow-lg flex items-center justify-center hover:bg-primary/90 transition-all;
}

.btn-floating i {
  @apply text-xl;
}

.invalid-feedback {
  @apply mt-1 text-sm text-destructive;
}

.form-group {
  @apply space-y-2;
}

.container {
  @apply px-4 mx-auto max-w-7xl;
}

.navbar {
  @apply bg-background border-b;
}

.navbar-brand {
  @apply text-xl font-bold;
}

.nav-link {
  @apply px-3 py-2 text-sm rounded-md hover:bg-accent hover:text-accent-foreground;
}

.nav-item {
  @apply flex;
}

.navbar-nav {
  @apply flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2;
}

.navbar-collapse {
  @apply flex-col md:flex-row w-full md:w-auto;
}

.navbar-toggler {
  @apply md:hidden p-2 rounded-md hover:bg-accent hover:text-accent-foreground;
}

.navbar-dark {
  @apply bg-primary text-primary-foreground;
}

.navbar-dark .nav-link {
  @apply text-primary-foreground hover:bg-primary/80;
}

.navbar-dark .navbar-brand {
  @apply text-primary-foreground;
}

.jumbotron {
  @apply py-12 px-4 rounded-lg bg-muted;
}

.display-4 {
  @apply text-4xl font-bold;
}

.lead {
  @apply text-xl text-muted-foreground;
}

.asset-details dt {
  @apply font-medium text-muted-foreground;
}

.asset-details dd {
  @apply mb-4;
}

.search-form {
  @apply flex space-x-2;
}

@media (max-width: 768px) {
  .btn-floating {
    @apply bottom-4 right-4 h-12 w-12;
  }
}
