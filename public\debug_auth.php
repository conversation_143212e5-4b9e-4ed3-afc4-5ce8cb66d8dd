<?php
/**
 * Authentication Debug Script
 * 
 * This script helps diagnose authentication issues with the reset password functionality.
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
// session_start();

// Load configuration and helpers
require_once '../app/config/config.php';
require_once '../app/helpers/session_helper.php';
require_once '../app/core/Database.php';
require_once '../app/models/User.php';

echo "<h1>Authentication Debug for Reset Password</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
</style>";

echo "<div class='section'>";
echo "<h2>Current Authentication Status</h2>";

// Check if logged in
$isLoggedIn = isLoggedIn();
echo "<p>Logged In: " . ($isLoggedIn ? "<span class='success'>YES</span>" : "<span class='error'>NO</span>") . "</p>";

if ($isLoggedIn) {
    echo "<p>User ID: <span class='info'>" . ($_SESSION['user_id'] ?? 'Not set') . "</span></p>";
    echo "<p>User Name: <span class='info'>" . ($_SESSION['user_name'] ?? 'Not set') . "</span></p>";
    echo "<p>User Email: <span class='info'>" . ($_SESSION['user_email'] ?? 'Not set') . "</span></p>";
    echo "<p>User Role: <span class='info'>" . ($_SESSION['user_role'] ?? 'Not set') . "</span></p>";
    
    // Check admin status
    $isAdmin = isAdmin();
    echo "<p>Is Admin: " . ($isAdmin ? "<span class='success'>YES</span>" : "<span class='error'>NO</span>") . "</p>";
    
    // Check specific roles and permissions
    echo "<h3>Role and Permission Details</h3>";
    echo "<p>Has 'Administrator' role: " . (hasRole('Administrator') ? "<span class='success'>YES</span>" : "<span class='error'>NO</span>") . "</p>";
    echo "<p>Has 'manage_users' permission: " . (hasPermission('manage_users') ? "<span class='success'>YES</span>" : "<span class='error'>NO</span>") . "</p>";
} else {
    echo "<p class='warning'>You are not logged in. This is why the reset password function redirects to login.</p>";
}

echo "</div>";

// Check database connection and users
echo "<div class='section'>";
echo "<h2>Database Status</h2>";

try {
    $db = new Database();
    echo "<p>Database Connection: <span class='success'>OK</span></p>";
    
    // Check if users exist
    $db->query("SELECT COUNT(*) as count FROM users");
    $result = $db->single();
    echo "<p>Total Users in Database: <span class='info'>" . $result->count . "</span></p>";
    
    // Check for admin users
    $db->query("SELECT * FROM users WHERE role = 'admin' OR id IN (SELECT user_id FROM user_roles ur JOIN roles r ON ur.role_id = r.id WHERE r.name = 'Administrator')");
    $adminUsers = $db->resultSet();
    echo "<p>Admin Users Found: <span class='info'>" . count($adminUsers) . "</span></p>";
    
    if (count($adminUsers) > 0) {
        echo "<h3>Available Admin Users:</h3>";
        echo "<ul>";
        foreach ($adminUsers as $admin) {
            echo "<li>ID: {$admin->id}, Name: {$admin->name}, Email: {$admin->email}, Role: {$admin->role}</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>Database Error: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Provide solutions
echo "<div class='section'>";
echo "<h2>Solutions</h2>";

if (!$isLoggedIn) {
    echo "<h3>Option 1: Log in as an existing admin user</h3>";
    echo "<p><a href='" . URLROOT . "/users/login' style='color: blue; text-decoration: underline;'>Go to Login Page</a></p>";
    
    echo "<h3>Option 2: Use the 'Forgot Password' feature (for self-reset)</h3>";
    echo "<p>If you want to reset your own password, use:</p>";
    echo "<p><a href='" . URLROOT . "/users/forgotPassword' style='color: blue; text-decoration: underline;'>Forgot Password Page</a></p>";
    
    echo "<h3>Option 3: Quick Admin Login (Development Only)</h3>";
    echo "<p><a href='" . URLROOT . "/../login_fix.php' style='color: blue; text-decoration: underline;'>Auto-login as Admin</a></p>";
    
} elseif (!isAdmin()) {
    echo "<h3>You are logged in but not as an admin</h3>";
    echo "<p class='warning'>The reset password function you're trying to access is for administrators only.</p>";
    echo "<p>Options:</p>";
    echo "<ul>";
    echo "<li>Log out and log in as an admin user</li>";
    echo "<li>Use the 'Forgot Password' feature to reset your own password</li>";
    echo "<li>Ask an administrator to reset the password for you</li>";
    echo "</ul>";
    
} else {
    echo "<h3>You are logged in as an admin!</h3>";
    echo "<p class='success'>You should be able to access the reset password functionality.</p>";
    echo "<p>Try accessing: <a href='" . URLROOT . "/users/manage' style='color: blue; text-decoration: underline;'>User Management Page</a></p>";
}

echo "</div>";

// Show session data for debugging
echo "<div class='section'>";
echo "<h2>Session Debug Information</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";
echo "</div>";

?>
