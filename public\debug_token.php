<?php
/**
 * Token Debug Script
 * 
 * This script helps debug password reset token issues.
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Load configuration and helpers
require_once '../app/config/config.php';
require_once '../app/core/Database.php';
require_once '../app/models/User.php';

echo "<h1>Password Reset Token Debug</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// Get token and email from URL or set test values
$token = $_GET['token'] ?? 'e5607cf77d67dee8ef823046973b416e0dd724a4c181f0ca3dc00dedaea3a604';
$email = $_GET['email'] ?? '<EMAIL>';

echo "<div class='section'>";
echo "<h2>Token Information</h2>";
echo "<p>Email: <span class='info'>" . htmlspecialchars($email) . "</span></p>";
echo "<p>Token: <span class='info'>" . htmlspecialchars($token) . "</span></p>";
echo "<p>Token Length: <span class='info'>" . strlen($token) . " characters</span></p>";
echo "</div>";

// Check database connection
echo "<div class='section'>";
echo "<h2>Database Connection</h2>";

try {
    $db = new Database();
    echo "<p>Database Connection: <span class='success'>OK</span></p>";
    
    // Check if password_reset_tokens table exists
    $db->query("SHOW TABLES LIKE 'password_reset_tokens'");
    $tableExists = $db->rowCount() > 0;
    echo "<p>password_reset_tokens table exists: " . ($tableExists ? "<span class='success'>YES</span>" : "<span class='error'>NO</span>") . "</p>";
    
    if (!$tableExists) {
        echo "<p class='error'>The password_reset_tokens table doesn't exist! This is likely the cause of the issue.</p>";
        echo "<p class='warning'>The table should be created automatically when you request a password reset.</p>";
        
        // Try to create the table
        echo "<h3>Attempting to create the table...</h3>";
        $sql = "CREATE TABLE IF NOT EXISTS password_reset_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            token VARCHAR(255) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            used TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (email),
            INDEX (token),
            INDEX (expires_at)
        ) ENGINE=InnoDB";
        
        $db->query($sql);
        if ($db->execute()) {
            echo "<p class='success'>Table created successfully!</p>";
            $tableExists = true;
        } else {
            echo "<p class='error'>Failed to create table.</p>";
        }
    }
    
    if ($tableExists) {
        // Show table structure
        echo "<h3>Table Structure</h3>";
        $db->query("DESCRIBE password_reset_tokens");
        $columns = $db->resultSet();
        echo "<pre>";
        foreach ($columns as $column) {
            echo $column->Field . " | " . $column->Type . " | " . $column->Null . " | " . $column->Key . " | " . $column->Default . " | " . $column->Extra . "\n";
        }
        echo "</pre>";
        
        // Check all tokens in the table
        echo "<h3>All Tokens in Database</h3>";
        $db->query("SELECT * FROM password_reset_tokens ORDER BY created_at DESC");
        $allTokens = $db->resultSet();
        echo "<p>Total tokens in database: <span class='info'>" . count($allTokens) . "</span></p>";
        
        if (count($allTokens) > 0) {
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>ID</th><th>Email</th><th>Token (first 20 chars)</th><th>Expires At</th><th>Used</th><th>Created At</th></tr>";
            foreach ($allTokens as $tokenRow) {
                $isExpired = strtotime($tokenRow->expires_at) < time();
                $rowClass = $isExpired ? 'style="background-color: #ffeeee;"' : '';
                echo "<tr $rowClass>";
                echo "<td>" . $tokenRow->id . "</td>";
                echo "<td>" . htmlspecialchars($tokenRow->email) . "</td>";
                echo "<td>" . htmlspecialchars(substr($tokenRow->token, 0, 20)) . "...</td>";
                echo "<td>" . $tokenRow->expires_at . ($isExpired ? ' <span class="error">(EXPIRED)</span>' : ' <span class="success">(VALID)</span>') . "</td>";
                echo "<td>" . ($tokenRow->used ? '<span class="error">YES</span>' : '<span class="success">NO</span>') . "</td>";
                echo "<td>" . $tokenRow->created_at . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>No tokens found in database. You need to request a password reset first.</p>";
        }
        
        // Check the specific token
        echo "<h3>Specific Token Verification</h3>";
        $db->query('SELECT * FROM password_reset_tokens WHERE email = :email AND token = :token');
        $db->bind(':email', $email);
        $db->bind(':token', $token);
        $specificToken = $db->single();
        $specificTokenCount = $db->rowCount();
        
        echo "<p>Token found in database: " . ($specificTokenCount > 0 ? "<span class='success'>YES</span>" : "<span class='error'>NO</span>") . "</p>";
        
        if ($specificTokenCount > 0) {
            echo "<h4>Token Details:</h4>";
            echo "<pre>";
            print_r($specificToken);
            echo "</pre>";
            
            $isExpired = strtotime($specificToken->expires_at) < time();
            $isUsed = $specificToken->used == 1;
            
            echo "<p>Token expired: " . ($isExpired ? "<span class='error'>YES</span>" : "<span class='success'>NO</span>") . "</p>";
            echo "<p>Token used: " . ($isUsed ? "<span class='error'>YES</span>" : "<span class='success'>NO</span>") . "</p>";
            echo "<p>Expires at: <span class='info'>" . $specificToken->expires_at . "</span></p>";
            echo "<p>Current time: <span class='info'>" . date('Y-m-d H:i:s') . "</span></p>";
            
            // Test the full verification query
            echo "<h4>Full Verification Query Test:</h4>";
            $db->query('SELECT * FROM password_reset_tokens WHERE email = :email AND token = :token AND expires_at > NOW() AND used = 0');
            $db->bind(':email', $email);
            $db->bind(':token', $token);
            $validToken = $db->single();
            $validTokenCount = $db->rowCount();
            
            echo "<p>Token passes full verification: " . ($validTokenCount > 0 ? "<span class='success'>YES</span>" : "<span class='error'>NO</span>") . "</p>";
            
            if ($validTokenCount === 0) {
                echo "<p class='error'>Token verification failed because:</p>";
                echo "<ul>";
                if ($isExpired) echo "<li>Token is expired</li>";
                if ($isUsed) echo "<li>Token has already been used</li>";
                if (!$isExpired && !$isUsed) echo "<li>Unknown reason - check database query</li>";
                echo "</ul>";
            }
        } else {
            echo "<p class='error'>Token not found in database. Possible reasons:</p>";
            echo "<ul>";
            echo "<li>Token was never created (password reset request failed)</li>";
            echo "<li>Token was deleted</li>";
            echo "<li>Email or token doesn't match exactly</li>";
            echo "</ul>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>Database Error: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test User Model
echo "<div class='section'>";
echo "<h2>User Model Test</h2>";

try {
    $userModel = new User();
    echo "<p>User Model loaded: <span class='success'>OK</span></p>";
    
    // Test the verification method
    echo "<h3>Testing verifyPasswordResetToken method:</h3>";
    $isValid = $userModel->verifyPasswordResetToken($email, $token);
    echo "<p>Token verification result: " . ($isValid ? "<span class='success'>VALID</span>" : "<span class='error'>INVALID</span>") . "</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>User Model Error: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Solutions
echo "<div class='section'>";
echo "<h2>Solutions</h2>";

echo "<h3>Option 1: Request a new password reset</h3>";
echo "<p>Go to the forgot password page and request a new reset link:</p>";
echo "<p><a href='" . URLROOT . "/users/forgotPassword' style='color: blue; text-decoration: underline;'>Forgot Password Page</a></p>";

echo "<h3>Option 2: Test with this URL after requesting new reset</h3>";
echo "<p>After requesting a new password reset, the new token will appear in the table above.</p>";

echo "<h3>Option 3: Manual token creation (for testing)</h3>";
if (isset($db) && $tableExists) {
    echo "<p><strong>For testing purposes only:</strong></p>";
    echo "<form method='post' style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo "<h4>Create Test Token</h4>";
    echo "<p>Email: <input type='email' name='test_email' value='" . htmlspecialchars($email) . "' required></p>";
    echo "<p><input type='submit' name='create_token' value='Create Test Token'></p>";
    echo "</form>";
    
    if (isset($_POST['create_token']) && isset($_POST['test_email'])) {
        $testEmail = $_POST['test_email'];
        $testToken = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour from now
        
        try {
            $db->query('DELETE FROM password_reset_tokens WHERE email = :email');
            $db->bind(':email', $testEmail);
            $db->execute();
            
            $db->query('INSERT INTO password_reset_tokens (email, token, expires_at) VALUES(:email, :token, :expires_at)');
            $db->bind(':email', $testEmail);
            $db->bind(':token', $testToken);
            $db->bind(':expires_at', $expires);
            
            if ($db->execute()) {
                $testUrl = URLROOT . "/users/resetPasswordWithToken?token=" . $testToken . "&email=" . urlencode($testEmail);
                echo "<p class='success'>Test token created successfully!</p>";
                echo "<p>Test URL: <a href='" . $testUrl . "' style='color: blue; text-decoration: underline;'>" . $testUrl . "</a></p>";
            } else {
                echo "<p class='error'>Failed to create test token.</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>Error creating test token: " . $e->getMessage() . "</p>";
        }
    }
}

echo "</div>";

?>
