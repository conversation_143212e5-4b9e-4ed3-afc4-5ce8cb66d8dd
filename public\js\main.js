// SweetAlert utility functions
const Toast = {
    fire: function(options) {
        const defaultOptions = {
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        };

        return Swal.fire({
            ...defaultOptions,
            ...options
        });
    },

    success: function(message) {
        return this.fire({
            icon: 'success',
            title: message
        });
    },

    error: function(message) {
        return this.fire({
            icon: 'error',
            title: message,
            position: 'center',
            timer: 5000
        });
    },

    warning: function(message) {
        return this.fire({
            icon: 'warning',
            title: message,
            position: 'center',
            timer: 5000
        });
    },

    info: function(message) {
        return this.fire({
            icon: 'info',
            title: message
        });
    }
};

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {

    // Initialize export buttons with SweetAlert2 notification
    const exportButtons = document.querySelectorAll('a[href*="/assets/export"]');
    if (exportButtons) {
        exportButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Show a loading message
                Swal.fire({
                    title: 'Preparing Export',
                    text: 'Your file is being generated...',
                    icon: 'info',
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Let the download happen
                setTimeout(() => {
                    Swal.close();
                    // Show success message
                    Toast.success('Export generated successfully');
                }, 1500);
            });
        });
    }

    // Initialize search functionality
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('basic-search-form').submit();
            }
        });
    }

    // Advanced search modal functionality
    const advancedSearchToggle = document.getElementById('advanced-search-toggle');
    const advancedSearchModal = document.getElementById('advanced-search-modal');
    const modalBackdrop = document.getElementById('modal-backdrop');
    const closeModalBtn = document.getElementById('close-modal-btn');
    const closeModalBtn2 = document.getElementById('close-modal-btn2');

    // Function to open modal with animation
    function openModal() {
        document.body.style.overflow = 'hidden'; // Prevent scrolling
        advancedSearchModal.classList.remove('hidden');

        // Add animation classes
        setTimeout(() => {
            modalBackdrop.classList.add('opacity-100');
            const modalContent = advancedSearchModal.querySelector('.transform');
            if (modalContent) {
                modalContent.classList.add('translate-y-0', 'opacity-100');
                modalContent.classList.remove('translate-y-4', 'opacity-0');
            }
        }, 10);
    }

    // Initialize modal if it's already visible (for search results page)
    function initializeVisibleModal() {
        if (advancedSearchModal && !advancedSearchModal.classList.contains('hidden')) {
            document.body.style.overflow = 'hidden';
            modalBackdrop.classList.add('opacity-100');
            const modalContent = advancedSearchModal.querySelector('.transform');
            if (modalContent) {
                modalContent.classList.add('translate-y-0', 'opacity-100');
                modalContent.classList.remove('translate-y-4', 'opacity-0');
            }
        }
    }

    // Function to close modal with animation
    function closeModal() {
        const modalContent = advancedSearchModal.querySelector('.transform');
        if (modalContent) {
            modalContent.classList.remove('translate-y-0', 'opacity-100');
            modalContent.classList.add('translate-y-4', 'opacity-0');
        }

        // Delay hiding the modal to allow for animation
        setTimeout(() => {
            advancedSearchModal.classList.add('hidden');
            document.body.style.overflow = ''; // Restore scrolling
        }, 200);
    }

    // Event listeners for opening/closing modal
    if (advancedSearchModal) {
        // Initialize modal if it's already visible
        initializeVisibleModal();

        if (advancedSearchToggle) {
            advancedSearchToggle.addEventListener('click', openModal);
        }

        // Close modal when clicking on backdrop or close button
        if (modalBackdrop) {
            modalBackdrop.addEventListener('click', closeModal);
        }

        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', closeModal);
        }

        if (closeModalBtn2) {
            closeModalBtn2.addEventListener('click', closeModal);
        }

        // Close modal when pressing Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !advancedSearchModal.classList.contains('hidden')) {
                closeModal();
            }
        });

        // Prevent clicks inside modal content from closing the modal
        const modalContent = advancedSearchModal.querySelector('.bg-white');
        if (modalContent) {
            modalContent.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }
    }

    // Confirm delete with SweetAlert2
    const deleteButtons = document.querySelectorAll('.delete-asset');
    if (deleteButtons) {
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#ef4444',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Yes, delete it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Show success message using our utility function
                        Toast.success('The asset has been deleted');

                        // Submit the form after a short delay to show the success message
                        setTimeout(() => {
                            this.closest('form').submit();
                        }, 1500);
                    }
                });
            });
        });
    }

    // Date picker initialization
    const dateInputs = document.querySelectorAll('input[type="date"]');
    if (dateInputs) {
        dateInputs.forEach(input => {
            // Set default date to today for new asset form
            if (input.value === '' && input.id === 'inventory_date') {
                const today = new Date();
                const yyyy = today.getFullYear();
                let mm = today.getMonth() + 1;
                let dd = today.getDate();

                if (dd < 10) dd = '0' + dd;
                if (mm < 10) mm = '0' + mm;

                input.value = yyyy + '-' + mm + '-' + dd;
            }
        });
    }

    // User Management - Toggle Status with SweetAlert
    const toggleStatusButtons = document.querySelectorAll('.toggle-status-btn');
    if (toggleStatusButtons) {
        toggleStatusButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const userId = this.getAttribute('data-user-id');
                const currentStatus = this.getAttribute('data-current-status');
                const userName = this.getAttribute('data-user-name');
                const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
                const actionText = currentStatus === 'active' ? 'deactivate' : 'activate';
                const actionColor = currentStatus === 'active' ? '#ef4444' : '#10b981'; // Red for deactivate, green for activate

                Swal.fire({
                    title: `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} User?`,
                    html: `Are you sure you want to <strong>${actionText}</strong> user <strong>${userName}</strong>?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: actionColor,
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: `Yes, ${actionText}!`,
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Show loading state
                        Swal.fire({
                            title: 'Processing...',
                            text: `${actionText.charAt(0).toUpperCase() + actionText.slice(1)}ing user...`,
                            icon: 'info',
                            allowOutsideClick: false,
                            showConfirmButton: false,
                            willOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        // Redirect to the action URL
                        window.location.href = `${URLROOT}/users/toggleStatus/${userId}`;
                    }
                });
            });
        });
    }

    // User Management - Toggle Role with SweetAlert
    const toggleRoleButtons = document.querySelectorAll('.toggle-role-btn');
    if (toggleRoleButtons) {
        toggleRoleButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const userId = this.getAttribute('data-user-id');
                const currentRole = this.getAttribute('data-current-role');
                const userName = this.getAttribute('data-user-name');
                const newRole = currentRole === 'admin' ? 'User' : 'Admin';

                Swal.fire({
                    title: 'Change User Role?',
                    html: `Are you sure you want to change <strong>${userName}</strong>'s role to <strong>${newRole}</strong>?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3b82f6', // Blue for role change
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Yes, change role!',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Show loading state
                        Swal.fire({
                            title: 'Processing...',
                            text: 'Changing user role...',
                            icon: 'info',
                            allowOutsideClick: false,
                            showConfirmButton: false,
                            willOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        // Redirect to the action URL
                        window.location.href = `${URLROOT}/users/toggleRole/${userId}`;
                    }
                });
            });
        });
    }

    // User Management - Reset Password with SweetAlert
    const resetPasswordLinks = document.querySelectorAll('a[href*="/users/resetPassword/"]');
    if (resetPasswordLinks) {
        resetPasswordLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const href = this.getAttribute('href');

                Swal.fire({
                    title: 'Reset Password?',
                    text: 'You will be redirected to the password reset form.',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#eab308', // Yellow for password reset
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Yes, proceed',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = href;
                    }
                });
            });
        });
    }

    // Function to show validation errors with SweetAlert
    function showValidationErrors() {
        // Check if we're on a page that was just submitted (has form errors)
        // We'll use the URL's search params to determine this
        const urlParams = new URLSearchParams(window.location.search);
        const formSubmitted = urlParams.get('form_submitted');

        // Only show the validation error message if the form was just submitted
        // This prevents showing the message when just loading the page
        if (formSubmitted === 'true') {
            const errorElements = document.querySelectorAll('.text-red-500, .border-red-500');
            if (errorElements.length > 0) {
                // Get all error messages
                const errorMessages = [];
                document.querySelectorAll('.text-red-500').forEach(el => {
                    if (el.textContent.trim() !== '') {
                        errorMessages.push(el.textContent.trim());
                    }
                });

                // If we have error messages, show them in a SweetAlert
                if (errorMessages.length > 0) {
                    Toast.error('Please correct the errors in the form');
                }
            }
        }
    }

    // Call the function when the page loads
    showValidationErrors();
});
