/**
 * Tag Manager - Handles tag addition and removal functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Add click event listeners to all tag remove buttons
    const tagItems = document.querySelectorAll('.tag-item');
    
    tagItems.forEach(tagItem => {
        // Add a remove button if it doesn't exist
        if (!tagItem.querySelector('.remove-tag-btn')) {
            const removeBtn = document.createElement('button');
            removeBtn.className = 'remove-tag-btn';
            removeBtn.innerHTML = '&times;'; // × symbol
            removeBtn.style.marginLeft = '5px';
            removeBtn.style.color = '#ff0000';
            removeBtn.style.fontWeight = 'bold';
            removeBtn.style.cursor = 'pointer';
            removeBtn.style.border = 'none';
            removeBtn.style.background = 'transparent';
            removeBtn.title = 'Remove this tag';
            
            // Add the button to the tag item
            tagItem.appendChild(removeBtn);
        }
    });
    
    // Add event delegation for tag removal
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-tag-btn')) {
            const tagItem = e.target.closest('.tag-item');
            if (tagItem) {
                // Get the tag name for confirmation message
                const tagName = tagItem.textContent.replace('×', '').trim();
                
                // Remove the tag item
                tagItem.remove();
                
                // Show a confirmation message
                const confirmationMsg = document.createElement('div');
                confirmationMsg.textContent = `Tag "${tagName}" removed`;
                confirmationMsg.style.color = 'green';
                confirmationMsg.style.marginTop = '5px';
                confirmationMsg.style.marginBottom = '10px';
                
                // Find the tags container and add the confirmation message
                const tagsContainer = document.querySelector('.tags-container');
                if (tagsContainer) {
                    tagsContainer.appendChild(confirmationMsg);
                    
                    // Remove the confirmation message after 3 seconds
                    setTimeout(() => {
                        confirmationMsg.remove();
                    }, 3000);
                }
            }
        }
    });
});
