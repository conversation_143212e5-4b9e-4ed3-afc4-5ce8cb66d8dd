<?php
/**
 * Test Email Script
 *
 * This script tests the email functionality with different SMTP configurations.
 * Run this script to verify your email configuration is working.
 */

// Set longer execution time for testing
set_time_limit(60);

// Load the application bootstrap\

// require_once str_replace('public','app/config',dirname(__FILE__).'/config.php');
require_once str_replace('public','app',dirname(__FILE__).'/bootstrap.php');
echo str_replace('public','app',dirname(__FILE__).'/bootstrap.php');
// require_once 'app/bootstrap.php';

// Test email configuration
echo "<h2>Email Configuration Test</h2>\n";
echo "<p>Testing email functionality with SMTP...</p>\n";

// Check if email constants are defined
$configCheck = [
    'SMTP_HOST' => defined('SMTP_HOST') ? SMTP_HOST_GMAIL : 'NOT DEFINED',
    'SMTP_PORT' => defined('SMTP_PORT') ? SMTP_PORT_GMAIL : 'NOT DEFINED',
    'SMTP_USERNAME' => defined('SMTP_USERNAME') ? SMTP_USERNAME : 'NOT DEFINED',
    'SMTP_PASSWORD' => defined('SMTP_PASSWORD') ? (SMTP_PASSWORD ? 'SET' : 'NOT SET') : 'NOT DEFINED',
    'MAIL_FROM_EMAIL' => defined('MAIL_FROM_EMAIL') ? MAIL_FROM_EMAIL : 'NOT DEFINED',
];

echo "<h3>Configuration Check:</h3>\n";
echo "<ul>\n";
foreach ($configCheck as $key => $value) {
    echo "<li><strong>$key:</strong> $value</li>\n";
}
echo "</ul>\n";

// Function to test SMTP connection without sending email
function testSMTPConnection($host, $port, $timeout = 10) {
    $connection = @fsockopen($host, $port, $errno, $errstr, $timeout);
    if ($connection) {
        fclose($connection);
        return true;
    }
    return false;
}

// Test SMTP connectivity
echo "<h3>SMTP Connectivity Test:</h3>\n";
$smtpHosts = [
    'Current Config' => ['host' => SMTP_HOST_GMAIL, 'port' => SMTP_PORT_GMAIL],
    'Gmail' => ['host' => 'smtp.gmail.com', 'port' => 465],
];

echo "<ul>\n";
foreach ($smtpHosts as $name => $config) {
    $canConnect = testSMTPConnection($config['host'], $config['port'], 5);
    $status = $canConnect ? "✓ Connected" : "✗ Cannot connect";
    $color = $canConnect ? "green" : "red";
    echo "<li><strong>$name ({$config['host']}:{$config['port']}):</strong> <span style='color: $color;'>$status</span></li>\n";
}
echo "</ul>\n";

// Test email sending (only if configuration looks good)
if (defined('SMTP_USERNAME') && defined('SMTP_PASSWORD') && SMTP_USERNAME !== '<EMAIL>') {
    echo "<h3>Sending Test Email...</h3>\n";

    $testEmail = SMTP_USERNAME; // Send test email to yourself
    $subject = "Test Email from " . SITENAME;
    $body = "
    <html>
    <head>
        <title>Test Email</title>
    </head>
    <body>
        <h2>Email Test Successful!</h2>
        <p>This is a test email from your Asset Visibility System.</p>
        <p>If you received this email, your SMTP configuration is working correctly.</p>
        <p><strong>Timestamp:</strong> " . date('Y-m-d H:i:s') . "</p>
        <p><strong>Server:</strong> " . $_SERVER['HTTP_HOST'] . "</p>
        <p><strong>SMTP Host:</strong> " . SMTP_HOST_GMAIL . "</p>
        <p><strong>SMTP Port:</strong> " . SMTP_PORT_GMAIL . "</p>
    </body>
    </html>";

    $altBody = "Email Test Successful!\n\n" .
               "This is a test email from your Asset Visibility System.\n" .
               "If you received this email, your SMTP configuration is working correctly.\n\n" .
               "Timestamp: " . date('Y-m-d H:i:s') . "\n" .
               "Server: " . $_SERVER['HTTP_HOST'] . "\n" .
               "SMTP Host: " . SMTP_HOST_GMAIL . "\n" .
               "SMTP Port: " . SMTP_PORT_GMAIL;

    echo "<p>Attempting to send email to: <strong>$testEmail</strong></p>\n";
    echo "<p>Using SMTP: <strong>" . SMTP_HOST_GMAIL . ":" . SMTP_PORT_GMAIL . "</strong></p>\n";

    if (sendEmail($testEmail, $subject, $body, $altBody)) {
        echo "<p style='color: green;'><strong>✓ Test email sent successfully!</strong></p>\n";
        echo "<p>Check your inbox at: $testEmail</p>\n";
    } else {
        echo "<p style='color: red;'><strong>✗ Failed to send test email.</strong></p>\n";
        echo "<p>Check the error details above and try different SMTP settings.</p>\n";
    }
} else {
    echo "<h3 style='color: orange;'>Configuration Required</h3>\n";
    echo "<p>Please update your email configuration in <code>app/config/config.php</code>:</p>\n";
    echo "<ol>\n";
    echo "<li>Replace <code><EMAIL></code> with your actual email address</li>\n";
    echo "<li>Replace <code>your-app-password</code> with your email password</li>\n";
    echo "</ol>\n";
}

echo "<hr>\n";
echo "<h3>Setup Instructions:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Enable 2-Factor Authentication</strong> on your Gmail account</li>\n";
echo "<li><strong>Generate App Password:</strong>\n";
echo "   <ul>\n";
echo "       <li>Go to Google Account settings</li>\n";
echo "       <li>Security → 2-Step Verification → App passwords</li>\n";
echo "       <li>Generate a new app password for 'Mail'</li>\n";
echo "   </ul>\n";
echo "</li>\n";
echo "<li><strong>Update config.php:</strong>\n";
echo "   <ul>\n";
echo "       <li>Set SMTP_USERNAME to your Gmail address</li>\n";
echo "       <li>Set SMTP_PASSWORD to the generated app password</li>\n";
echo "       <li>Set MAIL_FROM_EMAIL to your Gmail address</li>\n";
echo "   </ul>\n";
echo "</li>\n";
echo "</ol>\n";
?>
