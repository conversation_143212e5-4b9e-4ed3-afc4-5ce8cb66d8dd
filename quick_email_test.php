<?php
/**
 * Quick Email Test - Simple test without complex debugging
 */

// Load the application bootstrap
require_once 'app/bootstrap.php';

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

echo "<h2>Quick Email Test</h2>\n";

// Simple test function
function quickEmailTest() {
    $mail = new PHPMailer(true);
    
    try {
        // Basic settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = SMTP_PORT;
        
        // Timeout and options
        $mail->Timeout = 30;
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // Recipients
        $mail->setFrom(SMTP_USERNAME, 'Test Sender');
        $mail->addAddress(SMTP_USERNAME);

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Quick Test - ' . date('H:i:s');
        $mail->Body = '<h3>Success!</h3><p>Email is working correctly.</p>';

        $mail->send();
        return true;
        
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red;'>";
        echo "<strong>Error:</strong> " . $e->getMessage() . "<br>";
        echo "<strong>Mail Error:</strong> " . $mail->ErrorInfo . "<br>";
        echo "</div>";
        return false;
    }
}

echo "<p><strong>Configuration:</strong></p>";
echo "<ul>";
echo "<li>SMTP Host: " . SMTP_HOST . "</li>";
echo "<li>SMTP Port: " . SMTP_PORT . "</li>";
echo "<li>Username: " . SMTP_USERNAME . "</li>";
echo "<li>Security: " . SMTP_SECURE . "</li>";
echo "</ul>";

echo "<p>Testing email...</p>";

if (quickEmailTest()) {
    echo "<p style='color: green; font-size: 18px;'><strong>✓ SUCCESS! Email sent successfully!</strong></p>";
    echo "<p>Check your inbox at: " . SMTP_USERNAME . "</p>";
} else {
    echo "<p style='color: red; font-size: 18px;'><strong>✗ FAILED! Could not send email.</strong></p>";
    
    echo "<h3>Troubleshooting Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Check Gmail Settings:</strong>";
    echo "<ul>";
    echo "<li>Make sure 2-Factor Authentication is enabled</li>";
    echo "<li>Use an App Password, not your regular Gmail password</li>";
    echo "<li>Go to: Google Account → Security → App passwords</li>";
    echo "</ul></li>";
    echo "<li><strong>Check Network:</strong>";
    echo "<ul>";
    echo "<li>Make sure port 587 is not blocked by firewall</li>";
    echo "<li>Try from a different network if possible</li>";
    echo "</ul></li>";
    echo "<li><strong>Try Alternative Settings:</strong>";
    echo "<ul>";
    echo "<li>Port 465 with SSL instead of 587 with TLS</li>";
    echo "<li>Check if 'Less secure app access' needs to be enabled (not recommended)</li>";
    echo "</ul></li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>If successful, your email system is ready to use</li>";
echo "<li>If failed, run <a href='debug_email.php'>debug_email.php</a> for detailed analysis</li>";
echo "<li>Update your Gmail App Password if authentication fails</li>";
echo "</ul>";
?>
