<?php
// Test script to verify database connection and assets table

// Load configuration
require_once 'app/config/config.php';

// Display errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Database Connection Test</h1>";

try {
    // Create PDO connection
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $pdo = new PDO($dsn, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color:green;'>✓ Successfully connected to database: " . DB_NAME . "</p>";
    
    // Check if assets table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'assets'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color:green;'>✓ Assets table exists</p>";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE assets");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h2>Table Structure:</h2>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            foreach ($column as $key => $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Count records
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM assets");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h2>Record Count:</h2>";
        echo "<p>Number of assets in database: <strong>" . $result['count'] . "</strong></p>";
        
        // Test insert capability
        echo "<h2>Test Insert:</h2>";
        
        if (isset($_POST['test_insert'])) {
            try {
                $stmt = $pdo->prepare("INSERT INTO assets (inventory_date, site_name, employee_name, computer_host_name, equipment_type, serial_number) 
                                      VALUES (NOW(), 'Test Site', 'Test User', 'TEST-PC-" . time() . "', 'Test Equipment', 'TEST-SN-" . time() . "')");
                
                if ($stmt->execute()) {
                    $lastId = $pdo->lastInsertId();
                    echo "<p style='color:green;'>✓ Successfully inserted test record with ID: " . $lastId . "</p>";
                    
                    // Show the inserted record
                    $stmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
                    $stmt->execute([$lastId]);
                    $record = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    echo "<h3>Inserted Record:</h3>";
                    echo "<table border='1' cellpadding='5'>";
                    echo "<tr>";
                    foreach ($record as $key => $value) {
                        echo "<th>" . htmlspecialchars($key) . "</th>";
                    }
                    echo "</tr>";
                    echo "<tr>";
                    foreach ($record as $value) {
                        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                    }
                    echo "</tr>";
                    echo "</table>";
                    
                    // Option to delete the test record
                    echo "<form method='post'>";
                    echo "<input type='hidden' name='delete_id' value='" . $lastId . "'>";
                    echo "<button type='submit' style='margin-top: 10px;'>Delete Test Record</button>";
                    echo "</form>";
                } else {
                    echo "<p style='color:red;'>✗ Failed to insert test record</p>";
                }
            } catch (PDOException $e) {
                echo "<p style='color:red;'>✗ Error inserting test record: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<form method='post'>";
            echo "<input type='hidden' name='test_insert' value='1'>";
            echo "<button type='submit'>Insert Test Record</button>";
            echo "</form>";
        }
        
        // Delete test record if requested
        if (isset($_POST['delete_id'])) {
            try {
                $stmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
                if ($stmt->execute([$_POST['delete_id']])) {
                    echo "<p style='color:green;'>✓ Successfully deleted test record with ID: " . $_POST['delete_id'] . "</p>";
                } else {
                    echo "<p style='color:red;'>✗ Failed to delete test record</p>";
                }
            } catch (PDOException $e) {
                echo "<p style='color:red;'>✗ Error deleting test record: " . $e->getMessage() . "</p>";
            }
        }
        
    } else {
        echo "<p style='color:red;'>✗ Assets table does not exist</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color:red;'>✗ Connection failed: " . $e->getMessage() . "</p>";
}
