<?php
// Test script to verify file upload functionality

// Display errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>File Upload Test</h1>";

// Check PHP configuration for file uploads
echo "<h2>PHP File Upload Configuration:</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>upload_max_filesize</td><td>" . ini_get('upload_max_filesize') . "</td></tr>";
echo "<tr><td>post_max_size</td><td>" . ini_get('post_max_size') . "</td></tr>";
echo "<tr><td>max_file_uploads</td><td>" . ini_get('max_file_uploads') . "</td></tr>";
echo "<tr><td>max_input_time</td><td>" . ini_get('max_input_time') . "</td></tr>";
echo "<tr><td>memory_limit</td><td>" . ini_get('memory_limit') . "</td></tr>";
echo "<tr><td>file_uploads</td><td>" . (ini_get('file_uploads') ? 'Enabled' : 'Disabled') . "</td></tr>";
echo "<tr><td>upload_tmp_dir</td><td>" . (ini_get('upload_tmp_dir') ?: 'System default') . "</td></tr>";
echo "</table>";

// Check if upload directory is writable
$uploadDir = ini_get('upload_tmp_dir') ?: sys_get_temp_dir();
echo "<h2>Upload Directory Check:</h2>";
echo "<p>Upload directory: <strong>" . $uploadDir . "</strong></p>";
echo "<p>Is writable: <strong>" . (is_writable($uploadDir) ? 'Yes' : 'No') . "</strong></p>";

// Process file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    echo "<h2>Upload Results:</h2>";
    
    $file = $_FILES['test_file'];
    
    echo "<h3>File Information:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Property</th><th>Value</th></tr>";
    echo "<tr><td>Name</td><td>" . htmlspecialchars($file['name']) . "</td></tr>";
    echo "<tr><td>Type</td><td>" . htmlspecialchars($file['type']) . "</td></tr>";
    echo "<tr><td>Size</td><td>" . htmlspecialchars($file['size']) . " bytes</td></tr>";
    echo "<tr><td>Temporary Name</td><td>" . htmlspecialchars($file['tmp_name']) . "</td></tr>";
    echo "<tr><td>Error Code</td><td>" . htmlspecialchars($file['error']) . " - " . getUploadErrorMessage($file['error']) . "</td></tr>";
    echo "</table>";
    
    // Check if file was uploaded successfully
    if ($file['error'] === UPLOAD_ERR_OK) {
        echo "<p style='color:green;'>✓ File uploaded successfully</p>";
        
        // Check if the temporary file exists
        if (file_exists($file['tmp_name'])) {
            echo "<p style='color:green;'>✓ Temporary file exists at: " . htmlspecialchars($file['tmp_name']) . "</p>";
            
            // Try to read the file
            $content = file_get_contents($file['tmp_name']);
            if ($content !== false) {
                echo "<p style='color:green;'>✓ Successfully read file content, length: " . strlen($content) . " bytes</p>";
                
                // Display file content preview
                echo "<h3>Content Preview:</h3>";
                echo "<pre style='background-color: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow: auto; max-height: 300px;'>";
                echo htmlspecialchars(substr($content, 0, 1000)) . (strlen($content) > 1000 ? '...' : '');
                echo "</pre>";
                
                // If it's a CSV file, try to parse it
                if (strtolower(pathinfo($file['name'], PATHINFO_EXTENSION)) === 'csv') {
                    echo "<h3>CSV Parsing Test:</h3>";
                    
                    try {
                        $handle = fopen($file['tmp_name'], 'r');
                        if ($handle) {
                            echo "<p style='color:green;'>✓ Successfully opened CSV file</p>";
                            
                            echo "<table border='1' cellpadding='5'>";
                            $rowCount = 0;
                            $maxRows = 5; // Show first 5 rows
                            
                            while (($row = fgetcsv($handle)) !== FALSE && $rowCount < $maxRows) {
                                if ($rowCount === 0) {
                                    echo "<tr>";
                                    for ($i = 0; $i < count($row); $i++) {
                                        echo "<th>Column " . ($i + 1) . "</th>";
                                    }
                                    echo "</tr>";
                                }
                                
                                echo "<tr>";
                                foreach ($row as $cell) {
                                    echo "<td>" . htmlspecialchars($cell) . "</td>";
                                }
                                echo "</tr>";
                                
                                $rowCount++;
                            }
                            
                            echo "</table>";
                            
                            if ($rowCount === 0) {
                                echo "<p style='color:red;'>✗ No rows found in CSV file</p>";
                            } else {
                                echo "<p>Displayed first " . $rowCount . " rows of CSV file</p>";
                            }
                            
                            fclose($handle);
                        } else {
                            echo "<p style='color:red;'>✗ Failed to open CSV file for parsing</p>";
                        }
                    } catch (Exception $e) {
                        echo "<p style='color:red;'>✗ Error parsing CSV: " . $e->getMessage() . "</p>";
                    }
                }
                
                // Create a copy of the file
                $copyPath = sys_get_temp_dir() . '/' . uniqid('copy_') . '_' . $file['name'];
                if (copy($file['tmp_name'], $copyPath)) {
                    echo "<p style='color:green;'>✓ Successfully created a copy of the file at: " . htmlspecialchars($copyPath) . "</p>";
                    
                    // Verify the copy exists and is readable
                    if (file_exists($copyPath) && is_readable($copyPath)) {
                        echo "<p style='color:green;'>✓ Copy file exists and is readable</p>";
                        
                        // Compare file sizes
                        $originalSize = filesize($file['tmp_name']);
                        $copySize = filesize($copyPath);
                        
                        if ($originalSize === $copySize) {
                            echo "<p style='color:green;'>✓ Original and copy file sizes match: " . $originalSize . " bytes</p>";
                        } else {
                            echo "<p style='color:red;'>✗ File size mismatch: Original = " . $originalSize . " bytes, Copy = " . $copySize . " bytes</p>";
                        }
                    } else {
                        echo "<p style='color:red;'>✗ Copy file does not exist or is not readable</p>";
                    }
                } else {
                    echo "<p style='color:red;'>✗ Failed to create a copy of the file</p>";
                }
            } else {
                echo "<p style='color:red;'>✗ Failed to read file content</p>";
            }
        } else {
            echo "<p style='color:red;'>✗ Temporary file does not exist at: " . htmlspecialchars($file['tmp_name']) . "</p>";
        }
    } else {
        echo "<p style='color:red;'>✗ File upload failed with error code: " . $file['error'] . " - " . getUploadErrorMessage($file['error']) . "</p>";
    }
}

// Display upload form
echo "<h2>Upload Test Form:</h2>";
echo "<form action='' method='post' enctype='multipart/form-data'>";
echo "<p><input type='file' name='test_file' required></p>";
echo "<p><button type='submit'>Upload File</button></p>";
echo "</form>";

// Function to get upload error message
function getUploadErrorMessage($errorCode) {
    switch ($errorCode) {
        case UPLOAD_ERR_INI_SIZE:
            return 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
        case UPLOAD_ERR_FORM_SIZE:
            return 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form';
        case UPLOAD_ERR_PARTIAL:
            return 'The uploaded file was only partially uploaded';
        case UPLOAD_ERR_NO_FILE:
            return 'No file was uploaded';
        case UPLOAD_ERR_NO_TMP_DIR:
            return 'Missing a temporary folder';
        case UPLOAD_ERR_CANT_WRITE:
            return 'Failed to write file to disk';
        case UPLOAD_ERR_EXTENSION:
            return 'A PHP extension stopped the file upload';
        case UPLOAD_ERR_OK:
            return 'No error, file uploaded successfully';
        default:
            return 'Unknown upload error';
    }
}
