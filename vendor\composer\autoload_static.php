<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit118ff34e0e42d15e5bc2009bd6c53b26
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'P<PERSON><PERSON>ailer\\PHPMailer\\' => 20,
        ),
        'A' => 
        array (
            'AssetVisibility\\' => 16,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
        'AssetVisibility\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'PHPMailer\\PHPMailer\\DSNConfigurator' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/DSNConfigurator.php',
        'P<PERSON><PERSON>ail<PERSON>\\PHPMailer\\Exception' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/Exception.php',
        'PHPMailer\\PHPMailer\\OAuth' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/OAuth.php',
        'PHPMailer\\PHPMailer\\OAuthTokenProvider' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/OAuthTokenProvider.php',
        'PHPMailer\\PHPMailer\\PHPMailer' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/PHPMailer.php',
        'PHPMailer\\PHPMailer\\POP3' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/POP3.php',
        'PHPMailer\\PHPMailer\\SMTP' => __DIR__ . '/..' . '/phpmailer/phpmailer/src/SMTP.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit118ff34e0e42d15e5bc2009bd6c53b26::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit118ff34e0e42d15e5bc2009bd6c53b26::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit118ff34e0e42d15e5bc2009bd6c53b26::$classMap;

        }, null, ClassLoader::class);
    }
}
